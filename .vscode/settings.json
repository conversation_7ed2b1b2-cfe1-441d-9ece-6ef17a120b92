{"i18n-ally.annotationDelimiter": "·", "i18n-ally.localesPaths": ["public/locales"], "i18n-ally.defaultLocale": "en", "i18n-ally.enabledFrameworks": ["react", "next-intl"], "i18n-ally.keystyle": "nested", "i18n-ally.sourceLanguage": "en", "i18n-ally.displayLanguage": "zh", "i18n-ally.namespace": true, "i18n-ally.sortKeys": true, "i18n-ally.keepFulfilled": true, "i18n-ally.extract.autoDetect": true, "i18n-ally.extract.keygenStyle": "camelCase", "cSpell.words": ["aigc", "meitu", "<PERSON><PERSON>"]}