# ARG IMAGE_REGISTRY
# FROM ${IMAGE_REGISTRY}/public/hf_pm2_node12.22
# FROM swr.cn-north-4.myhuaweicloud.com/opsci/zcool_node:v12.22.11-9.0
FROM swr.cn-north-4.myhuaweicloud.com/opsci/whee-global-node-v22-11-0:v1

RUN echo Asia/Shanghai > /etc/timezone
LABEL stage=work

WORKDIR /app

ARG ENV
ENV ENV ${ENV}

COPY ./ /app/

# COPY package.json /app/package.json
# COPY next.config.js /app/next.config.js
# COPY ecosystem.json /app/ecosystem.json
# COPY public /app/public
# COPY shared /app/shared
# COPY lang /app/lang
# COPY node_modules /app/node_modules
# COPY .next /app/.next

# COPY plus.conf /etc/nginx/conf.d/

EXPOSE 5000

COPY entrypoint.sh /sbin/entrypoint.sh
RUN chmod +x /sbin/entrypoint.sh
ENTRYPOINT ["/sbin/entrypoint.sh"]

# CMD ["pm2-runtime", "start", "ecosystem.json"]
