module.exports = {
    disableEmoji: true,
    format: '{type}{scope}: {subject}',
    list: ['test', 'feat', 'fix', 'chore', 'docs', 'refactor', 'style', 'ci', 'perf'],
    maxMessageLength: 64,
    minMessageLength: 3,
    questions: ['type', 'scope', 'subject', 'body', 'breaking', 'issues', 'lerna'],
    scopes: [],
    types: {
      chore: {
        description: '杂务处理 其他不会修改源文件或者测试文件的更改',
        value: 'chore'
      },
      ci: {
        description: '脚本变更 对 CI 配置文件和脚本的更改',
        value: 'ci'
      },
      docs: {
        description: '档变更 添加或者更新文档',
        value: 'docs'
      },
      feat: {
        description: '添加功能 引入新的特性',
        value: 'feat'
      },
      fix: {
        description: '错误修复 修复 bug',
        value: 'fix'
      },
      perf: {
        description: '性能优化 更改代码以提高性能',
        value: 'perf'
      },
      refactor: {
        description: '码重构 即不是修复 Bug，也不是添加特性的代码更改',
        value: 'refactor'
      },
      revert: {
        description: '恢复版本 恢复到上一个版本',
        value: 'revert'
      },
      style: {
        description: '格式调整 不会影响代码含义的更改',
        value: 'style'
      },
      test: {
        description: '更新测试 添加或者更新测试',
        value: 'test'
      },
      messages: {
        type: 'Select the type of change that you\'re committing:',
        customScope: 'Select the scope this component affects:',
        subject: 'Write a short, imperative mood description of the change:\n',
        body: 'Provide a longer description of the change:\n ',
        breaking: 'List any breaking changes:\n',
        footer: 'Issues this commit closes, e.g #123:',
        confirmCommit: 'The packages that this commit has affected\n',
      },
    }
  };