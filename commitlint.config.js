/**
    build: 依赖调整 影响构建系统或外部依赖的更改 (示例作用域：gulp, broccoli, npm）
    chore: 杂务处理 其他不会修改源文件或者测试文件的更改
    ci: 脚本变更 对 CI 配置文件和脚本的更改（示例作用域： Travis, Circle, BrowserStack, SauceLabs)
    docs: 文档变更 添加或者更新文档
    feat: 添加功能 引入新的特性
    fix 错误修复 修复 bug
    perf: 性能优化 更改代码以提高性能
    refactor: 代码重构 即不是修复 Bug，也不是添加特性的代码更改
    revert: 恢复版本 恢复到上一个版本
    style: 格式调整 不会影响代码含义的更改（空格，格式缺少分号等）
    test: 更新测试 添加或者更新测试
 */

  module.exports = {

    // 使用 @commitlint/config-conventional规则
    extends: ['@commitlint/config-conventional'],
  
    // 定义验证规则
    rules: {
  
      // header最大100字符
      'header-max-length': [0, 'always', 100],
  
      // subject不能为空
      'subject-empty': [2, 'never'],
  
      // type的类型必须在指定范围内
      'type-enum': [
        2,
        'always',
        ['build', 'ci', 'chore', 'docs', 'feat', 'fix', 'perf', 'refactor', 'revert', 'style', 'test']
      ],
  
      // type不能为空
      "type-empty": [2, 'never'],
  
      // type必须小写
      "type-case": [2, "always", 'lowerCase'],
  
      // scope 不能为空
      "scope-empty": [0, "never"],
  
      // scope 必须小写
      "scope-case": [2, "always", "lowerCase"],
  
      // scope 不限制类型
      "scope-enum": [2, "always", []]
  
    }
  
  };
  