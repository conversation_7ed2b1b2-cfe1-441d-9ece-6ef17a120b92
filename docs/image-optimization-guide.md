# 图片渲染优化指南

## 问题描述
在React应用中，图片组件每次都会重新渲染，导致性能问题，特别是在包含大量图片的列表或选择器中。

## 优化方案

### 1. URL缓存优化
**问题**: `toAtlasImageView2URL` 每次调用都会生成新的URL，导致React认为props发生变化。

**解决方案**: 使用 `useImageCache` Hook
```typescript
// src/hooks/useImageCache.ts
const { getImageUrl } = useImageCache();
const cachedUrl = getImageUrl(originalUrl, { format: 'webp', height: 36 });
```

### 2. 组件Memo化
**问题**: 父组件重新渲染时，子组件也会重新渲染。

**解决方案**: 使用 `React.memo` 包装图片组件
```typescript
const OptimizedFontImage = memo(({ previewFile, name, className }) => {
  // 组件实现
});
```

### 3. 懒加载和预加载
**问题**: 大量图片同时加载影响性能。

**解决方案**: 实现智能加载策略
```typescript
<OptimizedImage
  src={imageUrl}
  lazy={true}        // 懒加载
  preload={false}    // 根据需要预加载
  cacheOptions={{ format: 'webp', height: 36 }}
/>
```

### 4. 全局缓存策略
**问题**: 相同图片在不同组件中重复处理。

**解决方案**: 全局缓存实例
```typescript
// 全局缓存，在应用生命周期内持久化
const globalImageCache = new Map<string, string>();
```

## 实施步骤

### 步骤1: 创建图片缓存Hook
```bash
# 创建 src/hooks/useImageCache.ts
```

### 步骤2: 创建优化的图片组件
```bash
# 创建 src/components/OptimizedImage/index.tsx
```

### 步骤3: 替换现有图片组件
```typescript
// 替换前
<Image src={toAtlasImageView2URL(item.previewFile, options)} />

// 替换后
<OptimizedFontImage previewFile={item.previewFile} name={item.name} />
```

### 步骤4: 添加性能监控
```typescript
// 开发环境启用性能监控
const { getMetrics } = useRenderPerformance('ComponentName', isDev);
```

## 性能提升效果

### 优化前
- 每次渲染都重新计算URL
- 图片组件频繁重新渲染
- 网络请求重复发送
- 内存使用不断增长

### 优化后
- URL计算结果被缓存
- 图片组件只在必要时重新渲染
- 网络请求去重
- 内存使用稳定

## 最佳实践

### 1. 缓存策略
- 为不同尺寸的图片使用不同的缓存键
- 定期清理不再使用的缓存
- 监控缓存大小，避免内存泄漏

### 2. 组件设计
- 使用 `React.memo` 包装纯组件
- 将图片相关逻辑抽离到专门的Hook
- 避免在render函数中创建新对象

### 3. 性能监控
- 在开发环境启用性能监控
- 定期检查组件渲染次数
- 监控图片加载时间

### 4. 渐进式优化
- 先优化最频繁渲染的组件
- 逐步扩展到其他图片组件
- 保持向后兼容性

## 注意事项

1. **内存管理**: 定期清理缓存，避免内存泄漏
2. **缓存失效**: 当图片URL参数变化时，确保缓存正确更新
3. **错误处理**: 添加图片加载失败的降级方案
4. **可访问性**: 保持alt文本和其他可访问性属性

## 监控指标

- 组件渲染次数
- 图片加载时间
- 缓存命中率
- 内存使用量
- 网络请求数量
