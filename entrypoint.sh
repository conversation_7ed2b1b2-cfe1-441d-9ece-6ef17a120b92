#!/bin/bash

# LOG_STREAM=/dev/mtstdout
# mkfifo $LOG_STREAM && chmod 777 $LOG_STREAM

echo "--------------------------------"
ls -l /app/.next/static/chunks/main*
echo "--------------------------------"

# sleep 18000

# 问题启动
# node /app/.next/n-server/index.js
sleep 3

# echo "--------------------------------"
# ls -l /app/.next/static/chunks/main*
# ls -l /app/next/static/chunks/main*
# echo "--------------------------------"

# pm2-runtime start ecosystem.config.json
echo "Start Server ----> ENV: ${ENV}"

yarn start:$ENV
# tail -f $LOG_STREAM
