/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: "https://whee.ai",
  generateRobotsTxt: true,
  sitemapSize: 5000,
  i18n: {
    locales: ["en"],
    defaultLocale: "en",
  },

  // 排除所有next-sitemap识别的页面，完全交给additionalPaths去控制
  exclude: ['/**'],

  
  additionalPaths: async (config) => {
    const PageCategorys = {
      Features: '1',
      Features2: '2',
      Tutorial: '3',
    }
    const res = await fetch(`https://webapi.whee.ai/seo_page/get_page_list.json?page=1&count=9999`);
    const data = await res.json();
    const list = data?.data?.dataList ?? [];
    //动态seo页面
    const dynamicSeoPaths = list
      ?.filter((p) => p.page_category === PageCategorys.Features || p.page_category === PageCategorys.Features2)
      ?.map((p) => ({
        path: `/${p.page_keyword.toLowerCase().replace(/\s+/g, "-")}`,
        priority: 0.5,
        lastmod: new Date(p.updated_at*1000).toISOString(),
      }));  
    // 动态教程页面
    const dynamicTutorialPaths = list
      ?.filter((p) => p.page_category === PageCategorys.Tutorial)
      ?.map(
        (p) => ({
          path: `/tutorial/${p.page_keyword.toLowerCase().replace(/\s+/g, "-")}`,
          priority: 0.5,
          lastmod: new Date(p.updated_at*1000).toISOString(),
        })
      );
    // 静态页面
    const staticPaths = [
      {
        path: "/",
        priority: 1.0,
        lastmod: new Date().toISOString(),
      },
      {
        path: "/plans",
        priority: 0.7,
        lastmod: new Date().toISOString(),
      },
      {
        path: "/tutorial",
        priority: 0.7,
        lastmod: new Date().toISOString(),
      }
    ];

    const paths = [...staticPaths, ...dynamicTutorialPaths, ...dynamicSeoPaths, ];
    const result = [];

    for (const item of paths) {
      const rawPath = item.path.startsWith("/") ? item.path : `/${item.path}`;

      // for (const locale of config.i18n.locales) {
        result.push({
          loc: `${config.siteUrl}${rawPath}`,
          priority: item.priority,
          lastmod: item.lastmod,
          changefreq: 'daily',
          // alternateRefs: config.i18n.locales.map((altLocale) => ({
          //   href: `${config.siteUrl}/${altLocale}${rawPath}`,
          //   hreflang: altLocale,
          // })),
        });
      // }
    }
    return result;
  },
};
