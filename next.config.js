/** @type {import('next').NextConfig} */
const path = require("path");
const withMiddleware = require("next-http-proxy-middleware");
const dotenv = require("dotenv");
const WorkerUrlPlugin = require("worker-url/plugin");
const withTranspileNodeModules = require("next-transpile-modules")([
  // 账号 JS SDK 海外版
  // "@meitu/account-ui-intl",
  // "@meitu/account-country-code-picker-intl",
  // "@meitu/account-region-picker-intl",
  // "@meitu/account-login-popup-intl",
]);
// const CompressionPlugin = require('compression-webpack-plugin')

const PROJECT_KEY = "wheeai";
const BUILD_ENV = process.env.NEXT_PUBLIC_ENV || "";
const PRE_STATIC_HOST = "//wheeai-public-pre.stariidata.com";
const RELEASE_STATIC_HOST = "//wheeai-public.stariidata.com";
let ASSETS_PATH = "";

dotenv.config({ path: `.env.${BUILD_ENV}` });
console.log("BUILD_ENV:", BUILD_ENV);
console.log(
  "process.env.NEXT_PUBLIC_SERVER_URL:",
  process.env.NEXT_PUBLIC_API_URL
);

switch (BUILD_ENV) {
  case "dev":
    break;
  case "pre":
    ASSETS_PATH = `${PRE_STATIC_HOST}/${PROJECT_KEY}-${BUILD_ENV}`;
    break;
  case "beta":
  case "release":
    ASSETS_PATH = `${RELEASE_STATIC_HOST}/${PROJECT_KEY}-release`;
    break;
  default:
    break;
}

console.log("ASSETS_PATH:", ASSETS_PATH);

// 根据环境配置图片域名
const imageConfig = {
  remotePatterns: [
    {
      protocol: "http",
      hostname: "*.meitu.com",
      port: "",
    },
    {
      protocol: "https",
      hostname: "*.meitu.com",
      port: "",
    },
    {
      protocol: "https",
      hostname: "*.aigc.meitudata.com",
      port: "",
    },
    {
      protocol: "https",
      hostname: "*.stariidata.com",
      port: "",
    },

  ],
  formats: ["image/avif", "image/webp"],
  minimumCacheTTL: 600,
};

// 只在开发环境添加 picsum.photos 域名
if (BUILD_ENV === "dev") {
  imageConfig.domains = ["picsum.photos"];
  imageConfig.remotePatterns.push({
    protocol: "https",
    hostname: "picsum.photos",
    port: "",
    pathname: "/**",
  });
}

const nextConfig = {
  // set a path prefix for the application, 作为html静态资源使用时，填充路由跳转的prefix
  basePath: "",

  // 指定用于自定义构建目录的名称来代替.next
  // distDir: 'build',

  // Use the CDN in production and localhost for development.作为html静态资源使用时，补充模版中js/css的cdn+path部分
  assetPrefix: BUILD_ENV === "dev" ? "" : ASSETS_PATH,

  // sass
  sassOptions: {
    includePaths: [path.join(__dirname, "styles")],
  },

  // images
  images: imageConfig,

  // header disable x-powered-by
  poweredByHeader: false,

  // 解决开发模式下 渲染两遍的问题
  reactStrictMode: false,

  webpack(webpackConfig, { isServer }) {
    // 移除所有 asset modules 的 generator 配置
    webpackConfig.module.rules.forEach((rule) => {
      if (rule.generator) {
        delete rule.generator;
      }
    });

    // SVG 配置
    webpackConfig.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    // worker-pool在浏览器端需要剔除以下依赖
    webpackConfig.resolve.alias = {
      ...webpackConfig.resolve.alias,
      os: false,
      child_process: false,
      worker_threads: false,
    };
    // webpackConfig.externals = {
    //   "@meitu/tracer-intl": "mtTracer",
    // };
    // worker-pool需要依赖的插件
    webpackConfig.plugins.push(new WorkerUrlPlugin());

    // // 开启压缩
    // if (!isServer) {
    //   webpackConfig.plugins.push(
    //     new CompressionPlugin({
    //       algorithm: 'brotliCompress',
    //       filename: '[path][base].br', // ✅ 指定输出 .br 文件
    //       test: /\.(js|css|html|svg|woff|woff2|ttf|eot)$/,
    //       compressionOptions: { level: 11 }, // 最优压缩比
    //       threshold: 0,
    //       minRatio: 0.8,
    //     })
    //   )
    // }

    return webpackConfig;
  },
  experimental: {
    staleTimes: {
      dynamic: 30,
      static: 180,
    },
  },
};

module.exports = withTranspileNodeModules(nextConfig);
