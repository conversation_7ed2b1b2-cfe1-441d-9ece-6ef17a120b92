{"name": "whee-global", "version": "0.0.1", "private": true, "scripts": {"dev": "NEXT_PUBLIC_ENV=dev  PORT=3000 next dev", "build:pre": "NEXT_PUBLIC_ENV=pre   next build", "build:beta": "NEXT_PUBLIC_ENV=beta   next build", "build:release": "NEXT_PUBLIC_ENV=release next build && next-sitemap", "start": "NEXT_PUBLIC_ENV=dev  next start --keepAliveTimeout 80000", "start:pre": "NEXT_PUBLIC_ENV=pre  next start --keepAliveTimeout 80000", "start:beta": "NEXT_PUBLIC_ENV=beta  next start --keepAliveTimeout 80000", "start:release": "NEXT_PUBLIC_ENV=release next start --keepAliveTimeout 80000", "lint": "next lint", "prepare": "husky install", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "postbuild": "next-sitemap"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@intercom/messenger-js-sdk": "^0.0.14", "@meitu/account-intl": "^4.0.8-alpha.0", "@meitu/account-login-popup-intl": "^4.0.8-alpha.0", "@meitu/account-web": "^4.0.8-alpha.0", "@meitu/candy-icons": "^1.0.16", "@meitu/mtstat-sdk": "^1.7.2", "@meitu/subscribe-intl": "2.0.0", "@meitu/tracer-intl": "^1.1.1", "@meitu/tracer-utm-params": "^1.0.2", "@meitu/upload-intl": "^4.8.4", "@meitu/util": "^1.8.1", "@meitu/whee-infinite-canvas": "1.0.71-beta", "@next/env": "^15.1.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-slot": "^1.2.0", "@svgr/webpack": "^8.1.0", "ahooks": "^3.8.4", "antd": "^5.22.4", "axios": "^1.7.9", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "compression-webpack-plugin": "^11.1.0", "cookie-parser": "^1.4.7", "cookies-next": "^5.1.0", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "driver.js": "^1.3.6", "embla-carousel-react": "^8.6.0", "express": "4.18.2", "file-saver": "^2.0.5", "framer-motion": "^12.6.2", "http-proxy-middleware": "^3.0.3", "immer": "^10.1.1", "jsep": "^1.4.0", "json-bigint": "^1.0.0", "jszip": "^3.10.1", "koa-compose": "^4.1.0", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "mime": "^4.0.6", "mitt": "^3.0.1", "mobx": "^6.13.6", "mobx-react": "^9.2.0", "mobx-react-lite": "^4.1.0", "next": "^14", "next-http-proxy-middleware": "^1.2.6", "next-international": "^1.3.1", "next-sitemap": "^4.2.3", "next-transpile-modules": "^10.0.1", "postcss-px-to-viewport": "^1.1.1", "postcss-scss": "^4.0.9", "react": "^18", "react-dom": "^18", "react-infinite-scroll-component": "^6.1.0", "react-masonry-css": "^1.0.16", "sass": "^1.70.0", "stylelint": "^16.12.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-scss": "^6.10.0", "swr": "^2.2.5", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "worker-url": "^1.1.0", "workerpool": "^9.2.0"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@faker-js/faker": "^9.6.0", "@types/canvas-confetti": "^1.9.0", "@types/express": "^5.0.0", "@types/file-saver": "^2.0.7", "@types/json-bigint": "^1.0.4", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "conventional-changelog": "^6.0.0", "conventional-changelog-cli": "^5.0.0", "css-loader": "^7.1.2", "eslint": "^8", "eslint-config-next": "^14", "husky": "^8.0.3", "lint-staged": "^15.2.11", "postcss": "^8", "style-loader": "^4.0.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "engines": {"node": ">=18.19.0"}}