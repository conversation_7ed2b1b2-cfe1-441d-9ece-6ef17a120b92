var workers=function(K){"use strict";function ve(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}function we(u){if(u.__esModule)return u;var m=u.default;if(typeof m=="function"){var d=function y(){return this instanceof y?Reflect.construct(m,arguments,this.constructor):m.apply(this,arguments)};d.prototype=m.prototype}else d={};return Object.defineProperty(d,"__esModule",{value:!0}),Object.keys(u).forEach(function(y){var E=Object.getOwnPropertyDescriptor(u,y);Object.defineProperty(d,y,E.get?E:{enumerable:!0,get:function(){return u[y]}})}),d}var z={exports:{}};const B=we(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));/**
 * workerpool.js
 * https://github.com/josdejong/workerpool
 *
 * Offload tasks to a pool of workers on node.js and in the browser.
 *
 * @version 9.2.0
 * @date    2024-10-11
 *
 * @license
 * Copyright (C) 2014-2022 Jos de Jong <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy
 * of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */var ke=z.exports,te;function ye(){return te||(te=1,function(u,m){(function(d,y){y(m)})(ke,function(d){var y={},E={exports:{}};(function(t){var s=function(f){return typeof f<"u"&&f.versions!=null&&f.versions.node!=null&&f+""=="[object process]"};t.exports.isNode=s,t.exports.platform=typeof process<"u"&&s(process)?"node":"browser";var i=t.exports.platform==="node"&&B;t.exports.isMainThread=t.exports.platform==="node"?(!i||i.isMainThread)&&!process.connected:typeof Window<"u",t.exports.cpus=t.exports.platform==="browser"?self.navigator.hardwareConcurrency:B.cpus().length})(E);var j=E.exports,I={},$;function U(){if($)return I;$=1;function t(f,T){var e=this;if(!(this instanceof t))throw new SyntaxError("Constructor must be called with the new operator");if(typeof f!="function")throw new SyntaxError("Function parameter handler(resolve, reject) missing");var W=[],O=[];this.resolved=!1,this.rejected=!1,this.pending=!0;var A=function(r,c){W.push(r),O.push(c)};this.then=function(o,r){return new t(function(c,h){var b=o?s(o,c,h):c,L=r?s(r,c,h):h;A(b,L)},e)};var S=function(r){return e.resolved=!0,e.rejected=!1,e.pending=!1,W.forEach(function(c){c(r)}),A=function(h,b){h(r)},S=a=function(){},e},a=function(r){return e.resolved=!1,e.rejected=!0,e.pending=!1,O.forEach(function(c){c(r)}),A=function(h,b){b(r)},S=a=function(){},e};this.cancel=function(){return T?T.cancel():a(new i),e},this.timeout=function(o){if(T)T.timeout(o);else{var r=setTimeout(function(){a(new w("Promise timed out after "+o+" ms"))},o);e.always(function(){clearTimeout(r)})}return e},f(function(o){S(o)},function(o){a(o)})}function s(f,T,e){return function(W){try{var O=f(W);O&&typeof O.then=="function"&&typeof O.catch=="function"?O.then(T,e):T(O)}catch(A){e(A)}}}t.prototype.catch=function(f){return this.then(null,f)},t.prototype.always=function(f){return this.then(f,f)},t.prototype.finally=function(f){var T=this,e=function(){return new t(function(O){return O()}).then(f).then(function(){return T})};return this.then(e,e)},t.all=function(f){return new t(function(T,e){var W=f.length,O=[];W?f.forEach(function(A,S){A.then(function(a){O[S]=a,W--,W==0&&T(O)},function(a){W=0,e(a)})}):T(O)})},t.defer=function(){var f={};return f.promise=new t(function(T,e){f.resolve=T,f.reject=e}),f};function i(f){this.message=f||"promise cancelled",this.stack=new Error().stack}i.prototype=new Error,i.prototype.constructor=Error,i.prototype.name="CancellationError",t.CancellationError=i;function w(f){this.message=f||"timeout exceeded",this.stack=new Error().stack}return w.prototype=new Error,w.prototype.constructor=Error,w.prototype.name="TimeoutError",t.TimeoutError=w,I.Promise=t,I}function F(t,s){(s==null||s>t.length)&&(s=t.length);for(var i=0,w=Array(s);i<s;i++)w[i]=t[i];return w}function X(t,s){var i=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=Le(t))||s){i&&(t=i);var w=0,f=function(){};return{s:f,n:function(){return w>=t.length?{done:!0}:{done:!1,value:t[w++]}},e:function(O){throw O},f}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var T,e=!0,W=!1;return{s:function(){i=i.call(t)},n:function(){var O=i.next();return e=O.done,O},e:function(O){W=!0,T=O},f:function(){try{e||i.return==null||i.return()}finally{if(W)throw T}}}}function Pe(t,s,i){return(s=Se(s))in t?Object.defineProperty(t,s,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[s]=i,t}function se(t,s){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var w=Object.getOwnPropertySymbols(t);s&&(w=w.filter(function(f){return Object.getOwnPropertyDescriptor(t,f).enumerable})),i.push.apply(i,w)}return i}function je(t){for(var s=1;s<arguments.length;s++){var i=arguments[s]!=null?arguments[s]:{};s%2?se(Object(i),!0).forEach(function(w){Pe(t,w,i[w])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):se(Object(i)).forEach(function(w){Object.defineProperty(t,w,Object.getOwnPropertyDescriptor(i,w))})}return t}function Ae(t,s){if(typeof t!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var w=i.call(t,s||"default");if(typeof w!="object")return w;throw new TypeError("@@toPrimitive must return a primitive value.")}return(s==="string"?String:Number)(t)}function Se(t){var s=Ae(t,"string");return typeof s=="symbol"?s:s+""}function V(t){"@babel/helpers - typeof";return V=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},V(t)}function Le(t,s){if(t){if(typeof t=="string")return F(t,s);var i={}.toString.call(t).slice(8,-1);return i==="Object"&&t.constructor&&(i=t.constructor.name),i==="Map"||i==="Set"?Array.from(t):i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?F(t,s):void 0}}var q={exports:{}},Q={},ae;function Me(){return ae||(ae=1,Q.validateOptions=function(s,i,w){if(s){var f=s?Object.keys(s):[],T=f.find(function(W){return!i.includes(W)});if(T)throw new Error('Object "'+w+'" contains an unknown option "'+T+'"');var e=i.find(function(W){return Object.prototype[W]&&!f.includes(W)});if(e)throw new Error('Object "'+w+'" contains an inherited option "'+e+'" which is not defined in the object itself but in its prototype. Only plain objects are allowed. Please remove the option from the prototype or override it with a value "undefined".');return s}},Q.workerOptsNames=["credentials","name","type"],Q.forkOptsNames=["cwd","detached","env","execPath","execArgv","gid","serialization","signal","killSignal","silent","stdio","uid","windowsVerbatimArguments","timeout"],Q.workerThreadOptsNames=["argv","env","eval","execArgv","stdin","stdout","stderr","workerData","trackUnmanagedFds","transferList","resourceLimits","name"]),Q}var J,ue;function De(){return ue||(ue=1,J=`!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).worker=n()}(this,(function(){"use strict";function e(n){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(n)}function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={};var r=function(e,n){this.message=e,this.transfer=n},o={};function i(e,n){var t=this;if(!(this instanceof i))throw new SyntaxError("Constructor must be called with the new operator");if("function"!=typeof e)throw new SyntaxError("Function parameter handler(resolve, reject) missing");var r=[],o=[];this.resolved=!1,this.rejected=!1,this.pending=!0;var a=function(e,n){r.push(e),o.push(n)};this.then=function(e,n){return new i((function(t,r){var o=e?u(e,t,r):t,i=n?u(n,t,r):r;a(o,i)}),t)};var f=function(e){return t.resolved=!0,t.rejected=!1,t.pending=!1,r.forEach((function(n){n(e)})),a=function(n,t){n(e)},f=d=function(){},t},d=function(e){return t.resolved=!1,t.rejected=!0,t.pending=!1,o.forEach((function(n){n(e)})),a=function(n,t){t(e)},f=d=function(){},t};this.cancel=function(){return n?n.cancel():d(new s),t},this.timeout=function(e){if(n)n.timeout(e);else{var r=setTimeout((function(){d(new c("Promise timed out after "+e+" ms"))}),e);t.always((function(){clearTimeout(r)}))}return t},e((function(e){f(e)}),(function(e){d(e)}))}function u(e,n,t){return function(r){try{var o=e(r);o&&"function"==typeof o.then&&"function"==typeof o.catch?o.then(n,t):n(o)}catch(e){t(e)}}}function s(e){this.message=e||"promise cancelled",this.stack=(new Error).stack}function c(e){this.message=e||"timeout exceeded",this.stack=(new Error).stack}return i.prototype.catch=function(e){return this.then(null,e)},i.prototype.always=function(e){return this.then(e,e)},i.prototype.finally=function(e){var n=this,t=function(){return new i((function(e){return e()})).then(e).then((function(){return n}))};return this.then(t,t)},i.all=function(e){return new i((function(n,t){var r=e.length,o=[];r?e.forEach((function(e,i){e.then((function(e){o[i]=e,0==--r&&n(o)}),(function(e){r=0,t(e)}))})):n(o)}))},i.defer=function(){var e={};return e.promise=new i((function(n,t){e.resolve=n,e.reject=t})),e},s.prototype=new Error,s.prototype.constructor=Error,s.prototype.name="CancellationError",i.CancellationError=s,c.prototype=new Error,c.prototype.constructor=Error,c.prototype.name="TimeoutError",i.TimeoutError=c,o.Promise=i,function(n){var t=r,i=o.Promise,u="__workerpool-cleanup__",s={exit:function(){}},c={addAbortListener:function(e){s.abortListeners.push(e)},emit:s.emit};if("undefined"!=typeof self&&"function"==typeof postMessage&&"function"==typeof addEventListener)s.on=function(e,n){addEventListener(e,(function(e){n(e.data)}))},s.send=function(e,n){n?postMessage(e,n):postMessage(e)};else{if("undefined"==typeof process)throw new Error("Script must be executed as a worker");var a;try{a=require("worker_threads")}catch(n){if("object"!==e(n)||null===n||"MODULE_NOT_FOUND"!==n.code)throw n}if(a&&null!==a.parentPort){var f=a.parentPort;s.send=f.postMessage.bind(f),s.on=f.on.bind(f),s.exit=process.exit.bind(process)}else s.on=process.on.bind(process),s.send=function(e){process.send(e)},s.on("disconnect",(function(){process.exit(1)})),s.exit=process.exit.bind(process)}function d(e){return Object.getOwnPropertyNames(e).reduce((function(n,t){return Object.defineProperty(n,t,{value:e[t],enumerable:!0})}),{})}function l(e){return e&&"function"==typeof e.then&&"function"==typeof e.catch}s.methods={},s.methods.run=function(e,n){var t=new Function("return ("+e+").apply(this, arguments);");return t.worker=c,t.apply(t,n)},s.methods.methods=function(){return Object.keys(s.methods)},s.terminationHandler=void 0,s.abortListenerTimeout=1e3,s.abortListeners=[],s.terminateAndExit=function(e){var n=function(){s.exit(e)};if(!s.terminationHandler)return n();var t=s.terminationHandler(e);return l(t)?(t.then(n,n),t):(n(),new i((function(e,n){n(new Error("Worker terminating"))})))},s.cleanup=function(e){if(!s.abortListeners.length)return s.send({id:e,method:u,error:d(new Error("Worker terminating"))}),new i((function(e){e()}));var n,t=s.abortListeners.map((function(e){return e()})),r=new i((function(e,t){n=setTimeout((function(){t(new Error("Timeout occured waiting for abort handler, killing worker"))}),s.abortListenerTimeout)})),o=i.all(t).then((function(){clearTimeout(n),s.abortListeners.length||(s.abortListeners=[])}),(function(){clearTimeout(n),s.exit()}));return i.all([o,r]).then((function(){s.send({id:e,method:u,error:null})}),(function(n){s.send({id:e,method:u,error:n?d(n):null})}))};var p=null;s.on("message",(function(e){if("__workerpool-terminate__"===e)return s.terminateAndExit(0);if(e.method===u)return s.cleanup(e.id);try{var n=s.methods[e.method];if(!n)throw new Error('Unknown method "'+e.method+'"');p=e.id;var r=n.apply(n,e.params);l(r)?r.then((function(n){n instanceof t?s.send({id:e.id,result:n.message,error:null},n.transfer):s.send({id:e.id,result:n,error:null}),p=null})).catch((function(n){s.send({id:e.id,result:null,error:d(n)}),p=null})):(r instanceof t?s.send({id:e.id,result:r.message,error:null},r.transfer):s.send({id:e.id,result:r,error:null}),p=null)}catch(n){s.send({id:e.id,result:null,error:d(n)})}})),s.register=function(e,n){if(e)for(var t in e)e.hasOwnProperty(t)&&(s.methods[t]=e[t],s.methods[t].worker=c);n&&(s.terminationHandler=n.onTerminate,s.abortListenerTimeout=n.abortListenerTimeout||1e3),s.send("ready")},s.emit=function(e){if(p){if(e instanceof t)return void s.send({id:p,isEvent:!0,payload:e.message},e.transfer);s.send({id:p,isEvent:!0,payload:e})}},n.add=s.register,n.emit=s.emit}(t),n(t)}));
//# sourceMappingURL=worker.min.js.map
`),J}var ce;function Ne(){if(ce)return q.exports;ce=1;var t=U(),s=t.Promise,i=j,w=Me(),f=w.validateOptions,T=w.forkOptsNames,e=w.workerThreadOptsNames,W=w.workerOptsNames,O="__workerpool-terminate__",A="__workerpool-cleanup__";function S(){var l=o();if(!l)throw new Error("WorkerPool: workerType = 'thread' is not supported, Node >= 11.7.0 required");return l}function a(){if(typeof Worker!="function"&&((typeof Worker>"u"?"undefined":V(Worker))!=="object"||typeof Worker.prototype.constructor!="function"))throw new Error("WorkerPool: Web Workers not supported")}function o(){try{return B}catch(l){if(V(l)==="object"&&l!==null&&l.code==="MODULE_NOT_FOUND")return null;throw l}}function r(){if(i.platform==="browser"){if(typeof Blob>"u")throw new Error("Blob not supported by the browser");if(!window.URL||typeof window.URL.createObjectURL!="function")throw new Error("URL.createObjectURL not supported by the browser");var l=new Blob([De()],{type:"text/javascript"});return window.URL.createObjectURL(l)}else return __dirname+"/worker.js"}function c(l,v){if(v.workerType==="web")return a(),h(l,v.workerOpts,Worker);if(v.workerType==="thread")return n=S(),b(l,n,v);if(v.workerType==="process"||!v.workerType)return L(l,D(v),B);if(i.platform==="browser")return a(),h(l,v.workerOpts,Worker);var n=o();return n?b(l,n,v):L(l,D(v),B)}function h(l,v,n){f(v,W,"workerOpts");var p=new n(l,v);return p.isBrowserWorker=!0,p.on=function(k,_){this.addEventListener(k,function(x){_(x.data)})},p.send=function(k,_){this.postMessage(k,_)},p}function b(l,v,n){var p,k;f(n==null?void 0:n.workerThreadOpts,e,"workerThreadOpts");var _=new v.Worker(l,je({stdout:(p=n==null?void 0:n.emitStdStreams)!==null&&p!==void 0?p:!1,stderr:(k=n==null?void 0:n.emitStdStreams)!==null&&k!==void 0?k:!1},n==null?void 0:n.workerThreadOpts));return _.isWorkerThread=!0,_.send=function(x,g){this.postMessage(x,g)},_.kill=function(){return this.terminate(),!0},_.disconnect=function(){this.terminate()},n!=null&&n.emitStdStreams&&(_.stdout.on("data",function(x){return _.emit("stdout",x)}),_.stderr.on("data",function(x){return _.emit("stderr",x)})),_}function L(l,v,n){f(v.forkOpts,T,"forkOpts");var p=n.fork(l,v.forkArgs,v.forkOpts),k=p.send;return p.send=function(_){return k.call(p,_)},v.emitStdStreams&&(p.stdout.on("data",function(_){return p.emit("stdout",_)}),p.stderr.on("data",function(_){return p.emit("stderr",_)})),p.isChildProcess=!0,p}function D(l){l=l||{};var v=process.execArgv.join(" "),n=v.indexOf("--inspect")!==-1,p=v.indexOf("--debug-brk")!==-1,k=[];return n&&(k.push("--inspect="+l.debugPort),p&&k.push("--debug-brk")),process.execArgv.forEach(function(_){_.indexOf("--max-old-space-size")>-1&&k.push(_)}),Object.assign({},l,{forkArgs:l.forkArgs,forkOpts:Object.assign({},l.forkOpts,{execArgv:(l.forkOpts&&l.forkOpts.execArgv||[]).concat(k),stdio:l.emitStdStreams?"pipe":void 0})})}function R(l){for(var v=new Error(""),n=Object.keys(l),p=0;p<n.length;p++)v[n[p]]=l[n[p]];return v}function H(l,v){if(Object.keys(l.processing).length===1){var n=Object.values(l.processing)[0];n.options&&typeof n.options.on=="function"&&n.options.on(v)}}function C(l,v){var n=this,p=v||{};this.script=l||r(),this.worker=c(this.script,p),this.debugPort=p.debugPort,this.forkOpts=p.forkOpts,this.forkArgs=p.forkArgs,this.workerOpts=p.workerOpts,this.workerThreadOpts=p.workerThreadOpts,this.workerTerminateTimeout=p.workerTerminateTimeout,l||(this.worker.ready=!0),this.requestQueue=[],this.worker.on("stdout",function(g){H(n,{stdout:g.toString()})}),this.worker.on("stderr",function(g){H(n,{stderr:g.toString()})}),this.worker.on("message",function(g){if(!n.terminated)if(typeof g=="string"&&g==="ready")n.worker.ready=!0,_();else{var M=g.id,P=n.processing[M];if(P!==void 0&&(g.isEvent?P.options&&typeof P.options.on=="function"&&P.options.on(g.payload):(delete n.processing[M],n.terminating===!0&&n.terminate(),g.error?P.resolver.reject(R(g.error)):P.resolver.resolve(g.result))),g.method===A){var N=n.tracking[g.id];N!==void 0&&(g.error?(clearTimeout(N.timeoutId),N.resolver.reject(R(g.error))):(n.tracking&&clearTimeout(N.timeoutId),N.resolver.resolve(N.result))),delete n.tracking[M]}}});function k(g){n.terminated=!0;for(var M in n.processing)n.processing[M]!==void 0&&n.processing[M].resolver.reject(g);n.processing=Object.create(null)}function _(){var g=X(n.requestQueue.splice(0)),M;try{for(g.s();!(M=g.n()).done;){var P=M.value;n.worker.send(P.message,P.transfer)}}catch(N){g.e(N)}finally{g.f()}}var x=this.worker;this.worker.on("error",k),this.worker.on("exit",function(g,M){var P=`Workerpool Worker terminated Unexpectedly
`;P+="    exitCode: `"+g+"`\n",P+="    signalCode: `"+M+"`\n",P+="    workerpool.script: `"+n.script+"`\n",P+="    spawnArgs: `"+x.spawnargs+"`\n",P+="    spawnfile: `"+x.spawnfile+"`\n",P+="    stdout: `"+x.stdout+"`\n",P+="    stderr: `"+x.stderr+"`\n",k(new Error(P))}),this.processing=Object.create(null),this.tracking=Object.create(null),this.terminating=!1,this.terminated=!1,this.cleaning=!1,this.terminationHandler=null,this.lastId=0}return C.prototype.methods=function(){return this.exec("methods")},C.prototype.exec=function(l,v,n,p){n||(n=s.defer());var k=++this.lastId;this.processing[k]={id:k,resolver:n,options:p};var _={message:{id:k,method:l,params:v},transfer:p&&p.transfer};this.terminated?n.reject(new Error("Worker is terminated")):this.worker.ready?this.worker.send(_.message,_.transfer):this.requestQueue.push(_);var x=this;return n.promise.catch(function(g){if(g instanceof s.CancellationError||g instanceof s.TimeoutError)return x.tracking[k]={id:k,resolver:s.defer()},delete x.processing[k],x.tracking[k].resolver.promise=x.tracking[k].resolver.promise.catch(function(M){delete x.tracking[k];var P=x.terminateAndNotify(!0).then(function(){throw M},function(N){throw N});return P}),x.worker.send({id:k,method:A}),x.tracking[k].timeoutId=setTimeout(function(){x.tracking[k].resolver.reject(g)},x.workerTerminateTimeout),x.tracking[k].resolver.promise;throw g})},C.prototype.busy=function(){return this.cleaning||Object.keys(this.processing).length>0},C.prototype.terminate=function(l,v){var n=this;if(l){for(var p in this.processing)this.processing[p]!==void 0&&this.processing[p].resolver.reject(new Error("Worker terminated"));this.processing=Object.create(null)}for(var k=0,_=Object.values(n.tracking);k<_.length;k++){var x=_[k];clearTimeout(x.timeoutId),x.resolver.reject(new Error("Worker Terminating"))}if(n.tracking=Object.create(null),typeof v=="function"&&(this.terminationHandler=v),this.busy())this.terminating=!0;else{var g=function(N){if(n.terminated=!0,n.cleaning=!1,n.worker!=null&&n.worker.removeAllListeners&&n.worker.removeAllListeners("message"),n.worker=null,n.terminating=!1,n.terminationHandler)n.terminationHandler(N,n);else if(N)throw N};if(this.worker)if(typeof this.worker.kill=="function"){if(this.worker.killed){g(new Error("worker already killed!"));return}var M=setTimeout(function(){n.worker&&n.worker.kill()},this.workerTerminateTimeout);this.worker.once("exit",function(){clearTimeout(M),n.worker&&(n.worker.killed=!0),g()}),this.worker.ready?this.worker.send(O):this.requestQueue.push({message:O}),this.cleaning=!0;return}else if(typeof this.worker.terminate=="function")this.worker.terminate(),this.worker.killed=!0;else throw new Error("Failed to terminate worker");g()}},C.prototype.terminateAndNotify=function(l,v){var n=s.defer();return v&&n.promise.timeout(v),this.terminate(l,function(p,k){p?n.reject(p):n.resolve(k)}),n.promise},q.exports=C,q.exports._tryRequireWorkerThreads=o,q.exports._setupProcessWorker=L,q.exports._setupBrowserWorker=h,q.exports._setupWorkerThreadWorker=b,q.exports.ensureWorkerThreads=S,q.exports}var Y,fe;function Ie(){if(fe)return Y;fe=1;var t=65535;Y=s;function s(){this.ports=Object.create(null),this.length=0}return s.prototype.nextAvailableStartingAt=function(i){for(;this.ports[i]===!0;)i++;if(i>=t)throw new Error("WorkerPool debug port limit reached: "+i+">= "+t);return this.ports[i]=!0,this.length++,i},s.prototype.releasePort=function(i){delete this.ports[i],this.length--},Y}var Z,le;function Re(){if(le)return Z;le=1;var t=U(),s=t.Promise,i=Ne(),w=j,f=Ie(),T=new f;function e(a,o){typeof a=="string"?this.script=a||null:(this.script=null,o=a),this.workers=[],this.tasks=[],o=o||{},this.forkArgs=Object.freeze(o.forkArgs||[]),this.forkOpts=Object.freeze(o.forkOpts||{}),this.workerOpts=Object.freeze(o.workerOpts||{}),this.workerThreadOpts=Object.freeze(o.workerThreadOpts||{}),this.debugPortStart=o.debugPortStart||43210,this.nodeWorker=o.nodeWorker,this.workerType=o.workerType||o.nodeWorker||"auto",this.maxQueueSize=o.maxQueueSize||1/0,this.workerTerminateTimeout=o.workerTerminateTimeout||1e3,this.onCreateWorker=o.onCreateWorker||function(){return null},this.onTerminateWorker=o.onTerminateWorker||function(){return null},this.emitStdStreams=o.emitStdStreams||!1,o&&"maxWorkers"in o?(W(o.maxWorkers),this.maxWorkers=o.maxWorkers):this.maxWorkers=Math.max((w.cpus||4)-1,1),o&&"minWorkers"in o&&(o.minWorkers==="max"?this.minWorkers=this.maxWorkers:(O(o.minWorkers),this.minWorkers=o.minWorkers,this.maxWorkers=Math.max(this.minWorkers,this.maxWorkers)),this._ensureMinWorkers()),this._boundNext=this._next.bind(this),this.workerType==="thread"&&i.ensureWorkerThreads()}e.prototype.exec=function(a,o,r){if(o&&!Array.isArray(o))throw new TypeError('Array expected as argument "params"');if(typeof a=="string"){var c=s.defer();if(this.tasks.length>=this.maxQueueSize)throw new Error("Max queue size of "+this.maxQueueSize+" reached");var h=this.tasks,b={method:a,params:o,resolver:c,timeout:null,options:r};h.push(b);var L=c.promise.timeout;return c.promise.timeout=function(R){return h.indexOf(b)!==-1?(b.timeout=R,c.promise):L.call(c.promise,R)},this._next(),c.promise}else{if(typeof a=="function")return this.exec("run",[String(a),o],r);throw new TypeError('Function or string expected as argument "method"')}},e.prototype.proxy=function(){if(arguments.length>0)throw new Error("No arguments expected");var a=this;return this.exec("methods").then(function(o){var r={};return o.forEach(function(c){r[c]=function(){return a.exec(c,Array.prototype.slice.call(arguments))}}),r})},e.prototype._next=function(){if(this.tasks.length>0){var a=this._getWorker();if(a){var o=this,r=this.tasks.shift();if(r.resolver.promise.pending){var c=a.exec(r.method,r.params,r.resolver,r.options).then(o._boundNext).catch(function(){if(a.terminated)return o._removeWorker(a)}).then(function(){o._next()});typeof r.timeout=="number"&&c.timeout(r.timeout)}else o._next()}}},e.prototype._getWorker=function(){for(var a=this.workers,o=0;o<a.length;o++){var r=a[o];if(r.busy()===!1)return r}return a.length<this.maxWorkers?(r=this._createWorkerHandler(),a.push(r),r):null},e.prototype._removeWorker=function(a){var o=this;return T.releasePort(a.debugPort),this._removeWorkerFromList(a),this._ensureMinWorkers(),new s(function(r,c){a.terminate(!1,function(h){o.onTerminateWorker({forkArgs:a.forkArgs,forkOpts:a.forkOpts,workerThreadOpts:a.workerThreadOpts,script:a.script}),h?c(h):r(a)})})},e.prototype._removeWorkerFromList=function(a){var o=this.workers.indexOf(a);o!==-1&&this.workers.splice(o,1)},e.prototype.terminate=function(a,o){var r=this;this.tasks.forEach(function(D){D.resolver.reject(new Error("Pool terminated"))}),this.tasks.length=0;var c=function(R){T.releasePort(R.debugPort),this._removeWorkerFromList(R)},h=c.bind(this),b=[],L=this.workers.slice();return L.forEach(function(D){var R=D.terminateAndNotify(a,o).then(h).always(function(){r.onTerminateWorker({forkArgs:D.forkArgs,forkOpts:D.forkOpts,workerThreadOpts:D.workerThreadOpts,script:D.script})});b.push(R)}),s.all(b)},e.prototype.stats=function(){var a=this.workers.length,o=this.workers.filter(function(r){return r.busy()}).length;return{totalWorkers:a,busyWorkers:o,idleWorkers:a-o,pendingTasks:this.tasks.length,activeTasks:o}},e.prototype._ensureMinWorkers=function(){if(this.minWorkers)for(var a=this.workers.length;a<this.minWorkers;a++)this.workers.push(this._createWorkerHandler())},e.prototype._createWorkerHandler=function(){var a=this.onCreateWorker({forkArgs:this.forkArgs,forkOpts:this.forkOpts,workerOpts:this.workerOpts,workerThreadOpts:this.workerThreadOpts,script:this.script})||{};return new i(a.script||this.script,{forkArgs:a.forkArgs||this.forkArgs,forkOpts:a.forkOpts||this.forkOpts,workerOpts:a.workerOpts||this.workerOpts,workerThreadOpts:a.workerThreadOpts||this.workerThreadOpts,debugPort:T.nextAvailableStartingAt(this.debugPortStart),workerType:this.workerType,workerTerminateTimeout:this.workerTerminateTimeout,emitStdStreams:this.emitStdStreams})};function W(a){if(!A(a)||!S(a)||a<1)throw new TypeError("Option maxWorkers must be an integer number >= 1")}function O(a){if(!A(a)||!S(a)||a<0)throw new TypeError("Option minWorkers must be an integer number >= 0")}function A(a){return typeof a=="number"}function S(a){return Math.round(a)==a}return Z=e,Z}var ee={},re,de;function he(){if(de)return re;de=1;function t(s,i){this.message=s,this.transfer=i}return re=t,re}var pe;function me(){return pe||(pe=1,function(t){var s=he(),i=U().Promise,w="__workerpool-terminate__",f="__workerpool-cleanup__",T=1e3,e={exit:function(){}},W={addAbortListener:function(c){e.abortListeners.push(c)},emit:e.emit};if(typeof self<"u"&&typeof postMessage=="function"&&typeof addEventListener=="function")e.on=function(r,c){addEventListener(r,function(h){c(h.data)})},e.send=function(r,c){c?postMessage(r,c):postMessage(r)};else if(typeof process<"u"){var O;try{O=B}catch(r){if(!(V(r)==="object"&&r!==null&&r.code==="MODULE_NOT_FOUND"))throw r}if(O&&O.parentPort!==null){var A=O.parentPort;e.send=A.postMessage.bind(A),e.on=A.on.bind(A),e.exit=process.exit.bind(process)}else e.on=process.on.bind(process),e.send=function(r){process.send(r)},e.on("disconnect",function(){process.exit(1)}),e.exit=process.exit.bind(process)}else throw new Error("Script must be executed as a worker");function S(r){return Object.getOwnPropertyNames(r).reduce(function(c,h){return Object.defineProperty(c,h,{value:r[h],enumerable:!0})},{})}function a(r){return r&&typeof r.then=="function"&&typeof r.catch=="function"}e.methods={},e.methods.run=function(c,h){var b=new Function("return ("+c+").apply(this, arguments);");return b.worker=W,b.apply(b,h)},e.methods.methods=function(){return Object.keys(e.methods)},e.terminationHandler=void 0,e.abortListenerTimeout=T,e.abortListeners=[],e.terminateAndExit=function(r){var c=function(){e.exit(r)};if(!e.terminationHandler)return c();var h=e.terminationHandler(r);return a(h)?(h.then(c,c),h):(c(),new i(function(b,L){L(new Error("Worker terminating"))}))},e.cleanup=function(r){if(!e.abortListeners.length)return e.send({id:r,method:f,error:S(new Error("Worker terminating"))}),new i(function(H){H()});var c=function(){e.exit()},h=function(){e.abortListeners.length||(e.abortListeners=[])},b=e.abortListeners.map(function(H){return H()}),L,D=new i(function(H,C){L=setTimeout(function(){C(new Error("Timeout occured waiting for abort handler, killing worker"))},e.abortListenerTimeout)}),R=i.all(b).then(function(){clearTimeout(L),h()},function(){clearTimeout(L),c()});return i.all([R,D]).then(function(){e.send({id:r,method:f,error:null})},function(H){e.send({id:r,method:f,error:H?S(H):null})})};var o=null;e.on("message",function(r){if(r===w)return e.terminateAndExit(0);if(r.method===f)return e.cleanup(r.id);try{var c=e.methods[r.method];if(c){o=r.id;var h=c.apply(c,r.params);a(h)?h.then(function(b){b instanceof s?e.send({id:r.id,result:b.message,error:null},b.transfer):e.send({id:r.id,result:b,error:null}),o=null}).catch(function(b){e.send({id:r.id,result:null,error:S(b)}),o=null}):(h instanceof s?e.send({id:r.id,result:h.message,error:null},h.transfer):e.send({id:r.id,result:h,error:null}),o=null)}else throw new Error('Unknown method "'+r.method+'"')}catch(b){e.send({id:r.id,result:null,error:S(b)})}}),e.register=function(r,c){if(r)for(var h in r)r.hasOwnProperty(h)&&(e.methods[h]=r[h],e.methods[h].worker=W);c&&(e.terminationHandler=c.onTerminate,e.abortListenerTimeout=c.abortListenerTimeout||T),e.send("ready")},e.emit=function(r){if(o){if(r instanceof s){e.send({id:o,isEvent:!0,payload:r.message},r.transfer);return}e.send({id:o,isEvent:!0,payload:r})}},t.add=e.register,t.emit=e.emit}(ee)),ee}var He=j.platform,Ue=j.isMainThread,qe=j.cpus;function Ce(t,s){var i=Re();return new i(t,s)}var $e=y.pool=Ce;function Fe(t,s){var i=me();i.add(t,s)}var Be=y.worker=Fe;function Qe(t){var s=me();s.emit(t)}var ze=y.workerEmit=Qe,Ve=U(),Ke=Ve.Promise,Ge=y.Promise=Ke,Xe=y.Transfer=he(),Je=y.platform=He,Ye=y.isMainThread=Ue,Ze=y.cpus=qe;d.Promise=Ge,d.Transfer=Xe,d.cpus=Ze,d.default=y,d.isMainThread=Ye,d.platform=Je,d.pool=$e,d.worker=Be,d.workerEmit=ze,Object.defineProperty(d,"__esModule",{value:!0})})}(z,z.exports)),z.exports}var ge=ye();const be=ve(ge);function ne(u,m){console.log(m);for(let d=0;d<u.data.length;d+=4){const y=u.data[d];u.data[d]=m[0],u.data[d+1]=m[1],u.data[d+2]=m[2],u.data[d+3]=y}return u}function oe(u){for(let m=0;m<u.data.length;m+=4)if(u.data[m+3]>0)return!0;return!1}function G(u,m,d,y=!1){const E=Math.round(y?m*u.width:m),I=(Math.round(y?d*u.height:d)*u.width+E)*4;return{r:u.data[I],g:u.data[I+1],b:u.data[I+2],a:u.data[I+3]}}function Oe(u,m,d,y){const E=(d*u.width+m)*4;u.data[E]=y.r,u.data[E+1]=y.g,u.data[E+2]=y.b,u.data[E+3]=y.a}function ie(u,m,d){const y=E=>{const j={x:E.x/u.width,y:E.y/u.height};return d({images:m,normalizedPos:j})};for(let E=0;E<u.height;++E)for(let j=0;j<u.width;++j){const I=y({x:j,y:E}),$=y({x:j+.5,y:E}),U=y({x:j,y:E+.5}),F=y({x:j+.5,y:E+.5}),X={r:(I.r+$.r+U.r+F.r)/4,g:(I.g+$.g+U.g+F.g)/4,b:(I.b+$.b+U.b+F.b)/4,a:(I.a+$.a+U.a+F.a)/4};Oe(u,j,E,X)}}function _e({images:u,normalizedPos:m}){const d=u[0],y=u[1],E=G(d,m.x,m.y,!0),j=G(y,m.x,m.y,!0);return{...E,a:j.a}}function Te(u,m){const d=new ImageData(u.width,u.height);return ie(d,[u,m],_e),d}function Ee({images:u,normalizedPos:m}){const d=u[0],y=u[1],E=G(d,m.x,m.y,!0),j=G(y,m.x,m.y,!0);return{...E,a:E.a*j.r}}function xe(u,m){const d=new ImageData(u.width,u.height);return ie(d,[u,m],Ee),d}function We(u){const m=u.data;for(let d=3;d<m.length;d+=4)if(m[d]<255)return!0;return!1}return be.worker({imageDataInvertByMaskImage:ne,isImageDataHasTransparent:oe,hasAlpha:We,upscaleReplaceAlpha:Te,cutoutWithMask:xe}),K.imageDataInvertByMaskImage=ne,K.isImageDataHasTransparent=oe,Object.defineProperty(K,Symbol.toStringTag,{value:"Module"}),K}({});
