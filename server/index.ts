// server 层
import express from "express";
import next from "next";
import path from "path";
import { createProxyMiddleware } from "http-proxy-middleware";

const cookieParser = require("cookie-parser");

const env = process.env.NEXT_PUBLIC_ENV || "dev";
// console.log(env);

const dev = process.env.NODE_ENV !== "production";
const hostname = "localhost";
const port = 5001;
// when using middleware `hostname` and `port` must be provided below
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  const server = express();
  server.use(cookieParser());

  // 添加 /aigc/api 代理配置
  server.use(
    "/aigc/api",
    createProxyMiddleware({
      target: process.env.NEXT_API_URL,
      changeOrigin: true,
      pathRewrite: {
        "^/aigc/api": "/api",
      },
    })
  );

  // 通过请求转发，我们可以使用一个nextjs服务，通过多个不同域名对外提供服务。

  server.use(express.static(path.resolve(__dirname, "../public")));
  app.setAssetPrefix("/_whee_static");

  server.get("/_next/**", (req, res) => {
    handle(req, res);
  });

  server.get("/_whee_static/_next/**", (req, res) => {
    req.url = req.url.replace(/^\/_whee_static/, "");
    handle(req, res);
  });

  // 我们约定项目目录以前缀 -- 开头，这样在浏览器内使用 navigate() 方法跳转的时候，浏览器地址栏内展示的将是修改后的正确地址。

  /**
   * 官方网站。
   */
  server.get("*", async (req, res) => {
    app.render(req, res, req.path, req.query as any);
  });

  server.listen(port, () => {
    // if (err) {
    //   throw err;
    // }
    console.log(`> Ready on http://${hostname}:${port}`);
  });
});
