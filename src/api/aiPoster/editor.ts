import { createInstance, createMockInstance } from "@/utils/request/request";
import {
  EditorConfigQuery,
  EditorConfigResponse,
  StyleModelResponse,
  StyleModelListQuery,
  StyleModelListResponse,
  EditorConfigModelListResponse,
  EditorConfigModuleListResponse,
  EditorConfigControlNetModel,
} from "../types/editorConfig";
import { toSnakeCase } from "@meitu/util";
import {
  PosterConfigRequest,
  PosterConfigType,
  PosterFontType,
} from "../types/poster";

const instance = createInstance({ baseURL: "/editor" });

/**
 * 获取模型列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120145 接口文档}
 */
export function fetchEditorConfig(params: EditorConfigQuery) {
  return instance.get<void, EditorConfigResponse>(
    "/config.json",
    toSnakeCase({ params })
  );
}

/**
 * 获取图生图编辑器配置列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120145 接口文档}
 */
export function fetchImageToImageEditorConfig(params?: EditorConfigQuery) {
  return instance.get<void, EditorConfigResponse>(
    "/image2image_config.json",
    toSnakeCase({ params })
  );
}

/**
 * 获取图生图编辑器配置列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/140844 接口文档}
 */
export function fetchStyleModelList(params: StyleModelListQuery) {
  return instance
    .get<void, StyleModelListResponse>(
      "/style_model.json",
      toSnakeCase({ params: {
        ...params,
        count: 1000,
      }})
    )
    .then(({ categoryList, moreButtonLabel }) => {
      return {
        styleModels: categoryList.map<StyleModelResponse>((category) =>
          Object.assign(category, {
            list:
              category.list?.map<EditorConfigModelListResponse>((item) =>
                Object.assign(item, {
                  styleModelWeight: item.weight,
                })
              ) ?? [],
          })
        ),
        moreButtonLabel,
      };
    });
}

/**
 * 获取controlnet模型列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/140844 接口文档}
 */
export function fetchControlNetList(params: { baseModelId: number }) {
  return instance
    .get<void, { modelList: EditorConfigModuleListResponse[] }>(
      "/controlnet_model.json",
      toSnakeCase({ params })
    )
    .then(({ modelList }) =>
      modelList.map<EditorConfigModuleListResponse>((category) =>
        Object.assign(category, {
          list:
            category.list?.map<EditorConfigControlNetModel>((item) =>
              Object.assign(item, {
                images: item.coverPic,
              })
            ) ?? [],
        })
      )
    );
}

/**
 * 概念图设计 - 配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/167623 接口文档}
 */
export function fetchConceptConfig() {
  return instance.get<void, EditorConfigResponse>("/ip_concept_config.json");
}

/**
 * AI 海报 - 配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/170204 接口文档}
 */
export async function fetchPosterConfig(params?: PosterConfigRequest) {
  const config = await instance.get<void, PosterConfigType>(
    "/ai_poster_config.json",
    {
      params: {
        ...toSnakeCase(params),
      },
    }
  );
  // 将categoryId记录到lora中
  config.template.forEach((category) => {
    category.list.forEach((lora) => {
      lora.categoryId = category.categoryId;
    });
  });

  return config;
}

/**
 * AI海报-字体
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/170536 接口文档}
 */
export async function fetchPosterFont() {
  const config = await instance.get<void, PosterFontType>(
    "/ai_poster_font.json"
  );

  return config;
}

/**
 * AI 海报 - 模型
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172458 接口文档}
 */
export function fetchPosterModel() {
  return instance.get<void, EditorConfigResponse>("/ai_poster_model.json");
}
