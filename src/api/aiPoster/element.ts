import { toSnakeCase } from "@meitu/util";
import {
  SaveElementRequest,
  SaveElementResponse,
  DeleteElementRequest,
  DeleteElementResponse,
} from "../types/aiPoster/element";
import { createInstance } from "@/utils/request/request";

const instance = createInstance({ baseURL: "/ai_poster/layer" });

/**
 * 创建/保存项目内元素
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172530 接口文档}
 */
export function saveElement(params: SaveElementRequest) {
  return instance.post<void, SaveElementResponse>("/save.json", {
    ...toSnakeCase(params),
  });
}

/**
 * 删除项目内元素
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172530 接口文档}
 */
export function deleteElement(params: DeleteElementRequest) {
  return instance.post<void, DeleteElementResponse>("/delete.json", {
    ...toSnakeCase(params),
  });
}
