import { createInstance } from "@/utils/request/request";
import { DraftType } from "../types/editorConfig";
import { toSnakeCase } from "@meitu/util";

const instance = createInstance({ baseURL: "/function_guide" });

export type ModelGuideListItem = {
  title: string;
  desc: string;
  pic: string;
};

export type ModelGuideResponse = {
  list: Array<ModelGuideListItem>;
};

/**
 * 创建摆渡页 - 弹窗内容
 * @description {@link https://api-mock.meitu-int.com/project/2891/interface/api/180277 接口文档}
 */
export function fetchCreateModelGuide(params: { from: DraftType }) {
  return instance.get<void, ModelGuideResponse>(
    "/list.json",
    toSnakeCase({ params })
  );
}
