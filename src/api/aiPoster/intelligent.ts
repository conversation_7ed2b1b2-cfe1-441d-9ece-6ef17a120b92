import { toSnakeCase } from "@meitu/util";
import { createInstance } from "@/utils/request/request";
import { Fetch } from "../types/aiPoster/intelligent";

const instance = createInstance({ baseURL: "/image_interactseg" });

/**
 * 创建智能识别任务
 * @description {@link https://api-mock.meitu-int.com/project/2891/interface/api/179827 接口文档}
 */
export function createSmartSwipeTask(params: Fetch.SmartSwipeRequestParams) {
    return instance.post<void, Fetch.SmartSwipeResponse>("/do.json", {
        ...toSnakeCase(params),
    });
}

/**
 * 获取智能识别任务结果
 * @description {@link https://api-mock.meitu-int.com/project/2891/interface/api/179830 接口文档}
 */
export function querySmartSwipeTaskResult(params: Fetch.QuerySmartSwipeTaskResultParams) {
    return instance.get<void, Fetch.QuerySmartSwipeTaskResultResponse[]>("/query.json", {
        params: {
            ...toSnakeCase(params),
        },
    });
}
