import { createInstance } from "@/utils/request/request";
import { CommonPostResponse } from "@/types";

const instance = createInstance({ baseURL: "/model" });

/**
 * 模型收藏请求参数
 */
export interface MarkModelBody {
  /**
   * 模型id
   */
  id: number;
}

/**
 * 模型收藏
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120354 接口文档}
 */
export function markModel(data: MarkModelBody) {
  return instance.post<void, CommonPostResponse>("/collect.json", data);
}

/**
 * 取消模型收藏
 * TODO：与收藏合并为一个接口
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120357 接口文档}
 */
export function cancelMarkModel(data: MarkModelBody) {
  return instance.post<void, CommonPostResponse>("/cancel_collect.json", data);
}
