import { toSnakeCase } from "@meitu/util";
import {
  ProjectConfigResponse,
  ProjectDetailResponse,
  SaveProjectRequest,
  SaveProjectResponse,
  ProjectListParams,
  ProjectListResponse,
  DeleteProjectRequest,
  DeleteProjectResponse,
} from "../types/aiPoster/project";
import { createInstance } from "@/utils/request/request";

const instance = createInstance({ baseURL: "/ai_poster/project" });

/**
 * 获取项目配置 用来获取最近编辑的项目列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172483 接口文档}
 */
export function fetchProjectConfig() {
  return instance.get<void, ProjectConfigResponse>("/config.json");
}

/**
 * 创建/保存项目
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172481 接口文档}
 */
export function saveProject(params: SaveProjectRequest) {
  return instance.post<void, SaveProjectResponse>("/save.json", {
    ...toSnakeCase(params),
  });
}

/**
 * 项目详情
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172522 接口文档}
 */

export function fetchProjectDetail(params: { projectId: number }) {
  return instance.get<void, ProjectDetailResponse>(
    "/detail.json",
    toSnakeCase({ params })
  );
}

/**
 * 删除项目
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172525 接口文档}
 */

export function deleteProject(params: DeleteProjectRequest) {
  return instance.post<void, DeleteProjectResponse>("/delete.json", toSnakeCase(params));
}

/**
 * 项目列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172474接口文档}
 */

export function getProjectList(params: ProjectListParams) {
  return instance.get<void, ProjectListResponse>("/user/ai_poster_feed.json", {
    baseURL: '/aigc/api',
    params: toSnakeCase(params),
  });
}

