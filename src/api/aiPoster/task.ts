import { toSnakeCase } from "@meitu/util";
import { createInstance } from "@/utils/request/request";
import { AIPoster, Fetch, Submit } from "../types/aiPoster/task";

const instance = createInstance({ baseURL: "/ai_poster/task" });
/*
 * 获取任务列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172553 接口文档}
 */
export async function fetchTaskList(params: Fetch.ListRequest) {
  const res = await instance.get<void, Fetch.ListResponse>("/list.json", {
    params: toSnakeCase(params),
  });

  return {
    ...res,
    list: res.list.map(transformResponseTask),
  };
}

/**
 * 查询任务
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172556 接口文档}
 */
export async function queryTask({ ids, projectId }: Fetch.QueryRequest) {
  const res = await instance.get<void, Fetch.QueryResponse>("/query.json", {
    params: toSnakeCase({
      ids: ids.join(","),
      projectId,
    }),
  });
  return res.map(transformResponseTask);
}

function getLoadingStatus(status: number): AIPoster.LoadingStatus {
  if (status === 100) {
    return AIPoster.LoadingStatus.Success;
  }

  if (status < 90) {
    return AIPoster.LoadingStatus.Loading;
  }

  return AIPoster.LoadingStatus.Failure;
}

function transformResponseTask(
  responseTask: Fetch.ResponseTask
): AIPoster.Task {
  return {
    ...responseTask,
    loadingStatus: getLoadingStatus(responseTask.status),
  };
}

/**
 * 创建任务
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172527}
 */
export function submitTask<T extends AIPoster.TaskCategory>(
  params: Submit.Request<T>
) {
  const taskParams = params.params;
  const data = {
    ...toSnakeCase(params),
    params: JSON.stringify(toSnakeCase(taskParams)),
  };

  return instance.post<void, Submit.Response>("/submit.json", data);
}

/**
 * 删除生图记录
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172571}
 */
export function deleteRecords(params: Fetch.DeleteRecordsRequest) {
  return instance.post<void, Fetch.DeleteRecordsResponse>(
    "/delete.json",
    toSnakeCase(params)
  );
}

export function downloadImage(params: Fetch.DownloadImageRequest) {
  return instance.post<void, Fetch.DownloadImageResponse>(
    "/download.json",
    toSnakeCase(params)
  );
}
