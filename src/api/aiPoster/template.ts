import { createInstance } from "@/utils/request/request";
import { CategoryListResponse, UpdateOrCreateTemplateRequest, UpdateOrCreateTemplateResponse, UpdateTemplateRequest } from "../types/aiPoster/template";
import { toSnakeCase } from "@meitu/util";
const instance = createInstance({ baseURL: "/ai_poster/template" });
/**
 * 获取分类列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/174721 接口文档}
 */
export function getCategoryList() {
  return instance.get<void, CategoryListResponse>("/category.json");
}

/**
 * 更新或创建模板
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/174711 接口文档}
 */
export function UpdateOrCreateTemplate(params: UpdateOrCreateTemplateRequest) {
  return instance.post<UpdateOrCreateTemplateRequest, UpdateOrCreateTemplateResponse>("/generate.json", {
    ...toSnakeCase(params),
  });
}
/**
 * 更新或创建模板
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/174711 接口文档}
 */
export function UpdateTemplate(params: UpdateTemplateRequest) {
  return instance.post<UpdateTemplateRequest, UpdateOrCreateTemplateResponse>("/update.json", params);
}
