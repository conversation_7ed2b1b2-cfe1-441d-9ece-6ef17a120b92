import type {
  OverviewBanner,
  CommonConfig,
  OverviewRecommendCard,
  OverviewOperation,
  OverviewBannerLinkType,
  OverviewRecommendCardStatus,
} from "@/types";
export interface FuncBaseConfig {
  /** 功能栏id */
  id: number;
  /** 功能栏名称 */
  name: string;
  /**  链接类型 */
  type: OverviewBannerLinkType;
  /**  任务类别 */
  taskCategory?: string;
}
export interface OriginalHomeConfigResponse {
  /** 横幅列表 */
  bannerList: Array<{
    /** 横幅编号 */
    id: number;

    /** 横幅文案 */
    content: string;

    /** 图片地址 */
    picture: string;

    /** 横幅链接 */
    url: string;

    /** 跳转类型 */
    type: OverviewBannerLinkType;
  }>;

  /** 功能栏列表 */
  funcBarList: Array<
    Omit<FuncBaseConfig, "type"> & {
      /**
       * 功能栏类型
       * @description {@link https://cf.meitu.com/confluence/pages/viewpage.action?pageId=402313315|接口文档}
       */
      type: FuncType;
      funcList: Array<
        Omit<FuncBaseConfig, "type"> &
          FunCoverConfig & {
            /** 状态 1可用，2不可用(前端只展示 不可用，点击提示开发中)	*/
            status: OverviewRecommendCardStatus;
            /** 标签 角标 url */
            tag: string;
            /** 副标题 */
            title: string;
            /** 跳转类型 */
            type: OverviewBannerLinkType;
            colspan?: number;
          }
      >;
    }
  >;

  /** 右上角运营栏列表 */
  rightUpperOperateList: Array<FuncBaseConfig & FunCoverConfig>;
}
export interface FunCoverConfig {
  /** 跳转地址 */
  url: string;
  /** 未选中图标 */
  pic: string;
  /** 已选中图标 */
  checkPic: string;
}

import { createInstance, createMockInstance } from "@/utils/request/request";

// import { addQueryParams, getSource } from "@/utils";
import { FuncType } from "@/types";

const instance = createInstance({ baseURL: "/home" });

/**
 * 获取首页配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/125550|接口文档}
 */
export function fetchHomeConfig() {
  return instance.get<void, OriginalHomeConfigResponse>("/config.json").then(
    (response) =>
      ({
        banners: response?.bannerList.map<OverviewBanner>(
          (originalBanner: any) => ({
            id: originalBanner.id.toString(),
            cover: originalBanner.picture,
            linkType: originalBanner.url ? originalBanner.type : undefined,
            link: originalBanner.url || undefined,
            title: originalBanner.content ?? "",
          })
        ),
        // HACK 目前只支持单行
        recommendCards: response?.funcBarList
          .find(({ type }) => FuncType.Card === type)
          ?.funcList?.map<OverviewRecommendCard>((func: any) => ({
            id: func.id.toString(),
            title: func.name,
            description: func.title,
            cover: func.pic,
            hoverCover: func.checkPic,
            status: func.status,
            // link: addQueryParams(func.url, { source: getSource() }),
            link: func.url,
            linkType: func.type,
            tag: func.tag,
            colspan: func.colspan,
          })),
        operations: response?.rightUpperOperateList?.map<OverviewOperation>(
          (originOperation: any) => ({
            id: originOperation.id.toString(),
            title: originOperation.name,
            cover: originOperation.pic,
            hoverCover: originOperation.checkPic,
            link: originOperation.url || undefined,
            linkType: originOperation.type,
            taskCategory: originOperation.taskCategory || undefined,
          })
        ),
      } as const)
  );
}

/**
 * 获取全局共用配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/127719|接口文档}
 */
export function fetchCommonConfig() {
  return instance
    .get<void, any>("/common.json")
    .then<CommonConfig>((response) => {
      const { id, title, subtitle, backImage, groupImage } =
        response?.addGroupPopup ?? {};
      return {
        showJumpZcoolBtn: response?.showJumpZcoolBtn,
        zcoolBtnPicUrl: response?.zcoolBtnPicUrl,
        activitySource: response?.activitySource,
        noticePopup: response?.noticePopup,
        groupPopupConfig: {
          id,
          title,
          description: subtitle,
          backgroundImage: backImage,
          qrcode: groupImage,
        },
      };
    });
}
