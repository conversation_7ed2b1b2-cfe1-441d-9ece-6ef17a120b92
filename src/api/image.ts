import { createInstance } from "@/utils/request/request";
import {
  ImageMonitorBody,
  ImageUploadSignBody,
  ImageUploadSignResponse,
} from "./types/image";
import { EditorConfigMattResponse } from "./types/editorConfig";
import { CommonPostResponse } from "./types/common";
import { toSnakeCase } from "@meitu/util";

const instance = createInstance({ baseURL: "/image" });

/**
 * 图片合规监测
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120384 接口文档}
 */
export function imageMonitor(data: ImageMonitorBody) {
  return instance.post<void, CommonPostResponse>("/monitor.json", data);
}

/**
 * 获取图片上传签名
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120393 接口文档}
 */
export function fetchImageUploadSign(params: ImageUploadSignBody) {
  return instance
    .get<void, EditorConfigMattResponse>("/get_sign.json", {
      params,
    })
    .then<ImageUploadSignResponse>((response) => {
      const { app, sig, sigTime, sigVersion, suffix, type, count } = response;

      return {
        app,
        sig,
        sigTime,
        sigVersion,
        suffix,
        type,
        count,
      };
    });
}

/**
 *  透明底图替换
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/153047 接口文档}
 */
export function replaceUpscaleImage(data: {
  id: string;
  resultImageUrl: string;
}) {

  const instance = createInstance({ baseURL: '/image_upscaler' });

  return instance.post<void, CommonPostResponse>(
    "/replace_alpha_image.json",
    toSnakeCase(data)
  );
}
