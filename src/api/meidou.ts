import { toSnakeCase } from '@meitu/util';
// import { createMockInstance, createInstance } from './utils';
import {
  MeiDouFetchPriceDescResponse,
  MeidouFetchPriceDescRequest,
  MeidouQueryBalanceResponse
} from './types/meidou';
import { MeidouBalance } from '@/types';
import { createInstance } from "@/utils/request/request";



// eslint-disable-next-line 
const instance = createInstance({ baseURL: '/sub' });

/**
 * 美豆余额查询
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/132759 接口文档}
 */
export function checkMeidouBalance() {
  return instance
    .get<void, MeidouQueryBalanceResponse>('/query_balance.json')
    .then<MeidouBalance>((response) => {
      const {
        availableAmount,
        tips,
        vipRightInfo,
        detailTitle,
        detailDesc,
        amountList = []
      } = response;

      return {
        availableAmount,
        tips,
        benefitsDescription: vipRightInfo,
        detailTitle,
        detailDesc,
        benefitsDetail: amountList.map(({ type, expireStr, desc, num }) => {
          return {
            type,
            key: expireStr,
            desc,
            value: num
          };
        })
      };
    });
}

/**
 * 获取定价信息
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/132762 接口文档}
 */
export function fetchPriceDesc(data: MeidouFetchPriceDescRequest) {
  return instance.post<void, MeiDouFetchPriceDescResponse>(
    '/get_price.json',
    toSnakeCase(data)
  );
}
