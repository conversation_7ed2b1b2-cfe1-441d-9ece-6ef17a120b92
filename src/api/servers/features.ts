import { fetcher } from "@/utils/request/fetcher";
import { getConfig } from "@/config";
import { toCamelCase, toSnakeCase } from "@meitu/util";
import { PageListResponse, PageModule } from "../types/feature";
import qs from 'qs';
import { PosterConfigRequest, TemplateListType, TemplateItemType } from "../types/poster";
const config = getConfig();

export const getPageList = async (fetchParams: {pageCategorys: string}): Promise<PageListResponse> => {
  const params = {
    page: 1,
    count: 9999,
    ...fetchParams,
  }
  const res = await fetcher<any>(`${config.API_URL}/seo_page/get_page_list.json?${qs.stringify(toSnakeCase(params))}`, {
    method: "GET",
    next: {
      revalidate:1,
    }
  });
  return toCamelCase(res?.data) || {};
};

export const getPageModuleList = async (params: { pageId: number }): Promise<PageModule[]> => {
  const res = await fetcher<any>(`${config.API_URL}/seo_page/get_module.json?${qs.stringify(toSnakeCase(params))}`, {
    method: "GET",
    next: {
      revalidate:1,
    }
  });
  return toCamelCase(res?.data) || [];
};

export const getTemplates = async (params: PosterConfigRequest): Promise<Array<TemplateListType>> => {
  const res = await fetcher<any>(`${config.API_URL}/editor/ai_poster_config.json?${qs.stringify(toSnakeCase(params))}`, {
    method: "GET",
    next: {
      revalidate: 60 * 60,
    }
  });
  return [
    ...((res?.data?.template) || []).map((item: any) => ({
      ...toCamelCase(item),
      list: item.list.map((el: any) => ({
        ...toCamelCase(el),
        newConfig: el.new_config,
      }))
    })) as TemplateListType[]
  ]
};

//	多个ID用英文逗号分隔
export const getTemplatesDetail = async (params: { ids: string }): Promise<Array<TemplateItemType>> => {
  const res = await fetcher<any>(`${config.API_URL}/ai_poster/template/info.json?${qs.stringify(toSnakeCase(params))}`, {
    method: "GET",
  });
  return [
    ...((res?.data?.list) || []).map((item: any) => ({
      ...toCamelCase(item),
      newConfig: item.new_config,
    })) as TemplateItemType[]
  ];
};