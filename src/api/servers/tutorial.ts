import { fetcher } from "@/utils/request/fetcher";
import { getConfig } from "@/config";
import { toCamelCase } from "@meitu/util";
import qs from 'qs';
import { TutorialListParams, TutorialListResponse, TutorialQueryResponse } from "../types/tutorial";
const config = getConfig();

export const getTutorialList = async (params: TutorialListParams): Promise<TutorialListResponse> => {
  const res = await fetcher<any>(`${config.API_URL}/tutorial/list.json?${qs.stringify(params)}`, {
    method: "GET",
    next: {
      revalidate: 0,
    }
  });
  return toCamelCase(res?.data) || [];
};

export const getTutorialQuery = async (params: { address: string }): Promise<TutorialQueryResponse> => {
  const res = await fetcher<any>(`${config.API_URL}/tutorial/query.json?${qs.stringify(params)}`, {
    method: "GET",
    next: {
      revalidate: 0,
    }
  });
  return toCamelCase(res?.data) || [];
};


