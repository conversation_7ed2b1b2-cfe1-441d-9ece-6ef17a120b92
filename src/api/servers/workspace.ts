import { fetcher } from "@/utils/request/fetcher";
import { getConfig } from "@/config";
import { toCamelCase } from "@meitu/util";
import {
  ConfigListResponse,
  FunctionListParams,
  FunctionListResponse,
} from "../types/workspace";
import { stringify } from "qs";
import {
  ProjectListParams,
  ProjectListResponse,
} from "../types/aiPoster/project";
import { TemplateItemType } from "../types/poster";
const config = getConfig();

export const getConfigList = async (
  customHeaders?: Record<string, string>
): Promise<ConfigListResponse> => {
  const res = await fetcher<any>(
    `${config.API_URL}/home/<USER>
    {
      method: "GET",
      headers: customHeaders,
    },
    {
      withCookies: true,
    }
  );
  return {
    ...(toCamelCase(res?.data) || {}),
    aiPosterTemplate: res?.data?.ai_poster_template?.map((item: any) => ({
      ...toCamelCase(item),
      newConfig: item.new_config,
    })) as TemplateItemType[],
  };
};

export const getFunctionList = async (
  params: FunctionListParams
): Promise<FunctionListResponse> => {
  const res = await fetcher<any>(
    `${config.API_URL}/func_bar/list.json?${stringify(params)}`,
    {
      method: "GET",
      next: {
        revalidate: 0,
      },
    }
  );
  return toCamelCase(res?.data) || {};
};

export const getProjectList = async (
  params: ProjectListParams
): Promise<ProjectListResponse> => {
  const res = await fetcher<any>(
    `${config.API_URL}/user/ai_poster_feed.json?${stringify(params)}`,
    {
      method: "GET",
    },
    {
      withCookies: true,
    }
  );
  return toCamelCase(res?.data) || {};
};
