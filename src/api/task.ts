import { createInstance } from "@/utils/request/request";
import { DownloadImageRequest, DownloadImageResponse } from "./types/task";
import { toSnakeCase } from "@meitu/util";

const instance = createInstance({ baseURL: "/task" });

export function downloadImage(params: DownloadImageRequest) {
  return instance.post<void, DownloadImageResponse>(
    "/download_image.json",
    toSnakeCase(params)
  );
}