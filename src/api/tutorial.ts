

import { toSnakeCase } from "@meitu/util";
import { createInstance } from "@/utils/request/request";
import { TutorialListResponse, TutorialListParams } from "./types/tutorial";

const instance = createInstance({ baseURL: "" });

/**
 * 获取教程列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172483 接口文档}
 */
export function getTutorialList(params: TutorialListParams) {
  return instance.get<void, TutorialListResponse>("/tutorial/list.json", {
    params: toSnakeCase(params),
  });
}
