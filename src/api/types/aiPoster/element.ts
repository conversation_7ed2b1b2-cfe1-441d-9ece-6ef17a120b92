export type LayerConfig = {
  /**
   * 元素id
   */
  layerId: string;
  /**
   * 元素参数
   */
  editorParams: string;
};

export type SaveElementRequest = {
  /**
   * 项目id
   */
  projectId: number;
  layers: Array<LayerConfig>;
};

export type SaveElementResponse = Array<{
  layerId: string;
}>;

export type DeleteElementRequest = {
  /**
   * 项目id
   */
  projectId: number;
  /**
   * 元素id，多个图层ID使用英文逗号分隔
   */
  layerIds: string;
};

export type DeleteElementResponse = {
  result: boolean;
};
