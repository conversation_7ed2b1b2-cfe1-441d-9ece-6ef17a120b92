

export enum SmartSwipeTaskStatus {
    PROCESSING = 0, // 流程中状态
    CREATED = 1, // 任务创建成功
    INPUT_FILE_DELIVERED = 10, // 输入文件已投递审核
    INPUT_FILE_APPROVED = 11, // 输入文件审核通过
    OPEN_PLATFORM_DELIVERED = 30, // 已投递到开放平台
    OPEN_PLATFORM_PROCESSED = 31, // 开放平台处理成功待安全审核
    RESULT_AUDITED = 32, // 结果审核完成
    
    // 失败状态
    OPEN_PLATFORM_DELIVERY_FAILED = 90, // 开放平台投递失败
    OPEN_PLATFORM_PROCESS_FAILED = 91, // 开放平台处理失败
    INPUT_FILE_AUDIT_FAILED = 92, // 输入文件送审失败
    OUTPUT_FILE_AUDIT_FAILED = 93, // 输出文件送审失败
    INPUT_FILE_BLOCKED = 94, // 输入文件审核拦截
    OUTPUT_FILE_BLOCKED = 95, // 输出文件审核拦截
    UNDEFINED_INTERRUPTION = 99, // 未定义的中断
    
    SUCCESS = 100 // 全流程完成
}


/**
 * 请求的参数类型和响应的数据类型
 */
export namespace Fetch {
    /**
     * 智能涂抹请求参数
     */
    export type SmartSwipeRequestParams = {
        projectId?: number; // 项目id
        initImage: string; // 初始图片
        maskImage?: string; // 涂抹的mask
        brushWidth?: string; // 涂抹宽度
        clickList: ([number, number, number])[]; // 涂抹点
    }

    export type SmartSwipeResponse = {
        id: string; // 任务ID
    }

    export type QuerySmartSwipeTaskResultParams = {
        ids: string; // 任务ID
    }

    export type QuerySmartSwipeTaskResultResponse = {
        id: string; // 任务ID
        fail_msg: string; // 失败信息
        status: SmartSwipeTaskStatus; // 任务结果
        resultImages: string[]; // 结果图片
    }

}