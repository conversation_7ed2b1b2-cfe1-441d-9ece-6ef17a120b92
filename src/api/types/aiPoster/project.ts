export type ProjectItem = {
  /**
   * 项目id
   */
  projectId: number;
  /**
   * 项目名称
   */
  projectName: string;
  /**
   * 封面图
   */
  picUrl?: string;
  /**
   * 描述
   */
  lastEditedTime: string;
  /**
   * 是否可编辑
   * @deprecated 着个字段服务端返回的是错的
   */
  canEdit: boolean;
};

export type SubscribeConfig = {
  normalMeidouCount: number;
  projectCountLimit: number;
  vipMeidouCount: number;
  vipPrice: number;
};

export type ProjectConfigResponse = {
  projectList: Array<ProjectItem>;
  config: SubscribeConfig;
};

export type SaveProjectRequest = Partial<ProjectItem>;

export type SaveProjectResponse = {
  projectId: number;
  projectName: string;
  picUrl?: string;
  canEdit: boolean;
};

export type ProjectDetailResponse = SaveProjectResponse & {
  editorParams?: Array<{
    layerId: string;
    params: string;
  }>;
  // 当前项目是否可编辑
  canEdit?: boolean;
};

export type DeleteProjectRequest = {
  projectId: number;
}

export type DeleteProjectResponse = {
  result: boolean;
}

export type ProjectListResponse = {
  list: ProjectItem[];
  cursor: string;
};

export type ProjectSortType = 'lastopened' | 'lastcreated' | 'alphabetically'

export type ProjectListParams = {
  cursor: string;
  count: number;
  sortType: ProjectSortType | undefined;
}