export type CategoryItem = {
    categoryId: number
    categoryName: string
}
export type CategoryListResponse = {
    list: Array<CategoryItem>
}


export type UpdateOrCreateTemplateRequest = {
    templateId?: string // 更新的模板ID
    categoryId: string // 分类ID
    name: string // 模板名称
    pic: string // 模板封面图
    ratio: string // 模板比例
    prompt: string // 提示词
    msgId: string // 消息ID
}

export type UpdateOrCreateTemplateResponse = {
    result: boolean // 是否成功
}

export type UpdateTemplateRequest = {
    template_id: string // 更新的模板ID
    config: Record<string, any> // 模板配置
}