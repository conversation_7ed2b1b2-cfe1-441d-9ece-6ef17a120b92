import { AppModuleParam } from "@/types";

/**
 * Post 请求公共返回类型
 */
export interface CommonPostResponse {
  result: boolean;
  resultImage?: string;
  message?: string;
}

/**
 * 请求错误信息
 */
export interface CommonRequestError {
  code?: number;
  message?: string;

  /** 秀秀社区的错误额外信息（一般是中文的） */
  msg?: string;

  /** 秀秀社区的错误额外信息（一般是英文的） */
  error?: string;
}

export interface CommonCreate {
  functionName?: string;
  mediaType?: string;
  resMediaType?: string;
}

export enum MediaType {
  Text = "text",
  Photo = "photo",
  Video = "video",
  Model = "model",
}
