import { ReactNode } from "react";
import { TemplateItemType } from "./poster";

export interface PageListResponse {
  dataList: Page[];
  total: number;
}

export enum FeatureStatus {
  open = 1,
  close = 2,
}

export interface Page {
  id: number;
  pageName: string;
  pageKeyword: string;
  seoTitle: string;
  seoDescribe: string;
  seoKeyword: string;
  status: FeatureStatus;
  pageCategory: PageCategorys;
}

export interface PageModule {
  moduleId?: number;
  title: string;
  subtitle?: string;
  content?: string;
  moduleType?: ModuleType;
  moduleIndex?: number;
  pageId?: number;
  images?: string[];
  children?: ChildrenItem[];
  pageKeyword?: string;
}

export interface ChildrenItem  {
  moduleId?: number;
  pageId?: number;
  title: string;
  content?: string | ReactNode;
  images?: string[];
  templateId?: string;
  template?: TemplateItemType;
  url?: string;
}

export enum ModuleType {
  Hero = 1,
  HeroFullScreen = 2,
  PosterView = 4,
  FreeGraphics = 5,
  FreeGraphics2 = 6,
  HorizontalImageAndText = 7,
  TextToImage = 8,
  GuideStep = 9,
  GuideStep2 = 10,
  FAQ = 11,
  CarouselFeature = 12,
  Template = 13,
  ExploreMore = 15,
  ToolCompare = 16,
}

export enum PageCategorys {
  Features = '1',
  Features2 = '2',
  Tutorial = '3',
}