import { EditorConfigMattResponse } from "./editorConfig";

/**
 * 图片合规监测请求参数
 */
export interface ImageMonitorBody {
  /**
   * 图片加密串
   */
  data: string;
  taskCategory?: string;
}

export interface ImageUploadSignBody {
  // 业务类型，例如 editor 、 training
  type: string;

  // 文件后缀，视情况上传，例如  jpg、.png
  suffix?: string;
}

/**
 * 获取图片上传签名响应
 */
export type ImageUploadSignResponse = Pick<
  EditorConfigMattResponse,
  "app" | "type" | "count" | "sig" | "sigTime" | "sigVersion" | "suffix"
>;
