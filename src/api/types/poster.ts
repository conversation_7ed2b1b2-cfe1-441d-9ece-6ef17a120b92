import { ElementOptions, FrameElementOptions, FrameElementParams } from "@meitu/whee-infinite-canvas";
import { CommonCreate } from "./common";

export enum LayerType {
  Text = 1,
  Image = 2,
}
// 模版的图层信息
export interface TemplateConfigType {
  type: LayerType;
  fontName: string;
  fontFile: string;
  wofFontFile: string;
  text?: string;
  fontSize?: number;
  marginTop?: number;
  marginLeft?: number;
  pic?: string;
  width?: number;
  height?: number;
  subsetFontFile?: string;
  subsetWofFontFile?: string;
  subsetZipFontFile?: string;
  rotation?: number;
}
// 单个模版
export interface TemplateItemType {
  categoryId?: number;
  id: number;
  name: string;
  pic: string;
  prompt: string;
  recommendPrompt: string;
  ratio: string;
  config: Array<TemplateConfigType>;
  newConfig: {
    data: FrameElementOptions;
    v2: boolean;
  };
}

export enum TemplateFuncType {
  Create = 3,
}

export type PosterConfigRequest = {
  categoryId?: number;
  cursor?: string;
  // 每一页的个数
  count?: number;
  funcType?: TemplateFuncType;
};

// 模版列表
export interface TemplateListType {
  categoryId: number;
  categoryName: string;
  categoryIcon?: string;
  hoverCategoryIcon?: string;
  cursor: string;
  list: Array<TemplateItemType>;
}
// 模版配置
export interface PosterConfigType {
  template: Array<TemplateListType>;
  // 默认字体名
  name: string;
  // 默认字体链接
  fontFile: string;
  wofFontFile: string;
  // 是否有上传模版的白名单权限
  generateTemplatePermission?: boolean;
}

export enum PosterRatio {
  ORIGINAL = "original",
  RATIO_1_1 = "1:1",
  RATIO_2_3 = "2:3",
  RATIO_3_4 = "3:4",
  RATIO_9_16 = "9:16",
  RATIO_3_2 = "3:2",
  RATIO_4_3 = "4:3",
  RATIO_16_9 = "16:9",
  RATIO_1_2 = "1:2",
  RATIO_2_1 = "2:1",
  FREE = "custom",
  CUSTOM = "custom",
}
export interface TextGenerationType {
  text: string;
  font?: string;
  position: [];
}
export interface CreatePosterTaskBody extends CommonCreate {
  /**
   * 编辑器生成图片的任务参数
   */
  params: {
    /**
     * 正向提示词/提示词
     */
    prompt: string;
    /**
     * 如果使用了填空输入提示词 这个字段用来保存回填的参数
     */
    userPrompt?: string;
    /**
     * 参数设定-画面尺寸-宽度
     */
    width: number;
    /**
     * 参数设定-画面尺寸-高度
     */
    height: number;
    /**
     * 参数设定-图片比例（不用上报，会根据尺寸下发）
     */
    picRatio: PosterRatio;
    /**
     * 选择生成的图片数量，1-10    默认数量4
     */
    batchSize: number;
    /**
     * 参考图图片-图片地址数组
     */
    initImages?: string[];
    /**
     * 文字生成控制
     */
    textGeneration?: Array<TextGenerationType>;
    /**
     * 编辑器数据，任务回填使用，map
     */
    tgEditorParams?: string;
    /**
     * 模版id
     */
    templateId?: number;
    /**
     * 模版分类id
     */
    templateCategoryId?: number;
  };
}

export interface PosterFontItemType {
  fontId: number;
  defaultName: string;
  name: string;
  fontFile: string;
  wofFontFile: string;
  previewFile: string;
}

export interface PosterFontType {
  cursor: string;
  list: Array<PosterFontItemType>;
}

//#region 上传模版
export enum TemplateSource {
  // 国内
  Domestic = 1,
  // 国外
  International = 2,
}

type GenerateTemplateCommonConfig = {
  marginLeft: number;
  marginTop: number;
  rotation: number;
  sort: number;
};

export type GenerateTemplateImageConfig = GenerateTemplateCommonConfig & {
  type: LayerType.Image;
  pic: string;
};
export type GenerateTemplateTextConfig = GenerateTemplateCommonConfig & {
  type: LayerType.Text;
  fontName: string;
  fontSize: number;
};

export type GenerateTemplateRequest = {
  source: TemplateSource;
  categoryId: number;
  name: string;
  pic: string;
  ratio: string;
  prompt: string;
  config: Array<GenerateTemplateImageConfig | GenerateTemplateTextConfig>;
};

export type TemplateCategory = {
  categoryId: number;
  categoryName: string;
};

export type TemplateCategoryListResponse = {
  list: Array<TemplateCategory>;
};

export type GenerateTemplateResponse = {
  result: boolean;
};
//#endregion
