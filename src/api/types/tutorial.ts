export interface TutorialItem {
  id: number;
  name: string;
  type: TutorialType;
  address: string;
  cover: string;
  width: number;
  height: number;
  colSpan: number;
}

export interface TutorialListParams {
  count?: number;
  cursor?: string;
}

export interface TutorialListResponse {
  list: TutorialItem[];
  cursor: string;
}

// 文章类型  1 是 url 地址，2 为 scheme 地址，3 为视频地址，4 表本地文章 (字符串或字符串 id)
export enum TutorialType {
  External = 1,
  Scheme = 2,
  Video = 3,
  Local = 4
}

// 详情接口返回
export interface TutorialQueryResponse {
  id: number;
  name: string;
  content: string;
  updatedAt: number;
  views: number;
}
