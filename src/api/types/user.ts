export interface UserInfoResponse {
  user: {
    id: number;
    screenName: string;
    avatar: string;
  };
  task: {
    // 发布作品数量
    feedCount: number;
    // 发布模型数量
    modelCount: number;
    // 收藏作品数量
    favoriteFeedCount: number;
    // 收藏模型数量
    favoriteModelCount: number;
    // 改图编辑器项目数量
    projectCount: number;
    // ai海报编辑器项目数量
    aiPosterCount: number;
  };
}

export interface UserInfo {
  // uid
  uid: number;
  // 用户名
  screenName: string;
  // 头像
  avatarUrl: string;
  // 发布作品数量
  feedCount: number;
  // 发布模型数量
  modelCount: number;
  // 收藏作品数量
  favoriteFeedCount: number;
  // 收藏模型数量
  favoriteModelCount: number;
  // 改图编辑器项目数量
  projectCount: number;
  // ai海报编辑器项目数量
  aiPosterCount: number;
}
