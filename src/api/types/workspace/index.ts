import { TemplateItemType } from "../poster";
import { ProjectItem } from "../aiPoster/project";
export interface FunctionListItem {
  id: number;
  name: string;
  type: string;
  icon: string;
  url: string;
  pic: string;
  hoverIcon: string;
  hoverBackgroundColor: string;
}

export interface ConfigListResponse {
  funcList: FunctionListItem[];
  aiPosterTemplate: TemplateItemType[];
  aiPosterProject: ProjectItem[];
}



export interface FunctionListParams {
  cursor?: string;
  count?: number;
}

export interface FunctionListResponse {
  list: FunctionListItem[];
  cursor: string;
}