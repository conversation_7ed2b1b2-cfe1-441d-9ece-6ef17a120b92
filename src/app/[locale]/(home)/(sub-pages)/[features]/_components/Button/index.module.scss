@import '@/styles/variable.scss';
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 11px 20px;
  border-radius: var(--radius-10, 10px);
  /* text_16_medium */
   font-family: var(--font-poppins);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%; /* 20.8px */
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
.button-default {
  background: var(--system-content-primary, #FFF);
  color: var(--Font-Color, #111826);
}

.button-primary {
  background: $system-content-brand-primary;
  color: var(--system-content-onPrimary, #181818);
}

