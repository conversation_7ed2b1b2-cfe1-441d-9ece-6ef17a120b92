import styles from './index.module.scss';
import classNames from 'classnames';
export interface ButtonProps {
  children: React.ReactNode;
  type?: 'default' | 'primary';
  className?: string;
  onClick?: () => void;
}
const Button = ({ children, type = 'default', className, onClick }: ButtonProps) => {
  return <div className={classNames(styles.button, styles[`button-${type}`], className)} onClick={onClick}>{children}</div>;
};

export default Button;
