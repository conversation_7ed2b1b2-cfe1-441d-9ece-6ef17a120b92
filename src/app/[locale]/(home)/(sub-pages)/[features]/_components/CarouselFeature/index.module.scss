.carousel-feature {
  width: 1200px;
  margin: 0 auto;
  padding: 80px 0;
  :global {
    .carousel-feature-title {
      color: var(--system-content-primary, #fff);
      text-align: center;
      /* Title/Large */
       font-family: var(--font-poppins);
      font-size: 40px;
      font-style: normal;
      font-weight: 700;
      line-height: 120%; /* 48px */
      margin-bottom: 16px;
    }
    .secondary-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;
      .secondary-left {
        .secondary-title {
          color: var(--system-content-primary, #fff);
           font-family: var(--font-poppins);
          font-size: 24px;
          font-style: normal;
          font-weight: 700;
          line-height: 32px; /* 133.333% */
        }
        .secondary-description {
          color: var(--system-content-secondary, #a3aebf);
           font-family: var(--font-poppins);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 137.5% */
        }
      }
      .secondary-right {
        display: flex;
        gap: 16px;
        .swiper-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          border-radius: var(--radius-16, 16px);
          background: #1D1E23;
          /* level_2 */
          box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 6px 24px 0px rgba(0, 0, 0, 0.06);
          cursor: pointer;
          &:hover {
            opacity: 0.8;
          }
        }
        .swiper-button-disabled {
          opacity: 0.7;
          cursor: not-allowed;
          &:hover {
            opacity: 0.7;
          }
        }
       
      }
    }
    .carousel-feature-carousel {
      .carousel-feature-carousel-item {
       width: 100%;
        height: 321px;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
        box-sizing: border-box;
      }
    }
    .carousel-feature-scroll-container {
      display: none;
    }
  }
  .card-item {
    width: auto;
    height: 100%;
    cursor: pointer;
    :global {
      .card-item-image-container {
        border-radius: var(--radius-16, 16px);
        border: 1px solid var(--system-stroke-input-default, #22272E);
        margin-bottom: 16px;
        overflow: hidden;
        .card-item-image {
          border-radius: var(--radius-16, 16px);
        }
      }
     
      .card-item-title {
        color: var(--system-content-primary, #FFF);
         font-family: var(--font-poppins);
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 30px; /* 150% */
      }
      .card-item-description {
        color: var(--system-content-secondary, #A3AEBF);
        /* Text-Base/Regular */
         font-family: var(--font-poppins);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 142.857% */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

}
@media (max-width: 1200px) {
  .carousel-feature {
    width: 682px;
    padding: 80px 16px;
  }
}

@media (max-width: 768px) {
  .carousel-feature {
    width: 100%;
    margin: 0;
    box-sizing: border-box;
    :global {
      .secondary-container {
        .secondary-left {
         text-align: center;
         .secondary-title {
          margin-bottom: 16px;
         }
        
        }
        .secondary-right {
          display: none;
        }
      }
      .carousel-feature-scroll-container {
        display: flex;
        overflow-x: auto;
        gap: 24px;
        scrollbar-width: none;
        &::-webkit-scrollbar {
          display: none;
        }

      }
      .carousel-feature-carousel {
        display: none;
      }
    }
    .card-item {
      flex-shrink: 0;
      width: 282px;
      height: 100%;
    }
  }
}
