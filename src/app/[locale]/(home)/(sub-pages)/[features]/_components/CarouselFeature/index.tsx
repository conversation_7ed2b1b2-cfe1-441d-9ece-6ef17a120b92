'use client'
import styles from './index.module.scss';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel"
// import image1 from '@/assets/images/home/<USER>/1.png'
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { type CarouselApi } from "@/components/ui/carousel"
import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { ChildrenItem, PageModule } from '@/api/types/feature';
import useRouterPush from '../../_hooks/useRouterPush';
import TransformImage from '@/components/TransformImage';
import { trackEvent } from "@/services/tracer";
import { ModuleType } from "@/api/types/feature";
import { usePathname } from "next/navigation";
// const list = [
//   {
      
//     title: 'AI Poster Designs',
//     description: 'Generate high-quality images in seconds with our AI tools and add a unique touch to your brand.',
//     image: image1
//   },
//   {
//     title: 'AI Poster Designs', 
//     description: 'Generate high-quality images in seconds with our AI tools and add a unique touch to your brand.',
//     image: image1
//   },
//   {
//     title: 'AI Poster Designs',
//     description: 'Generate high-quality images in seconds with our AI tools and add a unique touch to your brand.',
//     image: image1
//   },
//   {
//     title: 'AI Poster Designs',
//     description: 'Generate high-quality images in seconds with our AI tools and add a unique touch to your brand.',
//     image: image1
//   },
//   {
//     title: 'AI Poster Designs',
//     description: 'Generate high-quality images in seconds with our AI tools and add a unique touch to your brand.',
//     image: image1
//   },
// ]


const CardItem = ({item, pageKeyword}: {item: ChildrenItem, pageKeyword?: string}) => {
  const routerPush = useRouterPush(item?.url);
  const pathname = usePathname();
  return (
    <div className={styles['card-item']} onClick={() => {
      if (pathname.includes("tutorial")) {
        trackEvent("tutorial_detail_click", {
          tutorial_id: sessionStorage.getItem('tutorialId'),
          tutorial_name: sessionStorage.getItem('tutorialName'),
          click_type: 'get_started'
        });
      } else {
        trackEvent("landing_page_click", {
          module_id: ModuleType.CarouselFeature,
          url: location.href,
          keyword: pageKeyword,
        });
      }
      routerPush(item?.url)
    }}>
      <div className="card-item-image-container">
        <TransformImage className="card-item-image" src={item?.images?.[0] ?? ""} alt='card-item' width={282} height={211} />
      </div>
      <h4 className='card-item-title'>
        {item.title}
      </h4>
      <p className='card-item-description'>
        {item.content}
      </p>
    </div>
  )
}

const CarouselFeature = (props: PageModule) => {
  const { title, children, subtitle, content, pageKeyword } = props;
  const [api, setApi] = useState<CarouselApi>()
  const [count, setCount] = useState(0)
  const [current, setCurrent] = useState(0)

  useEffect(() => {
    if (!api) {
      return
    }
 
    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap() + 1)
 
    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  const generateCarouselItem = children?.reduce((acc:any, _, index) => {
    if (index % 4 === 0) {
      acc.push({
        id: index / 4,
        items: children?.slice(index, index + 4)
      });
    }
    return acc;
  }, []);
  
  
  return (
    <section className={styles['carousel-feature']}>
      {title && <h2 className={'carousel-feature-title'}>{title}</h2>}
      <div className='secondary-container'>
       <div className='secondary-left'>
       {subtitle && <h3 className='secondary-title'>
          {subtitle}
        </h3>}
        {content && <p className='secondary-description'>
        {content}
        </p>}
       </div>
       <div className='secondary-right'>
        <div className={classNames('swiper-button', {
          'swiper-button-disabled': current === 1
        })} onClick={() => api?.scrollPrev()}>
        <ChevronLeft className='w-7 h-7'/>
        </div>
        <div className={classNames('swiper-button', {
          'swiper-button-disabled': current === count
        })} onClick={() => api?.scrollNext()}>
        <ChevronRight className='w-7 h-7'/>
        </div>
       </div>
      </div>
      <Carousel className={'carousel-feature-carousel'} setApi={setApi}>
        <CarouselContent>
          {
            generateCarouselItem.map((item:any, index:number) => (  
              <CarouselItem className='carousel-feature-carousel-item' key={index}>
                {item.items.map((item:any) => (
                  <CardItem key={item.title} item={item} pageKeyword={pageKeyword} />
                ))}
              </CarouselItem>
            ))
          }
        </CarouselContent>
      </Carousel>
      <div className='carousel-feature-scroll-container'>
        {children?.map((item:any) => (
          <CardItem key={item.title} item={item} pageKeyword={pageKeyword} />
        ))}
      </div>
    
    </section>
  );
};

export default CarouselFeature;