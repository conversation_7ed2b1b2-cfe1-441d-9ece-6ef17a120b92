"use client";
import Button, { ButtonProps } from "../Button";
import { useStore } from "@/contexts/StoreContext";
import { useCreateProject } from "@/hooks/useCreateProject";
import { useRouter } from "next/navigation";
import { observer } from "mobx-react-lite";
import { wheeLoginPopup } from "@/utils/account";
const CreateButton = (props: ButtonProps) => {
  const { children, onClick, ...rest } = props;
  const { globalProjectStore, userStore } = useStore();
  const { createProject } = useCreateProject({
    globalProjectStore,
    userStore,
  });
  const router = useRouter();
  const handleClick = async () => {
    try {
      onClick?.();
      if (!userStore.isLogin) {
        await wheeLoginPopup({
          useLoginMethod: "third-party",
          setUserStore: false,
        });
        // router.replace("/workspace");
        location.href = `/workspace${location.search}`;
        return;
      }
      const project = await createProject();
      if (!project) return;
      router.push(`/editor/project/${project.projectId}`);
    } catch (error) {
      // console.log("error", error);
    }
  };
  return (
    <Button {...rest} onClick={handleClick}>
      {children}
    </Button>
  );
};

export default observer(CreateButton);
