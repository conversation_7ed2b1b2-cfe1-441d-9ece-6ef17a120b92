.explore-more {
  padding: 40px 0;
  width: 1200px;
  margin: 0 auto;
  :global {
    .title {
      text-align: center;
      color: var(--system-content-primary, #fff);
      /* Title/Large */
       font-family: var(--font-poppins);
      font-size: 40px;
      font-style: normal;
      font-weight: 700;
      line-height: 120%; /* 48px */
      margin-bottom: 40px;
    }
    .more-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      .more-item {
        display: flex;
        align-items: center;
        gap: 16px;
        border-radius: var(--radius-16, 16px);
        border: 1px solid var(--system-stroke-input-default, #22272E);
        background: var(--system-background-secondary, #1D1E23);
        padding: 24px;
        box-sizing: border-box;
        height: 98px;
        cursor: pointer;
        .more-item-title {
          width: 194px;
          color: var(--system-content-primary, #FFF);
           font-family: var(--font-poppins);
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          line-height: 25px; /* 138.889% */
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .more-item-arrow {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(107, 122, 143, 1);
          svg {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .explore-more {
    width: 682px;
    padding: 40px 64px;
    :global {
      .more-list {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
@media (max-width: 768px) {
  .explore-more {
    width: 100%;
    padding: 40px 16px;
    box-sizing: border-box;
    :global {
      .more-list {
        .more-item {
          width: 100%;
          justify-content: space-between;
          .more-item-title {
            flex: 1;
          }
        }
      }
    }
  }
}
