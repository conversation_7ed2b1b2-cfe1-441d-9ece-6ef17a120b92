'use client';
import { ForwardArrowBold } from '@meitu/candy-icons';
import styles from './index.module.scss';
import { ChildrenItem, ModuleType, PageModule } from '@/api/types/feature';
import useRouterPush from '../../_hooks/useRouterPush';
import { trackEvent } from '@/services/tracer';
const moreList = [
  {
    title: 'Watermark & Caption Remover',
    icon: 'watermark',
  },
  {
    title: 'Watermark & Caption Remover',
    icon: 'watermark',
  },
  {
    title: 'Watermark & Caption Remover',
    icon: 'watermark',
  },
  {
    title: 'Watermark & Caption Remover',
    icon: 'watermark',
  },
  {
    title: 'Watermark & Caption Remover',
    icon: 'watermark',
  },
  {
    title: 'Watermark & Caption Remover',
    icon: 'watermark',
  },
  {
    title: 'Watermark & Caption Remover  Remove Remove Remove',
    icon: 'watermark',
  },
  {
    title: 'Watermark & Caption Remover',
    icon: 'watermark',
  },
];
const ExploreMore = (props: PageModule) => {
  const { title, children, pageKeyword } = props;
  const routerPush = useRouterPush(children?.map(item => item?.url ?? ''));
  return (
    <section className={styles["explore-more"]}>
      {title && <h2 className={"title"}>{title}</h2>}
      <div className="more-list">
        {children?.map((item: ChildrenItem) => (
          <div className="more-item" key={item.title} onClick={() => {
            routerPush(item.url)
            trackEvent("landing_page_click", {
              module_id: ModuleType.ExploreMore,
              url: location.href,
              keyword: pageKeyword,
            });
          }}>
            <div className="more-item-title">
              <h3>{item.title}</h3>
            </div>
            <ForwardArrowBold className="more-item-arrow" />
          </div>
        ))}
      </div>
    </section>
  );
};

export default ExploreMore;
