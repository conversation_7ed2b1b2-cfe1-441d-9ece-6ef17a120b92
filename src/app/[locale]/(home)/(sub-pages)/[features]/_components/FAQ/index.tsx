"use client";
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordionTemp";
import styles from "./index.module.scss";
import { ChildrenItem, PageModule } from "@/api/types/feature";

const faqData = [
  {
    question: "What is the AI that makes viral videos?",
    answer:
      "Vmake&apos;s AI Video Generator helps create videos with the potential to go viral by turning text, images, and videos into engaging, high-quality content. Its easy-to-use interface and support for multiple platforms make it a powerful tool for viral video creation.",
  },
  {
    question: "What is the AI that makes viral videos?",
    answer:
      "Vmake&apos;s AI Video Generator helps create videos with the potential to go viral by turning text, images, and videos into engaging, high-quality content. Its easy-to-use interface and support for multiple platforms make it a powerful tool for viral video creation.",
  },
  {
    question: "What is the AI that makes viral videos?",
    answer:
      "Vmake&apos;s AI Video Generator helps create videos with the potential to go viral by turning text, images, and videos into engaging, high-quality content. Its easy-to-use interface and support for multiple platforms make it a powerful tool for viral video creation.",
  },
  {
    question: "What is the AI that makes viral videos?",
    answer:
      "Vmake&apos;s AI Video Generator helps create videos with the potential to go viral by turning text, images, and videos into engaging, high-quality content. Its easy-to-use interface and support for multiple platforms make it a powerful tool for viral video creation.",
  },
];

export interface FAQProps extends PageModule {
  children?: Array<ChildrenItem & {
    onTriggerClick?: () => void;
  }>
}

const FAQ = (props: FAQProps) => {
  const { title, children } = props;
  return (
    <section className={styles.faq}>
      {title && <h2 className="faq-title">{title}</h2>}
      <Accordion type="multiple" className="w-full">
        {children?.map((item, index) => (
          <AccordionItem key={index} value={`item-${index + 1}`} className="border-b-1 border-b-white/10 border-solid">
            <AccordionTrigger
              className="pt-0"
              onClick={() => {
                item?.onTriggerClick?.()
              }}
            >
              {item.title}
            </AccordionTrigger>
            <AccordionContent>
              {item.content}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  );
};
export default FAQ;
