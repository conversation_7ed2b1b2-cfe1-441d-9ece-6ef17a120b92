.free-graphics {
  width: 1200px;
  margin: 0 auto;
  padding: 64px 0;
  :global {
    .title {
      color: var(--system-content-primary, #fff);
      text-align: center;

      /* title_36_bold */
       font-family: var(--font-poppins);
      font-size: 36px;
      font-style: normal;
      font-weight: 600;
      line-height: 130%; /* 46.8px */
      margin-bottom: 40px;
    }
    .column-container {
      overflow: hidden;
      position: relative;
      .card {
        &:nth-child(1) {
          width: 728px;
          margin-bottom: 32px;
        }
        &:nth-child(2) {
          width: 728px;
        }
        &:nth-child(3) {
          width: 440px;
          height: 712px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: absolute;
          top: 0;
          right: 0;
          .card-content {
            height: 274px;
            padding: 40px;
            padding-bottom: 56px;
            .card-description {
              width: 100%;
            }
          }
          .card-image-container {
            width: 440px;
            height: 440px;
          }
        }
      }
    }

    .row-container {
      display: flex;
      gap: 32px;
      flex-wrap: wrap;
      justify-content: center;
    }
    .card {
      width: 582px;
      height: 338px;
      border-radius: var(--radius-16, 16px);
      border: 1px solid var(--system-stroke-input-default, #22272e);
      background: var(--system-background-secondary, #1d1e23);
      display: flex;
      .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-left: 40px;
        padding-top: 40px;
        padding-bottom: 40px;
        box-sizing: border-box;
        .card-title {
          color: var(--whee-neutral-0, #fff);
          /* Title/title-m/bold */
           font-family: var(--font-poppins);
          font-size: 24px;
          font-style: normal;
          font-weight: 700;
          line-height: 32px; /* 133.333% */
          margin-bottom: 16px;
        }
        .card-description {
          width: 100%;
          overflow: hidden;
          color: var(--system-content-secondary, #a3aebf);
          display: -webkit-box;
          flex: 1;
          min-height: 0;
          mask: linear-gradient(180deg, #000 69.23%, rgba(0, 0, 0, 0.00) 100%);
          overflow-y: auto;
          /* Text/text-lg/regular */
           font-family: var(--font-poppins);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 150% */
          scrollbar-width: none;
          &::-webkit-scrollbar {
            display: none;
          }
        }
      }
      .card-image-container {
        width: 340px;
        height: 340px;
        position: relative;
        img {
          object-fit: cover;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .free-graphics {
    width: 682px;
    padding: 64px 16px;
    :global {
      .column-container, .row-container {
        .card {
          .card-content {
            .card-description {
              width: 100%;
            }
          }
          &:nth-child(1) {
            width: 100%;
          }
          &:nth-child(2) {
            width: 100%;
            margin-bottom: 32px;
          }
          &:nth-child(3) {
            position: static;
            width: 100%;
            height: 338px;
            flex-direction: row;
            .card-content {
              padding: 24px;
              padding-left: 40px;
              padding-right: 40px;
             
            }
            .card-image-container {
              width: 340px;
              height: 340px;
            }
          }
        }
      }
      .row-container {
        width: 100%;
        .card {
          width: 100%;
          &:nth-child(2) {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .free-graphics {
    width: 100%;
    padding: 64px 16px;
    box-sizing: border-box;
    :global {
      .column-container, .row-container {
        .card {
          height: auto !important;
          flex-direction: column !important;
          .card-content {
            text-align: center;
            padding: 24px;
            padding-left: 40px;
            padding-right: 40px;
            .card-description {
              width: 100%;
            }
          }
          &:nth-child(3) {
            .card-image-container {
              width: 100%;
              aspect-ratio: 1/1;
              height: auto;
            }
          }
          .card-image-container {
            width: 100%;
            aspect-ratio: 1/1;
            height: auto;
          }
        }
      }
    }
  }
}
