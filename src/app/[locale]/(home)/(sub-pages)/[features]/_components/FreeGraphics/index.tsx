import styles from "./index.module.scss";
import Image from "next/image";
import material from "@/assets/images/home/<USER>/1.png";
import { ChildrenItem, PageModule } from "@/api/types/feature";
const freeGraphicsList = [
  {
    title: "Marketing Campaigns",
    description: "Enhance video quality to captivate and retain audience attention. Engaging visuals increase interest in products/services. Optimize videos with a quality enhancer for maximum impact, boosting engagement and conversions.",
    image: material,
  },
  {
    title: "Marketing Campaigns",
    description: "Enhance video quality to captivate and retain audience attention. Engaging visuals increase interest in products/services. Optimize videos with a quality enhancer for maximum impact, boosting engagement and conversions.",
    image: material,
  },
  {
    title: "Marketing Campaigns",
    description: "Enhance video quality to captivate and retain audvideo quality to captivate and retain audvideo quality to captivate and retain aud video quality to captivate and retain audience attention. Engaging visuals increase interest in products/services. Optimize videos with a quality enhancer for maximum impact, boosting engagement and conversions.",
    image: material,
  },
  {
    title: "Marketing Campaigns",
    description: "Enhance video quality to captivate and retain audience attention. Engaging visuals increase interest in products/services. Optimize videos with a quality enhancer for maximum impact, boosting engagement and conversions.",
    image: material,
  },
];

const Card = (item: ChildrenItem & { index: number }) => {
  return (
    <div className={'card'} key={item.index}>
      <div className="card-content">
        <h3 className="card-title">{item.title}</h3>
        <p className="card-description">{item.content}</p>
      </div>
      <div className="card-image-container">
        <Image unoptimized  src={item?.images?.[0] ?? ""} alt={item.title} fill  />
      </div>
    </div>
  )
}

const FreeGraphics = (props: PageModule) => {
  const { title, children } = props;
  return (
    <section className={styles["free-graphics"]}>
      {title && <h2 className="title">{title}</h2>}
      {
        children?.length === 3 ? (
          <div className="column-container">
            {children?.map((item, index) => (
              <Card key={item.title} {...item} index={index}/>
            ))}
          </div>
        ) : (
          <div className={"row-container"}>
            {children?.map((item, index) => (
              <Card key={item.title} {...item} index={index} />
            ))}
          </div>
        )
      }
    </section>
  );
};

export default FreeGraphics;
