.guide {
  width: 1200px;
  margin: 0 auto;
  padding: 64px 0;
  :global {
    .title {
      color: var(--system-content-primary, #FFF);
       font-family: var(--font-poppins);
      font-size: 36px;
      font-style: normal;
      font-weight: 600;
      line-height: 130%; /* 46.8px */
      text-align: center;
      margin-bottom: 40px;
    }
    .guide-container {
      display: flex;
      gap: 32px;
      .item {
        width: 378px;
        height: 553px;
        border-radius: var(--radius-16, 16px);
        border: 1px solid var(--system-stroke-input-default, #22272E);
        background: var(--system-background-secondary, #1D1E23);
        box-sizing: border-box;
        overflow: hidden;
        .item-content {
          padding: 40px;
          padding-bottom: 47px;
          .item-step {
            display: inline-block;
            color: var(--system-content-secondary, #A3AEBF);
            font-family: Raleway;
            font-size: 20px;
            font-style: italic;
            font-weight: 500;
            line-height: normal;
            margin-bottom: 16px;
            position: relative;
            box-sizing: border-box;
            &::after {
              content: '';
              display: block;
              width: 225px;
              height: 1px;
              background: var(--system-content-thirdary, #6B7A8F);
              position: absolute;
              top: 50%;
              left: 56px + 16px;
              transform: translate(0, -50%);
            }
          }
          .item-title {
            color: var(--system-content-primary, #FFF);
            font-size: 24px;
            font-style: normal;
            font-weight: 500;
            line-height: 130%; /* 31.2px */
            margin-bottom: 16px;
          }
          .item-description {
            color: var(--system-content-secondary, #A3AEBF);
             font-family: var(--font-poppins);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 4;
            overflow: hidden;
          }
        }
      }
    }
    .guide-container-no-img {
      .item {
        border: none;
        background: none;
        height: auto;
        .item-content {
          padding: 0;
        }
      }
    }
  }
}
@media (max-width: 1200px) {
  .guide {
    width: 682px;
    padding: 40px 64px;
    :global {
      .title {
        text-align: left;
      }
      .guide-container {
        flex-direction: column;
        .item {
          width: 100%;
          height: auto;
          .item-image {
            width: 100%;
            height: auto;
            object-fit: cover;
          }
        }
      }
    }
  }
}
@media (max-width: 768px) {
  .guide {
    width: 100%;
    padding: 40px 16px;
    box-sizing: border-box;
  }
}
