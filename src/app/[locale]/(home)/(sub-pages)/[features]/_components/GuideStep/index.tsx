"use client";
import styles from "./index.module.scss";
import Image from "next/image";
import guide from "@/assets/images/home/<USER>";
import { useState } from "react";
import { list } from "postcss";
import classNames from "classnames";
import { ChildrenItem, PageModule } from "@/api/types/feature";
const guideList = [
  {
    title: "Instant Upload",
    description:
      "Crisp visuals, vibrant colors, and sharp details will leave a lasting impression on your audience, making your brand appear more credible and trustworthy. ",
    image: guide,
  },
  {
    title: "Instant Upload",
    description:
      "Crisp visuals, vibrant colors, and sharp details will leave a lasting impression on your audience, making your brand appear more credible and trustworthy. ",
    image: guide,
  },
  {
    title: "Instant Upload",
    description:
      "Crisp visuals, vibrant colors, and sharp details will leave a lasting impression on your audience, making your brand appear more credible and trustworthy. ",
    image: guide,
  },
];
const Guide = (props: PageModule) => {
  const { title, children } = props;
  const isNoImg = children?.every((item: ChildrenItem) => item?.images?.[0] == null);
  return (
    <section className={styles.guide}>
      {title && <h2 className={"title"}>
        {title}
      </h2>}
      <div
        className={classNames("guide-container", {
          "guide-container-no-img": isNoImg,
        })}
      >
        {children?.map((item: ChildrenItem, index) => (
          <div className={"item"} key={index}>
            <div className={"item-content"}>
              <div className="item-step">Step{index + 1}</div>
              <h3 className="item-title">{item.title}</h3>
              <p className="item-description">{item.content}</p>
            </div>
            {item?.images?.[0] && (
              <Image
                className="item-image"
                unoptimized
                src={item?.images?.[0] ?? ""}
                alt={item.title}
                width={378}
                height={284}
              />
            )}
          </div>
        ))}
      </div>
    </section>
  );
};

export default Guide;
