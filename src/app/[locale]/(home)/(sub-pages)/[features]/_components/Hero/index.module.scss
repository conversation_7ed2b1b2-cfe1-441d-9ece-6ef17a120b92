.hero {
  width: 1200px;
  margin: 0 auto;
  padding: 80px 0;
  padding-top: 144px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 80px;
  box-sizing: border-box;
  :global {
    .hero-left {
      max-width: 480px;
      .title {
        color: var(--system-content-primary, #FFF);
        /* Title/Large */
         font-family: var(--font-poppins);
        font-size: 40px;
        font-style: normal;
        font-weight: 700;
        line-height: 120%; /* 48px */
        margin-bottom: 16px;
      }
      .description {
        color: var(--system-content-secondary, #A3AEBF);
        /* Text-Large/Regular */
         font-family: var(--font-poppins);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 137.5% */
        margin-bottom: 32px;
      }
      .btn-note {
        color: var(--system-content-secondary, #A3AEBF);
        /* text_14 */
         font-family: var(--font-poppins);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 130%; /* 18.2px */
        margin-top: 24px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .hero {
    width: 682px;
    padding: 66px 16px 16px;
    flex-direction: column;
    gap: 32px;
    :global {
      .hero-left {
        max-width: 100%;
        order: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 16px;
        box-sizing: border-box;
        .title {
          text-align: center;
        }
        .description {
          text-align: center;
        }
        
      }
      .hero-right {
        order: 1;
      }
      
    }
  }
}

@media (max-width: 682px) {
  .hero {
    width: 100%;
    margin: 0;
  }
}
