'use client'
import styles from "./index.module.scss";
import Image from "next/image";
import CreateButton from "../CreateButton";
import { ModuleType, PageModule } from "@/api/types/feature";
import { trackEvent } from "@/services/tracer";
import { usePathname } from "next/navigation";
const Hero = (params: PageModule) => {
  const { title, content, pageKeyword } = params;
  const pathname = usePathname();
  return (
    <section className={styles.hero}>
      <div className={"hero-left"}>
        {title && <h1 className="title">
          {title}
        </h1>}
        {content && <p className="description">
        {content}
        </p>}
        <div className={"hero-left-btn"}>
          <CreateButton type="primary" onClick={() => {
            if (pathname.includes("tutorial")) {
              const tutorialId = sessionStorage.getItem('tutorialId');
              const tutorialName = sessionStorage.getItem('tutorialName');
              trackEvent("tutorial_detail_click", {
                tutorial_id: tutorialId,
                tutorial_name: tutorialName,
                click_type: 'get_started'
              });
            } else {
              trackEvent("landing_page_click", {
                module_id: ModuleType.Hero,
                url: location.href,
                keyword: pageKeyword,
              });
            }
          }}>Get Started</CreateButton>
        </div>
        <p className="btn-note">
          Start by clicking on a template below
        </p>
      </div>
      <div className={"hero-right"}>
        <Image unoptimized src={params.images?.[0] ?? ''} alt="material" width={470} height={418} />
      </div>
    </section>
  );
};

export default Hero;
