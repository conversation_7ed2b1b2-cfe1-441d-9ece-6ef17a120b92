.hero-full-screen {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  :global {
    .hero-full-screen-title {
      color: var(--system-content-primary, #FFF);
      text-align: center;

      /* title_40_bold */
       font-family: var(--font-poppins);
      font-size: 48px;
      font-style: normal;
      font-weight: 600;
      line-height: 130%; /* 62.4px */
    }
    .hero-full-screen-description {
      color: var(--system-content-primary, #FFF);
      text-align: center;
      
      /* text_16 */
       font-family: var(--font-poppins);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 130%; /* 20.8px */
      margin: 24px 0;
    }
  }
}

@media (max-width: 1200px) {
  .hero-full-screen {
    padding: 0 40px;
    box-sizing: border-box;
  }
}