'use client'
import styles from './index.module.scss';
import CreateButton from '../CreateButton';
import { PageModule } from '@/api/types/feature';
import { trackEvent } from '@/services/tracer';
import { ModuleType } from '@/api/types/feature';
import { usePathname } from 'next/navigation';
const HeroFullScreen = (props: PageModule) => {
  const { title, content, images, pageKeyword } = props;
  const pathname = usePathname();
  const style = images?.[0] ? {
    backgroundImage: `url(${images?.[0]})`
  } : {};
  return <section className={styles['hero-full-screen']} style={style}>
    {title && <h1 className={'hero-full-screen-title'}>
     {title}
    </h1>}
    {content && <p className={'hero-full-screen-description'}>
    {content}
    </p>}
    <CreateButton type={'primary'} onClick={() => {
      if (pathname.includes("tutorial")) {
        const tutorialId = sessionStorage.getItem('tutorialId');
        const tutorialName = sessionStorage.getItem('tutorialName');
        trackEvent("tutorial_detail_click", {
          tutorial_id: tutorialId,
          tutorial_name: tutorialName,
          click_type: 'get_started'
        });
      } else {
        trackEvent("landing_page_click", {
          module_id: ModuleType.HeroFullScreen,
          url: location.href,
          keyword: pageKeyword,
        });
      }
    }}>
      Get Started
    </CreateButton>
  </section>;
};

export default HeroFullScreen;
