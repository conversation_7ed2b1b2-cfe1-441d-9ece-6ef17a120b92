.feature {
  width: 1200px;
  margin: 0 auto;
  padding: 80px 0;
  box-sizing: border-box;
  :global {
    .feature-title {
      color: var(--system-content-primary, #fff);
      text-align: center;
      /* Title/Large */
       font-family: var(--font-poppins);
      font-size: 40px;
      font-style: normal;
      font-weight: 700;
      line-height: 120%; /* 48px */
      margin-bottom: 16px;
      text-align: center;
    }
    .feature-description {
      color: var(--system-content-secondary, #a3aebf);
      text-align: center;
      /* text_16 */
       font-family: var(--font-poppins);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 130%; /* 20.8px */
      text-align: center;
    }
    .feature-list {
      .feature-item {
        display: flex;
        flex-direction: row;
        gap: 40px;
        margin-top: 40px;
        align-items: center;
        justify-content: center;
        .feature-item-image {
          border-radius: var(--radius-16, 16px);
          border: 1px solid var(--system-stroke-input-default, #22272e);
          background: var(--system-background-secondary, #1d1e23);
        }
        .feature-item-content {
          max-width: 480px;
          .feature-item-title {
            color: var(--system-content-primary, #fff);
            /* Title/Small */
             font-family: var(--font-poppins);
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 133.333% */
            margin-bottom: 16px;
          }
          .feature-item-description {
            color: var(--system-content-secondary, #a3aebf);
             font-family: var(--font-poppins);
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 130%; /* 20.8px */
            margin-bottom: 43px;
          }
        }
      }
    }
  }
}
@media (max-width: 1200px) {
  .feature {
    width: 682px;
    padding: 80px 16px;
    :global {
      .feature-list {
        .feature-item {
          flex-direction: column;
          .feature-item-image {
            order: 1;
            // width: 100%;
            // height: 274px;
          }
          .feature-item-content {
            order: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 100%;

            .feature-item-title {
              font-size: 20px;
            }
            .feature-item-description {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
@media (max-width: 682px) {
  .feature {
    width: 100%;
    margin: 0;
  }
}
