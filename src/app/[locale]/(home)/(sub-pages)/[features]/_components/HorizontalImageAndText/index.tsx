'use client'
import CreateButton from "../CreateButton";
import styles from "./index.module.scss";
import Image from "next/image";
import { ChildrenItem, ModuleType, PageModule } from "@/api/types/feature";
import { usePathname } from "next/navigation";
import { trackEvent } from "@/services/tracer";
const FeatureContent = ({ item, pageKeyword }: { item: ChildrenItem, pageKeyword?: string }) => {
  const pathname = usePathname();

  return (
    <div className="feature-item-content">
      <h3 className="feature-item-title">{item.title}</h3>
      <p className="feature-item-description">{item.content}</p>
      {!pathname.includes('tutorials') && <CreateButton onClick={() => {
        if (pathname.includes("tutorial")) {
          const tutorialId = sessionStorage.getItem('tutorialId');
          const tutorialName = sessionStorage.getItem('tutorialName');
          trackEvent("tutorial_detail_click", {
            tutorial_id: tutorialId,
            tutorial_name: tutorialName,
            click_type: 'get_started'
          });
        } else {
          trackEvent("landing_page_click", {
            module_id: ModuleType.HorizontalImageAndText,
            url: location.href,
            keyword: pageKeyword,
          });
        }
      }}>Get Started</CreateButton>}
    </div>
  );
};

const FeatureItem = ({ item, index, pageKeyword }: { item: ChildrenItem, index: number, pageKeyword?: string }) => {
  return (
    <div className="feature-item" key={item.title}>
      {index % 2 === 0 ? (
        <>
          <Image unoptimized className="feature-item-image" src={item?.images?.[0] ?? ""} alt={item.title} width={480} height={359} />
          <FeatureContent item={item} pageKeyword={pageKeyword}/>
        </>
      ) : (
        <>
          <FeatureContent item={item} pageKeyword={pageKeyword} />
          <Image unoptimized className="feature-item-image" src={item?.images?.[0] ?? ""} alt={item.title} width={480} height={359} />
        </>
        )}
    </div>
  );
};

const Feature = (props: PageModule) => {
  const { title, content, children, pageKeyword } = props;
  return (
    <section className={styles.feature}>
      {title && <h2 className="feature-title">{title}</h2>}
      {content && <p className="feature-description">
        {content}
      </p>}
      <div className="feature-list">
        {children?.map((item, index) => (
          <FeatureItem item={item} index={index} key={item.title} pageKeyword={pageKeyword} />
        ))}
      </div>
    </section>
  );
};

export default Feature;
