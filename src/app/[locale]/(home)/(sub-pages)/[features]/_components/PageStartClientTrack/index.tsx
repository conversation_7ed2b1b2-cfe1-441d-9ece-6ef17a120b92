'use client'
import { trackEvent } from '@/services/tracer';
import { mtstatReady } from '@/utils/mtstatReady';
import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { rootStore } from '@/stores/RootStore';
import { observer } from 'mobx-react-lite';

export const PageStartClientTrack = observer(({ keyword, url }: { keyword?: string, url?: string }) => {
  const pathname = usePathname();
  const { referrerStore } = rootStore;

  useEffect(() => {
    mtstatReady.then(() => {  
      let trackedReferrer = (referrerStore.previousPath ? window.location.origin + referrerStore.previousPath : document.referrer);

      if (pathname.includes('tutorial')) {
        trackEvent('tutorial_detail_expo', {
          tutorial_id: sessionStorage.getItem('tutorialId'),
          tutorial_name: sessionStorage.getItem('tutorialName'),
        });
      } else {
        trackEvent('page_enter', {
          keyword,
          url: location.href,
          referrer: trackedReferrer,
        });
      }
      // 不再在此处更新 referrerStore，由 GlobalRouteTracker 负责
    });
  }, [keyword, url, pathname, referrerStore]);

  return null;
});

export default PageStartClientTrack;