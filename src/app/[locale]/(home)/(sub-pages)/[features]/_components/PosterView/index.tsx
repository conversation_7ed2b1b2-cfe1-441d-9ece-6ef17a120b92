'use client'
import { PageModule } from "@/api/types/feature";
import styles from "./index.module.scss";
import useRouterPush from "../../_hooks/useRouterPush";
import TransformImage from "@/components/TransformImage";
import { trackEvent } from "@/services/tracer";
import { ModuleType } from "@/api/types/feature";
const PosterView = (props:PageModule) => {
  const { title, content, children, pageKeyword } = props;
  const routerPush = useRouterPush(children?.map(item => item?.url ?? ''));
  return (
    <section className={styles['poster-view']}>
      <div className="poster-view-content">
        {title && <h2 className="poster-view-title">{title}</h2>}
        {content && <p className="poster-view-description">{content}</p>}
      </div>
      <div className="poster-view-list-container">
        <div className="poster-view-list">
        {children?.map((item, index) => (
          <div className="poster-view-item" key={item.title} onClick={() => {
            routerPush(item.url)
            trackEvent("landing_page_click", {
              module_id: ModuleType.PosterView,
              url: location.href,
              keyword: pageKeyword,
            });
          }}>
            <div className="poster-view-item-image-container">
              <TransformImage  src={item?.images?.[0] ?? ""} alt={item.title} width={282} height={376} />
            </div>
            <h3 className="poster-view-item-title">{item.title}</h3>
            <p className="poster-view-item-description">{item.content}</p>
          </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PosterView;