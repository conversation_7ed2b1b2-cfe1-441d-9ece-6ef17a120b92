.review {
  padding: 80px 0;
  width: 1200px;
  margin: 0 auto;
  :global {
    .review-title {
      color: var(--Font-strong, #fff);
       font-family: var(--font-poppins);
      font-size: 40px;
      font-style: normal;
      font-weight: 700;
      line-height: 120%; /* 48px */
      margin-bottom: 16px;
      text-align: center;
    }
    .review-description {
      color: var(--Font-secondary, rgba(255, 255, 255, 0.5));
      text-align: center;
       font-family: var(--font-poppins);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 137.5% */
      margin-bottom: 40px;
    }
    .review-list {
      display: grid;
      gap: 24px;
      grid-template-columns: repeat(3, 1fr);
      .review-item {
        width: 384px;
        height: 202px;
        border-radius: var(--radius-16, 16px);
        border: 1px solid var(--system-stroke-input-default, #22272e);
        background: var(--system-background-secondary, #1d1e23);
        padding: 24px;
        box-sizing: border-box;
        .review-item-content {
          height: 66px;
          overflow: hidden;
          color: var(--Font-strong, #fff);
          text-overflow: ellipsis;
           font-family: var(--font-poppins);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 137.5% */
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          margin-bottom: 40px;
        }
        .review-item-footer {
          display: flex;
          align-items: center;
          .review-item-footer-avatar {
            border-radius: 48px;
            background: var(--system-content-fifth, #303741);
            margin-right: 12px;
            position: relative;
            width: 48px;
            height: 48px;
            overflow: hidden;
            .avatar-image {
              object-fit: cover;
            }
          }
          .review-item-footer-right {
            .review-item-footer-right-name {
              overflow: hidden;
              color: var(--system-content-primary, #fff);
              text-overflow: ellipsis;
              /* Text-Large/Semibold */
               font-family: var(--font-poppins);
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 22px; /* 137.5% */
              margin-bottom: 4px;
            }
            .review-item-footer-right-description {
              overflow: hidden;
              color: var(--system-content-secondary, #a3aebf);
              text-overflow: ellipsis;
              /* Text-Small/Regular */
               font-family: var(--font-poppins);
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 16px; /* 133.333% */
            }
          }
        }
      }
    }
  }
}
@media (max-width: 1200px) {
  .review {
    width: 682px;
    padding: 80px 64px;
    :global {
      .review-list {
        grid-template-columns: repeat(2, 1fr);
        .review-item {
          width: 100%;
          height: auto;
        }
      }
    }
  }
}
@media (max-width: 768px) {
  .review {
    width: 100%;
    padding: 80px 16px;
    box-sizing: border-box;
    :global {
      .review-list {
        grid-template-columns: repeat(1, 1fr);
      }
    }
  }
}
