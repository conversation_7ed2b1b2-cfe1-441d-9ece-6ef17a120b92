import styles from "./index.module.scss";
import Image from "next/image";
import avatar from "@/assets/images/home/<USER>/1.png";
const reviewList = [
  {
    id: 1,
    name: "<PERSON> Simmons",
    description: "Etsy Business Owner",
    content: "This tool has compl  This tool has complThis tool has compl  This tool has compl  This tool has complThis tool has completely transformed my store! I can create a clean, professional look without spending hours editing.",
  },
  {
    id: 2,
    name: "<PERSON> Simmons",
    description: "Etsy Business Owner",
    content: "This tool has compl  This tool has complThis tool has compl  This tool has compl  This tool has complThis tool has completely transformed my store! I can create a clean, professional look without spending hours editing.",
  },
  {
    id: 3,
    name: "<PERSON> Simmons",
    description: "Etsy Business Owner",
    content: "This tool has compl  This tool has complThis tool has compl  This tool has compl  This tool has complThis tool has completely transformed my store! I can create a clean, professional look without spending hours editing.",
  },
  {
    id: 4,
    name: "<PERSON>",
    description: "Etsy Business Owner",
    content: "This tool has compl  This tool has complThis tool has compl  This tool has compl  This tool has complThis tool has completely transformed my store! I can create a clean, professional look without spending hours editing.",
  },
  {
    id: 5,
    name: "<PERSON> Simmons",
    description: "Etsy Business Owner",
    content: "This tool has compl  This tool has complThis tool has compl  This tool has compl  This tool has complThis tool has completely transformed my store! I can create a clean, professional look without spending hours editing.",
  },
  {
    id: 6,
    name: "Brooklyn Simmons",
    description: "Etsy Business Owner",
    content: "This tool has compl  This tool has complThis tool has compl  This tool has compl  This tool has complThis tool has completely transformed my store! I can create a clean, professional look without spending hours editing.",
  },
];
const Review = () => {
  return (
    <section className={styles.review}>
      <h2 className="review-title">Customer Reviews</h2>
      <p className="review-description">
        We have received numerous positive reviews from our users, and we are
        proud to share some of their experiences with you.
      </p>
      <div className="review-list">
        {reviewList.map((item) => (
          <div className="review-item" key={item.id}>
            <div className="review-item-content">
              {item.content}
            </div>
            <div className="review-item-footer">
              <div className="review-item-footer-avatar">
                <Image className="avatar-image" unoptimized src={avatar} alt="avatar" fill />
              </div>
              <div className="review-item-footer-right">
                <div className="review-item-footer-right-name"> 
                  {item.name}
                </div>
                <div className="review-item-footer-right-description">
                  {item.description}
                </div>
              </div>
            </div>  
          </div>
        ))}
       
      </div>
    </section>
  );
};

export default Review;
