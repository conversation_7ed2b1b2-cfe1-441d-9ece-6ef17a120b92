.templates {
  width: 1200px;
  margin: 0 auto;
  padding: 80px 100px;
  box-sizing: border-box;
  position: relative;
  .templates-title {
    color: var(--system-content-primary, #fff);
    /* title_36_bold */
     font-family: var(--font-poppins);
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: 130%; /* 46.8px */
    margin-bottom: 16px;
    text-align: center;
  }
  .templates-description {
    color: var(--system-content-secondary, #a3aebf);
    text-align: center;
     font-family: var(--font-poppins);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 137.5% */
    margin-bottom: 40px;
    text-align: center;
  }
  .tab-container {
    width: 100%;
    .tab-header {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      margin-bottom: 16px;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      .header-item {
        flex-shrink: 0;
        color: var(--system-content-secondary, #a3aebf);
        text-align: center;
        /* text_14 */
         font-family: var(--font-poppins);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 130%; /* 18.2px */
        padding: 8px 16px;
        border-radius: var(--radius-8, 8px);
        border: 1px solid var(--system-stroke-input-default, #22272e);
        background: var(--system-background-secondary, #1d1e23);
        cursor: pointer;
      }
      .header-item-active {
        color: var(--system-content-primary, #fff);
        background: var(--system-background-thirdary, #272c33);
      }
    }
  }
  .tab-content-wrapper {
    display: none;
    width: 100%;
    height: 752px;
    overflow: hidden;
    .tab-content {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 16px;
      .content-item {
        width: 100%;
        aspect-ratio: 3 / 4;
        border-radius: var(--radius-8, 8px);
        border: 1px solid var(--system-stroke-input-default, #22272e);
        background: linear-gradient(
            0deg,
            rgba(0, 240, 255, 0.2) 0%,
            rgba(0, 240, 255, 0.2) 100%
          ),
          var(--system-background-secondary, #1d1e23);
        position: relative;
        overflow: hidden;
        cursor: pointer;
        img {
          border-radius: var(--radius-8, 8px);
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
  .tab-content-wrapper-active {
    display: block;
  }
  .mask {
    position: absolute;
    bottom: 80px;
    left: 0;
    width: 100%;
    height: 185px;
    background: linear-gradient(0deg, #000 0%, rgba(0, 0, 0, 0.00) 93.82%);
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .more-btn {
     margin-top: 80px;
    }
  }
}

@media (max-width: 1200px) {
  .templates {
    width: 100%;
    .tab-content-wrapper {
      .tab-content {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  }
}
@media (max-width: 768px) {
  .templates {
    padding: 40px 0;
    .tab-content-wrapper {
      .tab-content {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    .mask {
      bottom: 40px;
    }
  }
}
