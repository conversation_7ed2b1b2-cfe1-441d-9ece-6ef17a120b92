import styles from "./index.module.scss";
import { getTemplates } from "@/api/servers/features";
import TemplateFeed from "./templateFeed";
const Templates = async (props: {
  title: string;
  content?: string;
  pageKeyword?: string;
}) => {
  const templates = await getTemplates({});
  const { title, content, pageKeyword} = props;
  return (
    <section className={styles["templates"]}>
      {title && <h2 className={styles["templates-title"]}>{title}</h2>}
      {content && <p className={styles["templates-description"]}>{content}</p>}
      <TemplateFeed
        templates={templates}
        pageKeyword={pageKeyword}
      />
    </section>
  );
};

export default Templates;
