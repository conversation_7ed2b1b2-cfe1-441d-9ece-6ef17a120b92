"use client";
import styles from "./index.module.scss";
import classNames from "classnames";
import {  useEffect, useState } from "react";
import { TemplateListType, TemplateItemType } from "@/api/types/poster";
import TransformImage from "@/components/TransformImage";
import CreateButton from "../CreateButton";
import useAddTemplateToEditor from "@/hooks/useAddTemplateToEditor";
import { useCreateProject } from "@/hooks/useCreateProject";
import { useStore } from "@/contexts/StoreContext";
import { useRouter, usePathname } from "next/navigation";
import { observer } from "mobx-react-lite";
import { toAtlasImageView2URL } from "@meitu/util";
import { trackEvent } from "@/services/tracer";
import { ModuleType } from "@/api/types/feature";
const TemplateFeed = (props: {
  templates: TemplateListType[];
  pageKeyword?: string;
}) => {
  const { templates, pageKeyword } = props;
  const [activeTemplate, setActiveTemplate] = useState<TemplateListType>(
    templates[0]
  );
  const { addTemplateToEditor } = useAddTemplateToEditor();
  const { userStore, globalProjectStore } = useStore();
  const { createProject } = useCreateProject({
    globalProjectStore,
    userStore,
  });
  const router = useRouter();
  const pathname = usePathname();

  const handleClick = async (item: TemplateItemType) => {
     //因为此模块共用了教程页面 埋点分开报
     if (pathname.includes("tutorial")) {
      const tutorialId = sessionStorage.getItem('tutorialId');
      const tutorialName = sessionStorage.getItem('tutorialName');
      trackEvent("tutorial_detail_click", {
        tutorial_id: tutorialId,
        tutorial_name: tutorialName,
        click_type: 'use_template'
      });
    } else {
      trackEvent("landing_page_click", {
        module_id: ModuleType.Template,
        url: location.href,
        keyword: pageKeyword,
      });
    }
    try {
      addTemplateToEditor(item);
    } catch (error) {
      console.error(error);
    }
  };


  const handleMoreClick = async () => {
    //因为此模块共用了教程页面 埋点分开报
    if (pathname.includes("tutorial")) {
      const tutorialId = sessionStorage.getItem('tutorialId');
      const tutorialName = sessionStorage.getItem('tutorialName');
      trackEvent("tutorial_detail_click", {
        tutorial_id: tutorialId,
        tutorial_name: tutorialName,
        click_type: 'discover_more'
      });
    } else {
      trackEvent("landing_page_click", {
        module_id: ModuleType.Template,
        url: location.href,
        keyword: pageKeyword,
      });
    }
    try {
      const project = await createProject();
      if (!project) return;
      router.push(`/editor/project/${project.projectId}`);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className={styles["tab-container"]}>
      <div className={styles["tab-header"]}>
        {templates.map((template) => (
          <div
            key={template.categoryId}
            className={classNames(
              styles["header-item"],
              template.categoryId === activeTemplate.categoryId &&
                styles["header-item-active"]
            )}
            onClick={() => {
              setActiveTemplate(template);
            }}
          >
            {template.categoryName}
          </div>
        ))}
      </div>
      {templates.map((template) => (
        <div
          className={classNames(
            styles["tab-content-wrapper"],
            template.categoryId === activeTemplate.categoryId &&
              styles["tab-content-wrapper-active"]
          )}
          key={template.categoryId}
        >
          <div className={styles["tab-content"]}>
            {template.list.map((item) => (
              <div
                className={styles["content-item"]}
                key={item.id}
                onClick={() => handleClick(item)}
              >
                <TransformImage
                  src={toAtlasImageView2URL(item.pic, {
                    mode: 2 as any,
                    height: 360,
                  })}
                  alt={item.name}
                />
              </div>
            ))}
          </div>
        </div>
      ))}
      <div className={styles["mask"]}>
        <CreateButton className={styles["more-btn"]} onClick={handleMoreClick}>
          Discover more
        </CreateButton>
      </div>
    </div>
  );
};

export default observer(TemplateFeed);
