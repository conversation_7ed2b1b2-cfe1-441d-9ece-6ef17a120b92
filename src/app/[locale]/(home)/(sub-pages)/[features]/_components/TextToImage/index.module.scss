.text-to-image {
  width: 1200px;
  margin: 0 auto;
  padding: 80px 0;
  :global {
    .text-to-image-title {
      color: var(--system-content-primary, #fff);
      text-align: center;
       font-family: var(--font-poppins);
      font-size: 36px;
      font-style: normal;
      font-weight: 600;
      line-height: 130%; /* 46.8px */
      margin-bottom: 16px;
    }
    .text-to-image-description {
      color: var(--system-content-secondary, #a3aebf);
      text-align: center;
       font-family: var(--font-poppins);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 137.5% */
    }
    .text-swiper-tab {
      margin: 40px 0;
      display: flex;
      justify-content: space-between;
      border-radius: var(--radius-16, 16px);
      border: 1px solid #30333c;
      background: #16171a;
      padding: 8px;
      box-sizing: border-box;
      .text-swiper-tab-item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 166px;
        padding: 10px 20px;
        color: #a3aebf;
         font-family: var(--font-poppins);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 130%; /* 20.8px */
        text-align: center;
        cursor: pointer;
      }
      .text-swiper-tab-item-active {
        border-radius: var(--radius-12, 12px);
        background: #fff;
        color: #000;
        font-weight: 600;
      }
    }
  }
}
.card-item {
  display: flex;
  align-items: center;
  :global {
    .card-item-left {
      display: flex;
      flex-direction: column;
      justify-content:center;
      align-items: center;
      width: 544px;
      height: 544px;
      border-radius: var(--radius-16, 16px);
      border: 1px solid var(--system-stroke-input-default, #22272e);
      background: var(--system-background-secondary, #1d1e23);
      padding: 20px;
      box-sizing: border-box;
      .card-item-textarea {
        padding: 26px;
        height: 169px;
        align-items: center;
        border-radius: var(--radius-24, 24px);
        border: 1.5px solid rgba(99, 106, 116, 0.5);
        background: var(--system-background-input, #16171c);
        box-shadow: 0px 22px 28.6px 0px rgba(0, 0, 0, 0.2);
        box-sizing: border-box;
        color: var(--system-content-primary, #fff);
         font-family: var(--font-poppins);
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 130%; /* 31.2px */
        margin-bottom: 48px;
      }
      .card-item-button-container {
        width: 416px;
        height: 81px;
        display: flex;
        align-items: center;
        position: relative;
        gap: 8px;
        .mask {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
             rgba(0, 0, 0, 0) 68.1%,
             #1d1e23 98.96%
          );
        }
        .button {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 11px 12px;
          box-sizing: border-box;
          color: var(--system-content-secondary, #a3aebf);
           font-family: var(--font-poppins);
          font-size: 22px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px; /* 72.727% */
          border-radius: var(--radius-16, 16px);
          border: 1px solid var(--system-stroke-button, #323b48);
          background: var(--system-background-thirdary, #272c33);
          gap: 2px;
        }
      }
    }
    .icon-container {
      width: 64px;
      height: 64px;
      border-radius: 999px;
      background: #1d1e23;
      /* level_2 */
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
        0px 6px 24px 0px rgba(0, 0, 0, 0.06);
      margin: 0 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      // cursor: pointer;
    }
    .card-item-right {
      flex: 1;
      height: 544px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 12px;
    }
    .card-item-right-image-container {
      border-radius: var(--radius-16, 16px);
      border: 1px solid var(--system-stroke-input-default, #22272e);
      background: var(--system-background-secondary, #1d1e23);
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      .card-item-right-image {
        object-fit: cover;
      }
    }
   
  }
}
@media (max-width: 1200px) {
  .text-to-image {
    width: 682px;
    padding: 40px 64px;
  }
  .card-item {
    flex-direction: column;
    align-items: center;
    gap: 24px;
    :global {
      .card-item-left {
        width: 100%;
        height: auto;
        aspect-ratio: 1/1;
        box-sizing: border-box;
        overflow: hidden;
      }
      .icon-container {
        transform: rotate(90deg);
        background-color: transparent;
      }
      .card-item-right {
        width: 100%;
        aspect-ratio: 1/1;
        min-height: 0px;
      }
    }
  }
}
@media (max-width: 768px) {
  .text-to-image {
    width: 100%;
    padding: 40px 16px;
    box-sizing: border-box;
    :global {
      .text-swiper-tab {
        .text-swiper-tab-item {
          width: auto;
          padding: 10px 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .card-item  {
    :global {
     .card-item-left {
       width: 100%;
       height: auto;
     }
    }
     
   }
}