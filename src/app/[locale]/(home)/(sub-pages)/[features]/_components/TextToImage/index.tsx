"use client";
import styles from "./index.module.scss";
import material1 from "@/assets/images/home/<USER>/1.png";
import classNames from "classnames";
import { useEffect, useState } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { type CarouselApi } from "@/components/ui/carousel";
import { ChevronRight } from "lucide-react";
import Image, { StaticImageData } from "next/image";
import { AiSparkleBold } from "@meitu/candy-icons";
import { ChildrenItem, ModuleType, PageModule } from "@/api/types/feature";
import useAddTemplateToEditor from "@/hooks/useAddTemplateToEditor";
import { trackEvent } from "@/services/tracer";
const list = [
  {
    title: "Retail",
    imageList: [material1, material1, material1, material1],
    description: "Holiday shopping stants here",
  },
  {
    title: "School",
    description: "Holiday shopping stants here",
    imageList: [material1, material1, material1, material1],
  },
  {
    title: "Weeding",
    description: "Holiday shopping stants here",
    imageList: [material1, material1, material1, material1],
  },
  {
    title: "Concert venue",
    description: "Holiday shopping stants here",
    imageList: [material1, material1, material1, material1],
  },
];
const CardItem = ({ title, images, template, content, pageKeyword }: ChildrenItem & { pageKeyword?: string }) => {
  const { addTemplateToEditor } = useAddTemplateToEditor();
 
  const handleClick = async () => {
   try {
    trackEvent("landing_page_click", {
      module_id: ModuleType.TextToImage,
      url: location.href,
      keyword: pageKeyword,
    });
    if (!template) return
    addTemplateToEditor(template);
   } catch (error) {
    console.error(error);
   } 
  }
  return (
    <div className={styles['card-item']}>
      <div className="card-item-left">
        <div className="card-item-textarea">
          {content}
        </div>
        <div className="card-item-button-container">
         <div className="mask"></div>
         <div className="button">
          <AiSparkleBold className="w-6 h-6" />
            Associate
         </div>
         <div className="button">
          <AiSparkleBold className="w-6 h-6" />
          Lexicon
         </div>
         <div className="button">
          <AiSparkleBold className="w-6 h-6" />
          More
         </div>
        </div>
      </div>
      <div className="icon-container" onClick={handleClick}>
        <ChevronRight className='w-7 h-7'/>
      </div>
      <div className="card-item-right">
        {images?.map((item, index) => (
          <div className="card-item-right-image-container" key={index} >
            <Image unoptimized className="card-item-right-image" src={item} alt={title}  fill />
          </div>
        ))}
      </div>
    </div>
  );
};
const TextToImage = (props: PageModule) => {
  const { title, content, children, pageKeyword } = props;
  const [activeTab, setActiveTab] = useState(0);
  const [api, setApi] = useState<CarouselApi>();

  useEffect(() => {
    if (!api) {
      return
    }
 
 
    api.on("select", () => {
      setActiveTab(api.selectedScrollSnap())
    })
  }, [api])

  return (
    <section className={styles["text-to-image"]}>
      {title && <h2 className={"text-to-image-title"}>{title}</h2>}
      {content && <p className={"text-to-image-description"}>
        {content}
      </p>}
      <div className="text-swiper-tab">
        {children?.map((item, index) => (
          <div
            className={classNames("text-swiper-tab-item", {
              "text-swiper-tab-item-active": activeTab === index,
            })}
            key={item.title}
            onClick={() => {
              setActiveTab(index);
              api?.scrollTo(index);
            }}
          >
            {item.title}
          </div>
        ))}
      </div>
      <Carousel setApi={setApi}>
        <CarouselContent>
          {children?.map((item) => (
            <CarouselItem key={item.title} className="box-border">
              <CardItem {...item} pageKeyword={pageKeyword} />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </section>
  );
};

export default TextToImage;
