.tool-compare {
  width: 1200px;
  margin: 0 auto;
  padding: 80px 0;
  :global {
    .tool-compare-title {
      color: var(--system-content-primary, #fff);
      text-align: center;
      /* title_36_bold */
       font-family: var(--font-poppins);
      font-size: 36px;
      font-style: normal;
      font-weight: 600;
      line-height: 130%; /* 46.8px */
      margin-bottom: 16px;
    }
    .tool-compare-description {
      color: var(--system-content-secondary, #a3aebf);
      text-align: center;
      /* Text-Large/Regular */
       font-family: var(--font-poppins);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 137.5% */
    }
    .tool-compare-list {
      display: flex;
      gap: 24px;
      margin-top: 40px;
      .tool-compare-item {
        width: 378px;
        .tool-compare-item-image {
          border-radius: var(--radius-16, 16px);
          border: 1px solid var(--system-stroke-input-default, #22272e);
          background: var(--system-background-secondary, #1d1e23);
          margin-bottom: 16px;
        }
        .tool-compare-item-title {
          color: var(--system-content-primary, #fff);
           font-family: var(--font-poppins);
          font-size: 20px;
          font-style: normal;
          font-weight: 600;
          line-height: 30px; /* 150% */
          margin-bottom: 4px;
        }
        .tool-compare-item-description {
          color: var(--system-content-secondary, #a3aebf);
          /* Text-Base/Regular */
           font-family: var(--font-poppins);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 142.857% */
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .tool-compare {
    width: 682px;
    padding: 80px 16px;
    :global {
      .tool-compare-list {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .tool-compare {
    width: 100%;
    margin: 0;
    box-sizing: border-box;
    :global {
      .tool-compare-list-container {
        width: 100%;
        overflow-x: auto;
        scrollbar-width: none;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .tool-compare-list {
        width: 768px;
      }
    }
  }
}

