import { PageModule } from "@/api/types/feature";
import styles from "./index.module.scss";
import toolcompare from "@/assets/images/home/<USER>/pic.png";
import Image from "next/image";
const toolCompareList = [
  {
    title: "Event Promotion",
    description: "Generate realistic, high-quality AI backgrounds perfectly matched to your subject.",
    image: toolcompare,
  },
  {
    title: "Marketing Design",
    description: "Generate realistic, high-quality AI backgrounds perfectly matched to your subject.r.",
    image: toolcompare,
  },
  {
    title: "Social Media Content",
    description: "Generate realistic, high-quality AI backgrounds perfectly matched to your subject.",
    image: toolcompare,
  },
  
];
const ToolCompare  = (props: PageModule) => {
  const { title, content, children } = props;
  return (
    <section className={styles['tool-compare']}>
      <div className="tool-compare-content">
        {title && <h2 className="tool-compare-title">{title}</h2>}
        {content && <p className="tool-compare-description">{content}</p>}
      </div>
      <div className="tool-compare-list-container">
        <div className="tool-compare-list">
          {children?.map((item, index) => (
            <div className="tool-compare-item" key={item.title}>
              <Image unoptimized className="tool-compare-item-image" src={item?.images?.[0] ?? ""} alt={item.title} width={378} height={504} />
              <h3 className="tool-compare-item-title">{item.title}</h3>
              <p className="tool-compare-item-description">{item.content}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ToolCompare;