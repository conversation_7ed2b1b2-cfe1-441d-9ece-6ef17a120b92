'use client'
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const DOMAIN_REGEX = /^https?:\/\/(?:[\w-]+\.)*whee\.ai(?:\/|$)/;

//传递path 则预加载path 否则不预加载
const useRouterPush = (path?: string | string[]) => {
  const router = useRouter();

  useEffect(() => {
   if(path && Array.isArray(path)) {
    path.forEach(p => {
      if (DOMAIN_REGEX.test(p)) {
        router.prefetch(p);
      }
    });
   }
  }, [router, path]);
  
  const push = (path?: string) => {
    if (path) {
      if (DOMAIN_REGEX.test(path)) {
        router.push(path);
      } else {
        window.location.href = path;
      }
    }
  };

  return push;
};

export default useRouterPush;
