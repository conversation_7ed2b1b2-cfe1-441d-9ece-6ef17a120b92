import { notFound } from "next/navigation";
import { setStaticParamsLocale } from "next-international/server";
import { getStaticParams } from "@/locales/server";
import {
  CarouselFeature,
  ExploreMore,
  FAQ,
  FreeGraphics,
  Hero,
  HeroFullScreen,
  HorizontalImageAndText,
  PosterView,
  TextToImage,
  Templates,
  ToolCompare,
  GuideStep,
} from "@/app/[locale]/(home)/(sub-pages)/[features]/_components";
import {
  ChildrenItem,
  ModuleType,
  Page,
  PageModule,
} from "@/api/types/feature";
import { TemplateItemType } from "@/api/types/poster";
import {
  getPageList,
  getPageModuleList,
  getTemplatesDetail,
} from "@/api/servers/features";
import PageStartClientTrack from "./_components/PageStartClientTrack";

const moduleMap = {
  [ModuleType.Hero]: Hero,
  [ModuleType.HeroFullScreen]: HeroFullScreen,
  [ModuleType.PosterView]: PosterView,
  [ModuleType.FreeGraphics]: FreeGraphics,
  [ModuleType.FreeGraphics2]: FreeGraphics,
  [ModuleType.HorizontalImageAndText]: HorizontalImageAndText,
  [ModuleType.TextToImage]: TextToImage,
  [ModuleType.GuideStep]: GuideStep,
  [ModuleType.GuideStep2]: GuideStep,
  [ModuleType.FAQ]: FAQ,
  [ModuleType.CarouselFeature]: CarouselFeature,
  [ModuleType.Template]: Templates,
  [ModuleType.ExploreMore]: ToolCompare,
  [ModuleType.ToolCompare]: ExploreMore,
};

export const dynamicParams = true; // 允许新路径

// 生成页面元数据
export async function generateMetadata({
  params,
}: {
  params: { features: string; locale: string };
}) {
  const { dataList } = await getPageList({ pageCategorys: '1,2' });
  const pageConfigItem = dataList?.find(
    (item: Page) =>
      item.pageKeyword.toLowerCase().replace(/\s+/g, "-") === params.features
  );
  return {
    title: pageConfigItem?.seoTitle,
    description: pageConfigItem?.seoDescribe,
    keywords: pageConfigItem?.seoKeyword,
  };
}

// 生成页面静态参数
export async function generateStaticParams() {
  const { dataList } = await getPageList({ pageCategorys: '1,2' });
  const localeList = getStaticParams();
  const list = localeList.flatMap((localeItem) =>
    dataList?.map((configItem: Page) => ({
      features: configItem.pageKeyword.toLowerCase().replace(/\s+/g, "-"),
      locale: localeItem.locale,
    }))
  );
  return list;
}

// 生成页面
const Features = async ({
  params,
}: {
  params: { features: string; locale: string, keyword: string };
}) => {
  setStaticParamsLocale(params.locale);

  const res = await getPageList({ pageCategorys: '1,2' });
  const { id, pageKeyword } =
    res?.dataList?.find(
      (item: Page) =>
        item.pageKeyword.toLowerCase().replace(/\s+/g, "-") === params.features
    ) ?? {};

  if (!id) {
    notFound();
  }

  const moduleList = await getPageModuleList({ pageId: id });

  // 根据moduleIndex排序
  moduleList.sort(
    (a: PageModule, b: PageModule) =>
      (a.moduleIndex ?? 0) - (b.moduleIndex ?? 0)
  );

  // 处理TextToImage模块的模板数据
  const textToImageModules = moduleList.filter(
    (item: PageModule) =>
      item.moduleType === ModuleType.TextToImage && item.children?.length
  );

  if (textToImageModules.length > 0) {
    // 收集所有需要获取的templateIds
    const templateIds = textToImageModules
      .flatMap(
        (item: PageModule) =>
          item.children?.map((child: ChildrenItem) => child.templateId) || []
      )
      .filter(Boolean)
      .join(",");

    if (templateIds) {
      // 只发送一次请求获取所有模板
      const templates = await getTemplatesDetail({ ids: templateIds });

      // 将模板数据分配给对应的children
      textToImageModules.forEach((item: PageModule) => {
        item.children?.forEach((child: ChildrenItem) => {
          const template = templates.find(
            (template: TemplateItemType) =>
              template.id.toString() === child.templateId
          );
          if (template) {
            child.template = template;
          }
        });
      });
    }
  }

  // 根据moduleType渲染组件
  const modules = moduleList.map((item: PageModule, index: number) => {
    if (item.moduleType === ModuleType.Template) {
      return (
        <Templates title={item.title} content={item.content} key={index} pageKeyword={pageKeyword} />
      );
    }
    const Component = moduleMap[item.moduleType as keyof typeof moduleMap];
    return <Component key={index} {...item} pageKeyword={pageKeyword ?? ''} />;
  });

  return <>
  <PageStartClientTrack keyword={pageKeyword ?? ''} />
  {modules}
  </>;
};

export default Features;
