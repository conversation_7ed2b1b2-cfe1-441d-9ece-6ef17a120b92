.plans-content {
    width: 1200px;
    margin: 0 auto;
    min-height: 80%;
    padding-top: 64px;
    .title-wrapper {
      margin: 80px 0;
      position: relative;
      .title {
        color: #FFF;
        text-align: center;
         font-family: var(--font-poppins);
        font-size: 48px;
        font-style: normal;
        font-weight: 600;
        line-height: 120%; /* 67.2px */
        backdrop-filter: blur(10.25px);
        background: linear-gradient(98deg, #00FFB2 16.16%, #FFF 34.84%, #FF6CE8 81.54%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .description {
        color: #FFF;
        text-align: center;
         font-family: var(--font-poppins);
        font-size: 24px;
        font-style: normal;
        margin-top: 20px;
      }
    }
   
  }
  .plan-wrapper {
    display: flex;
    flex-direction: column;
    // 水平居中
    align-items: center;
    margin-top: 30px;
    gap: 24px;

    .plans-subscribe-tabs{
      margin: 0 auto 24px;
      display: inline-flex;
      position: relative;
      left: 50%;
      transform: translateX(-50%);
    }
  }
