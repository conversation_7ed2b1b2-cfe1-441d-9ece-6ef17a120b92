"use client";
// 忽略eslint
/* eslint-disable */
import styles from "./index.module.scss";

import FAQ from "../[features]/_components/FAQ";

import { useI18n } from "@/locales/client";
import { ModuleType } from "@/api/types/feature";
import { Spin } from 'antd';
import { Button } from "@/components/Button";
import { VipButton } from "@/components/Button/1VipButton";
import { useEffect, useRef, useState } from "react";
import { CrossBlack, CheckCircleBold } from '@meitu/candy-icons';

import { useSubscribe } from "@/hooks/useSubscribe";

import { observer } from 'mobx-react-lite';
import { useStore } from "@/contexts/StoreContext";
import { wheeLoginPopup } from "@/utils/account";
import { useRouter } from "next/navigation";
import cn from 'classnames'
import { trackEvent } from "@/services/tracer";
import { TrackPeriodType, TrackTabType } from "@/components/SubscribeModal/types";
import { IsLogin, usePageStart } from "@/hooks/useTrack";

const FAQ_DATA = [
  {
    moduleId: 0,
    pageId: 0,
    images: [],
    title: "Are AI tools on WheeAI free?",
    content:
      "Yes! WheeAI offers a free plan that gives all users access to templates and content at no cost. \n For work or professional use, consider upgrading to a paid   plan to enjoy more credits, the ability to run more tasks at once, expanded   project limits, commercial usage rights, and access to advanced features.",
  },
  {
    moduleId: 0,
    pageId: 0,
    images: [],
    title: "Can I use WheeAI creations for commercial purposes?",
    content:
      "Yes! However, if you're on the free plan, you must retain WheeAI's attribution (such as a watermark or text description) when using the   content. Paid plan users can use generated content freely without   attribution. For full details on our commercial licensing, click here ",
  },
  {
    moduleId: 0,
    pageId: 0,
    images: [],
    title: "What are credits in WheeAI?",
    content:
      "Credits are WheeAI's in-platform currency that is used to access  AI features like the AI Poster Maker, AI Background Remover, and AI Image Enhancer. Generating content with these and other features consumes credits.",
  },
  {
    moduleId: 0,
    pageId: 0,
    images: [],
    title: "What payment methods do you support?",
    content:
      "We accept major cards, including Visa, Mastercard, American   Express, JCB, and UnionPay, as well as digital wallets like Link.",
  },
  {
    moduleId: 0,
    pageId: 0,
    images: [],
    title: "Can I carry over my credits?",
    content:
      "No, unused credits do not roll over to the next billing cycle.   They will reset to zero at the end of each period.",
  },
  {
    moduleId: 0,
    pageId: 0,
    images: [],
    title: "Will my designs become public after my paid subscription ends?",
    content:
      "No, your designs will remain private even after your   subscription expires. The system will not automatically make them public,   ensuring your content stays secure.",
  },
];

const Plans =  observer(() => {
    usePageStart('plans', IsLogin.notLogin);
    const t =  useI18n();
    const [tab, setTab] = useState<'year' | 'month'>('year');

    const [currentPlan, setCurrentPlan] = useState<any>(null);
    const [plans, setPlans] = useState<any[]>([
        {
            type: 'basic',
            name: 'Basic',
            description: 'Unlock advanced features and create commercial-use content.',
        }, {
            type: 'plus',
            name: 'Premium',
            description: 'Premium for more monthly credits and unlimited project creations.',
        }
    ]);

    const { userStore } = useStore();
    const { isLogin } = userStore;

    const router = useRouter();
   
    
    const { loading, error, yearProduct, monthProduct, createTradeUrl, querySubscribeResult, initializeSubscription, submitLoading } = useSubscribe(); 
    const yearProductPromotion = yearProduct[0]?.promotionBanner || '';

    useEffect(() => {
      initializeSubscription();
       // 去掉告警
       // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
    useEffect(() => {
        // initializeSubscription(); 
        // 将yearProduct和monthProduct合并到plans中每一项，按照index 例如：plans[0].yearProduct = yearProduct[0]
        plans.forEach((plan, index) => {
            // 跳过第一项
           if(tab === 'year') {
               plan.project = yearProduct[index];
           } else if(tab === 'month') {
               plan.project = monthProduct[index];
           } else {
            plan.project = null;
           }
        });
       //  setCurrentPlanList(plans);
        setPlans([...plans]);
        setCurrentPlan(null);
       // 去掉告警
       // eslint-disable-next-line react-hooks/exhaustive-deps
   }, [tab, yearProduct, monthProduct, loading]);

   const handlePrice = (price: any, type: 'year' | 'month') => {
    // 如果price是字符串，则先转换为数字
    if (price === undefined) {
        return '';
    }
   // 去除掉非数字符号
   price = price.replace(/[^0-9]/g, '');
    if(typeof price === 'string') {
        price = parseInt(price);
    }
    // console.log('price=>>>>>>>>',  price / 100);
    return type === 'month' ? price / 100 : price;
}
    const handleSubscribe = async (plan: any, index: number) => {

        const product = (tab === "year" ? yearProduct : monthProduct)[index];
        const trackParams = {
            open_source: location.pathname,
            product_id: product?.productId,
            period_type: tab === "year" ? TrackPeriodType.Year : TrackPeriodType.Month,
            product_type: product?.productType,
            sku_type: plan?.type ||  'basic,plus' , //曝光时两个都曝光 点击和成功时单项
            tab: tab === 'year' ? TrackTabType.Year : TrackTabType.Month,
        };

        trackEvent("vip_pay_click", trackParams);


        // 设置当前选中的计划
        // setCurrentPlan(plan);
        if(!isLogin) {
            wheeLoginPopup({
             loginSuccess: async (accessToken?: string) => {
                const result = await initializeSubscription(accessToken);
                    const urlResponse = await result?.instance?.createTradeUrl(plan.project)
                    if(urlResponse) {
                         // 新开tab
                    
                    window.open(urlResponse?.url, '_blank');
                    router.push('/workspace');
                    
                }
               
             }
            });
            return;
        }
      
        const urlResponse = await createTradeUrl(plan.project)
        if(urlResponse) {
             // 新开tab
            
            window.open(urlResponse?.url, '_blank');
            querySubscribeResult(plan.project, () => {
                close();
                // onSuccessCallback?.();
                trackEvent("vip_paidprocess_success", trackParams);
            });
        }
    }
    const handleChangeTab = (tab: 'year' | 'month') => {
        setTab(tab);
      
    }



    const YearPriceComponent = (planItem: any) => {
       
        const { plan, index } = planItem;
        return (
            <>
                <div>
                    <span className='symbol'>{plan?.project?.productPrice?.moneySymbol}</span>
                    <span className='price'>{handlePrice(plan?.project?.productPrice?.productPriceExplain, 'year')}</span>
                    <span className='price-tips'>{plan?.project?.productPrice?.moneyUnit}/Month billed yearly</span>
                </div>
                <div className='subscribe-modal-plan-original-price'>
                    {
                      monthProduct[index]?.productPrice?.price ? `Original price: ${plan?.project?.productPrice?.moneySymbol}${handlePrice(monthProduct[index]?.productPrice?.price, 'month')}` : ''
                    }
                </div>
            </>
        )
    }

    const MonthPriceComponent = (planItem: any) => {
        const { plan, index } = planItem;
        return (
            <>
                <div>
                    <span className='symbol'>{plan?.project?.productPrice?.moneySymbol}</span>
                    <span className='price'>{handlePrice(plan?.project?.productPrice?.price, 'month')}</span>
                </div>
                <div className='subscribe-modal-plan-original-price'>
                    {
                        plan?.project?.productPrice?.originalPrice !== plan?.project?.productPrice?.price ?  
                        `${plan?.project?.productPrice?.moneySymbol}${handlePrice(plan?.project?.productPrice?.originalPrice, 'month')}` : ''
                    }
                </div>
            </>
        )
    }

    useEffect(() => {
        if (!monthProduct.length || !yearProduct.length) {
            return;
        }

        setTimeout(() => {
            switch(tab) {
                case "month": {
                    trackEvent("vip_price_exp", {
                        open_source: location.pathname,
                        product_id: monthProduct.map(p => p.productId).join(','),
                        period_type: TrackPeriodType.Month,
                        product_type: monthProduct[0]?.productType,
                        sku_type: "free,basic,plus",
                        tab: TrackTabType.Month,
                    });
                    return;
                }
    
                case "year": {
                    trackEvent("vip_price_exp", {
                        open_source: location.pathname,
                        product_id: yearProduct.map(p => p.productId).join(','),
                        period_type: TrackPeriodType.Year,
                        product_type: yearProduct[0]?.productType,
                        sku_type: "free,basic,plus",
                        tab: TrackTabType.Year,
                    });
                    return;
                }
            }
        }, 200);
    }, [tab, monthProduct, yearProduct]);

  return (
    <div className={styles["plans-content"]}>
      <div className={styles["title-wrapper"]}>
        <div className={styles["title"]}>
        Spark Your AI Creativity · Find Your Perfect Plan
        </div>
        <div className={styles["description"]}>
        Supercharge your AI creations — earn credits and unlock premium magic
        </div>

        <div className={styles["plan-wrapper"]}>
        <Spin spinning={loading}>
            <div className="subscribe-modal-content">

                <div className={cn("subscribe-modal-tabs",styles['plans-subscribe-tabs'])}>
                    <div className={`tab ${tab === 'year' ? 'tab-selected' : ''}`} onClick={() => handleChangeTab('year')}> {t("Annually")} {
                        yearProductPromotion && `(${yearProductPromotion})`
                    }</div>
                    <div className={`tab ${tab === 'month' ? 'tab-selected' : ''}`} onClick={() => handleChangeTab('month')}>{t("Monthly")}</div>
                </div>

                <div className="subscribe-modal-plans">
                <div className={`subscribe-modal-plan free`} >
                                <div className='subscribe-modal-plan-name'>{"Free"}</div>
                                <div className='subscribe-modal-plan-description'>{"AI tools bring your ideas to life. Start exploring at no cost."}</div>
                                <div className='subscribe-modal-plan-price'>
                                <div>
                    <span className='symbol'>{'$'}</span>
                    <span className='price'>{0}</span>
                    <span className='price-tips'>{tab == 'year' ?'/Month billed yearly': ''}</span>
                </div>
                <div className='subscribe-modal-plan-original-price'> </div>
                                </div>
                                <div className='subscribe-modal-button'>
                                            <Button type='primary' onClick={() => {
                                                const trackParams = {
                                                    open_source: location.pathname,
                                                    period_type: tab === "year" ? TrackPeriodType.Year : TrackPeriodType.Month,
                                                    sku_type: 'free',
                                                    tab: tab === 'year' ? TrackTabType.Year : TrackTabType.Month,
                                                };
                                                trackEvent("vip_pay_click", trackParams);

                                                if(!isLogin) {
                                                    wheeLoginPopup({
                                                    loginSuccess() {
                                                        router.push('/workspace');
                                                    },
                                                    }); 
                                                } else {
                                                    // 路由到workspaces
                                                    router.push('/workspace');
                                                }
                                            }} loading={submitLoading && currentPlan?.type === 'basic'}>Start now</Button>
                                </div>
                                {/* <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>{t("{count}credits per month" as any, { count: 20 })}</span>
                                </div> */}
                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>{t("Generated posters are public")}</span>
                                </div>

                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>{t("Generations per batch: {count}" as any, { count: 1 })}</span>
                                </div>

                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>2 {t("Projects")}</span>
                                </div>
                            </div>

                    {plans.map((plan, index) => {
                        return (
                            <div className={`subscribe-modal-plan ${plan?.type === 'plus' ? 'plus' : ''}`} key={index}>
                                <div className='subscribe-modal-plan-name'>{plan?.name}
                                    {
                                        plan?.type === 'plus' ?
                                        <span className='subscribe-modal-plan-name-number'>{"Most popular"}</span>
                                        : ''
                                    }
                                </div>
                                <div className='subscribe-modal-plan-description'>{plan?.description}</div>
                                <div className='subscribe-modal-plan-price'>
                                    {tab === 'year' ? <YearPriceComponent plan={plan} index={index} /> : <MonthPriceComponent plan={plan} index={index} />}
                                </div>
                                <div className='subscribe-modal-button'>
                                    {
                                        plan?.type === 'plus' ?
                                        <>
                                        {  
                                             <VipButton type='primary' onClick={() => handleSubscribe(plan, index)}>{t("Subscribe to the Premium plan")}</VipButton>
                                        }
                                        </> :
                                        <>
                                         {
                                           
                                  
                                            <Button type='primary' onClick={() => handleSubscribe(plan, index)}>{t('Subscribe to the Basic plan')}</Button>
                                         }
                                        </>
                                         
                                    }
                                </div>
                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    {/* <span className={"subscribe-modal-plan-item-text"}>{t("{count}credits per month" as any, { count: plan?.type === 'plus' ? 3000 : 1000 })}</span> */}
                                    <span className={"subscribe-modal-plan-item-text"}><span className='subscribe-modal-plan-item-text-number'>{plan?.type === 'plus' ? '3000' : '1000'} </span>credits per month</span>
                                </div>
                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>{t("Generated posters are private")}</span>
                                </div>

                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    {/* <span className={"subscribe-modal-plan-item-text"}>{t("Generations per batch: {count}" as any, { count: plan?.type === 'plus' ? 5 : 3 })}</span> */}
                                    <span className={"subscribe-modal-plan-item-text"}><span className='subscribe-modal-plan-item-text-number'>{plan?.type === 'plus' ? '5' : '3'} </span>Generations per batch</span>
                                </div>

                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    {/* <span className={"subscribe-modal-plan-item-text"}>{plan?.type === 'plus' ? 'Unlimited' : '10'} {t("Projects")}</span> */}
                                    <span className={"subscribe-modal-plan-item-text"}><span className='subscribe-modal-plan-item-text-number'>{plan?.type === 'plus' ? 'Unlimited' : '10'} </span>{t("Projects")}</span>
                                </div>

                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>{t("No restrictions on commercial use")}</span>
                                </div>
                                
                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>{t("Access to all member benefits")}</span>
                                </div>
                                
                                <div className={"subscribe-modal-plan-item"}>
                                    <CheckCircleBold/>
                                    <span className={"subscribe-modal-plan-item-text"}>{t("Watermark-free downloads")}</span>
                                </div>

                                {plan?.type === 'plus' ? (
                                    <div className={"subscribe-modal-plan-item"}>
                                        <CheckCircleBold/>
                                        <span className={"subscribe-modal-plan-item-text"}>{t("Early access to new features")}</span>
                                    </div>):   <div className={"subscribe-modal-plan-item"}></div>
                                }
                            </div>
                        )
                    })}
                </div>
            </div>
            </Spin>
        </div>
      </div>
      {
        <FAQ
          moduleId={0}
          moduleType={ModuleType.FAQ}
          moduleIndex={0}
          pageId={0}
          title="FAQ"
          content="FAQ"
          children={FAQ_DATA.map(item => {
            return {
                ...item,
                onTriggerClick: () => {
                    trackEvent("vip_common_problems_click", {
                        problem: item.title,
                    });
                }
            }
          })}
        />
      }
    </div>
  );
});

export default Plans;
