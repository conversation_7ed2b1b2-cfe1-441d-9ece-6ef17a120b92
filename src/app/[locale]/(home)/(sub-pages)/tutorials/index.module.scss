
.teaching-center-content {
  width: 1200px;
  margin: 0 auto;
  min-height: 80%;
  padding-top: 64px;
  .title-wrapper {
    margin: 80px 0;
    position: relative;
    .title {
      color: #FFF;
      text-align: center;
       font-family: var(--font-poppins);
      font-size: 56px;
      font-style: normal;
      font-weight: 600;
      line-height: 120%; /* 67.2px */
      backdrop-filter: blur(10.25px);
      background: linear-gradient(98deg, #00FFB2 16.16%, #FFF 34.84%, #FF6CE8 81.54%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }
  }
  .content {
    width: 800px !important;
    margin: 0 auto !important;
  }
  
 
}
.tutorial-detail {
  width: 1200px;
  margin: 0 auto;
  min-height: 90%;
  padding: 80px 0;
  box-sizing: border-box;
  margin-bottom: 16px;
  .time {
    color: var(--system-content-secondary, #a3aebf);
     font-family: var(--font-poppins);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 130%; /* 20.8px */
    margin-bottom: 16px;
  }
  .title {
    color: #FFF;
     font-family: var(--font-poppins);
     text-align: center;
    font-size: 48px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%; /* 57.6px */
    margin-bottom: 56px;
    text-align: center;
  }
}


@media (max-width: 1200px) {
  .teaching-center-content {
    width: 100%;
    padding: 64px 16px 0;
    box-sizing: border-box;
  }
  .tutorial-detail {
    width: 100%;
    padding: 40px 16px;
  }
}
