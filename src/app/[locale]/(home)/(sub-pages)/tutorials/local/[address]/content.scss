.content {
 /* Headings */
 width: 800px !important;
 margin: 0 auto !important;
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
h2 {
  font-size: 1.5em;
  margin: 0.75em 0;
}
h3 {
  font-size: 1.17em;
  margin: 0.83em 0;
}
h4 {
  font-size: 1em;
  margin: 1.12em 0;
}
h5 {
  font-size: 0.83em;
  margin: 1.5em 0;
}
h6 {
  font-size: 0.75em;
  margin: 1.67em 0;
}

/* Text */
p {
  display: block;
  margin: 1em 0;
}

strong {
  font-weight: bold;
}
em {
  font-style: italic;
}

/* Links */
a {
  color: -webkit-link;
  text-decoration: underline;
  cursor: pointer;
}

/* Lists */
ul, menu, dir {
  display: block;
  list-style-type: disc;
  margin: 1em 0;
  padding-left: 40px;
}
ol {
  display: block;
  list-style-type: decimal;
  margin: 1em 0;
  padding-left: 40px;
}
li {
  display: list-item;
}

/* Tables */
table {
  display: table;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #333;
  width: 100%;
  margin: 1em 0;
  background-color: #1a1a1a;
  color: #e0e0e0;
}
caption {
  display: table-caption;
  text-align: center;
  color: #e0e0e0;
  padding: 8px 0;
  font-weight: bold;
}
tr {
  display: table-row;
  border-bottom: 1px solid #333;
}
tr:hover {
  background-color: #2a2a2a;
}
td, th {
  display: table-cell;
  padding: 10px;
  text-align: left;
  border-right: 1px solid #333;
}
th {
  background-color: #252525;
  font-weight: bold;
  border-bottom: 2px solid #444;
}
td:last-child, th:last-child {
  border-right: none;
}

/* Form Elements */
input, textarea, select, button {
  font: inherit;
  margin: 0;
}
button {
  appearance: auto;
}
textarea {
  white-space: pre-wrap;
}
fieldset {
  padding: 0.35em 0.75em 0.625em;
  margin: 0 2px;
  border: 2px groove ThreeDFace;
}
legend {
  display: block;
  padding-left: 2px;
  padding-right: 2px;
}
label {
  cursor: default;
}

/* Block-level elements */
div, section, article, header, footer, nav, aside, main {
  display: block;
}

/* Inline elements */
span, b, i, u, small, abbr, acronym {
  display: inline;
}

/* Pre/code */
pre {
  display: block;
  font-family: monospace;
  white-space: pre;
  margin: 1em 0;
}
code {
  font-family: monospace;
}

/* Media */
img {
  display: inline-block;
  max-width: 100%;
  border: none;
  margin: 10px;
}
audio, video {
  display: inline-block;
}

/* Others */
hr {
  border: 1px inset;
  color: gray;
}
  
}