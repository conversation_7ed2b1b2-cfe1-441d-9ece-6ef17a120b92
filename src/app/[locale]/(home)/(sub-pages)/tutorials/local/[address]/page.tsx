import { getTutorialQuery } from "@/api/servers/tutorial";
import styles from "../../index.module.scss";
import { notFound } from "next/navigation";
import './content.scss'

// 格式化日期为 "Month Day, Year" 格式
const formatDate = (dateString: string | number) => {
  const date = new Date(Number(dateString)*1000);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const TutorialDetail = async ({ params }: { params: { address: string } }) => {
  try {
    const res = await getTutorialQuery({ address: params.address });
    return <div className={styles["tutorial-detail"]}>
      <div className={styles["time"]}>{formatDate(res.updatedAt)}</div>
      <div className={styles["title"]}>{res.name}</div>
      <div className={'content'} dangerouslySetInnerHTML={{ __html: res.content }} />
    </div>;
  } catch (error) {
    return notFound();
  }
};

export default TutorialDetail;
