import styles from "./index.module.scss";
import FeedContent from "../../../workspace/tutorials/_components/FeedContent";
import { getTutorialList } from "@/api/servers/tutorial";
import { getI18n } from "@/locales/server";

const TeachingCenter = async () => {
  const res = await getTutorialList({});
  const t = await getI18n();
  return (
    <div className={styles["teaching-center-content"]}>
        <div className={styles["title-wrapper"]}>
          <div className={styles["title"]}>
            {t('Create posters like never before with the power of AI.')}
          </div>
        </div>
        <FeedContent
          initialData={res}
          scrollableTarget="scroll-main"
          columnCount={{ default: 3, smallDesktop: 4, tablet: 2, mobile: 1 }}
        />
      </div>
  );
};

export default TeachingCenter;
