import React, { useEffect, useState, useRef } from "react";
import { motion, cubicBezier, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";

// 示例内容数组 - 保持原有的视频路径
const items = [
  // "/video/home/<USER>",
  "https://wheeai-public.stariidata.com/static/home/<USER>/output1.mp4",
  // "/video/home/<USER>",
  "https://wheeai-public.stariidata.com/static/home/<USER>/output2.mp4",
  // "/video/home/<USER>",
  "https://wheeai-public.stariidata.com/static/home/<USER>/output3.mp4",
  // "/video/home/<USER>",
  "https://wheeai-public.stariidata.com/static/home/<USER>/output4.mp4",
];

// 自定义缓动函数 - 更接近motion.dev的丝滑曲线
// 参考分析motion官网的动画曲线，修改贝塞尔参数
// 第一个值接近0表示开始移动较慢
// 第二个值较高表示迅速加速
// 第三、四个值控制减速过程，让动画优雅地结束
const slideEase = cubicBezier(0.22, 0.68, 0.29, 0.99);

// 动画持续时间调整为更长，让过渡更丝滑
const ANIMATION_DURATION = 1.2; // 秒

interface MotionShowcaseCarouselProps {
  activeIndex?: number;
  onChangeIndex?: (index: number) => void;
  autoplay?: boolean;
  videos?: string[];
  cardWidth?: number;
  cardHeight?: number;
  cardGap?: number;
  sideCardScale?: number;
}

const MotionShowcaseCarousel: React.FC<MotionShowcaseCarouselProps> = ({
  activeIndex: externalActiveIndex,
  onChangeIndex,
  autoplay = false,
  videos = items,
  cardWidth = 800,
  cardHeight = 500,
  cardGap = 40,
  sideCardScale = 0.8,
}) => {
  // 内部状态，如果提供了外部控制，则使用外部控制的索引
  const [internalActiveIndex, setInternalActiveIndex] = useState(0);
  const activeIndex = externalActiveIndex !== undefined ? externalActiveIndex : internalActiveIndex;
  
  const [isTransitioning, setIsTransitioning] = useState(false);
  // 添加方向状态，用于处理循环边界情况
  const [direction, setDirection] = useState(0); // 0=初始, 1=向右, -1=向左
  // 添加虚拟索引，用于解决循环过渡问题
  const [virtualIndex, setVirtualIndex] = useState(externalActiveIndex || 0);
  
  // 自动播放定时器引用，使用正确的类型注解
  const autoplayTimerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  
  // 过渡定时器引用，用于清除之前的过渡计时器
  const transitionTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  // 预加载所有视频，确保它们在切换前已经准备好
  const videoRefs = useRef<Array<HTMLVideoElement | null>>([]);

  // 同步外部activeIndex变化
  useEffect(() => {
    if (externalActiveIndex !== undefined) {
      // 计算新的虚拟索引基于当前虚拟索引和新旧activeIndex的差异
      const oldActualIndex = getActualIndex(virtualIndex);
      const diff = externalActiveIndex - oldActualIndex;
      
      // 设置新的虚拟索引和方向
      setVirtualIndex(prev => prev + diff);
      setDirection(diff > 0 ? 1 : diff < 0 ? -1 : 0);
      
      // 标记为过渡中
      if (diff !== 0) {
        setIsTransitioning(true);
        
        // 设置过渡完成后的回调
        clearTransitionTimer();
        transitionTimerRef.current = setTimeout(() => {
          setIsTransitioning(false);
        }, ANIMATION_DURATION * 1000);
      }
    }
  }, [externalActiveIndex]);

  // 映射虚拟索引到实际数组索引
  const getActualIndex = (vIndex: number) => ((vIndex % videos.length) + videos.length) % videos.length;
  
  // 设置自动播放
  const startAutoplay = () => {
    // 如果不需要自动播放，则不启动
    if (!autoplay) return;
    
    // 先清除任何现有的定时器
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
    }
    
    // 设置新的定时器
    autoplayTimerRef.current = setInterval(() => {
      if (!isTransitioning) {
        handleNext(false);
      }
    }, 5000);
  };

  // 清除过渡定时器
  const clearTransitionTimer = () => {
    if (transitionTimerRef.current) {
      clearTimeout(transitionTimerRef.current);
      transitionTimerRef.current = null;
    }
  };

  // 处理下一项 - 允许连续执行
  const handleNext = (isManual = true) => {
    // 不再检查isTransitioning，允许连续切换
    
    // 清除之前的过渡定时器，避免状态冲突
    clearTransitionTimer();
    
    // 如果是手动触发，重置自动播放
    if (isManual && autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
      autoplayTimerRef.current = null;
    }
    
    const nextIndex = (activeIndex + 1) % videos.length;
    
    // 如果有外部控制，调用回调
    if (onChangeIndex) {
      onChangeIndex(nextIndex);
    }
    
    // 无论是否有外部控制，都更新内部状态以确保动画正常进行
    setDirection(1); // 设置方向为向右
    setIsTransitioning(true);
    if (!onChangeIndex) {
      setInternalActiveIndex(nextIndex);
    }
    setVirtualIndex(prev => prev + 1); // 虚拟索引始终递增，不循环
    
    // 设置新的过渡定时器
    transitionTimerRef.current = setTimeout(() => {
      setIsTransitioning(false);
      
      // 如果是手动触发，延迟后重新启动自动播放
      if (isManual && autoplay) {
        setTimeout(startAutoplay, 2000);
      }
    }, ANIMATION_DURATION * 1000);
  };

  // 处理上一项 - 允许连续执行
  const handlePrev = () => {
    // 不再检查isTransitioning，允许连续切换
    
    // 清除之前的过渡定时器，避免状态冲突
    clearTransitionTimer();
    
    // 手动触发，重置自动播放
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
      autoplayTimerRef.current = null;
    }
    
    const prevIndex = (activeIndex - 1 + videos.length) % videos.length;
    
    // 如果有外部控制，调用回调
    if (onChangeIndex) {
      onChangeIndex(prevIndex);
    }
    
    // 无论是否有外部控制，都更新内部状态以确保动画正常进行
    setDirection(-1); // 设置方向为向左
    setIsTransitioning(true);
    if (!onChangeIndex) {
      setInternalActiveIndex(prevIndex);
    }
    setVirtualIndex(prev => prev - 1); // 虚拟索引始终递减，不循环
    
    // 设置新的过渡定时器
    transitionTimerRef.current = setTimeout(() => {
      setIsTransitioning(false);
      
      // 延迟后重新启动自动播放
      if (autoplay) {
        setTimeout(startAutoplay, 2000);
      }
    }, ANIMATION_DURATION * 1000);
  };

  // 自动轮播 - 组件挂载时启动，卸载时清除
  useEffect(() => {
    if (autoplay) {
      startAutoplay();
    }
    
    // 组件卸载时清除所有定时器
    return () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current);
      }
      clearTransitionTimer();
    };
  }, [autoplay]);

  // 创建一个扩展的虚拟数组，用于平滑循环过渡
  const getVirtualItems = () => {
    // 创建足够大的虚拟数组，使得循环时有足够的项目进行平滑过渡
    // 这里我们在前后各添加items.length个项目
    const result = [];
    
    // 计算要渲染的虚拟索引范围
    const lowerBound = virtualIndex - videos.length;
    const upperBound = virtualIndex + videos.length;
    
    // 为这个范围内的每个虚拟索引创建项目
    for (let i = lowerBound; i <= upperBound; i++) {
      const actualIndex = getActualIndex(i); // 将虚拟索引映射到实际数组索引
      result.push({ 
        virtualIndex: i,
        actualIndex,
        src: videos[actualIndex]
      });
    }
    
    return result;
  };

  // 计算每个卡片的位置和样式
  const calculateCardStyles = (virtualIdx: number) => {
    // 计算相对于当前virtualIndex的位置偏移
    const offset = virtualIdx - virtualIndex;
    const absOffset = Math.abs(offset);
    
    // 确定卡片样式
    const isActive = offset === 0;
    
    // 计算缩放比例 - 增加非线性缩放，更接近motion官网效果
    let scale;
    if (isActive) {
      scale = 1;
    } else if (absOffset === 1) {
      scale = sideCardScale;
    } else {
      // 更远的卡片逐渐变小，创造更强的深度效果
      scale = sideCardScale * Math.pow(0.9, absOffset - 1);
    }
    
    // 调整偏移量，考虑缩放因素带来的视觉间距变化
    const baseOffset = cardWidth / 2; // 半个卡片宽度
    const scaledWidth = cardWidth * sideCardScale; // 缩放后的卡片宽度
    
    // 根据偏移方向计算位置 - 采用更平滑的位置计算，优化"传送带"效果
    let xPosition;
    if (offset === 0) { // 中心卡片
      xPosition = 0;
    } else if (offset > 0) { // 右侧卡片
      // 增加非线性偏移，使卡片位置分布更自然
      xPosition = baseOffset + cardGap * 1.2 + scaledWidth/2;
      if (absOffset > 1) {
        // 更远的卡片间距渐增，创造透视感
        xPosition += (absOffset - 1) * (cardGap * 1.1 + scaledWidth);
      }
    } else { // 左侧卡片
      // 增加非线性偏移，使卡片位置分布更自然
      xPosition = -(baseOffset + cardGap * 1.2 + scaledWidth/2);
      if (absOffset > 1) {
        // 更远的卡片间距渐增，创造透视感
        xPosition -= (absOffset - 1) * (cardGap * 1.1 + scaledWidth);
      }
    }
    
    // 透明度 - 中心项完全不透明，远离中心项逐渐透明
    // 更平滑的透明度过渡
    const opacity = Math.max(0, 1 - absOffset * 0.15);
    
    // z-index - 中心项层级最高，远离中心项层级逐渐降低
    const zIndex = 10 - absOffset;
    
    return {
      xPosition,
      scale,
      opacity,
      zIndex,
      // 控制渲染可见性 - 对DOM中存在但视觉上不可见的元素
      isVisible: absOffset <= 5, // 保持更多卡片在DOM中，扩大可视范围
      display: absOffset > 6 ? 'none' : 'block', // 非常远的卡片不显示，但保持在DOM中
    };
  };

  // 设置视频引用的处理函数
  const setVideoRef = (el: HTMLVideoElement | null, index: number) => {
    videoRefs.current[index] = el;
  };

  // 获取要渲染的虚拟项目
  const virtualItems = getVirtualItems();

  return (
    <div className="relative w-full">
      <div className="relative mx-auto w-full flex items-center justify-center" 
           style={{ 
             height: `${cardHeight * 1.1}px`, // 容器高度根据卡片高度自动调整，留出一些空间
             minHeight: `${cardHeight * 1.1}px`
           }}>
        {/* 主轮播容器 */}
        <div className="relative w-full h-full">
          {/* 保证永远渲染所有卡片，使用虚拟索引系统 */}
          {virtualItems.map(({ virtualIndex: vIdx, actualIndex, src }) => {
            const {
              xPosition,
              scale,
              opacity,
              zIndex,
              isVisible,
              display
            } = calculateCardStyles(vIdx);
            
            return (
              <motion.div
                key={vIdx} // 使用虚拟索引作为key，确保项目在循环时保持身份
                className="absolute select-none"
                style={{
                  width: `${cardWidth}px`,
                  height: `${cardHeight}px`,
                  left: '50%',
                  top: '50%',
                  marginLeft: `-${cardWidth/2}px`,
                  marginTop: `-${cardHeight/2}px`,
                  display: display // 使用CSS控制可见性，而不是条件渲染
                }}
                initial={false} // 禁用初始动画
                animate={{
                  x: xPosition,
                  scale,
                  opacity,
                  zIndex
                }}
                transition={{
                  type: "tween", 
                  duration: ANIMATION_DURATION,
                  ease: slideEase,
                }}
              >
                {/* 如果卡片可能在视野内，渲染视频 */}
                {isVisible && (
                  <video
                    ref={(el) => setVideoRef(el, actualIndex)}
                    src={src}
                    autoPlay
                    muted
                    loop
                    playsInline
                    disablePictureInPicture
                    className="w-full h-full object-cover rounded-xl shadow-md video-item"
                    preload="auto" // 预加载视频内容
                  />
                )}
                
                {/* 如果卡片不在视野内，使用空白占位符保持DOM结构 */}
                {!isVisible && (
                  <div className="w-full h-full bg-gray-200 rounded-xl" />
                )}
              </motion.div>
            );
          })}
        </div>
        
        {/* 左侧导航按钮 */}
        <button
          onClick={handlePrev}
          className="absolute left-8 md:left-36 top-1/2 -translate-y-1/2 z-30 p-2 md:p-3 transition-transform hover:scale-110"
          style={{
            borderRadius: 'var(--radius-capsule, 999px)',
            border: '2px solid rgba(255, 255, 255, 0.10)',
            background: 'rgba(255, 255, 255, 0.10)',
            backdropFilter: 'blur(11.5px)'
          }}
          aria-label="Previous"
        >
          <ChevronLeft size={24} className="w-5 h-5 md:w-7 md:h-7" color="white" />
        </button>

        {/* 右侧导航按钮 */}
        <button
          onClick={() => handleNext(true)}
          className="absolute right-8 md:right-36 top-1/2 -translate-y-1/2 z-30 p-2 md:p-3 transition-transform hover:scale-110"
          style={{
            borderRadius: 'var(--radius-capsule, 999px)',
            border: '2px solid rgba(255, 255, 255, 0.10)',
            background: 'rgba(255, 255, 255, 0.10)',
            backdropFilter: 'blur(11.5px)'
          }}
          aria-label="Next"
        >
          <ChevronRight size={24} className="w-5 h-5 md:w-7 md:h-7" color="white" />
        </button>
      </div>
    </div>
  );
};

export default MotionShowcaseCarousel;
