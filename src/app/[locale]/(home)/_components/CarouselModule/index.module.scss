.carousel-module {
  width: 1300px;
  margin: 0 auto;
  padding: 64px 0;
  box-sizing: border-box;
  overflow: hidden;

  :global {
    .carousel-module-title {
      color: var(--system-content-primary, #fff);
      text-align: center;
      font-family: var(--font-poppins);
      font-size: 36px;
      font-style: normal;
      font-weight: 600;
      line-height: 130%;
      /* 46.8px */
      margin-bottom: 16px;
    }

    .carousel-module-description {
      color: var(--system-content-secondary, #a3aebf);
      text-align: center;
      font-family: var(--font-poppins);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 137.5% */
    }

    .carousel-module-tab-wrapper {
      width: 1200px;
      margin: 40px auto;
    }

    .carousel-module-tab {
      margin: 0 auto;
      width: fit-content;
      display: flex;
      gap: 57px;
      justify-content: space-between;
      border-radius: var(--radius-16, 16px);
      border: 1px solid #30333c;
      background: #16171a;
      padding: 8px;
      box-sizing: border-box;

      .carousel-module-tab-item {
        flex-shrink: 0;
        min-width: 0;
        padding: 10px 20px;
        color: #a3aebf;
        font-family: var(--font-poppins);
        font-family: var(--font-poppins);
        font-size: 16px;
        font-weight: 500;
        line-height: 130%;
        /* 20.8px */
        text-align: center;
        cursor: pointer;
      }

      .carousel-module-tab-item-active {
        border-radius: var(--radius-12, 12px);
        background: #fff;
        color: #000;
        font-weight: 600;
      }
    }

    .video-item {
      border-radius: var(--radius-24, 24px);
      border: 1px solid #34353a;
    }

    .carousel-wrapper {
      mask: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 11.08178490990991%, rgba(0, 0, 0, 1) 88.18447353603604%, rgba(0, 0, 0, 0) 98.64864864864865%) add;
    }
  }
}

.card-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 676px;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  border-radius: var(--radius-24, 24px);
  border: 2px solid rgba(240, 245, 255, 0.80);
  box-sizing: border-box;
  overflow: hidden;

  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

@media (max-width: 1300px) {
  .carousel-module {
    width: 100%;
    box-sizing: border-box;

    :global {
      .carousel-module-tab-wrapper {
        width: 100%;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .carousel-module-tab {
          gap: 0;
        }
      }

    }
  }

}

@media (max-width: 700px) {
  .carousel-module {
    width: 100%;
    padding: 40px 0;

    :global {
      .carousel-module-tab-wrapper {
        margin: 24px 0;

        .carousel-module-tab {
          background: transparent;
          border: none;

        }
      }
    }
  }
}