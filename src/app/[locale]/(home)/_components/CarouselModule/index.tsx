"use client";
import styles from "./index.module.scss";
import classNames from "classnames";
import { useEffect, useState } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { type CarouselApi } from "@/components/ui/carousel";
import MotionShowcaseCarousel from "./carousel";

// 定义三套不同屏幕尺寸下的卡片配置
const SCREEN_SIZES = {
  LARGE: {
    // 大屏幕 > 1200px
    cardWidth: 960,
    cardHeight: 540,
    cardGap: 40,
    sideCardScale: 0.8,
  },
  MEDIUM: {
    // 中等屏幕 768px-1200px
    cardWidth: 640,
    cardHeight: 360,
    cardGap: 30,
    sideCardScale: 0.8,
  },
  SMALL: {
    // 小屏幕 < 768px
    cardWidth: 360,
    cardHeight: 202,
    cardGap: 20,
    sideCardScale: 0.75,
  },
};

interface CarouselModuleProps {
  title: string;
  data: ChildrenItem[];
}
interface ChildrenItem {
  title: string;
  videoUrl: string;
}

interface CardItemProps extends ChildrenItem {}

const CardItem = ({ videoUrl }: CardItemProps) => {
  return (
    <div className={styles["card-item"]}>
      <video
        src={videoUrl}
        autoPlay
        muted
        loop
        playsInline
        disablePictureInPicture
      ></video>
    </div>
  );
};
const CarouselModule = (props: CarouselModuleProps) => {
  const { title, data } = props;
  const [activeTab, setActiveTab] = useState(0);
  const [api, setApi] = useState<CarouselApi>();
  // 添加屏幕尺寸状态
  const [screenSize, setScreenSize] = useState(SCREEN_SIZES.LARGE);

  // 监听屏幕尺寸变化并更新屏幕尺寸状态
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width > 1200) {
        setScreenSize(SCREEN_SIZES.LARGE);
      } else if (width >= 700) {
        setScreenSize(SCREEN_SIZES.MEDIUM);
      } else {
        setScreenSize(SCREEN_SIZES.SMALL);
      }
    };

    // 初始调用一次以设置初始值
    handleResize();

    // 添加resize事件监听
    window.addEventListener("resize", handleResize);

    // 清理函数
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    if (!api) {
      return;
    }

    api.on("select", () => {
      setActiveTab(api.selectedScrollSnap());
    });
  }, [api]);

  return (
    <section className={styles["carousel-module"]}>
      {title && <h2 className={"carousel-module-title"}>{title}</h2>}
      <div className="carousel-module-tab-wrapper">
        <div className="carousel-module-tab">
          {data?.map((item, index) => (
            <div
              className={classNames("carousel-module-tab-item", {
                "carousel-module-tab-item-active": activeTab === index,
              })}
              key={item.title}
              // onClick={() => {
              //   setActiveTab(index);
              //   api?.scrollTo(index);
              // }}
              onMouseEnter={() => setActiveTab(index)}
            >
              {item.title}
            </div>
          ))}
        </div>
      </div>
      <div className="carousel-wrapper">
        <MotionShowcaseCarousel
          activeIndex={activeTab}
          onChangeIndex={setActiveTab}
          cardWidth={screenSize.cardWidth}
          cardHeight={screenSize.cardHeight}
          cardGap={screenSize.cardGap}
          sideCardScale={screenSize.sideCardScale}
        />
      </div>
    </section>
  );
};

export default CarouselModule;
