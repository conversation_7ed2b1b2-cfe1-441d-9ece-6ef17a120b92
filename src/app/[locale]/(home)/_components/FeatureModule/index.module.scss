.feature {
  width: 1200px;
  margin: 0 auto;
  padding: 64px 0;
  box-sizing: border-box;
  :global {
    .feature-title {
      text-align: center;
      /* title_48_bold */
       font-family: var(--font-poppins);
      font-size: 48px;
      font-style: normal;
      font-weight: 600;
      line-height: 130%; /* 62.4px */
    }
    .feature-description {
      color: var(--system-content-secondary, #a3aebf);
      text-align: center;
      /* text_16 */
       font-family: var(--font-poppins);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 130%; /* 20.8px */
      text-align: center;
    }
    .feature-list {
      padding: 64px 0;
      .feature-item {
        &:last-child {
          margin-bottom: 0;
        }
        display: flex;
        flex-direction: row;
        gap: 120px;
        margin-bottom: 128px;
        align-items: center;
        // justify-content: space-between;
        .feature-item-video-wrapper {
          max-width: 600px;
          height: auto;
          aspect-ratio: 6/4;
          border: 1px solid var(--system-stroke-input-default, #22272e);
          background: var(--system-background-secondary, #1d1e23);
          border-radius: var(--radius-40, 40px);
          overflow: hidden;
          position: relative;
        }
        .feature-item-video-wrapper-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: var(--radius-40, 40px);
          box-shadow: 0px 0px 32px 0px rgba(255, 255, 255, 0.30) inset;
        }
        .feature-item-video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .feature-item-content {
          max-width: 480px;
          .feature-item-title {
            display: flex;
            align-items: center;
            gap: 16px;
            color: var(--system-content-primary, #FFF);
            /* title_36_bold */
             font-family: var(--font-poppins);
            font-size: 36px;
            font-style: normal;
            font-weight: 600;
            line-height: 130%; /* 46.8px */
            margin-bottom: 16px;
          }
          .feature-item-description {
            color: var(--system-content-secondary, #A3AEBF);
            /* text_18 */
             font-family: var(--font-poppins);
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 130%; /* 23.4px */
            margin-bottom: 32px;
          }
        }
      }
    }
  }
}
@media (max-width: 1200px) {
  .feature {
    width: 682px;
    padding: 80px 16px;
    :global {
      .feature-list {
        .feature-item {
          flex-direction: column;
          .feature-item-image {
            order: 1;
            // width: 100%;
            // height: 274px;
          }
          .feature-item-content {
            order: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 100%;

            .feature-item-title {
              font-size: 20px;
            }
            .feature-item-description {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
@media (max-width: 682px) {
  .feature {
    width: 100%;
    margin: 0;
    padding: 0px 16px;
  }
}
