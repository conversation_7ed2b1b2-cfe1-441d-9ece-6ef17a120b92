"use client";
import { ChevronRightBlack, UltraHdBold } from "@meitu/candy-icons";
import Button from "../../(sub-pages)/[features]/_components/Button";
import styles from "./index.module.scss";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { trackEvent } from "@/services/tracer";
interface PageModule {
  title: string;
  description: string;
  childrenData: ChildrenItem[];
}
interface ChildrenItem {
  title: string;
  description: string;
  btnText: string;
  videoUrl: string;
  titleIcon?: string;
  url: string;
  clickType: string;
}

const FeatureContent = ({ item, index }: { item: ChildrenItem, index: number }) => {
  const router = useRouter();
  return (
    <div className="feature-item-content">
      <h3 className="feature-item-title">
        {item.titleIcon && (
          <Image unoptimized src={item.titleIcon} alt={item.title} width={36} height={36} />
        )}
        {item.title}
      </h3>
      <p className="feature-item-description">{item.description}</p>
      <Button onClick={() => {
        trackEvent('unlogin_home_page_click', {
          click_type: 'start',
          location: index + 2,
          location_name: item.clickType,
        });
        router.push(item.url);
      }}>
        <span className="mr-1">{item.btnText} </span>
        <ChevronRightBlack />
      </Button>
    </div>
  );
};

const FeatureItemVideo = ({ videoUrl }: { videoUrl: string }) => {
  return (
    <div className="feature-item-video-wrapper">
      <div className="feature-item-video-wrapper-bg"></div>
      <video
        className="feature-item-video"
        src={videoUrl}
        autoPlay
        muted
        loop
        playsInline
        disablePictureInPicture
      />
    </div>
  );
};

const FeatureItem = ({
  item,
  index,
}: {
  item: ChildrenItem;
  index: number;
}) => {
  return (
    <div className="feature-item" key={item.title}>
      {index % 2 === 0 ? (
        <>
          <FeatureItemVideo videoUrl={item.videoUrl} />
          <FeatureContent item={item} index={index} />
        </>
      ) : (
        <>
          <FeatureContent item={item} index={index} />
          <FeatureItemVideo videoUrl={item.videoUrl} />
        </>
      )}
    </div>
  );
};

const Feature = (props: PageModule) => {
  const { title, description, childrenData } = props;
  return (
    <section className={styles.feature}>
      {title && <h2 className="feature-title">{title}</h2>}
      <div className="feature-list">
        {childrenData?.map((item, index) => (
          <FeatureItem item={item} index={index} key={item.title} />
        ))}
      </div>
    </section>
  );
};

export default Feature;
