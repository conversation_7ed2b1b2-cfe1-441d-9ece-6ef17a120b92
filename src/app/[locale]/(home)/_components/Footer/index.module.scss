.footer {
  width: 100%;
  padding: 40px 120px;
  box-sizing: border-box;
  // background: var(--system-background-secondary, #1d1e23);

  :global {
    .container {
      width: 1200px;
      margin: 0 auto;

      .top-content {
        display: flex;
        margin-bottom: 40px;

        .logo-container {
          width: 280px;
          margin-right: 80px;
        }

        .top-content-right {
          flex: 1;
          display: flex;
          gap: 40px;

          .list-container {
            flex: 1;

            .list-title {
              color: var(--Font-strong, #fff);
              /* Text-XL/Semibold */
              font-family: var(--font-poppins);
              font-size: 20px;
              font-style: normal;
              font-weight: 600;
              line-height: 30px;
              /* 150% */
              margin-bottom: 24px;
            }

            .list-item {
              display: block;
              color: var(--system-content-thirdary, #6b7a8f);
              /* Text-Base/Regular */
              font-family: var(--font-poppins);
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
              /* 142.857% */
              margin-bottom: 12px;

              &:hover {
                color: #E3EBF9;
              }

            }
          }
        }
      }

      .bottom-content {
        border-top: 1px solid var(--border-base, rgba(255, 255, 255, 0.1));
        padding: 16px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--system-content-secondary, #a3aebf);
        /* Text-Base/Regular */
        font-family: var(--font-poppins);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;

        /* 142.857% */
        .bottom-content-right {
          display: flex;
          align-items: center;
          gap: 8px;

          .separator {
            display: block;
            width: 1px;
            height: 100%;
            color: #2e2f32;
          }

          .custom-link {
            &:hover {
              color: #fff;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .footer {
    :global {
      .container {
        width: 100%;
        margin: 0;

        .top-content {
          flex-direction: column;

          .logo-container {
            margin-right: 0;
            margin-bottom: 20px;
          }

        }
      }
    }
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 40px 20px;

    :global {
      .container {
        .top-content {
          .top-content-right {
            flex-direction: column;
            gap: 32px;
          }
        }

        .bottom-content {
          flex-direction: column;
          align-items: flex-start;
        }
      }
    }
  }
}