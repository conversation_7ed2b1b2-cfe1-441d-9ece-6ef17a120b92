.header {
  display: flex;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  padding: 13px 24px;
  box-sizing: border-box;
  z-index: 100;

  :global {
    .header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-bottom: 1px solid rgba(229, 231, 235, 0.20);
      backdrop-filter: blur(30.399999618530273px);
      z-index: 1;
      opacity: 0;
    }

    .header-container {
      z-index: 2;
      display: flex;
      align-items: center;
      margin: 0 auto;
      width: 1200px;
      display: flex;
      align-items: center;
      box-sizing: border-box;

      .header-left {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 32px;
      }

      .header-right {
        margin-left: auto;
        display: flex;
        align-items: center;
        gap: 8px;

        .plans-link {
          color: var(--system-content-secondary, #A3AEBF);
          font-family: var(--font-poppins);
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 130%;
          /* 20.8px */
          padding: 8px 12px;
          box-sizing: border-box;

          &:hover {
            color: #fff;
          }

        }
      }

      .mobile-header-right {
        display: none;

        svg {
          width: 32px;
          height: 32px;
        }
      }
    }

  }
}

@media (max-width: 1200px) {
  .header {
    padding: 9px 16px;

    :global {
      .header-container {
        width: 100%;
        margin: 0;

        .header-right {
          display: none;
        }

        .header-center {
          display: none;
        }

        .mobile-header-right {
          display: block;
          margin-left: auto;
          cursor: pointer;
        }
      }
    }
  }
}