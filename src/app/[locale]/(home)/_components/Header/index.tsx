'use client';
import styles from './index.module.scss';
import Link from 'next/link';
import { LogoIcon } from "@/assets/icons";
import LoginBtn from '../LoginBtn';
import Nav from '../Nav';
import { useEffect } from 'react';
import { TextAlignmentJustifiedBold, CrossBlack } from '@meitu/candy-icons';
import { useI18n } from '@/locales/client';
import { useState } from 'react';
import MobileNav from '../Nav/mobile';
import { trackEvent } from '@/services/tracer';
const Header = () => {
  const t = useI18n();
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);

  useEffect(() => {
   const main = document.querySelector('#scroll-main');
   const headerBg = document.querySelector('.header-bg');
   if (main && headerBg) {
    main.addEventListener('scroll', () => {
      (headerBg as HTMLElement).style.opacity = `${main.scrollTop / 100}`;
    });
   }
  }, []);
  
  return <div className={styles.header}>
    <div className={'header-bg'}></div>
    <div className={'header-container'}>
    <div className={'header-left'}>
        <Link href="/"  onClick={() => {
          setIsMobileNavOpen(false);
          trackEvent('unlogin_home_page_top_click', {
            click_type: 'home',
          });
        }}>
          <LogoIcon/>
        </Link>
      </div>
      <div className={'header-center'}>
        <Nav />
      </div>
      <div className={'header-right'}>
      <Link href="/plans" className={'plans-link'} onClick={() => {
        trackEvent('unlogin_home_page_top_click', {
          click_type: 'plans',
        });
      }}>
         {t('Plans')}
        </Link>
        <LoginBtn onClick={() => {
          trackEvent('unlogin_home_page_top_click', {
            click_type: 'login/signup',
          });
        }}/>
      </div>
      <div className={'mobile-header-right'} onClick={() => setIsMobileNavOpen(!isMobileNavOpen)}>
      {isMobileNavOpen ? <CrossBlack /> : <TextAlignmentJustifiedBold />}
      </div>
      {isMobileNavOpen && <MobileNav onClose={() => setIsMobileNavOpen(false)}/>}
    </div>
  </div>;
};

export default Header;
