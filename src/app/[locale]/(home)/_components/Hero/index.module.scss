.hero {
  position: relative;
  width: 1200px;
  margin: 0 auto;
  padding-bottom: 42px;
  padding-top: calc(112px - 64px);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 52px;
  box-sizing: border-box;

  :global {
    .hero-bg {
      width: 1440px;
      height: 720px;
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      background-image: url("https://wheeai-public.stariidata.com/static/home/<USER>/hero_bg.webp");
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      z-index: 0;
    }

    .hero-left {
      flex: 1;
      min-width: 0;
      max-width: 516px;
      z-index: 1;

      .title {
        width: 100%;
        color: #fff;
        font-family: var(--font-poppins);
        font-size: 56px;
        font-style: normal;
        font-weight: 600;
        line-height: 110%;
        /* 61.6px */
        mix-blend-mode: overlay;
        background: linear-gradient(98deg, #00FFB2 14.37%, #FFF 40.78%, #FFB1F3 75.68%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 56px;
      }
    }

    .hero-right {
      flex: 1;
      z-index: 1;
      aspect-ratio: 1238 / 872;

      .hero-poster {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

@media (max-width: 1200px) {
  .hero {
    width: 682px;
    padding: 66px 16px 16px;
    flex-direction: column;
    gap: 32px;

    :global {
      .hero-left {
        max-width: 100%;
        // order: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 16px;
        box-sizing: border-box;

        .title {
          text-align: center;
        }
      }

      .hero-right {
        // order: 1;
      }
    }
  }
}

@media (max-width: 682px) {
  .hero {
    width: 100%;
    margin: 0;

    :global {
      .hero-left {
        .title {
          font-size: 40px;
        }
      }
    }
  }
}