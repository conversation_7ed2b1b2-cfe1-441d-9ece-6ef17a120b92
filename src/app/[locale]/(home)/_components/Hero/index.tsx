"use client";
import styles from "./index.module.scss";
import Image from "next/image";
import CreateButton from "../../(sub-pages)/[features]/_components/CreateButton";
import { AiSparkleBoldFill } from "@meitu/candy-icons";
import <PERSON><PERSON> from "lottie-react";
import loadingAnimation from "@/assets/lottie/home/<USER>/data.json";
import { useState } from "react";
import { trackEvent } from "@/services/tracer";
interface HeroProps {
  title: string;
  posterUrl: string;
}
const Hero = (params: HeroProps) => {
  const { title, posterUrl } = params;
  const [isLoaded, setIsLoaded] = useState(false);
  return (
    <section className={styles.hero}>
      <div className={"hero-left"}>
        {title && <h1 className="title">{title}</h1>}
        <div className={"hero-left-btn"}>
          <CreateButton type="primary" onClick={() => {
            trackEvent('unlogin_home_page_click', {
              click_type: 'start',
              location: 1,
              location_name: 'main'
            });
          }}>
            {" "}
            <AiSparkleBoldFill /> <span className="ml-1">Start Free Trial</span>
          </CreateButton>
        </div>
      </div>
      <div className={"hero-right"}>
        {/* <video
          preload="auto"
          src={videoUrl}
          autoPlay
          muted
          loop
          playsInline
          disablePictureInPicture
          poster={posterUrl}
        ></video> */}
      {!isLoaded && <img src={posterUrl} alt="hero-poster" className={styles["hero-poster"]}  />}
      <Lottie 
        style={{display: isLoaded ? 'block' : 'none'}} 
        animationData={loadingAnimation} 
        autoplay 
        loop 
        onLoadedImages={() => {
          setIsLoaded(true);
        }}
      />
      </div>
      <div className={"hero-bg"}></div>
      {/* <div className={"hero-title-bg"}></div> */}
    </section>
  );
};

export default Hero;
