"use client";
import styles from "./index.module.scss";
import { useI18n } from "@/locales/client";
import { wheeLoginPopup } from "@/utils/account";
import { useRouter } from "next/navigation";

const LoginBtn = (props: { className?: string; onClick?: () => void }) => {
  const t = useI18n();
  const router = useRouter();
  const handleClick = async () => {
    try {
      props.onClick?.();
      await wheeLoginPopup({
        useLoginMethod: "third-party",
        setUserStore: false,
      });
      location.href = `/workspace${location.search}`;
    } catch (error) {
      // console.log("error", error);
    }
  };
  return (
    <button
      className={`${styles["login-btn"]} ${props.className}`}
      onClick={handleClick}
    >
      <span>
        {t("Log in")} / {t("Sign up")}
      </span>
    </button>
  );
};

export default LoginBtn;
