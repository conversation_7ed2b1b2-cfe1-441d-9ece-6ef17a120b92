.nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;

  :global {
    .nav-item {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--system-content-secondary, #A3AEBF);
      font-size: 16px;
      font-family: var(--font-poppins);
      font-weight: 400;
      line-height: 22px;
      padding: 8px 12px;
      cursor: pointer;
      margin-right: 24px;
      border-radius: 10px;

      .nav-text {
        margin-right: 6px;
      }

      .nav-icon {
        color: rgba(163, 174, 191, 1);
        transition: transform 0.3s ease-in-out;
      }

      &:hover {
        color: #fff;
      }
    }

    .nav-item-active {
      color: #fff;

      // background: rgba(255, 255, 255, 0.20);
      .nav-icon {
        color: #fff;
        transform: rotate(180deg);
        transition: transform 0.3s ease-in-out;
      }
    }

    .sub-nav-wrapper {
      width: 100%;
      height: fit-content;
      position: absolute;
      top: 0px;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      border-bottom: 1px solid rgba(229, 231, 235, 0.09);
      padding-top: 96px;
      padding-bottom: 32px;
      box-sizing: border-box;
      // 添加了motion.div动画 往上移动了20px 不能溢出要隐藏
      clip-path: inset(20px 0px 0px 0px);

      .sub-nav-bg {
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.30);

      }

      .sub-nav-container {
        width: 1200px;
        height: auto;
        margin: 0 auto;
        display: flex;

        .sub-nav-group {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-right: 24px;
          margin-right: 32px;
          border-right: 1px solid rgba(229, 231, 235, 0.20);

          .sub-nav-item {
            font-size: 20px;
          }
        }

        .sub-nav-content {
          display: flex;
          flex-wrap: wrap;
          gap: 19px 48px;
        }

        .sub-nav-item {
          width: 200px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          width: 200px;
          height: 36px;
          color: var(--system-content-secondary, #a3aebf);
          /* text_16 */
          font-family: var(--font-poppins);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 130%;
          /* 20.8px */
          white-space: nowrap;

          &:hover {
            color: #fff;
          }
        }

        .sub-nav-item-active {
          color: #fff;
          width: 224px;

          &::before {
            content: "";
            display: block;
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
            margin-right: 12px;
          }
        }
      }
    }

    .sub-nav-hidden {
      visibility: hidden;
      pointer-events: none;
      z-index: -2;
      // 这保持元素在DOM中但不可见或不可交互
    }

    // SEO内容样式，对用户隐藏但对搜索引擎可见
    .seo-content {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border-width: 0;
      // 这种方式对搜索引擎可见，但对用户完全隐藏

      // 这里不使用display: none或visibility: hidden，因为那会被搜索引擎忽略
      // 注意：对于真实用户，这些内容是完全不可见的
    }
  }
}