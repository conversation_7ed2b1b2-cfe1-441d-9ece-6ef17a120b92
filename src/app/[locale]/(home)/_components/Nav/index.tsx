"use client";
import styles from "./index.module.scss";
import { ChevronDownBold } from "@meitu/candy-icons";
import classNames from "classnames";
import { useState } from "react";
import Link from "@/components/Link";
import { AnimatePresence, motion } from "framer-motion";
import useGetNavList from "./useGetNavList";
import { trackEvent } from "@/services/tracer";
const Nav = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [activeSubGroupIndex, setActiveSubGroupIndex] = useState<number>(0);
  const { navList } = useGetNavList();

  const handleClick = (index: number) => {
   setActiveIndex(activeIndex === index ? null : index);
   setActiveSubGroupIndex(0);

   if (activeIndex !== index) {
    trackEvent('unlogin_home_page_top_click', {
      click_type: navList[index]?.text?.toLowerCase()?.replace(/\s+/g, '_'),
    });
   }
  };

  const handleSubNavClick = (e: React.MouseEvent, index: number) => {
    e.stopPropagation();
    setActiveSubGroupIndex(activeSubGroupIndex === index ? 0 : index);
    trackEvent('unlogin_home_page_top_click', {
      click_type: navList[index]?.subGroup[activeSubGroupIndex]?.text?.toLowerCase()?.replace(/\s+/g, '_'),
    });
  };

  return (
    <ul className={styles.nav}>
      {navList.map((item, index) => (
        <li
          className={classNames({
            "nav-item": true,
            "nav-item-active": activeIndex === index,
          })}
          key={index}
          onMouseEnter={() => handleClick(index)}
          onMouseLeave={() => handleClick(index)}
          // onClick={() => handleClick(index)}
        >
          <span className="nav-text">{item.text}</span>
          <ChevronDownBold
            className={"nav-icon"}
          />
          <AnimatePresence>
            {activeIndex === index && (
              <motion.div
                className={"sub-nav-wrapper"}
                initial={{ opacity: 0, y: -20, backdropFilter: "blur(66px)" }}
                animate={{ opacity: 1, y: 0, backdropFilter: "blur(66px)" }}
                exit={{ opacity: 0, y: -20, backdropFilter: "blur(66px)" }}
                transition={{ duration: 0.3 }}
              >
                <div className="sub-nav-bg"></div>
                <div className={"sub-nav-container"}>
                  {index == 0 && (
                    <ul className={"sub-nav-group"}>  
                      {item.subGroup.map((subItem, subIndex) => (
                        <li
                          key={subIndex}
                          className={classNames({
                            "sub-nav-item": true,
                            "sub-nav-item-active": activeSubGroupIndex === subIndex,
                          })}
                          onClick={(e) => {
                            handleSubNavClick(e, subIndex);
                          }}
                        >
                          {subItem?.text}
                        </li>
                      ))}
                    </ul>
                  )}
                  <ul className={"sub-nav-content"}>
                    {item.subGroup[activeSubGroupIndex]?.subNav.map((subItem, subIndex) => (
                      <li
                        className={"sub-nav-item"}
                        key={subIndex}
                      >
                        <Link href={subItem.href} onClick={() => {
                          trackEvent('unlogin_home_page_top_click', {
                            click_type: subItem.text.toLowerCase().replace(/\s+/g, '_'),
                          });
                        }}>{subItem.text}</Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
         
        </li>
      ))}
    </ul>
  );
};

export default Nav;
