
.mobile-nav {
  display: none;
}

@media (max-width: 1200px) {
  .mobile-nav {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    padding-top: 52px;
    .mobile-nav-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.6);
      box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.30);
      backdrop-filter: blur(66px);
    }
    .mobile-nav-close {
      cursor: pointer;
      position: absolute;
      top: 9px;
      right: 16px;
      z-index: 2;
      svg {
        width: 32px;
        height: 32px;
      }
    }
    .mobile-nav-wrapper {
      position: relative;
      z-index: 1;
      padding: 32px;
      box-sizing: border-box;
      font-family: var(--font-poppins);
      .plans-link {
        display: block;
        width: 100%;
        padding-top: 32px;
        font-size: 20px;
        font-weight: 600;
      }
      .mobile-login-btn {
       padding: 16px 0;
        width: 100%;
      }
    }
    
  }
}
