import styles from './mobile.module.scss';
import useGetNavList from './useGetNavList';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordionTemp";
import LoginBtn from '../LoginBtn';
import Link from "@/components/Link";
import { trackEvent } from '@/services/tracer';

interface MobileNavProps {
  onClose: () => void;
}
const MobileNav = (props: MobileNavProps) => {
  const { navList } = useGetNavList();
  const { onClose } = props;
  return (
    <div className={styles['mobile-nav']}>
      <div className={styles['mobile-nav-wrapper']}>
        <LoginBtn className={styles['mobile-login-btn']} onClick={onClose}/>
        <Link href={'/plans'} className={styles['plans-link']} onClick={onClose}>Plans</Link>
        <Accordion type="multiple" className="w-full">
          {navList?.map((item, index) => (
            <AccordionItem key={index} value={`item-${index + 1}`} className="w-full">
              <AccordionTrigger className="pt-0 pb-0" onClick={() => {
                trackEvent('unlogin_home_page_top_click', {
                  click_type: item.text.toLowerCase().replace(/\s+/g, '_'),
                });
              }}>
                {item.text}
              </AccordionTrigger>
              <AccordionContent className="pb-0">
                {item.subGroup.map((subItem, subIndex) => (
                  <ul key={subIndex} className="pb-4">
                    <li className="text-white text-base font-normal font-['Inter'] leading-snug ">
                      {subItem.text}
                    </li>
                    <ul className="flex flex-col gap-6 cursor-pointer my-4">
                      {subItem.subNav.map((subItem, subIndex) => (
                        <li className="hover:text-white/80" key={subIndex}>
                          <Link onClick={() => {
                            onClose();
                            trackEvent('unlogin_home_page_top_click', {
                              click_type: subItem.text.toLowerCase().replace(/\s+/g, '_'),
                            });
                          }} href={subItem.href}>{subItem.text}</Link>
                        </li>
                    ))}
                  </ul>
                  </ul>
                ))}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
      <div className={styles['mobile-nav-bg']}></div>
    </div>
  );
};

export default MobileNav;