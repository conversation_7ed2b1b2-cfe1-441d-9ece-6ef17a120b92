import { useI18n } from "@/locales/client";

interface SubNavItem {
  text: string;
  href: string;
}

interface SubGroupItem {
  text?: string;
  subNav: SubNavItem[];
}

interface NavItem {
  text: string;
  subGroup: SubGroupItem[];
}

const useGetNavList = () => {
  const t = useI18n();
  const navList: NavItem[] = [
    {
      text: t("AI Tools"),
      subGroup: [
        {
          text: "AI Poster Generator",
          subNav: [
            {
              text: "AI Poster Design",
              href: "/ai-poster-design",
            },
            {
              text: "Infinite Canvas",
              href: "/infinite-canvas",
            },
          ],
        },
        {
          text: "AI Photo Editor",
          subNav: [
            {
              text: "Image Enhancer",
              href: "/image-enhancer",
            },
            {
              text: "Background Remover", 
              href: "/background-remover",
            },
            {
              text: "Object Remover",
              href: "/object-remover", 
            },
            {
              text: "Image Text Editor",
              href: "/image-text-editor",
            },
            {
              text: "Image Editor",
              href: "/image-editor",
            },
          ],
        },
      ],
     
    },
    {
      text: t("Templates"),
      subGroup: [ 
       {
        subNav: [
          {
            text: "Marketing Campaigns",
            href: "/marketing-campaigns",
          },
          {
            text: "Social Media Content",
            href: "/social-media-content",
          },
          {
            text: "Event Promotion",
            href: "/festival-promotion",
          },
          {
            text: "Culture and Art",
            href: "/culture-and-art",
          },
          
         ]
       }
      ],
    },
    {
      text: t("Resources"),
      subGroup: [
        {
          subNav: [
            {
              text: "Tutorials",
              href: "/tutorials",
            },
          ],
        }
      ]
    },
  ];
  return { navList };
};

export default useGetNavList;
