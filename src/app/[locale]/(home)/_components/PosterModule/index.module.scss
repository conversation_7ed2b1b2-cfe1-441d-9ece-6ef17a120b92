.poster-module {
  width: 100%;
  height: 332px;
  border: 2px solid transparent;
  border-radius: 8px;
  background-image: url("https://wheeai-public.stariidata.com/static/home/<USER>/poster-bg.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  margin-top: 61px;
  .poster-container {
    margin: 0 auto;
    width: 1200px;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .poster-btn-pc {
      display: inline-flex;
    }
    .poster-btn-mobile {
      display: none;
    }
    .poster-btn {
      .poster-btn-text {
        min-width: 78px;
        text-align: center;
        font-weight: 600;
      }
      .icon-wrapper {
        width: 22px;
        height: 22px;
        border-radius: var(--radius-8, 8px);
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        svg {
          width: 14px;
          height: 14px;
          color: #fff;
        }
      }
    }

    .poster-left {
      max-width: 543px;
      padding-top: 54px;
      box-sizing: border-box;
      .poster-left-title {
        margin-bottom: 24px;
        h2 {
          font-family: var(--font-poppins);
          font-size: 54px;
          font-style: normal;
          font-weight: 600;
          line-height: 100%; /* 54px */
          text-transform: uppercase;
          position: relative;
        }
      }
      .cursor-select {
        width: 401px;
        position: absolute;
        top: 0;
        left: 66px;
        background: rgba(83, 246, 180, 0.2);
        .cursor-icon {
          width: fit-content;
          height: 46px;
          display: flex;
          flex-direction: column;
          align-items: center;
          .cursor-circle {
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background-color: #56f69e;
          }
          .cursor-line {
            width: 1px;
            height: 41px;
            background-color: #56f69e;
          }
        }
      }
      
    }
    .poster-right {
      position: relative;
      display: block;
    }
    .image-green-wrapper {
      position: absolute;
      bottom: -30px;
      right: 180px;
      z-index: 3;
      transform: rotate(-25deg);
      width: 180px;
      height: 240px;
      .image-1 {
        max-width:fit-content;
        width: 184px;
        height: 240px;
        border-radius: var(--radius-16, 16px);
        border: 1px solid #fff;
        box-shadow: 0px 4px 46.5px 0px rgba(0, 0, 0, 0.13);
        position: absolute;
        top: 0;
        left: 4px;
      }
      .border-green {
        max-width:fit-content;
        position: absolute;
        top: -4px;
        left: 0;
        width: 193px;
        height: 240px;
      }
    }
   
    .image-bg-wrapper {
      width: 496px;
      height: 100%;
      overflow: hidden;
      position: relative;
      .image-3 {
        max-width:fit-content;
        width: 496px;
        height: 321px;
        position: absolute;
        right: 0;
        bottom: -61px;
        z-index: 1;
      }
    }
    .image-2 {
      position: absolute;
      z-index: 2;
      top: -35px;
      right: 224px;
      max-width:fit-content;
      width: 183px;
      height: 230px;
      border-radius: var(--radius-16, 16px);
      border: 1px solid #fff;
      box-shadow: 0px 4px 46.5px 0px rgba(0, 0, 0, 0.13);
      transform: rotate(-60.566deg);
    }
    .border-purple {
      max-width:fit-content;
      position: absolute;
      top: -40px;
      right: 221px;
      z-index: 4;
      width: 193px;
      height: 240px;
      transform: rotate(-60.566deg);
    }
    .cursor {
      position: absolute;
      top: 150px;
      left: -10px;
      width: 34px;
      height: 37px;
      z-index: 2;
      
    }
  }
}
@media (max-width: 1200px) {
  .poster-module {
    .poster-container {
      width: 100%;
      .poster-left {
       padding-left: 30px;
      }
    }
  }
}

@media (max-width: 1000px) {
  .poster-module {
    height: auto;
    overflow: hidden;
    background-image: url("https://wheeai-public.stariidata.com/static/home/<USER>/m-poster-bg.png");
    .poster-container {
      width: 100%;
      
      flex-direction: column;
      align-items: center;
      .poster-right {
        height: 400px;
        .image-bg-wrapper {
         
          .image-3 {
            width: 100%;
            height: auto;
            bottom: 0;
          }
        }
      }
      .poster-left {
        .poster-left-title {
          h2 {
            text-align: center;
          }
          padding-bottom: 50px;
        }
       .poster-btn-pc {
        display: none;
       }
      }
      .poster-btn-mobile {
        display: inline-flex;
        margin-top: 50px;
        margin-bottom: 40px;
       }
    }
  }
}

