/* eslint-disable @next/next/no-img-element */
"use client";
import styles from "./index.module.scss";
import CreateButton from "../../(sub-pages)/[features]/_components/CreateButton";
import { ChevronRightBlack } from "@meitu/candy-icons";
import { trackEvent } from "@/services/tracer";
// https://wheeai-public.stariidata.com/static/home/<USER>/border1.png
// https://wheeai-public.stariidata.com/static/home/<USER>/border2.png

const PosterModule = () => {
  const handleClick = () => {
    trackEvent('unlogin_home_page_click', {
      click_type: 'start',
      location: 5,
      location_name: 'footer',
    });
  }
  return (
    <div className={styles["poster-module"]}>
      <div className={styles["poster-container"]}>
        <div className={styles["poster-left"]}>
          <div className={styles["poster-left-title"]}>
            <h2>
              AI Poster Maker Generate, Edit, Done!
              {/* <div className={styles["cursor-select"]}>
                <div className={styles["cursor-icon"]}>
                  <div className={styles["cursor-circle"]}></div>
                  <div className={styles["cursor-line"]}></div>
                </div>
              </div> */}
            </h2>
          </div>
          <CreateButton
            className={`${styles["poster-btn"]} ${styles["poster-btn-pc"]}`}
            onClick={handleClick}
          >
            <div className={styles["poster-btn-text"]}>Start</div>
            <div className={styles["icon-wrapper"]}>
              <ChevronRightBlack />
            </div>
          </CreateButton>
        </div>
        <div className={styles["poster-right"]}>
          <img
            className={styles["image-2"]}
            src="https://wheeai-public.stariidata.com/static/home/<USER>/image2.png"
            alt="poster-image"
            loading="lazy"
          />
          <img
            className={styles["border-purple"]}
            src="https://wheeai-public.stariidata.com/static/home/<USER>/border1.png"
            alt="poster-image"
            loading="lazy"
          />
          <div className={styles["image-bg-wrapper"]}>
            <div className={styles["image-green-wrapper"]}>
              <img
                className={styles["image-1"]}
                src="https://wheeai-public.stariidata.com/static/home/<USER>/image1.png"
                alt="poster-image"
                loading="lazy"
              />
              <img
                className={styles["border-green"]}
                src="https://wheeai-public.stariidata.com/static/home/<USER>/border2.png"
                alt="poster-image"
                loading="lazy"
              />
            </div>
            <img
              className={styles["image-3"]}
              src="https://wheeai-public.stariidata.com/static/home/<USER>/image3.png"
              alt="poster-image"
              loading="lazy"
            />
          </div>
        </div>
        <CreateButton
          className={`${styles["poster-btn"]} ${styles["poster-btn-mobile"]}`}
          onClick={handleClick}
        >
          <div className={styles["poster-btn-text"]}>Start</div>
          <div className={styles["icon-wrapper"]}>
            <ChevronRightBlack />
          </div>
        </CreateButton>
      </div>
    </div>
  );
};

export default PosterModule;
