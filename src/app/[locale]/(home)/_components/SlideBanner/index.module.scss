@keyframes slide-translate {
  0% {
    // transform: translateY(-100%);
    height: 0;
    opacity: 0;
  }

  25% {
    // transform: translateY(0);
    height: 150px;
    opacity: 1;
  }

  75% {
    // transform: translateY(0);
    height: 150px;
    opacity: 1;
  }

  90% {
    height: 65px;
    opacity: 1;
  }

  100% {
    // transform: translateY(-100%);
    height: 65px;
    display: none;
    opacity: 0;
  }
}

.slide-banner {
  width: 100%;
  height: 150px;
  border-radius: var(--radius-0, 0px);
  // background: url('../TopBanner/background.jpg') no-repeat center center;
  // background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  transform-origin: top;
  animation: slide-translate 4s ease-in-out forwards;
  background: rgba(245, 245, 245, 0.8); // 添加默认背景色
  overflow: hidden;

  &.loading {

    // 加载时的渐变动画
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(90deg,
          rgba(255, 255, 255, 0.1) 25%,
          rgba(255, 255, 255, 0.2) 37%,
          rgba(255, 255, 255, 0.1) 63%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite linear;
    }
  }

  .background-wrapper {
    position: absolute;
    inset: 0;
    z-index: 0;
  }

  .background-image {
    opacity: 0;
    transition: all 0.5s ease-in-out;
    filter: blur(20px);

    &.loaded {
      opacity: 1;
      filter: blur(0);
    }
  }

  .banner-close {
    position: absolute;
    top: 12px;
    right: 12px;

    svg {
      width: 16px;
      height: 16px;
      color: #000;
      cursor: pointer;
    }
  }

  .banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 721px;
    padding: 0 40px;

    .banner-text {
      position: relative;

      h1 {
        color: var(--system-content-onPrimary, #181818);
        font-family: Poppins;
        font-size: 28px;
        font-style: normal;
        font-weight: 600;
        line-height: 110%;
      }

      .banner-button {
        margin-top: 12px;
        border-radius: 8px;
        background: #000;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 84px;
        height: 26px;
        box-sizing: border-box;
        gap: 8px;
        color: #F7F8FA;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
        cursor: pointer;

        svg {
          color: #fff;
        }
      }
    }

    .image {
      position: relative;
      width: 289px;
      height: 150px;

      .slide-image {
        opacity: 0;
        transition: all 0.5s ease-in-out;
        filter: blur(10px);

        &.loaded {
          opacity: 1;
          filter: blur(0);
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@media screen and (max-width: 450px) {
  .slide-banner {
    .banner-content {
      width: 100%;
      padding: 0 20px;

      .banner-text {
        h1 {
          font-size: 20px; // 从28px缩小
          line-height: 120%;
        }

        .banner-button {
          margin-top: 8px; // 减小间距
          min-width: 70px;
          width: 70px;
          height: 24px;
          font-size: 11px;
        }
      }

      .image {
        width: 200px; // 减小图片宽度
        height: 104px; // 等比例缩小高度
      }
    }
  }
}