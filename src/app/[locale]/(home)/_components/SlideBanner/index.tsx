"use client";

import { ChevronRightBlack, CrossBlack } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import Image from "next/image";
import backgroundImg from "../TopBanner/background.jpg";
import slidePic from "./slide-pic.png";
import { useState } from "react";
import classNames from "classnames";

interface SlideBannerProps {
  onClose?: () => void;
}

const SlideBanner = ({ onClose }: SlideBannerProps) => {
  const [bgLoading, setBgLoading] = useState(true);
  const [slideLoading, setSlideLoading] = useState(true);

  return (
    <div
      className={classNames(styles["slide-banner"], {
        [styles["loading"]]: bgLoading,
      })}
    >
      <div className={styles["background-wrapper"]}>
        <Image
          src={backgroundImg}
          alt="background"
          fill
          priority
          quality={100}
          onLoadingComplete={() => setBgLoading(false)}
          className={classNames(styles["background-image"], {
            [styles.loaded]: !bgLoading,
          })}
          placeholder="blur"
          blurDataURL={backgroundImg.blurDataURL}
          unoptimized
        />
      </div>
      <div
        className={styles["banner-close"]}
        onClick={(e) => {
          e.stopPropagation();
          onClose?.();
        }}
      >
        <CrossBlack />
      </div>
      <div className={styles["banner-content"]}>
        <div className={styles["banner-text"]}>
          <h1>“AI Poster Maker </h1>
          <h1>Generate , Edit, Done !”</h1>
          <div className={styles["banner-button"]}>
            <span>Join now</span>
            <ChevronRightBlack />
          </div>
        </div>
        <div className={styles["image"]}>
          <Image
            src={slidePic}
            alt="slide"
            fill
            quality={100}
            onLoadingComplete={() => setSlideLoading(false)}
            className={classNames(styles["slide-image"], {
              [styles.loaded]: !slideLoading,
            })}
            placeholder="blur"
            blurDataURL={slidePic.blurDataURL}
            unoptimized
          />
        </div>
      </div>
    </div>
  );
};

export default SlideBanner;
