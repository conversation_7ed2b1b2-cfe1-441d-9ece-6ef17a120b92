.templates {
  width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  .templates-card {
    flex: 1;
    height: 207px;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.10);
    background: #1D1E23;
    padding: 24px;
    box-sizing: border-box;
    cursor: pointer;
    overflow: hidden;
    position: relative;

    .templates-card-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 16px;
      background: #1D1E23;
      opacity: 0;
      transition: all 0.3s ease-in-out;
      z-index: 0;
    }
   
    .title {
      color: #FFF;
      font-size: 20px;
      font-weight: 600;
      line-height: 130%; /* 26px */
      margin-bottom: 4px;
      z-index: 1;
    }
    .description {
      color: #FFF;
      font-size: 14px;
      font-weight: 500;
      line-height: 130%; /* 18.2px */
      opacity: 0.8;
      z-index: 1;
    } 
    .image-list {
      position: absolute;
      right: 26px;
      bottom: -41px;
      display: flex;
      z-index: 1;
      .image-item {
        transition: all 0.2s ease-in-out;
        &:nth-child(1) {
          transform: rotate(-8deg) translate(70px, 37px);
        }
        &:nth-child(2) {
          transform: rotate(-4deg) translate(30px, 15px);
        }
        &:nth-child(3) {
          transform: translate(0, 0);
        }
      }
    }
    &:hover {
      // .title {
      //   color: #002413; 
      // }
      // .description {
      //   color: #002413;
      // }
      .image-list {
        .image-item {
          &:nth-child(1) {
            transform: rotate(-12deg) translate(70px, 17px);
          }
          &:nth-child(2) {
            transform: rotate(-8deg) translate(30px, -5px);
          }
          &:nth-child(3) {
            transform: translate(0, -20px);
          }
        }
      }
      border-bottom: 0;
    }
    &:nth-child(1) {
      &:hover {
        .title,
        .description {
          color: #002413; 
        }
        .templates-card-bg {
          opacity: 1;
          background: linear-gradient(0deg, #56F69E 0%, #56F69E 100%), linear-gradient(180deg, #56F69E 0%, #FFF 94.44%), #111113;
        }
      }
    }
    &:nth-child(2) {
      &:hover {
        .title,
        .description {
          color: #7E004B; 
        }
        .templates-card-bg {
          opacity: 1;
          background: linear-gradient(0deg, #FBB3FF 0%, #FBB3FF 100%), linear-gradient(180deg, #FBB3FF 0%, #FDDCFF 63.5%), #111113;
        }
      }
    }
    &:nth-child(3) {
      &:hover {
        .title,
        .description {
          color: #00375E; 
        }
        .templates-card-bg {
          opacity: 1;
          background: linear-gradient(0deg, #44C1FF 0%, #44C1FF 100%), linear-gradient(180deg, #18B2FE 0%, #FFF 100%);
        }
      }
    }
    &:nth-child(4) {
      &:hover {
        .title,
        .description {
          color: #5C0C78; 
        }
        .templates-card-bg {
          opacity: 1;
          background: linear-gradient(0deg, #D199FF 0%, #D199FF 100%), linear-gradient(180deg, #B459FF 0%, #FFF 100%);
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .templates {
    width: 100%;
    padding: 0 64px;
    box-sizing: border-box;
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 768px) {
  .templates {
    padding: 0 16px;
    gap: 4px;
  }
}
