'use client'
import styles from "./index.module.scss";
import Image from "next/image";
import Link from '@/components/Link'
import { trackEvent } from "@/services/tracer";

const Templates = () => {
  const templates = [
    {
      title: "Marketing Campaigns",
      description: "Choose from a variety of templates to get started",
      imageList: [
        "https://wheeai-public.stariidata.com/static/home/<USER>/image1.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image2.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image3.png",
      ],
      url: '/marketing-campaigns',
      clickType: 'marketing_campaign',
    },
    {
      title: "Social Media Content",
      description: "Boost your content to get more likes on   social media",
      imageList: [
        "https://wheeai-public.stariidata.com/static/home/<USER>/image4.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image5.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image6.png",
      ],
      url:'/social-media-content',
      clickType: 'social_media_content',
    },
    {
      title: "Event Promotion",
      description: "Easily create posters for your events",
      imageList: [
        "https://wheeai-public.stariidata.com/static/home/<USER>/image7.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image8.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image9.png",
      ],
      url: '/festival-promotion',
      clickType: 'event_promotion',
    },
    {
      title: "Culture and Art",
      description: "Creating artistic posters is just a   fingertip away",
      imageList: [
        "https://wheeai-public.stariidata.com/static/home/<USER>/image10.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image11.png",
        "https://wheeai-public.stariidata.com/static/home/<USER>/image12.png",
      ],
      url: '/culture-and-art',
      clickType: 'culture_and_art',
    },
    
  ];
  return (
    <div className={styles.templates}>
      {templates.map((item, index) => (
        <Link href={item.url} className={styles["templates-card"]} key={index} onClick={() => {
          trackEvent('unlogin_home_page_click', {
            click_type: item.clickType,
          });
        }}>
          <div className={styles["templates-card-bg"]}></div>
          <div className={styles["title"]}>{item.title}</div>
          <div className={styles["description"]}>
              {item.description}
          </div>
          <div className={styles["image-list"]}>
            {item.imageList.map((image, index) => (
              <Image
              unoptimized
              key={index}
              className={styles["image-item"]}
              src={image}
              alt="templates"
              width={84}
              height={110}
              />
              
            ))}
          </div>
        </Link>
      ))}
    </div>
  );
};

export default Templates;
