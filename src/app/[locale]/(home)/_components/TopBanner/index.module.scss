@keyframes top-translate {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }

  25% {
    transform: translateY(-100%);
    opacity: 0;
  }

  75% {
    transform: translateY(0);
    opacity: 0;
  }

  90% {
    transform: translateY(0);
    opacity: 1;
  }


  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.top-banner {
  width: 100%;
  height: 65px;
  border-radius: var(--radius-0, 0px);
  background: url('./background.jpg') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
  animation: top-translate 4s ease-in-out forwards;
  transform-origin: top;

  .banner-close {
    position: absolute;
    top: 12px;
    right: 12px;

    svg {
      width: 16px;
      height: 16px;
      color: #000;
      cursor: pointer;
    }
  }

  .banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 721px;
    padding: 0 40px;

    .banner-text {
      h3 {
        color: var(--system-content-onPrimary, #181818);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
      }

      p {
        color: var(--system-content-onPrimary, #181818);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 130%;

        span {
          text-decoration-line: underline;
          text-decoration-style: solid;
          text-decoration-skip-ink: auto;
          text-decoration-thickness: auto;
          text-underline-offset: auto;
          text-underline-position: from-font;
        }
      }
    }

    .banner-button {
      border-radius: 8px;
      background: #000;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 84px;
      height: 26px;
      min-width: 84px;
      box-sizing: border-box;
      gap: 8px;
      color: #F7F8FA;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      cursor: pointer;

      svg {
        color: #fff;
      }
    }
  }
}

@media screen and (max-width: 450px) {
  .top-banner {
    .banner-content {
      width: 100%;
      padding: 0 30px;

      .banner-text {
        h3 {
          font-size: 12px;
          line-height: 20px;
        }

        p {
          font-size: 12px;
          line-height: 120%;
        }
      }

      .banner-button {
        min-width: 70px;
        width: 70px;
        height: 24px;
        font-size: 11px;
      }
    }
  }
}