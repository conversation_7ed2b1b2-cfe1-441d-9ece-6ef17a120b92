"use client";

import { ChevronRightBlack, CrossBlack } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import "./modal.scss";
import { useStore } from "@/contexts/StoreContext";
import { trackEvent } from "@meitu/subscribe-intl";

interface TopBannerProps {
  onClose?: () => void;
}

const TopBanner = ({ onClose }: TopBannerProps) => {
  const { modalStore } = useStore();

  // Campaign details
  const detailList = [
    {
      title: "🎁 Campaign Details | Win a $240 Premium Pass!",
      desc: [
        "Log in to WheeAI during the event period and get a chance to win a Premium Annual Pass worth $240!",
      ],
    },
    {
      title: "📅 Event Duration",
      desc: ["May 30, 2025 – August 30, 2025"],
    },
    {
      title: "🎯 How to Participate",
      desc: [
        "· This campaign is open to users who are not currently logged in.",
        "· Tap the top banner or event popup and complete your login (or sign up & log in) to automatically enter the draw.",
      ],
    },
    {
      title: "🎁 Prizes",
      desc: [
        "· 🎉 10× Premium Annual Pass ($240 value) — Ten lucky winner will be selected after the campaign ends.",
        "· ✨ 100 Welcome Credits — Instantly awarded to all participants upon first login.",
      ],
    },
    {
      title: "📣 Winner Announcement",
      desc: [
        "· The winner will be notified via  email within 3 working days after the campaign ends.",
        "· If you do not win, don’t worry — more events are coming soon!",
      ],
    },
    {
      title: "📦 Redemption Rules",
      desc: [
        "· The Premium Pass will be automatically activated on the winner’s Whee account after confirmation.",
        "· The 100 welcome credits will be delivered instantly to your account after your first login.",
      ],
    },
    {
      title: "❗ Important Notes",
      desc: [
        "· Each account is eligible to participate once only.",
        "· Please ensure your email address is valid to receive prize notifications.",
        "· WheeAI reserves the right to disqualify users who are found to be abusing the system (e.g., fake accounts, bot activity).",
      ],
    },
    {
      title: "🛡️ Disclaimer",
      desc: [
        "· WheeAI reserves the right of final interpretation for this campaign.",
        "· The campaign may be modified or terminated based on unforeseen circumstances, with advance notice on the platform.",
        "· This campaign does not constitute any legally binding commitment or obligation.",
      ],
    },
  ];

  // Open modal
  const openModel = () => {
    trackEvent("unlogin_homepage_general_banner_click", {
      click_type: "event_detail",
    });
    modalStore.openModalImmediately({
      modalProps: {
        width: 568,
        centered: true,
        destroyOnClose: true,
        maskClosable: false,
        closable: false,
        footer: null,
        mask: true,
        classNames: {
          wrapper: "top-banner-detail-modal",
          mask: "top-banner-detail-modal-mask",
        },
      },
      renderContent: ({ onClose }) => {
        return (
          <div className="content-box">
            <div
              className="close-box"
              onClick={() => {
                onClose();
              }}
            >
              <CrossBlack />
            </div>
            <div className="bottom-mask"></div>
            <div className="content">
              {detailList.map((item, index) => (
                <div className="content-item" key={index}>
                  <h3>{item.title}</h3>
                  {item.desc.map((desc, descIndex) => (
                    <p key={descIndex}>{desc}</p>
                  ))}
                </div>
              ))}
            </div>
          </div>
        );
      },
    });
  };

  return (
    <div className={styles["top-banner"]}>
      <div
        className={styles["banner-close"]}
        onClick={(e) => {
          e.stopPropagation();
          onClose?.();
        }}
      >
        <CrossBlack />
      </div>
      <div className={styles["banner-content"]}>
        <div className={styles["banner-text"]}>
          <h3>🎁 Welcome to Whee AI!</h3>
          <p>
            Log in for a chance to win $240 Premium! 🚀 Limited time only! ｜
            <span
              onClick={(e) => {
                e.stopPropagation();
                openModel();
              }}
            >{`Event Details >`}</span>
          </p>
        </div>
        <div className={styles["banner-button"]}>
          <span>Join now</span>
          <ChevronRightBlack />
        </div>
      </div>
    </div>
  );
};
export default TopBanner;
