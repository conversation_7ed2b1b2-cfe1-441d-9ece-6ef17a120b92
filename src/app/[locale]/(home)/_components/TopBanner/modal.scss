.top-banner-detail-modal {

  .ant-modal {
    .ant-modal-content {
      padding: 24px;
      border-radius: var(--radi-xl, 16px);
      border: 1px solid var(--system-stroke-input, #22272E);
      background: var(--system-background-secondary, #1D1E23);
      box-shadow: 0px 4px 50px 0px var(--shadow-op2, rgba(33, 33, 33, 0.08)), 0px 4px 6px 0px var(--shadow-op1, rgba(33, 33, 33, 0.04));

      .ant-modal-body {
        .content-box {
          position: relative;

          .close-box {
            position: absolute;
            top: 0px;
            right: 0px;
            cursor: pointer;

            svg {
              width: 20px;
              height: 20px;
              color: #6B7A8F
            }
          }

          .bottom-mask {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 32px;
            background: linear-gradient(178deg, rgba(29, 30, 35, 0.00) 1.63%, #1D1E23 98.48%);
          }

          .content {
            width: 98%;
            height: 453px;
            overflow-y: auto;
            scrollbar-width: none;

            &::-webkit-scrollbar {
              display: none;
            }

            .content-item {
              margin-bottom: 16px;

              h3 {
                color: var(--system-content-primary, #FFF);
                font-family: var(--fontFamilies-primary, Inter);
                font-size: var(--fontSize-lg, 18px);
                font-style: normal;
                font-weight: 700;
                line-height: var(--lineHeights-xxl, 26px);
                letter-spacing: var(--letterSpacing-xsm, -0.2px);
                margin-bottom: 12px;
              }

              p {
                overflow: hidden;
                color: var(--system-content-secondary, #A3AEBF);
                text-overflow: ellipsis;
                font-family: Inter;
                font-size: var(--fontSize-sm, 14px);
                font-style: normal;
                font-weight: 400;
                line-height: var(--lineHeights-md, 20px);
                letter-spacing: var(--letterSpacing-md, 0px);
              }
            }
          }
        }
      }
    }
  }
}

.top-banner-detail-modal-mask {
  background: rgba(0, 0, 0, 0.70);
  backdrop-filter: blur(32px);
}