@keyframes box-height {
  0% {
    height: 0;
  }

  25% {
    height: 150px;
  }

  75% {
    height: 150px;
  }

  90% {
    height: 65px;
  }

  100% {
    height: 65px;
  }
}

.main {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  overflow: auto;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .banner-box {
    height: 150px;
    position: relative;
    z-index: 0;
    animation: box-height 4s ease-in-out forwards;
    transform-origin: top;
    will-change: height;
  }
}