"use client";

import styles from "./index.module.scss";
import Header from "./_components/Header";
import Footer from "./_components/Footer";
import "./global.scss";
import TopBanner from "./_components/TopBanner";
import SlideBanner from "./_components/SlideBanner";
import { wheeLoginPopup } from "@/utils/account";
import { useEffect, useState } from "react";
import { trackEvent } from "@meitu/subscribe-intl";

const BANNER_HIDE_KEY = "banner-hide-timestamp";
const HIDE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数

const HomeLayout = ({ children }: { children: React.ReactNode }) => {
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // 检查是否应该显示 banner
    const hideTimestamp = localStorage.getItem(BANNER_HIDE_KEY);
    if (!hideTimestamp || Date.now() - Number(hideTimestamp) > HIDE_DURATION) {
      setShowBanner(true);

      // 避免埋点没有初始化
      setTimeout(() => {
        trackEvent("unlogin_homepage_general_banner_expo");
      }, 1000);
    }
  }, []);

  // 吊起登陆
  const handleClick = async () => {
    trackEvent("unlogin_homepage_general_banner_click", {
      click_type: "join_now",
    });
    try {
      await wheeLoginPopup({
        useLoginMethod: "third-party",
        setUserStore: false,
      });
      location.href = `/workspace${location.search}`;
    } catch (error) {
      // console.log("error", error);
    }
  };

  // 处理关闭 banner
  const handleCloseBanner = () => {
    trackEvent("unlogin_homepage_general_banner_click", {
      click_type: "close",
    });
    localStorage.setItem(BANNER_HIDE_KEY, Date.now().toString());
    setShowBanner(false);
  };

  return (
    <main className={styles["main"]} id="scroll-main">
      {showBanner && (
        <div className={styles["banner-box"]} onClick={handleClick}>
          <TopBanner onClose={handleCloseBanner} />
          <SlideBanner onClose={handleCloseBanner} />
        </div>
      )}
      <>
        <Header />
        {children}
      </>
      <Footer />
    </main>
  );
};

export default HomeLayout;
