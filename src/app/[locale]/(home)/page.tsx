'use client'
import "./global.scss";
import Hero from "./_components/Hero";
import FeatureModule from "./_components/FeatureModule";
import TemplatesModule from "./_components/TemplatesModule";
import CarouselModule from "./_components/CarouselModule";
import PosterModule from "./_components/PosterModule";
import Head from "next/head";
import { IsLogin, usePageStart } from "@/hooks/useTrack";
export default function Home() {
  usePageStart('home', IsLogin.notLogin);
  return (
    <>
      <Head>
        <link rel="preload" href="/images/home/<USER>" as="image" />
      </Head>
      <Hero
        title={`AI Poster Maker Generate, Edit, Done!`}
        posterUrl={"/images/home/<USER>"}
      />
      <TemplatesModule />
      <CarouselModule
        title="Functional Advantages"
        data={[
          {
            title: "✨ Image Generation",
            videoUrl: "/video/home/<USER>",
          },
          {
            title: "🌈 Text Generation",
            videoUrl: "/video/home/<USER>",
          },
          {
            title: "🖌️AI Tools",
            videoUrl: "/video/home/<USER>",
          },
          {
            title: "🌌 Infinite Canvas",
            videoUrl: "/video/home/<USER>",
          },
        ]}
      />
      <FeatureModule
        title="AI Tools"
        description="Generate high-quality images in seconds with our AI tools and add a unique touch to your brand."
        childrenData={[
          {
            title: "Image Enhancer",
            description: "Enhance image   quality and enrich image details",
            btnText: "Start",
            videoUrl: "https://wheeai-public.stariidata.com/static/enhancer.mp4",
            titleIcon: "https://wheeai-public.stariidata.com/static/home/<USER>/enhancer.png",
            url: "/image-enhancer",
            clickType: 'image_enhancer',
          },
          {
            title: "Background Remover",
            description: "Remove background for a clear and   complete subject",
            btnText: "Start",
            videoUrl: "https://wheeai-public.stariidata.com/static/cut.mp4",
            titleIcon: "https://wheeai-public.stariidata.com/static/home/<USER>/cut.png",
            url: "/background-remover",
            clickType: 'background_remover',
          },
          {
            title: "Object Remover",
            description: "Remove any parts   that you don't need",
            btnText: "Start",
            videoUrl: "https://wheeai-public.stariidata.com/static/remove.mp4",
            titleIcon: "https://wheeai-public.stariidata.com/static/home/<USER>/remove.png",
            url: "/object-remover",
            clickType: 'object_remover',
          },
        ]}
      />
      <PosterModule />
    </>
  );
}
