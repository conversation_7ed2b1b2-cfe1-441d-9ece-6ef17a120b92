import { Form, InputNumber } from "antd";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/canvas/_store";
import { useEffect, useRef, useState } from "react";
import styles from "./index.module.scss";
import { defaultMinZoom } from "@meitu/whee-infinite-canvas";
import { disabledKeyboardScale } from "@/app/[locale]/canvas/_utils/disabledScreenScale";
type FormValue = {
  zoom: number;
};

function Zoom() {
  const { renderStore } = useRootStore();

  const [form] = Form.useForm<FormValue>();
  const zoomInput = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const render = renderStore.render;
    const renderHotkey = renderStore.renderHotkey;
    if (!render) {
      return;
    }

    const zoom = render._FC.getZoom();
    form.setFieldsValue({
      zoom: Math.round(zoom * 100),
    });

    const handleZoomChange = (event: { zoom: number }) => {
      form.setFieldsValue({
        zoom: Math.round(event.zoom * 100),
      });
    };

    render._FC.on("viewport:zoom", handleZoomChange);

    return () => {
      render._FC.off("viewport:zoom", handleZoomChange);
    };
  }, [renderStore.render]);

  const handleFinish = ({ zoom }: FormValue) => {
    const render = renderStore.render;

    if (!render) {
      return;
    }

    render.setZoom(zoom / 100);
    zoomInput.current?.blur();
  };

  return (
    <Form form={form} onFinish={handleFinish}>
      <Form.Item noStyle name="zoom">
        <InputNumber
          className={styles["zoom-input"]}
          controls={false}
          suffix="%"
          min={defaultMinZoom}
          max={2000}
          onChange={(value) => {
            // console.log('value', value);
          }}
          type="number"
          step={1}
          ref={zoomInput}
          onBlur={() => {
            const value = form.getFieldValue("zoom");
            form.setFieldsValue({
              zoom:
                value < defaultMinZoom
                  ? defaultMinZoom
                  : value > 2000
                  ? 2000
                  : value,
            });
            form.submit();
          }}
          onKeyDown={(e) => {
            e.stopPropagation();
            e.nativeEvent.stopImmediatePropagation();
            disabledKeyboardScale(e as unknown as KeyboardEvent);
          }}
        />
      </Form.Item>
    </Form>
  );
}

export default observer(Zoom);
