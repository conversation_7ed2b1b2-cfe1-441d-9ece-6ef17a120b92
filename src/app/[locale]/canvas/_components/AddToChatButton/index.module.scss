@import "@/styles/variable.scss";
@import "../zIndex.scss";


.btn {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  height: 39px;
  border-radius: 10px;
  background: $system-background-thirdary;
  user-select: none;
  font-size: 14px;
  color: $system-content-secondary;
  z-index: $z-index-add-to-chat-btn;

  :global {

    .btn-text {
      flex: 0 0 auto;
      font-size: 14px;
      flex: 0 0 auto;
    }

    .btn-shortcut {
      flex: 0 0 auto;
      margin-left: 5px;
      vertical-align: middle;
      &-key {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px * 1.1;
        border: 1px solid $system-stroke-button;
        background: $system-content-fifth;
        border-radius: 6px;
        vertical-align: middle;
      }

      &-plus {
        color: $system-content-thirdary;
        margin: 0 6px;
      }
    }
  }
}