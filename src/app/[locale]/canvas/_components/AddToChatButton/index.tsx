import { observer } from "mobx-react-lite";
import styles from './index.module.scss';
import { useRootStore } from "../../_store";
import { useMemo } from "react";
import { useFollowTargets } from "../../_hooks/useFollowTargets";
import { HeaderAction } from "../../_store/headerAction";
import { useAddSelectedImagesToChat } from "../../_hooks/useAddSelectedImagesToChat";
import { ElementName, getElementOptions } from "@meitu/whee-infinite-canvas";

type AddToChatButtonProps = {
  // 与图片的距离 
  offset?: number;

  // 通过边距定义展示范围 [top, right, bottom, left]
  rangePadding?: [number, number, number, number];
}
function AddToChatButton({
  offset = 16,
  rangePadding = [0, 400, 0, 300],
}: AddToChatButtonProps) {

  const { selectionStore, headerActionStore, renderStore } = useRootStore();
  const activeObjects = selectionStore.activeObjects;


  const { addToChat, selectedImages } = useAddSelectedImagesToChat();
  const { elementRef, translate } = useFollowTargets<HTMLButtonElement>({ targets: activeObjects, offset, rangePadding });

  if (headerActionStore.activeHeaderAction !== HeaderAction.Cursor) {
    return null;
  }

  if (!selectedImages.length) {
    return null;
  }

  return (
    <button className={styles.btn}
      style={{
        transform: `translate(${translate.x}px, ${translate.y}px)`,
      }}
      onClick={addToChat}
      ref={elementRef}
    >
      <span className="btn-text">Add to Chat</span>
      <span className="btn-shortcut">
        <span className="btn-shortcut-key">⌘</span>
        <span className="btn-shortcut-plus">+</span>
        <span className="btn-shortcut-key">↵</span>
      </span>
    </button>
  );
}

export default observer(AddToChatButton);