@import '@/styles/variable.scss';

$remove-controller-height: 26px;
$remove-controller-margin-bottom: 12px;
$record-item-border-radius: 8px;
$tabs-height:52px;

.historyBox {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: $system-background-secondary;
  z-index: 3;

  .topBox {
    height: 52px;
    border-bottom: 1px solid $system-stroke-input-default;
    background: $system-background-secondary;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;

    h3 {
      color: $system-content-primary;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%;
    }

    svg {
      width: 17.5px;
      height: 17.5px;
      color: $icon-color-secondary;
      cursor: pointer;
    }
  }
}


.history {
  box-sizing: border-box;
  width: 100%;
  height: calc(100vh - $header-height - $tabs-height);
  padding: 16px;
  overflow: hidden;

  :global {
    .remove-mode-btn {
      display: flex;
      align-items: center;
      height: $remove-controller-height;
      color: $system-content-thirdary;
      margin-bottom: $remove-controller-margin-bottom;
      cursor: pointer;
      user-select: none;

      &-label {
        font-size: 12px;
      }

      &-icon {
        font-size: 14px;
        margin-left: 4px;
      }
    }

    .remove-controller {
      font-size: 12px;
      color: $system-content-thirdary;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: $remove-controller-height;
      color: $system-content-thirdary;
      margin-bottom: $remove-controller-margin-bottom;
      cursor: default;

      &-selected {
        color: $system-content-brand-primary;
      }

      &-btns {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        &-item {
          cursor: pointer;
          user-select: none;

          &.ok {
            float: left;
            margin-right: 12px;
          }

          &.cancel {
            float: right;
          }
        }
      }
    }

    .record-list {
      width: 100%;
      height: 100%;
      overflow-x: hidden;
      overflow: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .infinite-scroll {


        &-content {
          display: grid;
          grid-template-columns: repeat(3, 117px);
          grid-auto-rows: 117px;
          row-gap: 12px;
          column-gap: 8px;

          .record-item {
            width: 100%;
            height: 100%;
            border-radius: $record-item-border-radius;
            position: relative;
            overflow: hidden;
            user-select: none;
            z-index: 0;

            .history-item-loading {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              background: $background-black-30;
              backdrop-filter: blur(10px);
              border-radius: 6px;
              overflow: hidden;
              display: flex;
              justify-content: center;
              align-items: center;

              .loading-box {
                width: 18px;
                height: 18px;
              }
            }

            &.loading {
              .loading {
                border-radius: 8px;
                box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
              }

              cursor: not-allowed;
            }

            &.success {
              .ant-image {
                width: 100%;
                height: 100%;
                border-radius: $record-item-border-radius;
                box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);

                &>img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  position: relative;
                  z-index: -1;
                }
              }

              :local {
                .hover-mask {
                  cursor: pointer;
                }
              }
            }

            &.error {
              .error-content {
                color: $system-content-thirdary;
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border-radius: $record-item-border-radius;
                box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);

                &-icon {
                  margin-bottom: 6px;

                  svg {
                    width: 28px;
                    height: 28px;
                  }
                }

                &-tips {
                  text-align: center;
                  font-size: 12px;
                  line-height: 16px;
                }
              }

              :local {
                .hover-mask {
                  cursor: not-allowed;
                }
              }
            }

            &.success,
            &.error {
              cursor: pointer;
            }

            &:hover {
              :local {
                .hover-mask {
                  opacity: 1;
                }
              }
            }
          }
        }

        &-end {
          margin: 16px auto;
          color: $system-content-thirdary;
          text-align: center;
          font-family: Inter;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 130%;
        }
      }
    }

    .empty-container {
      width: 100%;
      height: calc(100% - 76px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: $system-content-tertiary;
    }
  }
}

.hover-mask,
.selected-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.selected-mask {
  box-shadow: inset 0 0 0 2px $system-content-brand-primary;
  border-radius: $record-item-border-radius;

  :global {
    .selected-mask-corner {
      width: 26px;
      height: 24px;
      background: $system-content-brand-primary;
      position: absolute;
      right: 0;
      bottom: 0;
      border-radius: 10px 0 8px 0;
      display: flex;
      justify-content: center;
      align-items: center;

      &-icon {
        svg {
          width: 12px;
          height: 12px;
          color: $system-content-primary;
        }
      }
    }
  }
}

.hover-mask {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;

  :global {
    .popover-box {
      position: absolute;
      bottom: 4.3px;
      right: 4.3px;

      .dropdown-trigger {
        cursor: pointer;
        width: 22px;
        height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-6, 6px);
        background: $background-editor-popup-hover;
        color: $system-content-thirdary;
      }
    }

    .remove-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 20px;
      position: absolute;
      top: 4px;
      right: 4px;
      border-radius: 4px;
      background: $background-black-25;
      box-shadow: 0 0 0 1px $border-white-25;
      cursor: pointer;

      svg {
        width: 12px;
        height: 12px;
        color: #fff;
      }

      &:hover {
        background: $background-black-30;
      }

      &:active {
        background: $background-black-25;
      }
    }
  }
}

.remove-modal:global(.ant-modal) {
  :global {
    .ant-modal-content {
      padding: 24px 16px 16px;
      height: auto;

      .ant-modal-footer {
        margin: 0;
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;

    :global {
      .content-icon {
        color: $system-content-secondary;

        svg {
          width: 54px;
          height: 54px;
        }
      }

      h1 {
        margin-top: 12px;
        font-size: 16px;
      }

      p {
        font-size: 14px;
        color: $system-content-tertiary;
        text-align: center;
      }

      .content-btns {
        width: 100%;
        display: flex;
        justify-content: space-between;

        &>button {
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 0 0 auto;
          width: 128px;
          height: 36px;
          border-radius: 8px;
        }
      }
    }
  }
}