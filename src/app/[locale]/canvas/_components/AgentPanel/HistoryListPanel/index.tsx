import { useRootStore } from "@/app/[locale]/canvas/_store";
import { observer } from "mobx-react";
import React, { Fragment, useState } from "react";
import {
  CheckBlack,
  CrossBold,
  EllipsisVerticalBlack,
  TrashCan,
} from "@meitu/candy-icons";
import { useMount } from "react-use";
import InfiniteScroll from "react-infinite-scroll-component";
import { Image } from "antd";
import { toAtlasImageView2URL } from "@meitu/util";
import classNames from "classnames";
import { AIPoster, AlphaStatus } from "@/api/types/aiPoster/task";
import RemoveModal, { RemoveItem } from "./RemoveModal";
import { getImageEditStatus } from "@/app/[locale]/canvas/_utils/imageEdit";
import { createImage } from "@meitu/whee-infinite-canvas";
import { useSaveCanvas } from "@/app/[locale]/canvas/_hooks/useSaveProjectHistory";
import styles from "./index.module.scss";
import { Loading } from "@/components";
import { useI18n } from "@/locales/client";
import Lottie from "lottie-react";
import loadingAnimation from "@/assets/lottie/poster/imageLoading/data.json";
import DropdownPopover from "@/app/[locale]/workspace/_components/DropdownPopover";
import { downloadFile } from "@/utils/blob";

type HistoryListPanelType = {
  close: () => void;
};

function HistoryListPanel({ close }: HistoryListPanelType) {
  const { generateRecordStore, renderStore } = useRootStore();
  const t = useI18n();
  const rootStore = useRootStore();
  const { submitSaveElements } = useSaveCanvas(rootStore);
  const [loadingImages, setLoadingImages] = useState<Map<string, boolean>>(
    new Map()
  );

  // 删除弹窗
  const [removeModalProps, setRemoveModalProps] = useState({
    open: false,
    removeItems: [] as Array<RemoveItem>,
  });

  useMount(() => {
    generateRecordStore.fetchHistory({ enterStatus: "loading" });
  });

  const tasksList = generateRecordStore.list;
  const fetchNext = () => {
    // generateRecordStore.fetchHistory({ enterStatus: 'updating' });
  };

  const renderList = () => {
    return (
      <InfiniteScroll
        scrollableTarget="poster-records-scroll-container"
        className="infinite-scroll"
        dataLength={tasksList.length}
        next={fetchNext}
        hasMore={!tasksList.length}
        loader={<Loading />}
        endMessage={<div className="infinite-scroll-end">No more data</div>}
      >
        <div className="infinite-scroll-content">
          {tasksList.map((task) => {
            // 有透明通道的参数的不展示，替换完之后再展示
            const results = task.resultImages.filter(
              (item) => item.alphaStatus !== AlphaStatus.HasAlpha
            );

            return results.map((r, index) => {
              const key =
                `t${task.id}` + (r.urlShort ? `u${r.urlShort}` : `i${index}`);

              if (task.loadingStatus === AIPoster.LoadingStatus.Loading) {
                return (
                  <div key={key} className="record-item loading">
                    <Loading />
                  </div>
                );
              }

              const item: RemoveItem = { task, image: r };

              const handleRemove = () => {
                setRemoveModalProps({
                  open: true,
                  removeItems: [item],
                });
              };

              const handleDownload = () => {
                downloadFile(r.urlSign);
              };

              if (
                task.loadingStatus !== AIPoster.LoadingStatus.Success ||
                r.imageStatus !== AIPoster.ImageStatus.Success
              ) {
                return null;
              }

              const previewUrl =
                r.urlSign &&
                toAtlasImageView2URL(r.urlSign, {
                  mode: 2,
                  width: 107 * window.devicePixelRatio,
                  height: 105 * window.devicePixelRatio,
                });

              const handleInsertEditor = () => {
                if (!renderStore.render || loadingImages.get(r.urlSign)) {
                  return;
                }

                // 记录插入图片
                setLoadingImages((prev) => new Map(prev).set(r.urlSign, true));

                const render = renderStore.render;
                const historyPlugins = renderStore.historyPlugins;
                createImage("", {
                  src: r.urlSign,
                  _custom_data_history_: {
                    ...getImageEditStatus(task.taskCategory),
                    // params: transformResponseToImageParams(task.params),
                    msgId: task.id,
                    urlShort: r.urlShort,
                  },
                })
                  .catch(() => {
                    // openRefreshModal();
                    return null;
                  })
                  .then((group) => {
                    if (!group) {
                      return;
                    }
                    render.addToViewPortCenterByArrangement(group);
                    render._FC.setActiveObject(group);
                    render.backToOriginPosition({ target: group });
                    render._FC.requestRenderAll();
                    const operation =
                      historyPlugins?.baseAction.getAddOperation({
                        objects: [group],
                      });
                    if (operation) {
                      historyPlugins?.submit(operation);
                    }
                    submitSaveElements([group]);
                  })
                  .finally(() => {
                    // 插入完成后 取消加载状态
                    setLoadingImages((prev) =>
                      new Map(prev).set(r.urlSign, false)
                    );
                  });
              };

              return (
                <RemovableItem
                  key={key}
                  className="record-item success"
                  onRemove={handleRemove}
                  onDownload={handleDownload}
                  onClick={handleInsertEditor}
                >
                  <Image
                    src={toAtlasImageView2URL(previewUrl, {
                      mode: "0" as any,
                      width: 107 * window.devicePixelRatio,
                      height: 107 * window.devicePixelRatio,
                    })}
                    placeholder={<Loading />}
                    alt=""
                    preview={false}
                    loading="lazy"
                  />

                  {loadingImages.get(r.urlSign) === true && (
                    <div className={"history-item-loading"}>
                      <Lottie
                        className={"loading-box"}
                        animationData={loadingAnimation}
                        autoplay
                        loop
                      />
                    </div>
                  )}
                </RemovableItem>
              );
            });
          })}
        </div>
      </InfiniteScroll>
    );
  };

  //#region 不同状态的渲染函数
  const renderLoading = () => {
    return <div className="empty-container">{/* <Loading /> */}</div>;
  };
  const renderLoaded = () => {
    return (
      <Fragment>
        <div className="record-list" id="poster-records-scroll-container">
          {renderList()}
        </div>
      </Fragment>
    );
  };
  const renderLoadedEmpty = () => {
    return (
      <div className="empty-container">{t("Sorry, nothing to see here.")}</div>
    );
  };
  const renderError = () => {
    return (
      <div className="empty-container">{t("Sorry, nothing to see here.")}</div>
    );
  };
  //#endregion

  return (
    <Fragment>
      <div className={styles.historyBox}>
        <div className={styles.topBox}>
          <h3>Generated Files</h3>
          <CrossBold onClick={close} />
        </div>
        <div className={styles.contentBox}></div>
        <div className={styles.history}>
          {(generateRecordStore.status === "init" ||
            generateRecordStore.status === "loading") &&
            renderLoading()}

          {(generateRecordStore.status === "loaded" ||
            generateRecordStore.status === "updating") &&
            (generateRecordStore.successImagesCount
              ? renderLoaded()
              : renderLoadedEmpty())}

          {generateRecordStore.status === "error" && renderError()}
        </div>
      </div>

      <RemoveModal
        {...removeModalProps}
        onCancel={() => {
          setRemoveModalProps({ open: false, removeItems: [] });
        }}
        onFinish={() => {
          setRemoveModalProps({ open: false, removeItems: [] });
        }}
      />
    </Fragment>
  );
}

type HoverMaskProps = React.PropsWithChildren<{
  className?: string;
}>;
function HoverMask({ children, className }: HoverMaskProps) {
  return (
    <div className={classNames(styles["hover-mask"], className)}>
      {children}
    </div>
  );
}

type RemovableItemProps = React.PropsWithChildren<{
  className?: string;
  /**
   * 在非批量删除时 点击触发的事件
   */
  onClick?: () => any;
  /**
   * 单独删除
   */
  onRemove?: () => any;
  onDownload?: () => any;
}>;
function RemovableItem({
  children,
  className,
  onRemove,
  onDownload,
  onClick,
}: RemovableItemProps) {
  return (
    <div
      className={className}
      onClick={() => {
        onClick?.();
      }}
    >
      {children}

      <HoverMask>
        <div
          className="popover-box"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <DropdownPopover
            trigger={["hover"]}
            placement="bottomLeft"
            items={[
              {
                label: "Download",
                key: "download",
                onClick: () => {
                  // 下载
                  onDownload?.();
                },
              },
              {
                label: "Remove from list",
                key: "remove",
                onClick: () => {
                  onRemove?.();
                },
              },
            ]}
          >
            <div className="dropdown-trigger">
              <EllipsisVerticalBlack />
            </div>
          </DropdownPopover>
        </div>
        {/* <div
          className="remove-btn"
          onClick={(e) => {
            e.stopPropagation();
            e.nativeEvent.stopImmediatePropagation();
            // console.log('on remove');
            onRemove?.();
          }}
        >
          <TrashCan />
        </div> */}
      </HoverMask>
    </div>
  );
}

export default observer(HistoryListPanel);
