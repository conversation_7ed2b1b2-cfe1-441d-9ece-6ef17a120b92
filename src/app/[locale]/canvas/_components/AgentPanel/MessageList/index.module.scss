/* 消息通用样式 */
.message-role-content {
  margin-bottom: 16px;
}

.message {
  margin-bottom: 8px;
  display: flex;

}

.message-content {
  max-width: 100%;
  padding: 12px;
  line-height: 1.5;
  font-size: 15px;
  position: relative;
  word-wrap: break-word;
  color: #FFF;
  border-radius: var(--radius-12, 12px);
  border: 1px solid var(--system-stroke-button, #323B48);
  background: var(--system-background-thirdary, #272C33);
}

/* 用户消息 */
.message.user {
  justify-content: flex-end;

}

.message.user .message-content {
  max-width: 82.5%;
  border-radius: 12px;
  border: none;
  background: var(--system-content-agentMessage-userPrompt, #89FDCE);
  color: var(--system-content-onPrimary, #181818);
  white-space: pre-wrap;
}

/* AI消息 */
.message.assistant {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.message.assistant .message-content {
  border-radius: 12px;
  color: #FFF;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

/* 流式光标 */
.streaming-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: #666;
  vertical-align: baseline;
  margin-left: 2px;
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

.avator-container {
  display: flex;
  margin-bottom: 16px;

  .avator-icon-cover {
    background-color: #fff;
    overflow: hidden;
    border-radius: 50%;
    margin-right: 8px;
  }

  .logo-text {
    color: #FFF;
    font-size: 13px;
    font-style: normal;
    font-weight: 600;
    line-height: 160%;
    /* 20.8px */

  }
}

.media-container {
  display: flex;

  .media-image {
    width: 130px;
    height: 130px;
    object-fit: cover;
    border-radius: 12px;

    &.media-image-single {
      width: 200px;
      height: 200px;
    }
  }

  &>*:not(:last-child) {
    margin-right: 8px;
  }

  .image-cover {
    position: relative;
    min-width: 84px;
    max-width: 200px;
    min-height: 84px;
    max-height: 200px;
    overflow: hidden;
    border-radius: 12px;

    .video-play {
      display: inline-block;
      width: 24px;
      height: 24px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      // background-image: url(./play.svg);
      background-size: contain;
    }
  }
}