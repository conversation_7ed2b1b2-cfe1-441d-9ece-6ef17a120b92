import { observer } from "mobx-react";
import { Message, UserMessage } from "../types";
import classNames from "classnames";
import styles from "./index.module.scss";
import { Image } from "antd";
import { Fragment } from "react";
import AiAvatarText from "./mod/AiAvatarText";
import Markdown from "./mod/MarkDown";
import ImageAnalyzer from "./mod/ImageAnalyzer";
import SmartPlan from "./mod/SmartPlan";
import RequestCard from "./mod/RequestCard";
import Knowledge from "./mod/Knowledge";
import Design from "./mod/Design";

type MessageListProps = {
  messageList: (Message | UserMessage)[][];
  isBeginCard: boolean;
};
const MessageList = ({ messageList, isBeginCard }: MessageListProps) => {
  // 渲染用户消息
  const renderUserMessage = (userMsg: UserMessage, index: number) => {
    const hasBottom =
      (userMsg.imageList?.length || userMsg.videoList?.length) &&
      !!userMsg.content.trim();
    return (
      <div
        key={`${userMsg.id || "user"}-${index}`}
        className={classNames(styles.message, styles.user)}
      >
        <div className={styles["message-content"]}>
          {userMsg.imageList?.length ? (
            <div
              className={styles["media-container"]}
              style={{ marginBottom: hasBottom ? "12px" : "0px" }}
            >
              {userMsg.imageList.map((item, imgIdx) => (
                <Image
                  className={classNames(
                    styles["media-image"],
                    userMsg.imageList?.length === 1 &&
                      styles["media-image-single"]
                  )}
                  key={item.url || imgIdx}
                  src={item.url}
                  alt=""
                  preview={!item.isHidePreview}
                />
              ))}
            </div>
          ) : null}
          {userMsg.content ? (
            <div className={styles["user-text"]}>{userMsg.content}</div>
          ) : null}
        </div>
      </div>
    );
  };
  // 渲染助手消息
  const renderAssistantMessage = (
    assistantMsg: Message,
    index: number,
    hiddenIcon?: boolean
  ) => {
    return (
      <div
        key={`${assistantMsg.id || "assistant"}-${index}`}
        className={classNames(styles.message, styles.assistant)}
      >
        {index === 1 && !hiddenIcon ? (
          <AiAvatarText
            // style={{ marginTop: "8px" }}
            status={`${assistantMsg.isHistory ? "default" : "ending"}`}
          />
        ) : null}
        {/* 第一个loading */}
        {assistantMsg.type === "simple_text" ? (
          <AiAvatarText
            content={assistantMsg.content}
            status={assistantMsg.status}
          />
        ) : (
          <div
            className={styles["message-content"]}
            style={{
              background: `${
                assistantMsg.type === "pay_confirm_text" ? "transparent" : ""
              }`,
            }}
          >
            {renderCard(assistantMsg)}
          </div>
        )}
      </div>
    );
  };
  // 渲染不同类型的卡片
  const renderCard = (assistantMsg: Message) => {
    switch (assistantMsg.type) {
      case "text":
      case "error":
        return <Markdown content={assistantMsg.content} />;
      case "image_analyzer":
        return (
          <ImageAnalyzer
            title={assistantMsg.title || ""}
            content={assistantMsg.content}
            isHistory={!!assistantMsg?.isHistory}
          />
        );
      case "make_plan":
        return (
          <SmartPlan
            title={assistantMsg.title || ""}
            steps={assistantMsg.content_steps || []}
          />
        );
      case "design":
        return (
          <Design
            title={assistantMsg.title || ""}
            mediasInfo={assistantMsg.mediasInfo}
            taskId={assistantMsg.task_id}
            isHistory={!!assistantMsg?.isHistory}
          />
        );
      case "request":
        return (
          <RequestCard
            title={assistantMsg.title || ""}
            content={assistantMsg.question}
            subTitle={assistantMsg?.content_title || ""}
          />
        );
      case "knowledge":
        return (
          <Knowledge
            title={assistantMsg.title || ""}
            subTitle={assistantMsg?.content_title || ""}
            desc={assistantMsg.desc}
            keywords={assistantMsg.keywords}
            isHistory={!!assistantMsg?.isHistory}
          />
        );
      default:
        return <span />;
    }
  };
  // 是否隐藏头像
  const shouldHiddenAvatar = (msg: any[]) => {
    if (isBeginCard) {
      const mustWithAvatar = msg?.length && msg[1]?.mustWithAvatar;
      if (mustWithAvatar) return false;
      if (!mustWithAvatar && msg?.length <= 2) return true;
    }
    return false;
  };
  return (
    <>
      {messageList?.map((group, groupIdx) => (
        <Fragment key={group?.[0]?.id || `group-${groupIdx}`}>
          {(group || []).map((messageItem, index) => {
            const hiddenIcon = shouldHiddenAvatar(group);
            return (
              <div key={`${messageItem.id || messageItem.role}-${index}`}>
                {messageItem.role === "user"
                  ? renderUserMessage(messageItem as UserMessage, index)
                  : renderAssistantMessage(
                      messageItem as Message,
                      index,
                      hiddenIcon
                    )}
              </div>
            );
          })}
        </Fragment>
      ))}
    </>
  );
};

export default observer(MessageList);
