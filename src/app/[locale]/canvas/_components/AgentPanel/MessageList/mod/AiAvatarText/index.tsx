import React, { memo } from "react";
import styles from "./index.module.scss";
import { LogoSimpleIcon } from "@/assets/icons";

interface IProps {
  content?: string;
  status: "loading" | "ending" | "default";
  style?: React.CSSProperties;
}

function AiAvatar(props: IProps) {
  const { content = "Whee AI", status, style } = props;
  return (
    <div className={styles["avator-container"]} style={style}>
      <div className={styles["avator-icon-cover"]}>
        <LogoSimpleIcon />
      </div>
      <div className={styles["logo-text"]}>{content}</div>
    </div>
  );
}
export default memo(AiAvatar);
