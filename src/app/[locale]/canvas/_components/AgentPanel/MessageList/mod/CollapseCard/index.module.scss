.collapse-container {
    display: flex;
    flex-direction: column;


    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 12px;

        .title-area {
            display: flex;
            align-items: center;
        }

        .expand-icon {
            display: flex;
            align-items: center;
            transition: transform 0.3s;

            &-expand {
                transform: rotate(-180deg);
            }
        }

        .card-logo-cover {
            margin-right: 8px;
        }

        .title {
            color: #FFF;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 160%;
            /* 25.6px */
        }
    }

    .collapse-filter {
        height: 60px;
        position: absolute;
        bottom: 0px;
        left: 0;
        right: 0;
        background: linear-gradient(180deg, rgba(39, 39, 43, 0.00) 0%, #27272B 75%);
        backdrop-filter: blur(5px);
        border-bottom-right-radius: 20px;
        border-bottom-left-radius: 20px;
    }

}