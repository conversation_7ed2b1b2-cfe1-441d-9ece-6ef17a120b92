import React, { memo, useState, useRef, useEffect } from "react";
import Image from "next/image";
import classNames from "classnames";
import { useSize } from "ahooks";
import analyzerIcon from "./analyzer.svg";
import planIcon from "./plan.svg";
import requestIcon from "./message.svg";
import knowledgeIcon from "./knowledge.svg";
import styles from "./index.module.scss";
import { ChevronUpBlack } from "@meitu/candy-icons";

interface IProps {
  title: string;
  iconKey: "imageAnalyzer" | "smartPlan" | "request" | "knowledge";
  titleColor: React.CSSProperties["color"];
  needExpand?: boolean;
  defaultExpanded?: boolean;
}

function CollapseCard(props: React.PropsWithChildren<IProps>) {
  const {
    title = "",
    titleColor,
    iconKey,
    children,
    needExpand = true,
    defaultExpanded = false,
  } = props;

  const [isExpand, setIsExpand] = useState(defaultExpanded);
  const contentRef = useRef<HTMLDivElement>(null);
  const contentSize = useSize(contentRef);
  const [containerHeight, setContainerHeight] = useState<string | number>(
    "auto"
  );

  const iconMap = {
    imageAnalyzer: analyzerIcon,
    smartPlan: planIcon,
    request: requestIcon,
    knowledge: knowledgeIcon,
  };

  useEffect(() => {
    if (!isExpand) {
      setContainerHeight(200); // Collapsed height (header only)
      return;
    }

    if (contentSize?.height) {
      setContainerHeight(40 + contentSize.height); // Header + content height
    } else {
      setContainerHeight(200); // Fallback for initial render
    }
  }, [isExpand, contentSize?.height]);

  const renderIcon = () => {
    return (
      <div className={styles["card-logo-cover"]}>
        <Image src={iconMap[iconKey]} width={20} height={20} alt="analyzer" />
      </div>
    );
  };

  return (
    <div
      className={styles["collapse-container"]}
      style={{ height: !needExpand ? " auto" : containerHeight }}
    >
      <div className={styles.header}>
        <div className={styles["title-area"]}>
          {renderIcon()}
          <div className={styles.title} style={{ color: titleColor }}>
            {title}
          </div>
        </div>
        {needExpand && (
          <div
            onClick={() => {
              setIsExpand(!isExpand);
            }}
            className={classNames(styles["expand-icon"], {
              [styles["expand-icon-expand"]]: !isExpand,
            })}
          >
            <ChevronUpBlack />
          </div>
        )}
      </div>
      <div style={{ overflow: "hidden" }} ref={contentRef}>
        {children}
      </div>
      {!isExpand && needExpand && <div className={styles["collapse-filter"]} />}
    </div>
  );
}
export default memo(CollapseCard);
