.reboneo-steps {
    display: flex;
    flex-direction: column;
    // overflow: hidden;

    &-item {
        display: block;
        flex: 1 0 auto;
        padding-inline-start: 0;
        overflow: visible;


        &-container {
            outline: none;
            display: flex;
            box-sizing: border-box;
            position: relative;



            .reboneo-steps-item-tail {
                display: block;
                position: absolute;
                inset-inline-start: 10px;
                top: 0;
                width: 1px;
                height: 100%;
                padding: 28px 0 6px;

                &::before {
                    display: inline-block;
                    background-color: #F8AB78;
                    ;
                    width: 1px;
                    height: 100%;
                    content: '';
                }
            }



            .reboneo-steps-item-icon {
                flex-shrink: 0;
                border-radius: 50%;
                width: 22px;
                margin-right: 6px;
                float: left;
                height: 22px;
                background-image: url(./checkBoxMark.svg);
                background-repeat: no-repeat;
                background-position: center;
                line-height: 22px;
                margin-bottom: 6px;
            }

            .reboneo-steps-item-content {
                display: flex;
                flex-direction: column;

                .reboneo-steps-item-name {
                    color: #F8AB78;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 160%;
                    margin-bottom: 6px;


                    /* 22.4px */
                }

                .reboneo-steps-item-description {

                    color: #FFF;
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 160%;
                    padding-bottom: 6px;

                    /* 20.8px */
                }

            }

        }

    }

}