import React from "react";
import styles from "./index.module.scss";

export interface StepItem {
  name: string;
  description: string;
}
interface Iprops {
  steps: StepItem[];
}

export default function CustomSteps({ steps = [] }: Iprops) {
  return (
    <div className={styles["reboneo-steps"]}>
      {steps.map((item: StepItem, index) => (
        <div key={item.name} className={styles["reboneo-steps-item"]}>
          <div className={styles["reboneo-steps-item-container"]}>
            <div className={styles["reboneo-steps-item-tail"]} />
            <div className={styles["reboneo-steps-item-icon"]} />
            <div className={styles["reboneo-steps-item-content"]}>
              <div className={styles["reboneo-steps-item-name"]}>
                {item.name}
              </div>
              <div className={styles["reboneo-steps-item-description"]}>
                {item.description}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
