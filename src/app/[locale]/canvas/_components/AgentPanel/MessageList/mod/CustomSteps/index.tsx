import React from "react";
import styles from "./index.module.scss";

export interface StepItem {
  name: string;
  description: string;
}
interface Iprops {
  steps: StepItem[];
}

export default function CustomSteps({ steps = [] }: Iprops) {
  return (
    <div className={styles["content-steps"]}>
      {steps.map((item: StepItem, index) => (
        <div key={item.name} className={styles["content-steps-item"]}>
          <div className={styles["content-steps-item-container"]}>
            <div className={styles["content-steps-item-tail"]} />
            <div className={styles["content-steps-item-icon"]} />
            <div className={styles["content-steps-item-content"]}>
              <div className={styles["content-steps-item-name"]}>
                {item.name}
              </div>
              <div className={styles["content-steps-item-description"]}>
                {item.description}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
