/* 全局样式或模块CSS中 */
.animation-container {
  position: relative;
  width: 164px;
  height: 216px;
  /* 全屏高度 */
  overflow: hidden;
  background-color: #000;
  /* 可选背景色 */
  border-radius: 6px;
}

.animation-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* 或 cover，根据需求调整 */
  transition: opacity 0.5s ease-in-out;
  // border-radius: 6px;
}

/* 第一张动画的特定样式 */
.first-animation {
  z-index: 10;
  animation: fadeIn 0.5s forwards;
  // border-radius: 6px;
}

/* 第二张动画的特定样式 */
.loading-animation {
  z-index: 5;
  animation: pulse 2s infinite ease-in-out;
  // border-radius: 6px;
}

/* 内容区域的样式 */
.content-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  /* 内容背景色 */
  animation: fadeIn 0.5s forwards;
}

.progress-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  color: #FFF;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%;
  /* 19.2px */
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0, 0, 0, 0.8);
  z-index: 10;
  max-width: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.remain-time {
  margin-bottom: 4px;
  color: #FFF;
  font-size: 12px;
  font-weight: 400;
  line-height: 160%;
  height: 20px;
  max-width: 150px;
  /* 19.2px */
}