"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import bootomTop from "./bottomToTop.webp";
import loadingIcon from "./loading.webp";
import styles from "./index.module.scss";

export default function DualWebPAnimation(props: {
  total: number;
  index: number;
  percent: number;
  remainTime: string;
}) {
  const { percent = 0, remainTime = "", total, index } = props;
  const [showFirstAnimation, setShowFirstAnimation] = useState(true);
  const [firstAnimationCompleted, setFirstAnimationCompleted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(
      () => {
        setShowFirstAnimation(false);
        setFirstAnimationCompleted(true);
      },

      300
    );

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      {" "}
      <div className={styles["remain-time"]}> {remainTime}</div>{" "}
      <div
        className={styles["animation-container"]}
        style={{
          marginTop: index === 1 && total >= 2 ? "20px" : "0",
        }}
      >
        {" "}
        {/* 第一张动画 - 只播放一次 */}
        {showFirstAnimation && (
          <div className={styles["first-animation"]}>
            {" "}
            <Image
              src={bootomTop}
              alt="Initial Animation"
              width={150}
              height={250}
              unoptimized
              priority
              className={styles["animation-image"]}
              onLoadingComplete={() => console.log("First animation loaded")}
            />{" "}
            <div className={styles["progress-indicator"]}> {percent}%</div>{" "}
            <div> {remainTime}</div>{" "}
          </div>
        )}
        {/* 第二张动画 - 循环播放直到加载完成 */}
        {(!showFirstAnimation || firstAnimationCompleted) && (
          <div className={styles["loading-animation"]}>
            {" "}
            <Image
              src={loadingIcon}
              alt="Loading Animation"
              width={150}
              height={200}
              unoptimized
              priority
              className={styles["animation-image"]}
            />{" "}
            <div className={styles["progress-indicator"]}>
              {" "}
              <div className={styles["percentshow"]}> {percent}%</div>{" "}
            </div>{" "}
          </div>
        )}
        {/* 加载完成后的内容 */}
        {/* {!isLoading && firstAnimationCompleted && (
        <div className={styles["content-area"]}>
          <h1>内容加载完成!</h1>
        </div>
      )} */}
      </div>{" "}
    </>
  );
}
