.design-description-area {
    display: flex;
    align-items: center;

    margin-bottom: 12px;
    margin-top: 12px;

    .title {
        margin-left: 6px;
        color: #d4f878;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 160%;
        /* 22.4px */
    }
}

.design-description {
    color: var(--content-primary, rgba(247, 248, 250, 0.96));
    font-size: 14px;
    font-weight: 400;
    margin: 8px 0;

    /* 17.6px */
}

.design-image-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    border-radius: 8px;
    //  background: var(--background-spaceHolder, rgba(240, 245, 255, 0.10));
    // margin-left: -8px;
    // margin-right: -8px;

    .media-container {
        .image-cover {
            overflow: hidden;
            position: relative;
            // height: auto;

            .image-mask {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                opacity: 0;
                background: linear-gradient(180deg,
                        rgba(39, 39, 43, 0) 0%,
                        rgba(39, 39, 43, 0.2) 100%);
                padding-bottom: 30%;
                // height: 60px;

                .full-icon {
                    position: absolute;
                    bottom: 12px;
                    right: 30px;
                }

                .downlod-icon {
                    position: absolute;
                    bottom: 12px;
                    right: 8px;
                }
            }
        }
    }

    .media-container:hover {
        cursor: pointer;

        .image-cover {
            .image-mask {
                opacity: 1;
            }
        }
    }

    &>*:not(:last-child) {
        padding-right: 8px;
    }
}

.placeholder-image-container {
    // width:100%;
    // max-height: 500px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // justify-content: center;
    gap: 8px;
}

.image-error {
    width: 150px;
    height: 200px;
    /* 全屏高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: var(--background-spaceHolder, rgba(240, 245, 255, 0.1));
}

.design-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .header {
        display: flex;
        justify-content: space-between;
        padding-bottom: 12px;

        .title-area {
            display: flex;
            align-items: center;
        }

        .card-logo {
            margin-right: 8px;

            svg {
                width: 20px;
                height: 20px;
                color: #d3ffa5;
            }
        }

        .title {
            color: #d3ffa5;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 160%;
            /* 25.6px */
        }
    }
}