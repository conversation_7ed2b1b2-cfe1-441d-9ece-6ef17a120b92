import React from "react";
import errImage from "./errImage.svg";
import styles from "./index.module.scss";
import finishIcon from "./checkbox1.svg";
import DualWebPAnimation from "./WebpAnimation";
import { AiGenerateImageBoldFill } from "@meitu/candy-icons";
import { Image } from "antd";

export interface ChatGeneratorMedias {
  media_type: "image" | "video";
  media_url: string;
  media_cover_url: string;
  isNewGenerator?: boolean;
  width?: number;
  height?: number;
}
export interface MediaInfo {
  content: string;
  content_title: string;
  media_items: ChatGeneratorMedias[];
  media_num: number;
  render_percent?: number[];
  render_remain_time?: string[];
  hasDesignWithMediaUrl?: boolean; // 辅助插入的位置
  task_id?: string;
}

interface IProps {
  isHistory?: boolean;
  title: string;
  taskId?: string;
  mediasInfo?: MediaInfo[];
}

function Design(props: IProps) {
  const { title = "", isHistory = false, taskId, mediasInfo = [] } = props;
  const renderErrorImage = (total: number, width?: string, height?: string) => {
    return (
      <div
        className={styles["image-error"]}
        style={{
          width: width || `${150 / total}px`,
          height: height || `${200 / total}px`,
        }}
      >
        <Image alt="error" src={errImage} width={36} height={36} />
      </div>
    );
  };
  const renderProgress = (
    itemInfo: MediaInfo,
    placeholderImages: number[],
    percent: number[] | undefined,
    render_remain_time: string[]
  ) => {
    const percents =
      !percent || !percent.length
        ? new Array(placeholderImages.length).fill("0")
        : percent;

    return (
      <div
        className={styles["placeholder-image-container"]}
        style={{ width: "100%" }}
      >
        {placeholderImages.map((mediaItem, index: number) => {
          const total = Math.min(itemInfo.media_items?.length, 2);

          if (itemInfo.media_items?.length) {
            const currentMedia = itemInfo.media_items[index];

            // 查找相邻的成功媒体项（前一个或后一个）
            const findAdjacentSuccessMedia = () => {
              // 先检查前一个
              if (index > 0 && itemInfo.media_items[index - 1]?.media_url) {
                return itemInfo.media_items[index - 1];
              }
              // 再检查后一个
              if (
                index < itemInfo.media_items.length - 1 &&
                itemInfo.media_items[index + 1]?.media_url
              ) {
                return itemInfo.media_items[index + 1];
              }
              return null;
            };
            const adjacentSuccessMedia = findAdjacentSuccessMedia();

            if (itemInfo.media_items[index]?.media_url) {
              // 成功
              // 计算可用区域

              const availableWidth = 250 / total;
              const availableHeight = 250 / total;
              // 计算缩放比例
              const widthRatio = availableWidth / (currentMedia.width || 200);
              const heightRatio =
                availableHeight / (currentMedia.height || 200);
              const scaleRatio = Math.min(widthRatio, heightRatio);
              const width = currentMedia.width
                ? `${currentMedia.width * scaleRatio}px`
                : "250px";
              const height = currentMedia.height
                ? `${currentMedia.height * scaleRatio}px`
                : "200px";

              return (
                <div
                  className={styles["media-container"]}
                  style={{
                    width: currentMedia.media_type === "image" ? width : "100%",
                    height:
                      currentMedia.media_type === "image" ? height : "100%",
                    borderRadius: "8px",
                  }}
                >
                  {/* 底色区域 */}
                  <div
                    className={styles["image-cover"]}
                    style={{
                      borderRadius:
                        currentMedia.media_type === "image" ? "8px" : 0,
                      width:
                        currentMedia.media_type === "image" ? width : "auto",
                      height:
                        currentMedia.media_type === "image" ? height : "auto",
                      flexGrow: 1, // 视频待改造
                    }}
                  >
                    {currentMedia.media_type === "image" && (
                      <Image
                        style={{
                          borderRadius: "8px",
                          width:
                            currentMedia.media_type === "image"
                              ? width
                              : "auto",
                          height:
                            currentMedia.media_type === "image"
                              ? height
                              : "auto",
                        }}
                        src={currentMedia.media_url}
                        alt=""
                      />
                    )}
                  </div>
                </div>
              );
            }
            if (adjacentSuccessMedia) {
              // 复用相邻成功项的宽高
              const availableWidth = 250 / total;
              const availableHeight = 250 / total;
              const widthRatio =
                availableWidth / (adjacentSuccessMedia.width || 200);
              const heightRatio =
                availableHeight / (adjacentSuccessMedia.height || 200);
              const scaleRatio = Math.min(widthRatio, heightRatio);
              const width = adjacentSuccessMedia.width
                ? `${adjacentSuccessMedia.width * scaleRatio}px`
                : "250px";
              const height = adjacentSuccessMedia.height
                ? `${adjacentSuccessMedia.height * scaleRatio}px`
                : "200px";

              return renderErrorImage(total, width, height);
            }
            return renderErrorImage(total);
          }
          return (
            // eslint-disable-next-line react/jsx-no-useless-fragment
            <div>
              {isHistory ? (
                renderErrorImage(total)
              ) : (
                <DualWebPAnimation
                  index={index}
                  total={itemInfo.media_items.length}
                  percent={percents[index]}
                  remainTime={render_remain_time[index]}
                />
              )}
            </div>
          );
        })}
      </div>
    );
  };
  return (
    <div className={styles["design-container"]}>
      <div className={styles.header}>
        <div className={styles["title-area"]}>
          {title && <AiGenerateImageBoldFill className={styles["card-logo"]} />}
          <div className={styles.title}>{title}</div>
        </div>
      </div>
      <div>
        {mediasInfo.map((item) => {
          return (
            <>
              {item?.content_title ? (
                <div className={styles["design-description-area"]}>
                  {item?.content_title && (
                    <Image
                      src={finishIcon}
                      alt=""
                      width={13}
                      height={13}
                      style={{ height: 13 }}
                    />
                  )}
                  <div className={styles.title}>{item?.content_title}</div>
                </div>
              ) : null}
              {item.content && (
                <div className={styles["design-description"]}>
                  {item.content}
                </div>
              )}
              <div
                className={styles["design-image-list"]}
                style={{ width: "100%" }}
              >
                {renderProgress(
                  item,
                  isHistory
                    ? new Array(item.media_items.length).fill(1)
                    : new Array(item.media_num).fill(1),
                  item.render_percent,
                  item?.render_remain_time || [""]
                )}
              </div>
            </>
          );
        })}
      </div>
    </div>
  );
}
export default Design;
