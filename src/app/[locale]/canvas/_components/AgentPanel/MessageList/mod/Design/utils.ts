import type { ChatGeneratorMedias } from "./index";

export const findFirstMediaUrlIndex = (
  mediasInfo: Array<{
    media_items: Array<ChatGeneratorMedias>;
    [key: string]: any;
  }>
): {
  i: number;
  j: number;
  referenceWidth: number;
  referenceHeight: number;
} | null => {
  // Validate input
  if (!Array.isArray(mediasInfo)) {
    console.warn("mediasInfo must be an array");
    return null;
  }

  for (let i = 0; i < mediasInfo.length; i++) {
    const mediaGroup = mediasInfo[i];

    // Skip if media_items doesn't exist or isn't an array
    if (!Array.isArray(mediaGroup?.media_items)) continue;

    for (let j = 0; j < mediaGroup.media_items.length; j++) {
      const mediaItem = mediaGroup.media_items[j];

      // Return indices if media_url exists and is truthy
      if (mediaItem?.media_url) {
        return {
          i,
          j,
          referenceWidth: mediaItem.width || 500,
          referenceHeight: mediaItem.height || 500,
        };
      }
    }
  }

  return null;
};
