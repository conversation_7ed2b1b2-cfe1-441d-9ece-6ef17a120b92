/* eslint-disable no-irregular-whitespace */
import React, { memo } from 'react';
import Markdown from '../MarkDown';
import styles from './index.module.scss';
import CollapseCard from '../CollapseCard';

interface IProps {
  title: string;
  content: string;
  isHistory: boolean;
}

function ImageAnalyzerCard(props: IProps) {
  const { title = '', content = '', isHistory } = props;
  // const text = '## Design Suggestions\n\n### Style  \n- Contemporary sports aesthetic blending sharp geometric accents with subtle holographic foiling to evoke premium trading-card culture.  \n- Use motion-blur streaks and faint particle effects behind the player to amplify dynamism without overwhelming legibility.\n\n### Layout  \n1. Header (top 15 % height)  \n   • Team logo left, player name right — both spilling slightly outside margins for energy.  \n2. Hero Action Shot (central 60 %)  \n   • Full-bleed cut-out of player, feet or ball breaking the border for 3-D pop.  \n3. Stats & Bio Panel (bottom 25 %)  \n   • Semi-transparent team-color block; 3-column grid: season averages, career highlights, player position/number.  \n   • Include micro-icons (basketball, arrow, trophy) as quick visual anchors.  \n4. Edge Treatment  \n   • 3 mm bleed, 2 mm safe zone. Rounded corners radius ≈4 mm to match physical card trimming.  \n   • Integrate a subtle dotted “ticket stub” perforation pattern along one side as a collectible signature.\n\n### Elements  \n- Holographic foil frame or diagonal slash behind player for chase/parallel editions.  \n- QR code on rear (or bottom-right front if rear not designed) for AR highlight reel.  \n- Serial number & rarity icon embossed in silver gloss.\n\n### Color Palette  \nPrimary: official team colors (e.g., Lakers #552583 purple, #FDB927 gold).  \nSecondary Neutrals: charcoal #1A1A1A, off-white #F5F5F5 for text zones.  \nAccent: iridescent foil gradient (cyan-magenta-gold shift) for premium tiers.\n\n### Typography  \n- Player Name: Condensed slab-serif (e.g., “United Sans Condensed SemiBold”) at 150 pt equivalent, tracking ‑10.  \n- Stats Labels: Monospaced grotesque (e.g., “Space Mono”) for retro score-table feel.  \n- Body/Notes: Clean sans (e.g., “Inter Regular”) 40 pt equivalent for readability at print size.\n\n### Mood & Textures  \n- High-contrast lighting on player cut-out; rim-light in team secondary color.  \n- Subtle carbon-fiber texture in background shadows to add depth while keeping focus on action.\n\n### Finishing & Printing  \n- 16-pt soft-touch laminate card stock with spot UV on player silhouette and team logo.  \n- Optional cold-foil stamping on frame edges for limited “Rare” variants.  \n- CMYK + White + Foil layers separated in production files.\n\n## Image Generator Prompt (1024 × 1536, portrait)\n\n```\nResolution: 1024x1536\nA premium basketball trading card, portrait orientation. Center a dynamic, high-energy action shot of a professional male basketball player mid-air windmill dunk, jersey fluttering, intense arena lighting, captured at 1/8000s — hyper-realistic detail, sharp focus on face and ball, slight motion blur on crowd. Background: stylized diagonal slash in official team purple (#552583) fading into gold (#FDB927), with subtle carbon-fiber texture and scattered glowing particles. Around the edges, a sleek titanium-silver frame with holographic foil shimmer effect; top left embedded team logo badge, top right bold player name in condensed white slab-serif font with slight bevel. Bottom quarter: semi-transparent purple overlay containing three neat columns of white monospaced text placeholders for statistics (PPG, RPG, APG) separated by thin silver dividers, micro basketball icons above each stat. Add serial number “#004/250” embossed in silver at bottom right. Lighting dramatic, rim light in gold outlining the player. Overall mood: elite, collectible, modern, energetic. No watermarks, no text outside described elements, cinematic aspect framing.\n```\n\n(End of prompt)';
  // const ol = '## Design Suggestions\n\n### 1. Style  \n• Highly-realistic digital oil painting with subtle brush-texture, echoing 19-century portraiture.  \n• Softened facial modelling and slightly narrower muzzle to denote femininity while preserving the species realism.  \n• Carefully retain the refined, dignified posture seen in Cat Uncle.\n\n### 2. Character Design  \n• Breed / fur: cool silver-grey tabby (harmonises with original), slight pearlescent sheen for sophistication.  \n• Facial cues: gentle almond-shaped amber eyes, fine eyelashes, faint cheek fur tufts, elegantly arched brows.  \n• Expression: composed confidence with a hint of warmth—small closed-mouth smile.\n\n### 3. Attire  \n• Period: late-1920s English aristocracy (same era as original).  \n• Garment: tailored three-piece skirt suit in muted olive-brown herringbone; knee-length A-line skirt with inverted pleats, single-breasted jacket, matching waistcoat.  \n• Inner layer: ivory silk high-neck blouse with mother-of-pearl buttons and subtle lace trim.  \n• Accessories:  \n  – Burgundy silk ascot tucked into waistcoat (mirrors Cat Uncle’s tie).  \n  – Delicate gold pocket-watch chain draping from waistcoat.  \n  – Slim rosewood cane with engraved brass knob (keeps “cane” silhouette).  \n  – Cloche hat in deep burgundy with understated feather accent; removable for alternate render.  \n  – Thin pearl-rimmed pince-nez attached by a fine chain (feminine echo of monocle).  \n  – Kid-leather gloves, one hand holding them casually.\n\n### 4. Colour Palette  \n• Base neutrals: olive-brown, dark moss, warm taupe.  \n• Accents: burgundy, deep plum, antiqued gold, pearl white.  \n• Fur/eyes: silver-grey + amber to ensure contrast against attire.\n\n### 5. Mood & Lighting  \n• Quiet authority, subtle charm.  \n• Soft Rembrandt key light from camera left; warm bounce on camera right.  \n• Slight vignette to focus on figure.\n\n### 6. Layout & Composition  \n• Full-length, 3/4 view, head positioned on upper third line; cane angled slightly forward to break vertical monotony.  \n• Negative space around hat plume and tail curve for breathing room.  \n• Background: painterly deep umber gradient with faint suggestion of a mahogany panel or library curtain—consistent with Cat Uncle’s setting.\n\n### 7. Reference Image Usage  \nPosition the female cat exactly where Cat Uncle stands—central, weight on rear leg, tail forming an S-curve into right lower quadrant. Maintain similar camera height and distance to ensure continuity when both characters are shown side-by-side in future materials.\n\n### 8. Recommended Resolution  \n1024 × 1536 – vertical portrait format best accommodates full-body character plus headwear details.\n\n---\n\n## Comprehensive Image Generation Prompt\n\n(Use exactly, then tweak values if needed)\n\n“Ultra-detailed digital oil portrait, vertical 1024×1536. Subject: sophisticated anthropomorphic female cat aristocrat, silver-grey tabby fur with delicate pearlescent sheen, almond amber eyes, subtle eyelashes, composed elegant smile. Wearing late-1920s tailored olive-brown herringbone three-piece skirt suit, single-breasted jacket, matching waistcoat, A-line knee-length skirt, ivory silk high-neck blouse with lace trim, burgundy silk ascot, gold pocket-watch chain. Accessories: slim rosewood cane with engraved brass knob held in right paw, pearl-rimmed pince-nez on fine chain, cloche hat in deep burgundy with understated feather (optional for variant), kid-leather gloves held in left paw. Standing in refined posture, 3/4 view, one hand in waistcoat pocket, cane angled forward, tail forming graceful S-curve. Background: dark painterly mahogany-toned studio backdrop, soft vignette. Lighting: classical Rembrandt, warm softbox camera left, subtle bounce from right, gentle rim light outlining silhouette. Mood: dignified, intelligent, poised, hint of warmth. Painterly texture visible on fur and fabric, realistic material rendering, cinematic depth of field, no visible brush strokes on background, 8k detail fidelity, rich muted color grading, expansive negative space above hat.”\n\n---\n\n*End of design suggestions.*';

  return (
    <CollapseCard title={title} iconKey="imageAnalyzer" titleColor="#B89BF6" needExpand defaultExpanded={!isHistory}>
      <Markdown content={content} className={styles['analyzer-mk-content']} />
    </CollapseCard>
  );
}
export default memo(ImageAnalyzerCard);
