.request-content {
    display: flex;
    padding: 12px 16px 16px 16px;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 16px;
    background: var(--chatBubble-background-nestedCards, #1E1F21);

    .header {
        display: flex;
        align-items: center;
        margin-bottom: 6px;

        &-title {
            margin-left: 6px;

            color: #78A7F8;
            font-size: 13px;
            font-style: normal;
            font-weight: 600;
            line-height: 160%;
            /* 20.8px */
        }
    }

    .category-list {
        display: flex;
        flex-wrap: wrap;
        margin-top: 11px;

        &-item {
            margin-right: 6px;
            margin-bottom: 6px;
            display: flex;
            padding: 3px 8px;
            justify-content: center;
            align-items: center;
            border-radius: 99px;
            background: rgba(255, 255, 255, 0.12);


        }
    }

}