import React, { memo } from "react";
import Image from "next/image";
import CollapseCard from "../CollapseCard";
import icon from "./checkBoxMark.svg";
import styles from "./index.module.scss";
// import Markdown from '../MarkDown';

interface IProps {
  title: string;
  subTitle: string;
  desc: string;
  keywords: string[];
  isHistory: boolean;
}

function Knowledge(props: IProps) {
  const {
    title = "",
    subTitle = "",
    keywords = [],
    desc = "",
    isHistory,
  } = props;
  return (
    <CollapseCard
      needExpand
      titleColor="#78A7F8"
      iconKey="knowledge"
      title={title}
      defaultExpanded={!isHistory}
    >
      <div className={styles["request-content"]}>
        <div className={styles.header}>
          <Image width={13} height={13} alt="" src={icon} />
          <div className={styles["header-title"]}>{subTitle}</div>
        </div>
        {/* <Markdown content={desc} /> */}
        {desc && <p>{desc}</p>}
        {/* markdown移除 影响下拉加载 */}

        <div className={styles["category-list"]}>
          {(keywords || []).map((item) => (
            <div className={styles["category-list-item"]}>{item}</div>
          ))}
        </div>
      </div>
    </CollapseCard>
  );
}
export default memo(Knowledge);
