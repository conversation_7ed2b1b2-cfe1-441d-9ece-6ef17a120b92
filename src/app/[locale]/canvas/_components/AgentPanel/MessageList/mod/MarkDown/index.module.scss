.markdown-container {
    color: #fff;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    // line-height: 1.2;
    // width: fit-content;
    // padding: 10px;
    // align-items: center;

    // line-height: 1.6;
    &>*:not(:last-child) {

        word-wrap: break-word !important;
        -webkit-user-select: text;
        user-select: text;
        cursor: text;
        // margin-bottom: 6px !important;
        // font-size: 16px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
        margin: 0;
        color: #0e1014;
        -webkit-user-select: text;
        user-select: text;
        cursor: text;
        font-weight: 600;
        color: #fff;
    }

    p {
        // font-size: 16px;
    }

    ul,
    ol {
        // margin: 12px 0;
        // padding-left: 24px;

        li::marker {}

    }

    ol {

        // display: flex;
        // flex-direction: column;
        li {
            list-style-type: none;
        }
    }


    p {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: 160%;
        /* 24px */
        letter-spacing: 0.9px;
    }

    h1 {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 32px;
        font-weight: 600;
        line-height: 160%;
        /* 51.2px */
        letter-spacing: 1.92px;
    }

    h2 {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 24px;
        font-weight: 600;
        line-height: 160%;
        /* 38.4px */
        letter-spacing: 1.44px;

    }

    h3 {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 160%;
        /* 38.4px */
        letter-spacing: 1.44px;
    }

    h4 {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 16px;
        font-weight: 600;
        line-height: 160%;
        /* 25.6px */
        letter-spacing: 0.96px;
    }

    h5 {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 14px;
        font-weight: 500;
        line-height: 160%;
        /* 28.8px */
        letter-spacing: 1.08px;
    }

    h6 {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 13px;
        font-weight: 500;
        line-height: 160%;
        /* 25.6px */
        letter-spacing: 0.96px;
    }

    strong {
        color: var(--content-primary, rgba(247, 248, 250, 0.96));
        font-size: 13px;
        font-weight: 600;
        line-height: 160%;
        /* 20.8px */
        letter-spacing: 0.78px;

    }
}