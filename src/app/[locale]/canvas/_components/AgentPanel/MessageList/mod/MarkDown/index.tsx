"use client";

import React, { useRef } from "react";
import { marked } from "marked";
import classNames from "classnames";
import styles from "./index.module.scss";

interface IProps {
  content: string;
  className?: string;
}

function Markdown(props: IProps) {
  const { content = "", className } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const html = marked.parse(content);

  return (
    <div
      className={classNames(styles["markdown-container"], className)}
      ref={containerRef}
    >
      <div dangerouslySetInnerHTML={{ __html: html }} />
    </div>
  );
}

export default Markdown;
