.request-content {
    display: flex;
    padding: 12px 16px 16px 16px;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 16px;
    background: var(--chatBubble-background-nestedCards, #1E1F21);

    .header {
        display: flex;
        align-items: center;
        margin-bottom: 6px;

        &-title {
            margin-left: 6px;

            color: var(--chatBubble-messageType-content-request, #F8AB78);
            font-size: 13px;
            font-style: normal;
            font-weight: 600;
            line-height: 160%;
            /* 20.8px */

        }
    }

    .request-mk-container {
        ul > li {
            color: var(--chatBubble-text-describe, #FFF);
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 160%;
            /* 20.8px */

        }

    }

}