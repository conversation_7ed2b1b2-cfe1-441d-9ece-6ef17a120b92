import React, { memo } from "react";
import Image from "next/image";
import CollapseCard from "../CollapseCard";
import icon from "./checkBoxMark.svg";
import styles from "./index.module.scss";
// import Markdown from '../MarkDown';

interface IProps {
  title: string;
  subTitle: string;
  content: string;
}

function RequestCard(props: IProps) {
  const { title = "", subTitle = "", content = "" } = props;
  return (
    <CollapseCard
      needExpand={false}
      titleColor="#F8AB78"
      iconKey="request"
      title={title}
    >
      <div className={styles["request-content"]}>
        <div className={styles.header}>
          {subTitle && <Image width={13} height={13} alt="" src={icon} />}
          <div className={styles["header-title"]}>{subTitle}</div>
        </div>
        <div
          className={styles["content-question"]}
          style={{ whiteSpace: "pre-line" }}
        >
          {content}
        </div>
        {/* <Markdown content={content} className={styles['request-mk-container']} /> */}
      </div>
    </CollapseCard>
  );
}
export default memo(RequestCard);
