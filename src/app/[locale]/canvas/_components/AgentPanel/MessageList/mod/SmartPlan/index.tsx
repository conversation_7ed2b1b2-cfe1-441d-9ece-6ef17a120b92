import React, { memo } from "react";
import CollapseCard from "../CollapseCard";
import CustomSteps from "../CustomSteps";

export interface StepItem {
  name: string;
  description: string;
}

interface IProps {
  title: string;
  steps: StepItem[];
}

function SmartPlan(props: IProps) {
  const { title = "Smart Plan", steps = [] } = props;

  return (
    <CollapseCard
      title={title}
      iconKey="smartPlan"
      titleColor="#F8AB78"
      needExpand={false}
    >
      <CustomSteps steps={steps} />
    </CollapseCard>
  );
}
export default memo(SmartPlan);
