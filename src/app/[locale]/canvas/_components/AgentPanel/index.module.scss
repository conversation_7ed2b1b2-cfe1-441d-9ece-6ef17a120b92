@import '@/styles/variable.scss';

.agentPanelBox {
  .entryBox {
    transition: all 0.4s cubic-bezier(0.33, 1, 0.68, 1);
    opacity: 1;
    visibility: visible;
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 3;
    width: 151px;
    height: 34px;
    border-radius: var(--radius-10, 10px);
    border: 1px solid $system-stroke-button;
    background: $system-background-thirdary;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.hidden {
      opacity: 0;
      visibility: hidden;
      pointer-events: none;
    }

    .entryName {
      color: $system-content-secondary;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%;
      margin: 0 10px 0 5px;
    }

    svg {
      width: 18px;
      height: 18px;
      color: $icon-color-secondary;
    }
  }

  .panelBox {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    width: 400px;
    height: 100%;
    border-left: 1px solid $system-stroke-input;
    background: $system-background-secondary;
    transition: all 0.4s cubic-bezier(0.33, 1, 0.68, 1);
    transform-origin: top right;
    opacity: 1;
    transform: none;
    visibility: visible;
    overflow: hidden;

    &.collapsed {
      opacity: 0;
      width: 0;
      height: 0;
      visibility: hidden;
      pointer-events: none;
    }

    .topBox {
      width: 100%;
      height: 52px;
      box-sizing: border-box;
      border-bottom: 1px solid $system-stroke-divider;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;

      .logoBox {
        width: 28px;
        height: 28px;

        svg {
          transform: scale(0.875);
        }
      }

      .optBox {
        display: flex;
        gap: 14px;

        svg {
          width: 17.5px;
          height: 17.5px;
          color: $icon-color-secondary;
          cursor: pointer;
        }
      }
    }

    .contentMessageBox {
      width: 100%;
      height: calc(100% - 52px - 100px - 16px);
      padding: 16px;
      box-sizing: border-box;
      overflow-y: auto;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .messageTitle {
        h3 {
          color: var(--system-content-primary, #FFF);
          font-family: Inter;
          font-size: 24px;
          font-style: normal;
          font-weight: 600;
          line-height: 130%;
        }

        p {
          color: var(--system-content-secondary, #A3AEBF);
          font-family: Inter;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 130%;
          margin-top: 4px;
        }
      }
    }

    .bottomInputBox {
      width: 368px;
      position: absolute;
      left: 16px;
      bottom: 16px;
    }
  }
}