import { observer } from "mobx-react";
import styles from "./index.module.scss";
import {
  ArrowExpandBold,
  ArrowShrinkBold,
  LightBulbBold,
  PlusCircleBoldFill,
  TexHistoryBold,
} from "@meitu/candy-icons";
import classNames from "classnames";
import { LogoSimpleIcon } from "@/assets/icons";
import { useRootStore } from "../../_store";
import HistoryListPanel from "./HistoryListPanel";
import { useEffect, useRef, useState } from "react";
import { useAgentContext } from "@/components/Agent/context";
import AgentInput from "@/components/Agent/Input";
import MessageList from "./MessageList";

const AgentPanel = () => {
  // 获取全局状态管理中的editorStatusStore
  const { editorStatusStore } = useRootStore();
  const [showHistory, setShowHistory] = useState(false);
  const agentContext = useAgentContext();
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    editorStatusStore.agentPanelContainer = elementRef.current;
  });

  return (
    <div className={styles.agentPanelBox}>
      {/* 面板入口区域，点击可展开面板 */}
      <div
        className={classNames(
          styles.entryBox,
          editorStatusStore.isAgentExpanded && styles.hidden
        )}
        onClick={() => editorStatusStore.setAgentExpanded(true)}
      >
        <LightBulbBold />
        <span className={styles.entryName}>AI designer</span>
        <ArrowExpandBold />
      </div>
      {/* 主要面板内容区域 */}
      <div
        className={classNames(
          styles.panelBox,
          !editorStatusStore.isAgentExpanded && styles.collapsed
        )}
        ref={elementRef}
      >
        {/* 面板顶部区域，包含logo和操作按钮 */}
        <div className={styles.topBox}>
          <div className={styles.logoBox}>
            <LogoSimpleIcon />
          </div>
          <div className={styles.optBox}>
            {/* 新会话 */}
            <PlusCircleBoldFill />
            {/* 会话历史 */}
            <TexHistoryBold />
            {/* 生图历史 */}
            <TexHistoryBold
              onClick={() => {
                setShowHistory(true);
              }}
            />
            {/* 收起按钮 */}
            <ArrowShrinkBold
              onClick={() => editorStatusStore.setAgentExpanded(false)}
            />
          </div>
        </div>

        {/* 面板内容区域 */}
        <div className={styles.contentMessageBox}>
          <div className={styles.messageTitle}>
            <h3>Hey megan man</h3>
            <p>What design adventure begins now？</p>
          </div>
          <MessageList
            messageList={agentContext.messages}
            isBeginCard={agentContext.isBeginCard}
          />
        </div>

        {/* 底部输入区域，用于输入设计需求 */}
        <div className={styles.bottomInputBox}>
          <AgentInput
            text={agentContext.input.text}
            onTextChange={agentContext.input.setText}
            onSend={agentContext.send}
            status={agentContext.status}
            images={agentContext.input.images}
            uploadImage={agentContext.input.uploadImage}
            removeImage={agentContext.input.removeImage}
          />
        </div>

        {showHistory && (
          <HistoryListPanel close={() => setShowHistory(false)} />
        )}
      </div>
    </div>
  );
};

export default observer(AgentPanel);
