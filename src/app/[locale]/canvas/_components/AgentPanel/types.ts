export interface ChatGeneratorMedias {
  media_type: "image" | "video";
  media_url: string;
  media_cover_url: string;
  isNewGenerator?: boolean;
  width?: number;
  height?: number;
}

export interface StepItem {
  name: string;
  description: string;
}

export interface MediaInfo {
  content: string;
  content_title: string;
  media_items: ChatGeneratorMedias[];
  media_num: number;
  render_percent?: number[];
  render_remain_time?: string[];
  hasDesignWithMediaUrl?: boolean; // 辅助插入的位置
}

export interface Message {
  id: number;
  title?: string;
  role: "user" | "assistant"; // ai or user
  content: string;
  isStreaming?: boolean;
  task_id: string;
  text?: string;
  type: string; // 事件类型
  show_type: string;
  media_num: number;
  percent?: number[];
  showIcon?: boolean | null | undefined;
  content_steps?: StepItem[];
  content_title?: string;
  desc: string;
  question: string;
  keywords: string[];
  media_item?: ChatGeneratorMedias;
  imageMediaInfo: MediaInfo;
  videoMediaInfo: MediaInfo;
  mediasInfo: MediaInfo[];
  design_child_card?: MediaInfo[];
  msgParams?: any;
  isHistory?: boolean; // 媒体区分
  mustWithAvatar?: boolean; // 特殊情况隐藏头像
  status: "loading" | "ending" | "default"; /// 头像动画
  is_retransfer?: boolean; // 续传图片重复 下发sse,插入画布的临时追加的字段
  sseing?: boolean;
}

export interface ImageInfo {
  url: string;
  uri: string;
}
export interface VideoInfo {
  url: string;
  uri: string;
  coverUrl: string;
}
interface CommonPreviewItem {
  isHidePreview?: boolean;
}
export interface UserMessage {
  content: string;
  role: "user" | "assistant"; // ai or user
  imageList?: (Omit<ImageInfo, "uri"> & CommonPreviewItem)[];
  videoList?: (Omit<VideoInfo, "uri"> & CommonPreviewItem)[];
  id: number;
  type: string;
}
