import { MinusCircleBold, MinusCircleBold<PERSON>ill, PlusCircleBold, PlusCircleBoldFill } from "@meitu/candy-icons";
import { AreaMode } from "../types";
import { useI18n } from "@/locales/client";
import classNames from "classnames";
import styles from "./index.module.scss";

type AreaModeProps = {
  value?: AreaMode;
  onChange?: (value: AreaMode) => void;
  className?: string;

  items?: {
    value: AreaMode;
    renderLabel?: (activeMode?: AreaMode) => React.ReactNode;
  }[]
};

function getDefaultAreaModeItems(t: ReturnType<typeof useI18n>) {
  return [
    {
      value: AreaMode.Add,
      renderLabel: (activeMode?: AreaMode) => {
        return (
          <>
            {
              activeMode === AreaMode.Add 
              ? <PlusCircleBoldFill className="radio-btn-icon"/> 
              : <PlusCircleBold className="radio-btn-icon"/> 
            }

            <span className="radio-btn-text">
              {/* {t("editor.Add Area")} */}
              Add
            </span>
          </>
        );
      }
    },
    {
      value: AreaMode.Reduce,
      renderLabel: (activeMode?: AreaMode) => {
        return (
          <>
            {
              activeMode === AreaMode.Reduce 
              ? <MinusCircleBoldFill className="radio-btn-icon"/>
              : <MinusCircleBold className="radio-btn-icon"/>
            }

            <span className="radio-btn-text">
              {/* {t("editor.Reduce Selection")} */}
              Reduce
            </span>
          </>
        );
      }
    }
  ];
}

export function AreaModeRadio({
  value,
  onChange,
  className,
  items,
}: AreaModeProps) {

  const t = useI18n();

  const modes = items || getDefaultAreaModeItems(t);

  return(
    <div className={classNames(styles.radio, className)}>
      {
        modes.map(mode => {
          return (
            <label
              key={mode.value}
              className={classNames("radio-btn", {
                "radio-btn-active": value === mode.value,
              })}
            >
              <input
                className="radio-btn-input"
                type="radio"
                name="areaMode"
                value={mode.value}
                checked={value === mode.value}
                onChange={(e) => {
                 onChange?.(e.target.value as AreaMode);
                }}
              />

              {mode.renderLabel?.(value)}
            </label>
          )
        })
      }
    </div>
  )
}