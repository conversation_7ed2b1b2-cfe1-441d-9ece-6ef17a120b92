import { useI18n } from "@/locales/client";
import classNames from "classnames";
import buttonStyles from "@/styles/button.module.scss";
import styles from "./index.module.scss";
import { observer } from "mobx-react-lite";
import { useRootStore } from "@/app/[locale]/canvas/_store";
import { useEffect } from "react";
import { isTextEditing } from "@/app/[locale]/canvas/_utils/isTextEditing";

type CancelAreaButtonProps = {
  onClick?: () => void;
  disabled?: boolean;
};

function CancelAreaButton({ onClick, disabled }: CancelAreaButtonProps) {
  const t = useI18n();
  const { renderStore } = useRootStore();

  useEffect(() => {
    const hotkey = renderStore.renderHotkey;
    const render = renderStore.render;
    if (!hotkey || !render || disabled) {
      return;
    }

    const hotkeyHandler = (event: KeyboardEvent) => {
      if (isTextEditing(render)) {
        return;
      }

      onClick?.();
      event.preventDefault();
    };

    hotkey.registerHotKey("esc", hotkeyHandler);
    return () => {
      hotkey.unregisterHotKey("esc", hotkeyHandler);
    };
  }, [renderStore.render, renderStore.renderHotkey, onClick, disabled]);

  return (
    <button
      className={classNames(buttonStyles.secondary, styles.button)}
      disabled={disabled}
      onClick={onClick}
    >
      {t("Cancel")}
    </button>
  );
}

export default observer(CancelAreaButton);
