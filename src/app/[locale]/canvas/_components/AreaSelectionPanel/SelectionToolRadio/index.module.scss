@import "@/styles/variable.scss";

.radio {
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $system-content-secondary;
  gap: 8px;

  :global {
    .radio-btn {
      padding: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      &-input {
        display: none;
      }

      &-icon {
        svg {
          width: 25px;
          height: 25px;
        }
      }

      &.radio-btn-active {
        color: #fff;
        border-radius: 10px;
        // border: 1px solid $system-stroke-button;
        background: $system-background-thirdary;
        box-shadow: inset 0 0 0 1px $system-stroke-button;
      }
    }
  }
}

.params {
  padding: 16px;
  border-radius: 12px;
  border: 1px solid $system-stroke-input-default;
  background: $system-background-secondary;
}