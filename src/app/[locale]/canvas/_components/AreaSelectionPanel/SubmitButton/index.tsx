import classNames from "classnames";
import buttonStyles from "@/styles/button.module.scss";
import styles from "./index.module.scss";
import { observer } from "mobx-react-lite";
import { MtBeanFill } from "@meitu/candy-icons";

type SubmitButtonProps = {
  onClick?: () => void;
  price?: number;
  disabled?: boolean;
};

function SubmitButton({ onClick, disabled, price }: SubmitButtonProps) {

  return (
    <button
      className={classNames(buttonStyles.secondary, styles.button)}
      disabled={disabled}
      onClick={onClick}
    >
      <MtBeanFill />
      {price}
    </button>
  );
}

export default observer(SubmitButton);
