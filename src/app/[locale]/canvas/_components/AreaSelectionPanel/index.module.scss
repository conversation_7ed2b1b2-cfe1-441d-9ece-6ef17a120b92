@import "@/styles/variable.scss";

.panel {
  display: inline-grid;
  grid-template-columns: repeat(2, auto);
  grid-auto-rows: 44px;
  padding: 12px;
  column-gap: 12px;
  row-gap: 12px;
  position: absolute;
  left: 0;
  top: 0;

  border-radius: var(--radius-16, 16px);
  border: 1px solid $system-stroke-input-default;
  background: $system-background-secondary;


  :global {
    .selection-area{
      display: flex;
      min-width: 252px;
      align-items: center;
      gap: 12px;
      position: relative;
      padding-right: 12px;

      .divider {
        width: 2px;
        height: 26px;
        background: $system-stroke-divider;
      }


      &.area-mode {
        &::after {
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 2px;
          height: 26px;
          background: $system-stroke-divider;
        }
      }
    }
  }
}