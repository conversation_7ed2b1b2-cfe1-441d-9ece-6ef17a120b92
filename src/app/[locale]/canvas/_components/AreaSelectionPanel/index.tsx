import { AreaModeRadio } from './AreaModeRadio';
import styles from './index.module.scss';
import { AreaMode, SelectionTool, SelectionToolDesc } from './types';
import { SelectionToolRadio } from './SelectionToolRadio';
import { Params } from './Params';
import { observer } from 'mobx-react-lite';
import { useFollowTargets } from '../../_hooks/useFollowTargets';
import { useRootStore } from '../../_store';
import classNames from 'classnames';
import { FormProps } from 'antd';
import { useMemo } from 'react';

type AreaSelectionPanelProps = {
  areaMode?: AreaMode;
  onAreaModeChange?: (mode: AreaMode) => void;

  tools?: SelectionToolDesc[];
  activeTool?: SelectionTool;
  onActiveToolChange?: (tool: SelectionTool) => void;

  prompt?: string;
  onPromptChange?: (prompt: string) => void;
  promptPlaceholder?: string;

  form?: {
    props?: FormProps;
    renderFormItems?: () => React.ReactNode;
  },

  renderCancelSelectionButton?: () => React.ReactNode;

  renderSubmitButton?: () => React.ReactNode;
}

function AreaSelectionPanel({
  areaMode,
  onAreaModeChange,

  tools,
  activeTool,
  onActiveToolChange,

  prompt,
  onPromptChange,
  promptPlaceholder,

  form,

  renderCancelSelectionButton,

  renderSubmitButton,
}: AreaSelectionPanelProps) {

  const { editorStatusStore, selectionStore } = useRootStore();

  const { singleImage } = selectionStore;
  const followTargets = useMemo(() => {
    if (singleImage?.image) {
      return [singleImage.image];
    }
  }, [singleImage?.image]);

  const { elementRef, translate } = useFollowTargets<HTMLDivElement>({ 
    targets: followTargets,
    offset: 16,
    rangePadding: [
      editorStatusStore.canvasVisibleArea.top + 16,
      editorStatusStore.canvasVisibleArea.right + 16,
      editorStatusStore.canvasVisibleArea.bottom + 16,
      editorStatusStore.canvasVisibleArea.left + 16,
  ]});

  return (
    <div className={styles.panel} ref={elementRef} style={{
      transform: `translate(${translate.x}px, ${translate.y}px)`,
    }}>
      <div className={classNames("selection-area", !!areaMode && "area-mode")}>
        { 
          !!areaMode && (<>
            <AreaModeRadio value={areaMode} onChange={onAreaModeChange}/>
            <div className="divider"></div>
          </>)
        }
        <SelectionToolRadio
          items={tools}
          activeTool={activeTool}
          onActiveToolChange={onActiveToolChange}
        />
      </div>

      {renderCancelSelectionButton?.()}

      <Params
        prompt={prompt}
        onPromptChange={onPromptChange}
        placeholder={promptPlaceholder}
        
        onPromptInputKeyDown={e => e.nativeEvent.stopImmediatePropagation()}
        renderFormItems={form?.renderFormItems}
        formProps={form?.props}
      />
        
      {renderSubmitButton?.()}
    </div>
  )
}

export default observer(AreaSelectionPanel);