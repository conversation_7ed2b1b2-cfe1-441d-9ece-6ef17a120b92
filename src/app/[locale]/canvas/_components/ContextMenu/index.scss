.custom-menu-container {
  width: 208px;
  padding: 4px;
  border-radius: var(--radius-12, 12px);
  border: 1px solid var(--system-stroke-input-default, #22272E);
  background: var(--system-background-secondary, #1D1E23);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 24px 128px 0px rgba(0, 0, 0, 0.16);
  backdrop-filter: blur(14px);
  font-family: Inter;
  .custom-menu-item {
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    position: relative;
    
    .custom-menu-item-name-wrapper {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      .custom-menu-item-icon {
        margin-right: 11px;
        svg {
          color: var(--system-content-secondary, #A3AEBF);
          width: 18px;
          height: 18px;
        }
      }
      .custom-menu-item-name {
       
        overflow: hidden;
        color: var(--system-content-secondary, #A3AEBF);
        text-overflow: ellipsis;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        white-space: nowrap;
        .watermark-item-display {
          display: flex;
          align-items: center;
          .watermark-item-icon {
            width: 29px;
            height: 14px;
            margin-left: 8px;
          }
        }
      }
      
    }
    .custom-menu-item-hotKey {
      white-space: nowrap;
      color: var(--system-content-thirdary, #6B7A8F);
      font-size: 14px;
      font-weight: 500;
    }
    .custom-menu-item-icon {
      width: 16px;
      height: 16px;
      svg {
        color: var(--system-content-thirdary, #6B7A8F);
      }
    }
    &:hover {
      border-radius: var(--radius-10, 10px);
      background: linear-gradient(0deg, var(--system-background-thirdary, #272C33) 0%, var(--system-background-thirdary, #272C33) 100%), #F5F7FA;
      >.custom-menu-item-name-wrapper {
        .custom-menu-item-name {
          font-weight: 500;
          color: var(--system-content-primary, #fff);
        }
        .custom-menu-item-icon {
          svg {
            color: var(--system-content-primary, #fff) !important;
          }
        }
      }
    
    }
  }
  .custom-menu-item-disabled {
    cursor: not-allowed;
    opacity: 0.3;
    &:hover {
      border-radius: 0;
      background: transparent;
      .custom-menu-item-name-wrapper {
        .custom-menu-item-name {
          font-weight: 400;
          color: var(--system-content-secondary, #A3AEBF);
        }
        .custom-menu-item-icon {
          svg {
            color: var(--system-content-secondary, #A3AEBF);
          }
        }
      }
    }
  }
  .custom-menu-divider {
    margin: 2px 0;
    height: 1px;
    background: #1f2329;
  }
}
.custom-menu-item-children {
  position: absolute;
}
