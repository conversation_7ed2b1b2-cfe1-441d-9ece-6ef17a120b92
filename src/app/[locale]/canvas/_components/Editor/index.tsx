"use client";

import { observer } from "mobx-react";
import { Fragment, useEffect, useRef } from "react";
import {
  <PERSON>K<PERSON>,
  MouseAction,
  Render,
  Selection,
  ContextMenu,
  RenderStyle,
  ownDefaultsMouseStyle,
  HistoryPlugins,
  Operation,
  HistoryChangeType,
  FabricObject,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { RootStore, useRootStore } from "@/app/[locale]/canvas/_store";
import ContextMenuComponent from "../ContextMenu";
import useRegisterHotKey from "@/app/[locale]/canvas/_hooks/useRegisterHotKey";
import {
  handleHistoryOperationsParams,
  useSaveCanvas,
} from "@/app/[locale]/canvas/_hooks/useSaveProjectHistory";

import styles from "./index.module.scss";
import FileDropTarget from "@/components/FIleDropTarget";
import { useUpload } from "../../_hooks/useUpload";
import ImagesNumberLimitModal, {
  useImagesNumberLimitModal,
} from "./ImagesNumberLimitModal";
import { useStore } from "@/contexts/StoreContext";
import { useI18n, useScopedI18n } from "@/locales/client";
import { UserStore } from "@/stores/UserStore";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";

const canvasContainerId = "poster-editor";

/**
 * 允许在localstorage中配置最大图片数量
 */
function getMaxImageCount() {
  let count = 100;

  // 生产环境不允许配置
  if (process.env.API_ENV === "release") {
    return count;
  }
  const debugMaxImageCountKey = "debug:poster-editor/max-image-count";
  const countStr = localStorage.getItem(debugMaxImageCountKey);
  try {
    const debugCount = countStr && parseInt(countStr);
    if (debugCount) {
      count = debugCount;
    }
  } catch (e) {
    if (process.env.NODE_ENV === "development") {
      // console.log(debugMaxImageCountKey, '配置的数字异常');
    }
  }

  if (process.env.NODE_ENV === "development") {
    // console.log('图片数量限制：', count);
  }
  return count;
}

function Editor() {
  const rootStore = useRootStore();
  const { userStore } = useStore();
  const { renderStore } = rootStore;
  const t = useI18n();
  const scopedT = useScopedI18n("shortcuts");
  const uid = userStore.UID;

  const { projectsStore, configStore } = useRootStore();
  const posterConfigStore = configStore.posterConfigStore;
  const projectId = projectsStore.activeProjectId;

  const { open, openModal, closeModal } = useImagesNumberLimitModal({
    uid,
    projectId,
  });

  const subscribeModal = useSubscribeModal();

  const upload = useUpload();
  const selectionRef = useRef<Selection>();

  useEffect(() => {
    const render = new Render({
      container: canvasContainerId,
      maxImageCount: getMaxImageCount(),
    });
    const historyPlugins = new HistoryPlugins(render, {
      undoChangeCallback,
      redoChangeCallback,
      onInteractionModifiedCallback,
    });
    const hotKey = new HotKey(render);
    const mouseAction = new MouseAction(render);
    const selection = new Selection(render, {
      text: {
        text: t("Type your text here"),
      },
    });
    selectionRef.current = selection;
    const contextMenu = new ContextMenu<{
      rootStore: RootStore;
      userStore: UserStore;
      subscribeModal: ReturnType<typeof useSubscribeModal>;
      t: ReturnType<typeof useI18n>;
      scopedT: ReturnType<typeof useScopedI18n>;
    }>(render, ContextMenuComponent, {
      rootStore: rootStore,
      userStore,
      subscribeModal,
      t,
      scopedT,
    });
    const renderStyle = new RenderStyle(render, ownDefaultsMouseStyle);
    render
      .use(hotKey)
      .use(selection)
      .use(contextMenu)
      .use(renderStyle)
      .use(historyPlugins)
      .use(mouseAction);

    renderStore.configRenderStore({
      render,
      renderSelection: selection,
      renderStyle: renderStyle,
      historyPlugins: historyPlugins,
      renderMouseAction: mouseAction,
      renderHotkey: hotKey,
    });

    return () => {
      render.unmount();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!selectionRef.current || !posterConfigStore.config?.name) return;

    selectionRef.current.opt = {
      ...selectionRef.current.opt,
      text: {
        ...selectionRef.current.opt.text,
        fontFamily: posterConfigStore.config?.name,
      },
    };
  }, [posterConfigStore.config?.name]);

  useEffect(() => {
    const render = renderStore.render;
    if (!render) {
      return;
    }

    const handleExceedImagesLimit = (payload: {
      objects: FabricObject[];
      type: WarningType;
    }) => {
      if (payload.type !== WarningType.IMAGE_COUNT) {
        return;
      }

      openModal();
    };

    render._FC.on("warning", handleExceedImagesLimit);

    return () => {
      render._FC.off("warning", handleExceedImagesLimit);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.render, uid, projectId]);

  useRegisterHotKey(rootStore);
  const { submitSaveElements, submitDeleteElements } = useSaveCanvas(rootStore);

  const undoChangeCallback = (operations: Operation[]) => {
    const { deleteOptions, saveOptions } = handleHistoryOperationsParams({
      operations,
      historyChangeType: HistoryChangeType.UNDO,
    });
    submitDeleteElements([], deleteOptions);
    submitSaveElements([], saveOptions);
  };

  const redoChangeCallback = (operations: Operation[]) => {
    const { deleteOptions, saveOptions } = handleHistoryOperationsParams({
      operations,
      historyChangeType: HistoryChangeType.REDO,
    });
    submitDeleteElements([], deleteOptions);
    submitSaveElements([], saveOptions);
  };

  const onInteractionModifiedCallback = (operation: Operation) => {
    const objects = operation.options.afterData?.objects ?? [];
    submitSaveElements([], objects);
  };

  return (
    <Fragment>
      <FileDropTarget
        className={styles["file-drop-target"]}
        onFilesDropped={({ files, event }) =>
          upload(files, { dragEvent: event })
        }
      >
        <div id={canvasContainerId} className={styles.editor}></div>
        <ImagesNumberLimitModal open={open} onClose={closeModal} />
      </FileDropTarget>
    </Fragment>
  );
}

export default observer(Editor);
