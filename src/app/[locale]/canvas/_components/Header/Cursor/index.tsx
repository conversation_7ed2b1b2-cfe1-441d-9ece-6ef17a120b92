import { useEffect, useMemo, useState } from "react";
import styles from "./index.module.scss";
import {
  CheckBlack,
  ChevronDownBlack,
  Hand,
  ToolSelect,
} from "@meitu/candy-icons";
import { Popover } from "antd";
import classNames from "classnames";
import { CursorType, HeaderAction } from "../../../_store/headerAction";
import { observer } from "mobx-react-lite";
import { useRootStore } from "../../../_store";
import { runInAction } from "mobx";
import { useI18n } from "@/locales/client";

export type CursorOptionItem = {
  value: CursorType;
  icon: React.ReactNode;
  display: string;
};

export type CursorProps = {
  cursorOptions?: Array<CursorOptionItem>;
  disabled?: boolean;
};

function Cursor({ disabled }: CursorProps) {
  const t = useI18n();

  const cursorOptions: Array<CursorOptionItem> = useMemo(
    () => [
      {
        value: CursorType.Select,
        icon: <ToolSelect />,
        display: `${t("header.Select")}`,
      },
      {
        value: CursorType.Drag,
        icon: <Hand />,
        display: `${t("header.Hand tool")}`,
      },
    ],
    [t]
  );

  const { headerActionStore } = useRootStore();

  const currentCursor = headerActionStore.activeCursor;
  const setActiveCursor = (cursor: CursorType) =>
    headerActionStore.setActiveHeaderAction(HeaderAction.Cursor, cursor);
  const activateCursor = () =>
    headerActionStore.setActiveHeaderAction(HeaderAction.Cursor);
  const isActive = headerActionStore.activeHeaderAction === HeaderAction.Cursor;

  const cursor = useMemo(() => {
    return cursorOptions?.find((c) => c.value === currentCursor);
  }, [cursorOptions, currentCursor]);

  const [isOpen, setIsOpen] = useState(false);

  if (!cursor) {
    return null;
  }

  const renderOptions = () => {
    return cursorOptions.map((c) => {
      const handleCursorChange = () => {
        runInAction(() => {
          setActiveCursor(c.value);
          activateCursor();
        });

        setIsOpen(false);
      };

      return (
        <div
          key={c.value}
          className={styles["cursor-item"]}
          onClick={handleCursorChange}
        >
          <span className="dropdown-item-icon">{c.icon}</span>
          <span className="dropdown-item-name">{c.display}</span>
          {currentCursor === c.value && (
            <CheckBlack className="dropdown-item-checked-icon" />
          )}
        </div>
      );
    });
  };

  return (
    <div className={styles.cursor}>
      <button
        disabled={disabled}
        className={classNames(
          "cursor-button",
          isActive && "selected",
          disabled && "disabled"
        )}
        onClick={activateCursor}
      >
        {cursor.icon}
      </button>

      <Popover
        overlayClassName={styles["cursor-overlay"]}
        title={renderOptions()}
        open={isOpen}
        trigger="click"
        onOpenChange={setIsOpen}
        mouseLeaveDelay={0.2}
        arrow={false}
        align={{
          offset: [0, -20],
        }}
        destroyTooltipOnHide
      >
        <span className="cursor-change-wrapper">
          <button
            disabled={disabled}
            className={classNames(
              "cursor-change",
              isOpen && "open",
              disabled && "disabled"
            )}
          >
            <ChevronDownBlack />
          </button>
        </span>
      </Popover>
    </div>
  );
}

export default observer(Cursor);
