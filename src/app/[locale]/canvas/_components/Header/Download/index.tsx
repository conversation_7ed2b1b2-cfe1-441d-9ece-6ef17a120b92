import { Popover, Spin, Switch } from "antd";
import { Fragment, useEffect, useState } from "react";
import VIPIcon from "@/assets/icons/icon-vip.png";
import { ChevronDownBlack, DownloadBold } from "@meitu/candy-icons";
import classNames from "classnames";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/canvas/_store";
// import { useMembershipDesc } from '@/hooks/useMember';
// import { useOpenSubscribePopup } from '@/hooks/useSubscribe';
import { useExportShape } from "@/app/[locale]/canvas/_hooks/useExportShape";
import styles from "./index.module.scss";
import { useStore } from "@/contexts/StoreContext";
import { useI18n } from "@/locales/client";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { trackEvent } from "@meitu/subscribe-intl";

//TODO: 商业化接入

function Download() {
  //#region 会员和去水印开关
  // const { isVipCurrent, refreshMembershipDesc } = useMembershipDesc();
  const { userStore } = useStore();
  const [fetchVipLoading, setFetchVipLoading] = useState(false);
  const isVipCurrent = userStore.isVipCurrent;
  const [isRemoveWatermark, setIsRemoveWatermark] = useState(isVipCurrent);
  const subscribeModal = useSubscribeModal();
  const { open: openSubscribeModal } = subscribeModal;

  const t = useI18n();

  useEffect(() => {
    setIsRemoveWatermark(isVipCurrent);
  }, [isVipCurrent]);

  // const openSubscribePopup = useOpenSubscribePopup();
  const handleChangeIsMoveWaterMark = async () => {
    // 切换开关的时候  获取会员状态
    setFetchVipLoading(true);
    await userStore.refreshUserInfo();
    setFetchVipLoading(false);

    if (userStore.isVipCurrent) {
      setIsRemoveWatermark((isRemove) => !isRemove);
    } else {
      // 非会员直接关闭去水印
      setIsRemoveWatermark(false);
      openSubscribeModal({
        productType: SubscribeModalType.Watermark,
      });
    }
  };
  //#endregion
  const { selectionStore, projectsStore, renderStore } = useRootStore();
  const activeOptions = selectionStore.singleImage?.options;

  const { downloadable, download, downloading } = useExportShape({
    selectionStore,
    projectsStore,
    renderStore,
    userStore,
    subscribeModal,
  });

  const [open, setOpen] = useState(false);
  const handleOpenChange = (open: boolean) => {
    if (!downloadable) {
      return;
    }

    setOpen(open);
  };

  const renderPopover = () => {
    const handleDownload = () => {
      if (downloading) {
        return;
      }

      trackEvent("ai_create_image_download", {
        task_id: activeOptions?._custom_data_history_?.msgId,
      });

      download({ removeWatermark: isRemoveWatermark });
    };

    return (
      <Fragment>
        <div
          className={styles["download-dropdown-item"]}
          onClick={handleDownload}
        >
          <Spin spinning={downloading}>{t("PNG")}</Spin>
        </div>
        <Spin spinning={fetchVipLoading}>
          <div
            className={classNames(
              styles["download-dropdown-item"],
              "watermark-item"
            )}
            onClick={handleChangeIsMoveWaterMark}
          >
            <div className="watermark-item-display">
              <span className="watermark-item-label">
                {t("Remove watermarks")}
              </span>
              <img className="watermark-item-icon" src={VIPIcon.src} alt="" />
            </div>
            <Switch
              checked={isRemoveWatermark}
              className={styles["watermark-item-switch"]}
            />
          </div>
        </Spin>
      </Fragment>
    );
  };

  return (
    <Popover
      classNames={{
        root: styles["download-popover"],
      }}
      title={renderPopover()}
      mouseLeaveDelay={0.2}
      arrow={false}
      placement="bottomLeft"
      align={{
        offset: [0, 8],
      }}
      open={open}
      onOpenChange={handleOpenChange}
      destroyTooltipOnHide
    >
      <div className={classNames(styles.download, !downloadable && "disabled")}>
        <DownloadBold className="download-icon" />
        <span className="download-label">{t("Download")}</span>
        <ChevronDownBlack className="download-suffix" />
      </div>
    </Popover>
  );
}

export default observer(Download);
