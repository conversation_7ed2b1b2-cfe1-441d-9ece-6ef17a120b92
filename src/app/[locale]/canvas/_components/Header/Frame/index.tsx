import { FrameToPoster } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import classNames from "classnames";
import { Tooltip } from "antd";
import { observer } from "mobx-react-lite";
import { useRootStore } from "../../../_store";
import { HeaderAction } from "../../../_store/headerAction";
import { useSaveCanvas, useSubmitAddHistory } from "../../../_hooks/useSaveProjectHistory";
import { Fragment, useEffect } from "react";
import {
  CoreMouseMode,
  createFrame,
  ElementName,
  FabricObject,
  Group,
} from "@meitu/whee-infinite-canvas";
import { FrameSider } from "./FrameSider";
import { useI18n } from "@/locales/client";
import { trackEvent } from "@meitu/subscribe-intl";
import _ from 'lodash';
import { useAttachToFrame } from "../../../_hooks/useAttachToFrame";
import { useInsertFirstFrameGuide } from "../../../_hooks/first-frame-guide";
import { useStore } from "@/contexts/StoreContext";
import { needShowGuide } from "../../../_utils/frame-guide/record";
import zoomToFitCanvas from "../../../_utils/zoomToFitCanvas";

type FrameProps = {
  disabled?: boolean;
};

function Frame({ disabled }: FrameProps) {
  const rootStore = useRootStore();
  const {
    headerActionStore,
    renderStore,
    editorStatusStore,
    paramsEditorStore,
    configStore,
  } = rootStore;
  const { userStore } = useStore();
  const isActive =
    !editorStatusStore.isClearScreenUI &&
    headerActionStore.activeHeaderAction === HeaderAction.InsertFrame;
  const activateInsertFrame = () => {
    headerActionStore.activateActionAndResetCursor(HeaderAction.InsertFrame);
  };

  const {submitSaveElements} = useSaveCanvas(rootStore);
  const insertFirstFrameGuide = useInsertFirstFrameGuide({
    configStore,
    renderStore,
    userStore,
  });

  const t = useI18n();
  // const insertFrameGuide = useInsertFrameGuide({
  //   configStore,
  //   renderStore,
  // });

  const attachToFrame = useAttachToFrame({ renderStore })

  const submitAddHistory = useSubmitAddHistory(rootStore);

  const afterInsert = async (frame: FabricObject) => {
    const render = renderStore.render;
    if (!render) {
      return;
    }

    headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    attachToFrame(frame as Group, { preset: "default" });
    render.backToOriginPosition();
    submitAddHistory([frame]);
  };

  useEffect(() => {
    const render = renderStore.render;
    const renderSelection = renderStore.renderSelection;

    if (!render || !isActive || !renderSelection) {
      return;
    }

    renderSelection.setMode(CoreMouseMode.FRAME);
    const handleAfterInsert = ({ objects }: { objects: FabricObject[] }) => {
      if (objects.length !== 1 || objects[0]._name_ !== ElementName.FRAME) {
        return;
      }

      const frame = objects[0];
      afterInsert(frame);
    };
    render._FC.on("object:insert", handleAfterInsert);
    return () => {
      render._FC.off("object:insert", handleAfterInsert);
      renderSelection.setMode(CoreMouseMode.SELECTION);
    };
  }, [isActive, renderStore]);

  const handleSelectDimension = ([width, height]: [number, number]) => {
    const render = renderStore.render;
    if (!render) {
      return;
    }

    const frame = createFrame("", {
      width,
      height,
      backgroundColor: "#ffffff",
    });

    render.addToViewPortCenter(frame);
    afterInsert(frame);
  };

  const handleInsertFirstFrame = async () => {
    const render = renderStore.render;
    const historyPlugins = renderStore.historyPlugins;
    if (!render || !historyPlugins) {
      return;
    }

    const elements = await insertFirstFrameGuide();
    const frame = elements && elements[0];

    if (frame) {
      const operation =
        historyPlugins?.baseAction.getAddOperation({
          objects: elements,
        });
      if (!operation) return;
      historyPlugins.submit(operation);

      render._FC.setActiveObject(frame);
      if (elements.length > 1) {
        zoomToFitCanvas(render, { targets: elements });
      } else {
        render.backToOriginPosition({ target: frame });
      }
      render._FC.requestRenderAll();
      
      await submitSaveElements(elements);
    }
  }
  return (
    <Fragment>
      <Tooltip title={`${t("Palette")} F`}>
        <button
          id="insert-frame"
          disabled={disabled}
          className={classNames(
            styles.frame,
            isActive && "selected",
            disabled && "disabled"
          )}
          role="button"
          onClick={() => {
            const uid = userStore.UID;
            if (!uid) {
              return;
            }

            if (editorStatusStore.showCreate) {
              trackEvent("transition_page_click", {
                click_type: "ratio",
                location: "top",
              });
            }
            // activateInsertFrame();
            if (needShowGuide(uid, "frame")) {
              handleInsertFirstFrame();
            } else {
              activateInsertFrame();
            }

            editorStatusStore.setShowCreate(false);
            paramsEditorStore.setIsOpenImageParams(false);
          }}
        >
          <FrameToPoster />
        </button>
      </Tooltip>

      <FrameSider show={isActive} onSelect={handleSelectDimension} />
    </Fragment>
  );
}

export default observer(Frame);
