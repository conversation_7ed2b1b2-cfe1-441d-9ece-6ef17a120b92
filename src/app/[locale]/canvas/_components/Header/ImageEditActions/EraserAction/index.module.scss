.eraserBox {
  position: relative;
}

.eraserPopBox {
  width: 347px;
  height: 44px;
  overflow: hidden;

  :global {
    .ant-popover-content {
      .ant-popover-inner {
        box-sizing: border-box;
        width: 347px;
        height: 44px;
        border-radius: var(--radius-12, 12px);
        border: 1px solid var(--system-stroke-input-default, #22272E);
        background: var(--system-background-secondary, #1D1E23);
        box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.06);
        padding: 0 12px;
        color: var(--content-btnPrimary, #FFF);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        display: flex;
        align-items: center;

        .ant-popover-inner-content {
          width: 100%;
        }
      }
    }
  }
}

.opt-box-container {
  box-sizing: border-box;
  width: 347px;
  height: 44px;
  border-radius: var(--radius-12, 12px);
  border: 1px solid var(--system-stroke-input-default, #22272E);
  background: var(--system-background-secondary, #1D1E23);
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.06);
  padding: 0 12px;
  color: var(--content-btnPrimary, #FFF);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  display: flex;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  :global {
    .optBox {
      width: 347px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 28px;
      gap: 12px;
      .leftBox {
        height: 28px;
        display: flex;
        align-items: flex-end;
        gap: 3px;
        position: relative;
    
        .brushIcon {
          width: 22px;
          height: 30px;
          cursor: pointer;
    
          &.active {
            height: 38px;
          }
    
          svg {
            width: 22px;
            height: 50px;
            color: #fff;
          }
        }
      }
    
      .centerBox {
        flex: 1;
    
        // :global {
        //   .ant-slider {
        //     margin: 0;
    
        //     .ant-slider-rail {
        //       background-color: var(--system-background-thirdary, #272C33);
        //     }
    
        //     .ant-slider-track {
        //       background-color: var(--system-primary, #0C84FF);
        //     }
    
        //     .ant-slider-handle {
        //       width: 12px;
        //       height: 12px;
        //       border: 2px solid var(--system-primary, #0C84FF);
        //       background-color: #fff;
        //     }
        //   }
        // }
      }
    
      .rightBox {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 10px;
        border-radius: var(--radius-8, 8px);
        background: var(--system-background-disabled, #272C33);
        cursor: pointer;
        color: var(--content-btnPrimary, #FFF);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
    
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
    
        &:hover {
          background: var(--system-background-quaternary, #323B48);
        }
    
        svg {
          margin-right: 2px;
          color: #FBCD6F;
        }
      }
    }
  }
}