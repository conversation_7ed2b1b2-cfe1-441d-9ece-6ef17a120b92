import { createPortal } from "react-dom";
import AreaSelectionPanel from "../../../AreaSelectionPanel";
import { useEffect, useMemo, useRef, useState } from "react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { AreaMode, SelectionTool } from "../../../AreaSelectionPanel/types";
import { ElementName, FabricObject, getElementOptions, Group, IImage, LassoBrush, LoadingType, ownDefaultsMouseStyle, PathBrush, RectBrush, RenderMaskCursor, WarningType } from "@meitu/whee-infinite-canvas";
import toast from "@/components/Toast";
import { useI18n } from "@/locales/client";
import { PaintBold, SelectionBoxBold, SelectionLassoBold } from "@meitu/candy-icons";
import PaintSize from "../../../AreaSelectionPanel/tool-params/PaintSize";
import styles from "./index.module.scss"
import QuantitySection from "../../../AreaSelectionPanel/params-options/QuantitySection";
import { Form } from "antd";
import { editorLayoutContent } from "@/app/[locale]/canvas/_constant/element";
import { observer } from "mobx-react-lite";
import { toSnakeCase } from "@meitu/util";
import runes from 'runes2';
import { createPureUpload } from "@/utils/uploader";
import { HeaderAction } from "@/app/[locale]/editor/_store/headerAction";
import { redrawImage } from "@/utils/redraw";
import { AIPoster, Submit } from "@/api/types/aiPoster/task";
import { submitTask } from "@/api/aiPoster/task";
import { useStore } from "@/contexts/StoreContext";
import { dispatch } from "@/app/[locale]/editor/_utils/TaskDispatcher";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { handleSubmitError } from "@/app/[locale]/editor/_utils/submit";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import SubmitButton from "../../../AreaSelectionPanel/SubmitButton";
import CancelAreaButton from "../../../AreaSelectionPanel/CancelAreaButton";
import { imageEditBrushOptions } from "./constants";
import { validateHasMaskArea } from "./utils";
import { useMeiDouBalance } from "@/hooks/useMeidou";

function ImageEditPanel () {

  const { userStore } = useStore();
  const rootStore = useRootStore();
  const { selectionStore, renderStore, projectsStore, headerActionStore, editorStatusStore } = rootStore;
  const { updateMeiDouBalance } = useMeiDouBalance({
    userStore,
  });
  const t = useI18n();
  const [form] = Form.useForm();
  const render = renderStore.render;
  const renderStyle = renderStore.renderStyle;
  const singleImage = selectionStore.singleImage;
  const historyPlugins = renderStore.historyPlugins;
  const image = singleImage?.image;
  const [activeTool, setActiveTool] = useState<SelectionTool>(SelectionTool.Rectangle);
  const [prompt, setPrompt] = useState("");
  const [brushWidth, setBrushWidth] = useState(30);
  const [areaMode, setAreaMode] = useState<AreaMode>(AreaMode.Add);
  const [hasArea, setHasArea] = useState(false);
  const { open: openSubscribeModal } = useSubscribeModal();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const imageOptions = selectionStore.singleImage?.options;
  const objectLoading = imageOptions?._loading_;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const handleChangeActiveTool = (tool: SelectionTool) => {
    setActiveTool(tool);
    setAreaMode(AreaMode.Add);
  }

  const brushes = useRef({
    [SelectionTool.Rectangle]: null as null | RectBrush,
    [SelectionTool.Lasso]: null as null | LassoBrush,
    [SelectionTool.Swipe]: null as null | PathBrush,
  });

  const getBrush = (key: SelectionTool) => {

    if (!render || !image) {
      return;
    }

    const brush = brushes.current[key];
    if (brush) {
      return brush;
    }

    switch(key) {
      case SelectionTool.Rectangle: {
        const brush = brushes.current[SelectionTool.Rectangle] = new RectBrush(render._FC, {
          ...imageEditBrushOptions,
          targetElement: image as IImage,
        });
        return brush;
      }

      case SelectionTool.Lasso: {
        const brush = brushes.current[SelectionTool.Lasso] = new LassoBrush(render._FC, {
          ...imageEditBrushOptions,
          strokeDashArray: [5, 5],
          targetElement: image as IImage,
        });
        return brush;
      }


      case SelectionTool.Swipe: {
        const brush = brushes.current[SelectionTool.Swipe] = new PathBrush(render._FC, {
          ...imageEditBrushOptions,
          width: brushWidth,
          targetElement: image as IImage,
          eraseShapeColor: '#FA99FF',
          eraseShapeOpacity: 0.3,
        });
        return brush;
      }
    }
  };

  const destroyBrushes = () => {
    Object.keys(brushes.current).forEach(key => {
      brushes.current[key as SelectionTool]?.destroy();
      brushes.current[key as SelectionTool] = null
    })
  };

  const getCursor = () => {
    const isErase = areaMode === AreaMode.Reduce;
    switch (activeTool) {
      // case ToolKey.SmartSwipe: {
      //   return isErase ? RenderMaskCursor.intelligentMinus : RenderMaskCursor.intelligentPlus;
      // }
      case SelectionTool.Rectangle: {
        return isErase ? RenderMaskCursor.rectMinus : RenderMaskCursor.rectPlus;
      }
      case SelectionTool.Lasso: {
        return isErase ? RenderMaskCursor.lassoMinus : RenderMaskCursor.lassoPlus;
      }
      case SelectionTool.Swipe: {
        return isErase ? RenderMaskCursor.pathMinus(brushWidth) : RenderMaskCursor.pathPlus(brushWidth);
      }
    }
    return 'default';
  }

  useEffect(() => {
    const selected = singleImage?.image;
    if (!render || !selected) {
      return;
    }

    const brush = getBrush(activeTool);
    if (!brush) {
      return;
    }

    brush.setErase(areaMode === AreaMode.Reduce);
    render._FC.isDrawingMode = true;
    render._FC.freeDrawingBrush = brush;
    render._FC.freeDrawingCursor = getCursor();
    renderStyle?.setCursorStyle({
      mousedown: getCursor(),
      move: getCursor(),
      hover: getCursor(),
      defaults: getCursor(),
    })

    return () => {
      render._FC.isDrawingMode = false
      render._FC.freeDrawingBrush = undefined
      renderStyle?.setCursorStyle({
        mousedown: ownDefaultsMouseStyle.mousedown,
        move: ownDefaultsMouseStyle.move,
        hover: ownDefaultsMouseStyle.hover,
        defaults: ownDefaultsMouseStyle.defaults,
      })
    }

  }, [render, activeTool, areaMode]);

  // 处理是否存在Mask区域
  const handleMaskChange = async () => {
    if (!render || !image) {
      return;
    }

    const brush = render._FC.freeDrawingBrush as PathBrush;
    if (!brush) {
      return;
    }
    const result = await validateHasMaskArea(brush);
    setHasArea(result);
  }
    
  
  /**
   * 处理涂抹区状态同步
   * 1. 涂抹区变化时 更新hasArea
   * 2. 涂抹区变化时 更新表单
   */
  useEffect(() => {
    if (!render) {
      return () => {
        destroyBrushes();
      };
    }

    handleMaskChange();

    render._FC.on({
      'mask:rect:created': handleMaskChange,
      'mask:path:created': handleMaskChange,
      'mask:lasso:created': handleMaskChange,
      'mask:smart:created': handleMaskChange,
      'mask:rect:deleted': handleMaskChange,
      'mask:path:deleted': handleMaskChange,
      'mask:lasso:deleted': handleMaskChange,
      'mask:smart:deleted': handleMaskChange,
    });

    return () => {
      render._FC.off({
        'mask:rect:created': handleMaskChange,
        'mask:path:created': handleMaskChange,
        'mask:lasso:created': handleMaskChange,
        'mask:rect:deleted': handleMaskChange,
        'mask:path:deleted': handleMaskChange,
        'mask:lasso:deleted': handleMaskChange,
      });
      destroyBrushes();
    }
  }, []);


  const handleClickCancelArea = () => {
    if (!render?._FC || !image || !hasArea) return
    const beforeData = historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
    getBrush(SelectionTool.Swipe)?.clearTargetElement();
    render?._FC.requestRenderAll();
    const afterData = historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
    const operation = historyPlugins?.baseAction.getModifiedOperation({
      beforeData,
      afterData,
    });
    if (!operation) return;
    historyPlugins?.submit(operation);
  }

  const [submitLoading, setSubmitLoading] = useState(false);
  const handleSubmit = async () => {
    if (submitLoading) {
      return;
    }


    const { batchSize } = form.getFieldsValue();

    const render = renderStore.render;
    const historyPlugins = renderStore.historyPlugins;
    const brush = render?._FC.freeDrawingBrush;
    const image = singleImage?.image;
    const imageOptions = singleImage?.options;
    const projectId = projectsStore.activeProjectId;
    if (
      !render ||
      !brush ||
      !image ||
      !projectId ||
      !imageOptions ||
      !historyPlugins
    ) {
      return;
    }

    // 图形loading
    const loadingOperation = historyPlugins.baseAction.getLoadOperation([
      image,
    ]);

    // 图形开始loading
    const startLoading = () => {
      render.Actions.setLoading({
        type: LoadingType.FADE_IN_TO_OUT,
        text: "Generating...",
        id: imageOptions._id_,
      });
      setSubmitLoading(true);
    };

    // 图形loading结束
    const loaded = () => {
      render.Actions.setLoaded(imageOptions._id_);
      setSubmitLoading(false);
    };

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== image
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    startLoading();
    const upload = createPureUpload();
    try {
      // 1. 导出画布上的mask
      const masksBlob = await brush.exportShapesToBlob({
        isMerge: true,
        ext: "jpeg",
        shapeFill: "#fff",
        backgroundFill: "#000000",
        exportContainerType: imageEditBrushOptions.shapeContainerName, // 导出容器类型
      });
      const beforeData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      render._FC.freeDrawingBrush?.clearTargetElement();
      const afterData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      const operation = historyPlugins?.baseAction.getModifiedOperation({
        beforeData,
        afterData,
      });
      abortController.signal.throwIfAborted();
      if (!masksBlob) {
        return;
      }

      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
      if (!operation) return;
      historyPlugins?.submit(operation);
      loadingOperation && historyPlugins.submit(loadingOperation);

      // 2. 导出画布上原图的blob
      const clippedInitImageBlob = await render.Actions.exportElementToBlob(
        image as Group,
        {
          includeType: [ElementName.IMAGE, ElementName.TEXT],
          exportType: "png",
          multiplier: 1,
        }
      );
      abortController.signal.throwIfAborted();
      if (!clippedInitImageBlob) {
        return;
      }

      // 3. 上传原图
      const initRes = await upload({ file: clippedInitImageBlob });
      abortController.signal.throwIfAborted();
      const clippedInitImageUrl = initRes.result;
      if (!clippedInitImageUrl?.previewUrl) {
        return;
      }

      // 4. 上传mask图
      const uploadContext = await Promise.all(
        masksBlob.map((blob) => {
          return upload({ file: blob });
        })
      );

      const maskUrls = uploadContext.map(
        (context) => context.result?.url ?? ""
      );

      const params: Submit.ImageEditParams = {
        batchSize: batchSize,
        initImage: clippedInitImageUrl.previewUrl,
        maskImage: maskUrls[0],
        prompt: prompt,
      };

      const res = await submitTask({
        projectId,
        params,
        taskCategory: AIPoster.TaskCategory.ImageEdit,
      });
      editorStatusStore.addActiveTask(res.id);

      const msgId = res.id;
      userStore.refreshMtBeanBalance();
      await dispatch({
        msgId,
        rootStore,
        shape: image,
        abortController,
        expandCustomData: {
          ...params,
          initWatermarkImage: imageOptions.src,
        },
        t,
      }).finally(() => {
        updateMeiDouBalance();
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === "development") {
        console.warn("改图", e);
      }

      if (abortController.signal.aborted) {
        return;
      }

      if (uploadErrorhandler(e, t)) {
        return;
      }

      if (
        handleSubmitError(e, rootStore, {
          subscribeModal: {
            open: openSubscribeModal,
          },
          userStore: userStore,
          openMeiDouRecordsPopup,
        })
      ) {
        return;
      }
    } finally {
      loaded();
      userStore.refreshMtBeanBalance();
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    }
  };

  const batchSize = Form.useWatch("batchSize", form);
  const functionBody = useMemo(() => {
    return JSON.stringify(toSnakeCase(form.getFieldsValue()));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchSize]);

  return createPortal((
    <AreaSelectionPanel
      prompt={prompt}
      promptPlaceholder={t("Enter your ideas for poster creation")}
      onPromptChange={setPrompt}
      activeTool={activeTool}
      onActiveToolChange={handleChangeActiveTool}
      areaMode={areaMode}
      onAreaModeChange={setAreaMode}
      
      tools={[
        {
          tookKey: SelectionTool.Rectangle,
          renderLabel: () => {
            return (
              <SelectionBoxBold className="radio-btn-icon"/>
            );
          }
        },
        {
          tookKey: SelectionTool.Lasso,
          renderLabel: () => {
            return (
              <SelectionLassoBold className="radio-btn-icon" />
            );
          }
        },
        {
          tookKey: SelectionTool.Swipe,
          renderLabel() {
            return (
              <PaintBold className="radio-btn-icon" />
            );
          },
          renderParams() {
            const handleBrushWidthChange = (value: number) => {  
              setBrushWidth(value);
              const brush = getBrush(SelectionTool.Swipe) as null | PathBrush;
              brush?.setWidth(value);
            }
            return (
              <PaintSize value={brushWidth} onChange={handleBrushWidthChange}/>
            );
          },
        }
      ]}
      form={{
        props: {
          form,
          initialValues: {
            batchSize: 4,
          },
        },
        renderFormItems() {
          return (
            <div className={styles.form}>
              <div className="batch-size">
                <div className="batch-size-label">
                  <strong>{t("Batch Size")}</strong>
                </div>
                <QuantitySection/>
              </div>
            </div>
          );
        }
      }}

      renderCancelSelectionButton={() => {
        return (
          <CancelAreaButton disabled={!hasArea} onClick={handleClickCancelArea}/>
        );
      }}

      renderSubmitButton={() => {
        return (
          <SubmitButton
            disabled={!hasArea}
            onClick={handleSubmit}
          />
        );
      }}
    />
  ), document.getElementById(editorLayoutContent)!);
}

export default observer(ImageEditPanel);