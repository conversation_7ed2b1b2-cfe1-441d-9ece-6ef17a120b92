import { AiGenerative } from "@meitu/candy-icons";
import { Tooltip } from "antd";
import classNames from "classnames";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/canvas/_store";
import { HeaderAction } from "@/app/[locale]/canvas/_store/headerAction";
import { AIPoster } from "@/api/types/aiPoster/task";
import { Fragment, useEffect } from "react";
import { isTextEditing } from "@/app/[locale]/canvas/_utils/isTextEditing";
import { useI18n } from "@/locales/client";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "@/app/[locale]/canvas/_constant/track";
import ImageEditPanel from "./ImageEditPanel";
import { useEnsureSelectSingleImage } from "@/app/[locale]/canvas/_hooks/useEnsureSelectSingleImage";

function ImageEditAction() {
  const t = useI18n();
  const rootStore = useRootStore();
  const { headerActionStore, renderStore } = rootStore;

  const { singleImage } = useEnsureSelectSingleImage();
  const image = singleImage?.image;
  const imageOptions = singleImage?.options;

  const objectLoading = imageOptions?._loading_;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const disabled =
    !image || objectLoading || imageStatus === AIPoster.ImageStatus.AuditFailed;

  //快捷键注册
  useEffect(() => {
    const renderHotkey = renderStore.renderHotkey;
    if (!renderHotkey || disabled) return;

    const hotkeyHandler = (event: KeyboardEvent) => {
      if (isTextEditing(renderStore.render)) {
        return;
      }
      event.preventDefault();
      headerActionStore.setActiveHeaderAction(HeaderAction.ModifyImage);
    };

    renderHotkey.registerHotKey("shift + i", hotkeyHandler);

    return () => {
      renderHotkey.unregisterHotKey("shift + i", hotkeyHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.renderHotkey, disabled]);

  if (!image || !imageOptions) {
    return null;
  }

  const isActive =
    headerActionStore.activeHeaderAction === HeaderAction.ModifyImage;

  const handleActiveTextEdit = () => {
    if (disabled) {
      return;
    }

    trackEvent("edit_page_top_function_click", {
      function: Track.FunctionEnum.ModifyImage,
    });

    if (headerActionStore.activeHeaderAction !== HeaderAction.ModifyImage) {
      headerActionStore.setActiveHeaderAction(HeaderAction.ModifyImage);
    } else {
      headerActionStore.activateActionAndResetCursor();
    }
  };

  return (
    <Fragment>
      <Tooltip title={t("Image Editor")}>
        <div
          className={classNames("actions-item", isActive && "selected", {
            disabled,
          })}
          onClick={handleActiveTextEdit}
        >
          <div className="icon-box">
            <AiGenerative />
          </div>
          <span className="icon-desc">{t("Image Editor")}</span>
        </div>
      </Tooltip>
      {isActive && <ImageEditPanel/>}
    </Fragment>
  );
}

export default observer(ImageEditAction);
