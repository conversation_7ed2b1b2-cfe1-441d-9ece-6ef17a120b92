import { FabricObject, IBaseBrush, Path } from "@meitu/whee-infinite-canvas";
import { getWorkerPool } from "../../../../_workers/pool";
import { CommonImageOptions } from "../../../../_store/selection";
import { AreaMode } from "../../../AreaSelectionPanel/types";

const pool = getWorkerPool();
/**
 * 转换智能涂抹参数
 * @param shape 
 * @param imageOptions 
 * @param mode 
 */
export function transformSmartSwipeParams(
    shape: Path,
    imageShape: FabricObject,
    imageOptions: CommonImageOptions,
    mode: AreaMode
) {
     // 计算图片短边
  const minEdge = Math.min(
    imageOptions.imageHeight,
    imageOptions.imageWidth
  )
  // 计算笔刷宽度相对短边
  const brushWidth = (
    shape.strokeWidth * imageOptions.scaleX / minEdge
  ).toString()
  // 判断是否是增加选区还是减少选区
  const isMinus = mode === AreaMode.Reduce

  // 计算path中的点相对于图片的left和top，基于图片的原始缩放计算
  const pointsOnPath = shape.path.map(command => {
    const [_cmd, ...args] = command
     return {x: args[0], y: args[1]}
  }) as {x: number, y: number}[]
  const realWidth = imageOptions.imageWidth * imageOptions.scaleX
  const realHeight = imageOptions.imageHeight * imageOptions.scaleY
  const imageLeft = imageShape.getCenterPoint().x - (realWidth) / 2
  const imageTop = imageShape.getCenterPoint().y - (realHeight) / 2
  const relativePointsByImage = pointsOnPath.map(({x, y}) => {
    return {
      x: (x - imageLeft) / (realWidth),
      y: (y - imageTop) / (realHeight),
    }
  })

  // 计算方向
  const direction = isMinus ? 0 : 1
  // 计算点击点
  const clickList = relativePointsByImage.map(({x, y}) => {
    return [
      direction,
      y,
      x,
    ]
  }) as ([number, number, number])[]
  return {
    brushWidth,
    clickList,
  }
}


/**
 * 验证是否存在mask区域
 * @param brush 
 * @returns 
 */
export async function validateHasMaskArea(
    brush: IBaseBrush
) {
    const imageData = brush.exportContainerToCanvas()
    if (!imageData) {
        return false
    }
   
    return await pool.exec('isImageDataHasTransparent', [imageData])
}

export async function loadImage(url: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.src = url
    img.crossOrigin = 'Anonymous'
    img.onload = () => {
      resolve(img)
    }
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
  })
}


export async function transformMaskImageData(maskUrl: string, rgb: [number, number, number]) {
    const image = await loadImage(maskUrl)
    const canvas = document.createElement('canvas')
    canvas.width = image.width
    canvas.height = image.height
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      return
    }
    ctx.drawImage(image, 0, 0)
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const newImageData = await pool.exec('imageDataInvertByMaskImage', [imageData, rgb])
    const offCanvas = document.createElement('canvas')
    offCanvas.width = image.width
    offCanvas.height = image.height
    const offCtx = offCanvas.getContext('2d')
    if (!offCtx) {
      return
    }
    offCtx.putImageData(newImageData, 0, 0)
    const dataUrl = offCanvas.toDataURL()
    const newImage = await loadImage(dataUrl)
    return newImage
}