import { observer } from "mobx-react";
import CutoutAction from "./CutoutAction";
import styles from "./index.module.scss";
import UpscalerAction from "./UpscalerAction";
import TextEditAction from "./TextEditAction";
import EraserAction from "./EraserAction";
import ImageEditAction from "./ImageEditAction";
function ImageEditActions() {
  return (
    <div className={styles.actions}>
      <CutoutAction />
      <UpscalerAction />
      <EraserAction />
      <ImageEditAction />
      <TextEditAction />
    </div>
  );
}

export default observer(ImageEditActions);
