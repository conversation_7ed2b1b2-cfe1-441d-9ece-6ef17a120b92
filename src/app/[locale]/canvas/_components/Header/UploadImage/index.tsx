import { PictureAdd, TextFont } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import classNames from "classnames";
import { Tooltip } from "antd";
import { observer } from "mobx-react-lite";
import { useRootStore } from "../../../_store";
import { HeaderAction } from "../../../_store/headerAction";
import { useI18n } from "@/locales/client";
import FilePicker from "@/components/FilePicker";
import { useUpload } from "../../../_hooks/useUpload";
import { trackEvent } from "@meitu/subscribe-intl";
import { useEffect, useRef } from "react";
import { isTextEditing } from "../../../_utils/isTextEditing";

type UploadImageProps = {
  disabled?: boolean;
};

function UploadImage({ disabled }: UploadImageProps) {
  const rootStore = useRootStore();
  const { headerActionStore, renderStore, editorStatusStore } = rootStore;
  const isActive =
    headerActionStore.activeHeaderAction === HeaderAction.InsertText;

  const t = useI18n();

  const upload = useUpload();

  const handleFileChange = async (fileList: FileList | null) => {
    const file = fileList?.[0];
    if (!file) {
      return;
    }
    await upload(file);

    if (editorStatusStore.showCreate) {
      trackEvent("upload_image_success", {
        location: "transition",
      });
    } else {
      trackEvent("upload_image_success", {
        location: "edit_top",
      });
    }
  };

  const handleUploadRef = useRef<(() => void) | null>(null);
  useEffect(() => {
    const renderHotkey = renderStore.renderHotkey;
    if (!renderHotkey) {
      return;
    }

    const uploadHotKey = (event: KeyboardEvent) => {
      const render = renderStore.render;
      if (disabled || !render || !renderHotkey || isTextEditing(render)) {
        return;
      }

      handleUploadRef.current?.();
      event.preventDefault();
    };

    renderHotkey.registerHotKey("u", uploadHotKey);
    return () => {
      renderHotkey.unregisterHotKey("u", uploadHotKey);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.renderHotkey]);

  return (
    <FilePicker accept={[".png", ".jpg", ".jpeg"]} onChange={handleFileChange}>
      {({ openPicker }) => {
        const handleClick = () => {
          if (disabled) {
            return;
          }

          headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
          editorStatusStore.setShowCreate(false);
          openPicker();
        };

        handleUploadRef.current = handleClick;

        return (
          <Tooltip title={`${t("header.Upload")} U`}>
            <button
              disabled={disabled}
              className={classNames(styles.upload, disabled && "disabled")}
              role="button"
              onClick={() => {
                if (editorStatusStore.showCreate) {
                  trackEvent("transition_page_click", {
                    click_type: "upload",
                    location: "top",
                  });

                  trackEvent("upload_image_click", {
                    location: "transition",
                  });
                } else {
                  trackEvent("upload_image_click", {
                    location: "edit_top",
                  });
                }

                handleClick();
              }}
            >
              <PictureAdd />
            </button>
          </Tooltip>
        );
      }}
    </FilePicker>
  );
}

export default observer(UploadImage);
