@import '@/styles/variable.scss';
@import '../zIndex.scss';


.helper {
  z-index: $z-index-shortcut-helper;
  box-sizing: border-box;
  width: 44px;
  height: 44px;
  border: 1px solid $system-stroke-button;
  display: flex;
  justify-content: center;
  align-items: center;

  position: fixed;
  right: 12px;
  bottom: 16px;
  transition: all 0.4s cubic-bezier(0.33, 1, 0.68, 1);

  background: $system-background-thirdary;
  border-radius: 50%;

  &.panel-expanded {
    right: 412px; // 400px (面板宽度)
  }

  user-select: none;
  cursor: pointer;

  &:hover {
    // color: $content-btn-transparency;
    background: rgba(247, 248, 250, 1);
  }

  &:active {
    opacity: 0.95;
  }

  svg {
    width: 18px;
    height: 18px;
    color: $system-content-secondary;
  }

  &:hover:not(:global(.disabled)):not(:global(.selected)) {
    background: $system-background-thirdary;
    box-shadow: 0px 0px 0px 1px $system-stroke-button inset;

    svg {
      color: #fff;
    }
  }

}

.shortcut-helper-modal-wrap {
  .shortcut-helper-modal {
    :global(.ant-modal-content) {
      height: 527px;
      padding: 0;
      padding-top: 17px;
      border-radius: var(--radius-12, 12px);
      border: 1px solid var(--system-stroke-input, #22272E);
      background: var(--system-background-secondary, #1D1E23);
      /* level_3 */
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);
      display: flex;
      flex-direction: column;

      :global(.ant-modal-header) {
        text-align: center;
        margin-bottom: 40px;
      }

      :global(.ant-modal-body) {
        overflow: auto;
        padding: 0 40px;
        flex: 1;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .shortcut-helper-modal-content {
          display: flex;
          // align-items: center;
          justify-content: center;
          padding-bottom: 40px;

          .column {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;

            min-height: 300px;

            .row {
              &:last-child {
                margin-bottom: 0;
              }

              margin-bottom: 24px;
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .text {
                color: var(--system-content-thirdary, #6B7A8F);
                font-size: 14px;
              }

              .key-wrapper {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                .key-container {
                  display: flex;
                  min-width: 30px;
                  height: 30px;
                  padding: 5px 8px;
                  box-sizing: border-box;
                  justify-content: center;
                  align-items: center;
                  border-radius: var(--radius-6, 6px);
                  border: 1px solid var(--system-stroke-button, #323B48);
                  background: var(--system-content-fifth, #303741);
                }

                .sign {
                  color: var(--system-content-thirdary, #6B7A8F);
                  font-size: 14px;
                  margin: 0 6px;
                }
              }
            }
          }

          .divider {
            width: 1px;
            background: #1f2329;
            margin: 0 39px;
          }
        }
      }

      :global(.ant-modal-close) {
        color: #606f84;

        &:hover {
          color: #fff;
        }
      }
    }
  }
}