"use client";

import { QuestionMarkCircleBold } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import { Fragment } from "react";
import useModal from "./useModal";
import { useRootStore } from "@/app/[locale]/canvas/_store";
import { observer } from "mobx-react";
import classNames from "classnames";

export const ShortcutHelper = observer(function () {
  const { editorStatusStore } = useRootStore();

  const { contextHolder, open } = useModal();
  const handleOpen = () => {
    open();
  };
  return !editorStatusStore.isClearScreenUI ? (
    <Fragment>
      <div
        className={classNames(
          styles.helper,
          editorStatusStore.isAgentExpanded && styles["panel-expanded"]
        )}
        onClick={handleOpen}
      >
        <QuestionMarkCircleBold />
      </div>
      {contextHolder}
    </Fragment>
  ) : null;
});
