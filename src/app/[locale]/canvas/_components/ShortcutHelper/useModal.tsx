import { Modal } from "antd";
import { useEffect, useRef, useState } from "react";
import styles from "./index.module.scss";
import { CrossBlack } from "@meitu/candy-icons";
import { isWindows } from "@/utils/getOperationSystem";
import { useI18n, useScopedI18n } from "@/locales/client";

const useModal = () => {
  const t = useI18n();
  const scopedT = useScopedI18n("shortcuts");
  const [visible, setVisible] = useState(false);
  const open = () => setVisible(true);
  const close = () => setVisible(false);
  const keyCurrent = useRef("⌘");
  useEffect(() => {
    const key = isWindows() ? "Ctrl" : "⌘";
    keyCurrent.current = key;
  }, []);
  const hotKeyLeftColumn = [
    {
      name: scopedT("Generate"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Enter</div>
        </div>
      ),
    },
    {
      name: scopedT("Generate Image"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>G</div>
        </div>
      ),
    },
    {
      name: t("Upload"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>U</div>
        </div>
      ),
    },
    {
      name: t("Text"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>T</div>
        </div>
      ),
    },
    {
      name: t("Palette"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>F</div>
        </div>
      ),
    },
    {
      name: t("Select"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>S</div>
        </div>
      ),
    },
    {
      name: t("Hand tool"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>H</div>
        </div>
      ),
    },
    {
      name: t("Image Editor"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>I</div>
        </div>
      ),
    },
    {
      name: t("Image Text Editor"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>T</div>
        </div>
      ),
    },
    {
      name: scopedT("Background Remover"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>R</div>
        </div>
      ),
    },
    {
      name: scopedT("Object Remover"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>A</div>
        </div>
      ),
    },
    {
      name: t("Image Extender"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>E</div>
        </div>
      ),
    },
    // {
    //   name: '分层',
    //   key: (
    //     <div className={styles['key-wrapper']}>
    //       <div className={styles['key-container']}>Shift</div>
    //       <div className={styles.sign}>+</div>
    //       <div className={styles['key-container']}>S</div>
    //     </div>
    //   )
    // },
    {
      name: t("Image Enhancer"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>U</div>
        </div>
      ),
    },
    // {
    //   name: '矢量',
    //   key: (
    //     <div className={styles['key-wrapper']}>
    //       <div className={styles['key-container']}>Shift</div>
    //       <div className={styles.sign}>+</div>
    //       <div className={styles['key-container']}>V</div>
    //     </div>
    //   )
    // },
    {
      name: scopedT("Show/Hide UI"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>\</div>
        </div>
      ),
    },
    {
      name: scopedT("Fit to Screen"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>1</div>
        </div>
      ),
    },
    {
      name: scopedT("Fit to Selection"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>Shift</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>2</div>
        </div>
      ),
    },
    // {
    //   name: '移动图层',
    //   key: (
    //     <div className={styles['key-wrapper']}>
    //       <div className={styles['key-container']}>↑</div>
    //       <div className={styles['key-container']}>↓</div>
    //       <div className={styles['key-container']}>←</div>
    //       <div className={styles['key-container']}>→</div>
    //     </div>
    //   )
    // },
    // {
    //   name: '大幅移动图层',
    //   key: (
    //     <div className={styles['key-wrapper']}>
    //       <div className={styles['key-container']}>⇧ Shift</div>
    //       <div className={styles.sign}>+</div>
    //       <div className={styles['key-container']}>↑</div>
    //       <div className={styles['key-container']}>↓</div>
    //       <div className={styles['key-container']}>←</div>
    //       <div className={styles['key-container']}>→</div>
    //     </div>
    //   )
    // }
  ];
  const hotKeyRightColumn = [
    {
      name: scopedT("Copy"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>C</div>
        </div>
      ),
    },
    {
      name: t("Paste"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>V</div>
        </div>
      ),
    },
    {
      name: scopedT("Crop"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>X</div>
        </div>
      ),
    },
    {
      name: scopedT("Delete"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>⌫</div>
          <div className={styles.sign}>{scopedT("or")}</div>
          <div className={styles["key-container"]}>Delete</div>
        </div>
      ),
    },
    {
      name: t("Combination"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>G</div>
        </div>
      ),
    },
    {
      name: t("Cancel combination"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>⌫</div>
        </div>
      ),
    },
    {
      name: scopedT("Undo"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>Z</div>
        </div>
      ),
    },
    {
      name: scopedT("Redo"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>Y</div>
        </div>
      ),
    },
    {
      name: scopedT("100%"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>0</div>
        </div>
      ),
    },
    {
      name: t("Zoom in"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>+</div>
        </div>
      ),
    },
    {
      name: t("Zoom out"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>-</div>
        </div>
      ),
    },
    {
      name: scopedT("Bring to front"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>]</div>
        </div>
      ),
    },
    {
      name: scopedT("Send to back"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>{keyCurrent.current}</div>
          <div className={styles.sign}>+</div>
          <div className={styles["key-container"]}>[</div>
        </div>
      ),
    },
    {
      name: scopedT("Bring forward"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>]</div>
        </div>
      ),
    },
    {
      name: scopedT("Send backward"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>[</div>
        </div>
      ),
    },
    {
      name: scopedT("Increase brush size"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>+</div>
        </div>
      ),
    },
    {
      name: scopedT("Decrease brush size"),
      key: (
        <div className={styles["key-wrapper"]}>
          <div className={styles["key-container"]}>-</div>
        </div>
      ),
    },
  ];

  const contextHolder = (
    <Modal
      width={880}
      title={t("Shortcuts")}
      open={visible}
      onOk={open}
      onCancel={close}
      footer={null}
      wrapClassName={styles["shortcut-helper-modal-wrap"]}
      className={styles["shortcut-helper-modal"]}
      centered
      closeIcon={<CrossBlack />}
      destroyOnClose
    >
      <div className={styles["shortcut-helper-modal-content"]}>
        <div className={styles.column}>
          {hotKeyLeftColumn.map((item) => (
            <div key={item.name} className={styles.row}>
              <div className={styles.text}>{item.name}</div>
              <div className={styles["key-wrapper"]}>{item.key}</div>
            </div>
          ))}
        </div>
        <div className={styles.divider}></div>
        <div className={styles.column}>
          {hotKeyRightColumn.map((item) => (
            <div key={item.name} className={styles.row}>
              <div className={styles.text}>{item.name}</div>
              <div className={styles["key-wrapper"]}>{item.key}</div>
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
  return {
    open,
    close,
    contextHolder,
  };
};
export default useModal;
