@import '@/styles/variable.scss';

.TemplatePanel {
  .entryBox {
    transition: all 0.4s cubic-bezier(0.33, 1, 0.68, 1);
    opacity: 1;
    visibility: visible;
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 3;
    width: 145px;
    height: 34px;
    border-radius: var(--radius-10, 10px);
    border: 1px solid $system-stroke-button;
    background: $system-background-thirdary;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &.hidden {
      opacity: 0;
      visibility: hidden;
      pointer-events: none;
    }

    .entryName {
      color: $system-content-secondary;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%;
      margin: 0 10px 0 5px;
    }

    svg {
      width: 18px;
      height: 18px;
      color: $icon-color-secondary;
    }
  }

  .listBox {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    width: 254px;
    height: 100%;
    border-right: 1px solid $system-stroke-input;
    background: $system-background-secondary;
    transition: all 0.4s cubic-bezier(0.33, 1, 0.68, 1);
    transform-origin: top left;
    opacity: 1;
    transform: none;
    visibility: visible;
    overflow: hidden;

    &.collapsed {
      opacity: 0;
      width: 0;
      height: 0;
      visibility: hidden;
      pointer-events: none;
    }

    .topBox {
      width: 100%;
      height: 48px;
      box-sizing: border-box;
      border-bottom: 1px solid $system-stroke-divider;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px;

      .nameBox {
        display: flex;
        justify-content: center;
        align-items: center;

        .topName {
          margin-right: 4px;
          color: $system-content-primary;
          font-family: Inter;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
        }

        svg {
          width: 14px;
          height: 14px;
          color: $icon-color-thirdary;
        }
      }

      svg {
        width: 16px;
        height: 16px;
        color: $icon-color-secondary;
        cursor: pointer;
      }
    }

    .contentBox {
      height: 100%;
      margin-top: 16px;
    }
  }
}

.templateTips {
  &:global(.ant-tooltip) {
    width: 280px;
    height: 264px;
    max-width: max-content;
    border-radius: var(--radius-12, 12px);
    border: 1px solid $system-stroke-button;
    background: $background-editor-popup-default;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);
    padding: 16px;

    :global {
      .ant-tooltip-inner {
        background: transparent;
        padding: 0;
        color: $system-content-primary;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
      }
    }

    .templateTipsContent {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .contentBox {
        width: 248px;
        height: 140px;
        border-radius: var(--radius-6, 6px);
        border: 1px solid var(--system-stroke-content, rgba(255, 255, 255, 0.10));
      }

      .desc {
        margin: 16px 0;
      }

      .button {
        width: 248px;
        height: 36px;
        border-radius: var(--radius-8, 8px);
        border-top: 1px solid rgba(255, 255, 255, 0.50);
        background: $system-content-brand-primary;
        color: $system-content-on-primary;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 130%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}