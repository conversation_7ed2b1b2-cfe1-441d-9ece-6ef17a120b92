import { observer } from "mobx-react";
import styles from "./index.module.scss";

import {
  ArrowExpandBold,
  ArrowShrinkBold,
  QuestionMarkCircle,
  TemplateBold,
} from "@meitu/candy-icons";
import Template from "./Template";

import classNames from "classnames";
import { Tooltip } from "antd";
import { useRootStore } from "../../_store";
import { useEffect, useRef } from "react";

const TemplatePanel = () => {
  const { editorStatusStore } = useRootStore();
  const isExpanded = editorStatusStore.isTemplateExpanded;
  const setIsExpanded = (expand: boolean) =>
    editorStatusStore.setTemplateExpanded(expand);

  const elementRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    editorStatusStore.templatePanelContainer = elementRef.current;
  });

  return (
    <div className={styles.TemplatePanel}>
      <div
        className={classNames(styles.entryBox, isExpanded && styles.hidden)}
        onClick={() => setIsExpanded(true)}
      >
        <TemplateBold />
        <span className={styles.entryName}>Templates</span>
        <ArrowExpandBold />
      </div>
      <div
        className={classNames(styles.listBox, !isExpanded && styles.collapsed)}
        ref={elementRef}
      >
        <div className={styles.topBox}>
          <div className={styles.nameBox}>
            <span className={styles.topName}>Template</span>
            <Tooltip
              arrow={false}
              rootClassName={styles.templateTips}
              align={{
                offset: [14, -10],
              }}
              title={
                <div className={styles.templateTipsContent}>
                  <div className={styles.contentBox}>1111</div>
                  <div className={styles.desc}>Layer-Control Editing</div>
                  <div className={styles.button}>Button</div>
                </div>
              }
            >
              <QuestionMarkCircle />
            </Tooltip>
          </div>
          <ArrowShrinkBold onClick={() => setIsExpanded(false)} />
        </div>
        <div className={styles.contentBox}>
          <Template />
        </div>
      </div>
    </div>
  );
};

export default observer(TemplatePanel);
