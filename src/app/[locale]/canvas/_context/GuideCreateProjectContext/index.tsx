"use client";
import { createContext, useContext, useRef } from "react";
import { useRootStore } from "../../_store";
import { useCreateProjectInEditor } from "../../_hooks/useCreateProjectInEditor";
import { usePathname } from "next/navigation";
import {
  SaveProjectRequest,
  SaveProjectResponse,
} from "@/api/types/aiPoster/project";
import defer, { Defer } from "@/utils/defer";
import { saveElements } from "../../_utils/saveProjectHistory";

type GuideCreateProjectContextType = {
  createGuideProject: (
    project?: Partial<Omit<SaveProjectRequest, "projectId">>
  ) => Promise<SaveProjectResponse | undefined>;
  isInGuidePage: boolean;
};

const GuideCreateProjectContext = createContext<GuideCreateProjectContextType>({
  createGuideProject: () => {
    return Promise.resolve(undefined);
  },
  isInGuidePage: false,
});

type GuideCreateProjectContextProviderProps = React.PropsWithChildren;
export function GuideCreateProjectContextProvider({
  children,
}: GuideCreateProjectContextProviderProps) {
  const { projectsStore, renderStore } = useRootStore();
  const { createProject } = useCreateProjectInEditor();
  const pathname = usePathname();
  const creatingDefer = useRef<Defer<SaveProjectResponse | undefined> | null>(
    null
  );

  const isInGuidePage = pathname.startsWith("/canvas/project/guide");

  const createProjectInGuide = async (
    project?: Partial<Omit<SaveProjectRequest, "projectId">>
  ) => {
    const render = renderStore.render;
    if (!render) {
      return;
    }

    const projectId = projectsStore.activeProjectId;
    /**
     * 1. 如果正在创建项目 不需要重复创建
     */
    if (creatingDefer.current) {
      return creatingDefer.current.promise;
    }

    /**
     * 2. 如果当前不在引导页面 不需要处理
     */
    if (!isInGuidePage || projectId) {
      return;
    }
    try {
      creatingDefer.current = defer();
      const res = await createProject({ backfill: false, project });
      if (res) {
        await saveElements({
          targets: render._FC.getObjects(),
          render,
          projectId: res.projectId,
        });
      }

      creatingDefer.current.resolve(res);
      return res;
    } catch (e) {
      if (process.env.NODE_ENV === "development") {
        console.error(e);
      }
    } finally {
      creatingDefer.current = null;
    }
  };

  return (
    <GuideCreateProjectContext.Provider
      value={{
        createGuideProject: createProjectInGuide,
        isInGuidePage,
      }}
    >
      {children}
    </GuideCreateProjectContext.Provider>
  );
}

/**
 * 用来处理在功能引导创建新项目
 */
export function useGuideCreateProject() {
  return useContext(GuideCreateProjectContext);
}
