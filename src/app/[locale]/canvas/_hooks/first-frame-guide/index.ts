/**
 * 这个hook用来创建第一次插入frame时的引导
 * 
 * 1. frame中内容为默认模版
 * 2. frame左侧为引导内容
 */

import { TemplateItemType } from "@/api/types/poster";
import { ConfigStore } from "../../_store/config";
import { RenderStore } from "../../_store/render";
import { useInsertTemplate } from "../../_store/useInsertTemplate";
import defaultTemplate from './default-template.json';
import { needShowGuide, recordGuideShown } from "../../_utils/frame-guide/record";
import { UserStore } from "@/stores/UserStore";

type UseInsertFirstFrameGuideDeps = {
  configStore: ConfigStore;
  renderStore: RenderStore;
  userStore: UserStore;
}

export function useInsertFirstFrameGuide({
  configStore,
  renderStore,
  userStore,
}: UseInsertFirstFrameGuideDeps) {
  const insertTemplate = useInsertTemplate({
    configStore,
    renderStore,
  });

  return async function() {
    const uid = userStore.UID;
    if (!uid) {
      return;
    }

    const needsGuide = needShowGuide(uid, "frame");
    const elements = await insertTemplate(defaultTemplate as unknown as TemplateItemType, { needsGuide });
    recordGuideShown(uid, "frame");

    return elements;
  }
}