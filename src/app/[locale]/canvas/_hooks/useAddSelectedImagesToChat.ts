import { useAgentContext } from "@/components/Agent/context";
import { useRootStore } from "../_store";
import { Agent } from "@/components/Agent/types";
import { useMemo } from "react";
import { ElementName, getElementOptions, IImage, ImageOptions } from "@meitu/whee-infinite-canvas";

export function useAddSelectedImagesToChat() {

  const agentContext = useAgentContext();
  const { selectionStore, renderStore } = useRootStore();
  
  const activeObjects = selectionStore.activeObjects;
  const render = renderStore.render;

  const selectedImages = useMemo(() => {
    if (!render || !activeObjects.length ||
      !activeObjects.every(shape => {
        return getElementOptions.call(render, shape)._name_ === ElementName.IMAGE
      })
    ) {
      return [];
    }

    return activeObjects as IImage[];
  }, [render, activeObjects])

  
  const handleAddToChat = () => {
    if (!selectedImages.length || !render) {
      return;
    }

    selectedImages.forEach(image => {
      const options = getElementOptions.call(render, image) as ImageOptions;
      agentContext.input.unshiftImage({
        type: Agent.InputImageType.Exist,
        src: options.src,
      });
    })
  };
  
  return {
    selectedImages,
    addToChat: handleAddToChat
  };
}