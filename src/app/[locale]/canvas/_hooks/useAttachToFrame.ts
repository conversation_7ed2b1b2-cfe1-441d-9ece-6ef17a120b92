import { createText, Group } from "@meitu/whee-infinite-canvas";
import { RenderStore } from "../_store/render";


export type UseAttachToFrameOptions = {
  preset: "init" | "default",
}
export type UseAttachToFrameDeps = {
  renderStore: RenderStore,
}
export function useAttachToFrame(deps: UseAttachToFrameDeps) {

  return function (frame: Group, options: UseAttachToFrameOptions) {
    switch(options.preset) {
      case "init": return attachInitElements(frame, options, deps);
      case "default": return attachDefaultElements(frame, options, deps);
    }
  }
}

function attachInitElements(frame: Group, options: UseAttachToFrameOptions, deps: UseAttachToFrameDeps) {

}

function attachDefaultElements(frame: Group, options: UseAttachToFrameOptions, deps: UseAttachToFrameDeps) {

  const render = deps.renderStore.render;
  if (!render) {
    return;
  }

  const titleFontSize = 36;
  const subtitleFontSize = 23;

  const title = createText(frame._id_, {
    text: "Your Poster Title",
    fill: "#000",
    fontSize: titleFontSize,
    fontFamily: "Roboto-Bold",
  });

  title.set({
    left: 0,
    top: 0,
  });

  const subtitle = createText(frame._id_, {
    text: "Generate posters with text",
    fill: "#000",
    fontSize: subtitleFontSize,
    fontFamily: "Roboto-Regular",
  });

  subtitle.set({
    left: 0,
    top: title.getScaledHeight() + 16,
  });

  const group = new Group();
  group.add(title, subtitle);

  const paddingTop = 0.07 * frame.getScaledHeight();
  
  const widthScale = 0.85 * frame.getScaledWidth() / group.getScaledWidth();
  group.scale(widthScale);
  
  const heightScale = Math.min(frame.getScaledHeight() / (paddingTop + group.getScaledHeight()), 1);
  const scale = widthScale * heightScale;

  group.removeAll();

  let offsetTop = paddingTop + frame.top - frame.getScaledHeight() / 2;
  title.set({
    fontSize: scale * titleFontSize,
    scaleX: 1,
    scaleY: 1
  });

  title.set({
    left: frame.left,
    top: offsetTop + title.getScaledHeight() / 2,
  });

  offsetTop += title.getScaledHeight() + scale * 16;

  subtitle.set({
    fontSize: scale * subtitleFontSize,
    scaleX: 1,
    scaleY: 1,
  });

  subtitle.set({
    left: frame.left,
    top: offsetTop + subtitle.getScaledHeight() / 2,
  });

  frame.add(title, subtitle);

  return [title, subtitle];
}
