import { useEffect, useState } from 'react';

export function useDelayMemo<T>(getter: () => T, delay = 0) {
  const [state, setState] = useState(getter());
  const nextState = getter();

  useEffect(() => {
    if (nextState === state) {
      return;
    }

    const timeoutKey = setTimeout(() => {
      setState(nextState);
    }, delay);

    return () => {
      clearTimeout(timeoutKey);
    };
  }, [state, nextState, delay]);

  return state;
}
