import { every } from "lodash";
import { ProjectsStore } from "@/app/[locale]/canvas/_store/projects";
import { RenderStore } from "@/app/[locale]/canvas/_store/render";
import { SelectionStore } from "@/app/[locale]/canvas/_store/selection";
import {
  exportImage,
  WatermarkOptions,
} from "@/app/[locale]/canvas/_utils/exportImage";
import { useMemo, useState } from "react";
import {
  ContainerElementOptions,
  ElementName,
  ElementOptions,
  Group,
  ImageOptions,
} from "@meitu/whee-infinite-canvas";
import { AIPoster } from "@/api/types/aiPoster/task";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import { downloadFile } from "@/utils/blob";
import JSZip from "jszip";
import { v4 as uuid } from "uuid";
import mime from "mime";
import { UserStore } from "@/stores/UserStore";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { downloadImage } from "@/api/task";

type UseExportImageDeps = {
  selectionStore: SelectionStore;
  projectsStore: ProjectsStore;
  renderStore: RenderStore;
  userStore: UserStore;
  subscribeModal: ReturnType<typeof useSubscribeModal>;
};

type ExportOptions = {
  removeWatermark?: boolean;
};

// 仅有图片和组合支持下载
const downloadableElements = new Set<string>([
  ElementName.IMAGE,
  ElementName.CONTAINER,
]);

export function useExportShape({
  selectionStore,
  projectsStore,
  renderStore,
  userStore,
  subscribeModal,
}: UseExportImageDeps) {
  const activeObjects = selectionStore.activeObjects;
  const activeOptions = selectionStore.objectsOptions;
  const activeProjectId = projectsStore.activeProjectId;
  const { open } = subscribeModal;

  const downloadable = useMemo(() => {
    return (
      // 1.选中了图形
      !!activeObjects.length &&
      // 2.选中的图形都是可以下载的类型
      every(activeObjects, (obj) => downloadableElements.has(obj._name_)) &&
      // 3.不是所有的图形都在loading
      !every(activeObjects, (_, i) => {
        const options = activeOptions[i];
        return options._loading_;
      }) &&
      // 4. 不是所有的图形都是失败图
      !every(activeObjects, (_, i) => {
        const imageStatus = (activeOptions[i] as ImageOptions)
          ?._custom_data_history_?.imageStatus;
        return imageStatus === AIPoster.ImageStatus.AuditFailed;
      })
    );
  }, [activeObjects, activeOptions]);

  const [downloading, setDownloading] = useState(false);
  async function download(options: ExportOptions) {
    const render = renderStore.render;
    if (!downloadable || !activeProjectId || !render) {
      return;
    }

    setDownloading(true);
    try {
      if (options.removeWatermark) {
        await userStore.refreshUserInfo();
        if (!userStore.isVipCurrent) {
          return open({ productType: SubscribeModalType.Watermark });
        }
      }

      const downloadPromises = activeOptions.map((objOptions, i) => {
        const shape = activeObjects[i] as Group | undefined;
        if (!shape) {
          return;
        }

        const watermark: undefined | WatermarkOptions =
          isNeedWatermark(objOptions) && !options.removeWatermark
            ? {
                position: "SourceWest",
                offset: [40, 40],
              }
            : undefined;

        // 上报到后台 非业务逻辑
        if (
          objOptions._name_ === ElementName.IMAGE &&
          objOptions._custom_data_history_?.msgId &&
          (<ImageOptions>objOptions).src
        ) {
          downloadImage({
            msgId: objOptions._custom_data_history_.msgId,
            imageUrl: (<ImageOptions>objOptions).src,
          });
        }

        return exportImage({ render, shape, watermark });
      });

      const files = await Promise.allSettled(downloadPromises);
      if (files.length === 1) {
        if (files[0].status === "fulfilled" && files[0].value) {
          return downloadFile(files[0].value, uuid());
        }
      }

      const zip = JSZip();
      files.forEach((file) => {
        if (file.status !== "fulfilled" || !file.value) {
          return;
        }

        zip.file(`${uuid()}.${mime.getExtension(file.value.type)}`, file.value);
      });

      const zipBlob = await zip.generateAsync({ type: "blob" });
      downloadFile(zipBlob, uuid());
    } catch (e) {
      defaultErrorHandler(e);
    } finally {
      setDownloading(false);
    }
  }
  return {
    downloading,
    downloadable,
    download,
  };
}

/**
 * 判断被选中的图形是否需要加水印
 * 1. 如果是图片元素，非用户上传的图片（有msgId），需要加水印
 * 2. 如果是图片元素，且为模版预览图（_custom_data_history_记录了isFromTemplatePreview），需要加水印
 * 2. 如果是group，检查所有子元素中是否包含需要加水印的图片，如果存在，则group需要加水印
 */
function isNeedWatermark(options: ElementOptions) {
  /**
   * 判断图片是否需要加水印
   * 如果图片包含了msgId，表示是AI生成的结果，因此需要加水印
   */
  const isImageNeedsWatermark = (imageOptions: ImageOptions) => {
    const customData = imageOptions?._custom_data_history_ ?? {};
    return !!customData.msgId || customData.isFromTemplatePreview;
  };

  const isGroupNeedsWatermark = (groupOptions: ContainerElementOptions) => {
    for (const options of groupOptions.children) {
      if (
        options._name_ === ElementName.IMAGE &&
        isImageNeedsWatermark(options as ImageOptions)
      ) {
        return true;
      }

      if (
        options._name_ === ElementName.CONTAINER &&
        isGroupNeedsWatermark(options as ContainerElementOptions)
      ) {
        return true;
      }
    }

    return false;
  };

  if (options._name_ === ElementName.IMAGE) {
    return isImageNeedsWatermark(options as ImageOptions);
  }

  if (options._name_ === ElementName.CONTAINER) {
    return isGroupNeedsWatermark(options as ContainerElementOptions);
  }

  return true;
}
