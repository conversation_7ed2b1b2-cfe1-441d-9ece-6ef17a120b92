import { useCallback, useEffect, useLayoutEffect, useRef, useState } from "react";
import { useRootStore } from "../_store";
import { clamp, extend } from "lodash-es";
import { FabricObject, util } from "@meitu/whee-infinite-canvas";

type UseFollowTargetsDeps = {
  // 跟随的对象
  targets?: FabricObject[]

  // 与图片的距离 
  offset: number;

  // 通过边距定义展示范围 [top, right, bottom, left]
  rangePadding: [number, number, number, number];
}
export function useFollowTargets<T extends HTMLElement>({
  targets,
  offset,
  rangePadding
}: UseFollowTargetsDeps) {
  
  const [el, setEl] = useState<T | null>(null);

  const { renderStore } = useRootStore();
  const render = renderStore.render;
  const [translate, setTranslate] = useState({ x: 0, y: 0 });

  useLayoutEffect(() => {
    if (!targets?.length || !render || !el) {
      return;
    }

    const [topPadding, rightPadding, bottomPadding, leftPadding] = rangePadding;

    let elBBox = el.getBoundingClientRect();
    const updateElBBox = () => {
      elBBox = el.getBoundingClientRect();
    };

    const handleUpdatePosition = () => {

      const points = targets.flatMap(shape => {
        const bBox = shape.calcACoords();
        return [bBox.tl, bBox.bl, bBox.br, bBox.tr].map(p => {
          return p.transform(util.invertTransform(shape.calcOwnMatrix()))
            .transform(shape.calcTransformMatrix())
            .transform(render._FC.viewportTransform);
        });
      });

      const { minX, minY, maxX, maxY } = points.reduce((acc, p) => {
        acc.minX = Math.min(acc.minX, p.x);
        acc.minY = Math.min(acc.minY, p.y);
        acc.maxX = Math.max(acc.maxX, p.x);
        acc.maxY = Math.max(acc.maxY, p.y);
        return acc;
      }, {
        minX: points[0].x,
        minY: points[0].y,
        maxX: points[0].x,
        maxY: points[0].y,
      });

      const clientPoints = [
        { x: minX, y: minY },
        { x: minX, y: maxY },
        { x: maxX, y: maxY },
        { x: maxX, y: minY },
      ];

      const left = leftPadding;
      const right = render._FC.width - rightPadding - elBBox.width;
      const top = topPadding;
      const bottom = render._FC.height - bottomPadding - elBBox.height;

      setTranslate({
        x: clamp((clientPoints[1].x + clientPoints[2].x) / 2 - elBBox.width / 2, left, right),
        y: clamp((clientPoints[2].y + clientPoints[2].y) / 2 + offset, top,  bottom),
      });
    }

    handleUpdatePosition();


    const resizeObserver = new ResizeObserver(() => {
      updateElBBox();
      handleUpdatePosition();
    });

    resizeObserver.observe(el);
    resizeObserver.observe(render._FC.wrapperEl);

    render._FC.on({
      "viewport:zoom": handleUpdatePosition,
      "viewport:translate": handleUpdatePosition,
      "object:moving": handleUpdatePosition,
      "object:scaling": handleUpdatePosition,
      "object:rotating": handleUpdatePosition,
      "object:resizing": handleUpdatePosition,
      "object:skewing": handleUpdatePosition,
    });

    return () => {
      render._FC.off({
        "viewport:zoom": handleUpdatePosition,
        "viewport:translate": handleUpdatePosition,
        "object:moving": handleUpdatePosition,
        "object:scaling": handleUpdatePosition,
        "object:rotating": handleUpdatePosition,
        "object:resizing": handleUpdatePosition,
        "object:skewing": handleUpdatePosition,
      });
      resizeObserver.disconnect();
    };
  }, [targets, el, render, ...rangePadding]);

  return {
    elementRef: useCallback((element: T) => {
      if (element !== el) {
        setEl(element); 
      }
    }, []),
    translate,
  }
}