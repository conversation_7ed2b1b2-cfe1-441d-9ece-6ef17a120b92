import { TemplateItemType } from "@/api/types/poster";
import { useInsertFrameGuide } from "../_hooks/useInsertFrameGuide";
import { insertTemplate } from "../_utils/insertTemplate";
import { FabricObject } from "@meitu/whee-infinite-canvas";
import _ from "lodash";
import { UserStore } from "@/stores/UserStore";
import { needShowGuide, recordGuideShown } from "../_utils/frame-guide/record";
import { ConfigStore } from "../_store/config";
import { RenderStore } from "../_store/render";

type UseInsertTemplateDeps = {
  configStore: ConfigStore;
  renderStore: RenderStore;
};

type InsertTemplateOptions = {
  needsGuide: boolean;
};

export function useInsertTemplate({
  configStore,
  renderStore,
}: UseInsertTemplateDeps) {
  const insertTemplateGuide = useInsertFrameGuide({ configStore, renderStore });

  /**
   * 插入模版
   * @param template 模版数据
   * @param options  插入模版的选项
   *
   * @returns 返回一个图形数组，第一个为frame，后续为引导元素（如果存在的话）
   */
  return async function (
    template: TemplateItemType,
    options?: Partial<InsertTemplateOptions>
  ) {
    const mergedOptions: InsertTemplateOptions = {
      needsGuide: false,
      ...options,
    };

    const render = renderStore.render;
    if (!render) {
      return;
    }

    const elements = [] as Array<FabricObject>;

    const shape = await insertTemplate(
      render,
      template,
      mergedOptions.needsGuide
    );
    if (!shape?.frame) {
      return;
    }

    elements.push(shape.frame);

    if (mergedOptions.needsGuide) {
      const guideElements = await insertTemplateGuide({
        frame: {
          left: shape?.frame.left ?? 0,
          top: shape?.frame.top ?? 0,
          width: shape?.frame.getScaledWidth() ?? 0,
          height: shape?.frame.getScaledHeight() ?? 0,
        },
      });

      elements.push(...(guideElements ?? []));
    }

    return elements;
  };
}

export function useInsertTemplateWithGuide(
  deps: UseInsertTemplateDeps & { userStore: UserStore }
) {
  const insertTemplate = useInsertTemplate(deps);

  return async function (template: TemplateItemType) {
    const uid = deps.userStore.UID;
    if (!uid) {
      return;
    }

    const needsGuide = needShowGuide(uid, "template");
    const elements = await insertTemplate(template, { needsGuide });
    recordGuideShown(uid, "template");

    return elements;
  };
}
