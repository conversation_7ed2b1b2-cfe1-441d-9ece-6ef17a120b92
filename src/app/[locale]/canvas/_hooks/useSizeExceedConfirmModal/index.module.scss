@import '@/styles/variable.scss';

.modal {
  :global {
    .confirm-modal {
      .ant-modal-content {
        .confirm-modal-content {

          .confirm-modal-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .ignore-today {
              color: $system-content-thirdary;

              &:hover {
                .ant-checkbox {
                  &-inner {
                    border-color: $system-content-brand-primary;
                  }

                }
              }
              
              .ant-checkbox-checked {
                .ant-checkbox-inner {
                  border-color: $system-content-brand-primary;
                  background: $system-content-brand-primary;
                  &::after {
                    border-color: $system-content-on-primary;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}