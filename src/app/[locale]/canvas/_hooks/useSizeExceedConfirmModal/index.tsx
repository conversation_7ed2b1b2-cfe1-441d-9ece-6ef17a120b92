import { Checkbox, Modal } from "antd";
import { useState } from "react";
import styles from "./index.module.scss";
import { CrossBlack } from "@meitu/candy-icons";
import useConfirmModal, { ConfirmModalProps } from "@/hooks/useConfirmModal";
import { Features, needsOpen, recordFeature } from "./sizeConfirmModalRecords";
import { useStore } from "@/contexts/StoreContext";
import { useI18n } from "@/locales/client";

type SizeExceedConfirmModalProps = ConfirmModalProps & {
  feature: Features
}


const useSizeExceedConfirmModal = (props: SizeExceedConfirmModalProps) => {

  const { feature, onConfirm, ...modalProps } = props;
  const [ignoreCheck, setIgnoreCheck] = useState(false);
  const { userStore } = useStore();
  const t = useI18n();
  
  const renderTodayIgnore = () => {
    return (
      <Checkbox className="ignore-today" value={ignoreCheck} onChange={e => setIgnoreCheck(e.target.checked)}>
        {t("Don't show this again today")}
      </Checkbox>
    )
  }

  const getNeedsOpen = () => {
    if (!userStore.UID) {
      return;
    }

    return needsOpen(userStore.UID, feature);
  }
  
  const handleConfirm = () => {
    if (!userStore.UID) {
      return;
    }

    if (ignoreCheck) {
      recordFeature(userStore.UID, feature);
    }
    onConfirm?.();
  }

  const { open, close, contextHolder } = useConfirmModal({
    ...modalProps,
    className: styles.modal,
    onConfirm: handleConfirm,
    slots: {
      footer: renderTodayIgnore,
    }
  });


  const handleOpen = () => {
    setIgnoreCheck(false);
    open();
  }

  return {
    getNeedsOpen,
    open: handleOpen,
    close,
    contextHolder,
  };
};
export default useSizeExceedConfirmModal;
