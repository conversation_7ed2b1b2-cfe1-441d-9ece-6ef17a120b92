/**
 * 
 * 在localStorage中记录生图超尺寸后弹窗打开的时间
 */

import { AIPoster } from "@/api/types/aiPoster/task";

type ModalOpenRecordItem = {
  openTime: number;
}

export type Features = AIPoster.TaskCategory.Cutout
| AIPoster.TaskCategory.Upscaler
| AIPoster.TaskCategory.Image2Image
| AIPoster.TaskCategory.Text2Image

type UserModalOPenRecordItem = Record<Features, ModalOpenRecordItem>

type RecordsBook = {
  [uid in number]: UserModalOPenRecordItem;
}

const bookKey = 'poster-editor/size-exceed-tips-modal';

function getBook(): RecordsBook {
  const bookJSONStr = localStorage.getItem(bookKey);
  if (!bookJSONStr) {
    return {};
  }
  
  try {
    return JSON.parse(bookJSONStr);
  } catch(e: any) {
    return {}
  }
}

function getRecord(uid: number, feature: Features) {
  const book = getBook();
  const userRecord = book[uid] ?? {};
  book[uid] = userRecord;

  const featureRecord = userRecord[feature] ?? {
    openTime: 0
  }
  userRecord[feature] = featureRecord;
  return {book, featureRecord};
}


export function recordFeature(uid: number, feature: Features) {
  const {book, featureRecord} = getRecord(uid, feature);
  featureRecord.openTime = Date.now();
  localStorage.setItem(bookKey, JSON.stringify(book));
}

export function needsOpen(uid: number, feature: Features) {
  const {featureRecord} = getRecord(uid, feature);
  return Date.now() - featureRecord.openTime > 24 * 3600 * 1000;
}