.poster-tutorials-popover {
  &.driver-popover {
    width: 280px;
    padding: 16px;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-input, #22272E);
    background: linear-gradient(0deg, var(--system-background-secondary, #1D1E23) 0%, var(--system-background-secondary, #1D1E23) 100%), #FFF;
  }

  .driver-popover-arrow {
    display: none;
  }

  .driver-popover-title {
    color: var(--system-content-primary, #FFF);
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 130%;
  }

  .driver-popover-description {
    color: var(--system-content-thirdary, #6A7B94);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    margin-top: 8px;
  }

  .driver-popover-footer {
    margin-top: 16px;

    .driver-popover-progress-text {
      color: var(--system-content-thirdary, #6B7A8F);
      font-family: Inter;
      font-size: var(--fontSize-sm, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: var(--lineHeights-md, 20px);
      letter-spacing: var(--letterSpacing-md, 0px);
    }

    .driver-popover-navigation-btns {
      .driver-popover-next-btn {
        border-radius: 8px;
        background: var(--system-content-brandPrimary, #53F6B4);
        padding: 0 10px;
        height: 34px;
        color: var(--system-content-onPrimary, #181818);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        width: 99px;
        text-align: center;
        text-shadow: none;
        border: 0;

        &:hover {
          background: linear-gradient(0deg, #38D294 55.77%, #047246 251.92%);
        }
      }
    }
  }
}