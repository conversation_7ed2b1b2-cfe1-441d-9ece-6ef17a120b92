import { makeAutoObservable, observable, observe, reaction, toJS } from "mobx";
import { RootStore } from ".";
import { CursorType, HeaderAction } from "./headerAction";

type KeyType = number | string | Symbol;

type UploadingDescription = {
  key: KeyType;
};

export enum RightSiderTabsKey {
  Template = "template",
  History = "history",
}

export class EditorStatusStore {
  /**
   * 在切换项目时的loading
   */
  changeProjectLoading = false;
  public setChangeProjectLoading(loading: boolean) {
    this.changeProjectLoading = loading;
  }

  initFinish = false;
  public setInitFinish(initFinish: boolean) {
    this.initFinish = initFinish;
  }

  /**
   * 是否展示创建页
   */
  showCreate = false;
  public setShowCreate(val: boolean) {
    this.showCreate = val;
  }

  /**
   * 是否清空屏幕UI组件
   * @param isClear 是否清空
   */
  isClearScreenUI: boolean = false;
  public setIsClearScreenUI(isClear: boolean) {
    this.isClearScreenUI = isClear;
  }

  //#region 上传loading
  uploadingDescriptions: Map<KeyType, UploadingDescription> = new Map();
  public appendUploadingDescription(desc: UploadingDescription) {
    this.uploadingDescriptions.set(desc.key, desc);
  }
  public deleteUploadingDescription(key: KeyType) {
    this.uploadingDescriptions.delete(key);
  }
  public get isUploading() {
    return this.uploadingDescriptions.size > 0;
  }
  //#endregion

  // 用来处理滚动 不需要响应式
  templateTabs: Map<number, HTMLElement> = new Map();
  templateScrollContainer: HTMLElement | null = null;

  //#region 全局禁用
  globalDisabled = false;
  globalDisable() {
    this.globalDisabled = true;
    this.rootStore.headerActionStore.setActiveHeaderAction(
      HeaderAction.Cursor,
      CursorType.Drag
    );
  }
  globalEnable() {
    this.globalDisabled = false;
    this.rootStore.headerActionStore.activateActionAndResetCursor();
  }
  //#endregion

  // 当前正在进行的任务列表
  activeTasks: String[] = [];
  // 添加新任务
  addActiveTask(task: string) {
    this.activeTasks.push(task);
  }
  // 检查任务是否存在
  hasActiveTask(taskId: string) {
    return this.activeTasks.some((task) => task === taskId);
  }

  // agent面板的展开状态
  isAgentExpanded: boolean = true;
  setAgentExpanded(expanded: boolean) {
    this.isAgentExpanded = expanded;
  }
  agentPanelContainer: HTMLElement | null = null;

  // 模版侧边栏展开状态
  isTemplateExpanded: boolean = true;
  setTemplateExpanded(expanded: boolean) {
    this.isTemplateExpanded = expanded;
  }
  templatePanelContainer: HTMLElement | null = null;


  canvasVisibleArea = {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  };
  // }
  // 获取可视区域的范围
  // get canvasVisibleArea() {
  //   const templatePanelWidth = this.templatePanelContainer?.getBoundingClientRect().width || 0;
  //   const agentPanelWidth = this.agentPanelContainer?.getBoundingClientRect().width || 0;
    
    
  //   return {
  //     top: 0,
  //     bottom: 0,
  //     left: this.isTemplateExpanded ? templatePanelWidth : 0,
  //     right: this.isAgentExpanded ? agentPanelWidth : 0, 
  //   }
  // }

  rootStore: RootStore;

  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      templateScrollContainer: false,
      templateTabs: false,
      activeTasks: false,
      agentPanelContainer: observable.ref,
      templatePanelContainer: observable.ref,
      _observeVisibleAreaEffects: false,
    });

    this.rootStore = rootStore;

    this._observeVisibleAreaEffects();
  }

  _observeVisibleAreaEffects() {
    
    const updateCanvasVisibleArea = () => {
      const templatePanelWidth = this.templatePanelContainer?.getBoundingClientRect().width || 0;
      const agentPanelWidth = this.agentPanelContainer?.getBoundingClientRect().width || 0;
    
      this.canvasVisibleArea = {
        top: 0,
        bottom: 0,
        left: templatePanelWidth,
        right: agentPanelWidth, 
      }
    }

    const resizeObserver = new ResizeObserver(updateCanvasVisibleArea);

    reaction(
      () => this.templatePanelContainer,
      (container, prevContainer) => {
        if (container) {
          resizeObserver.observe(container);
        }
        if (prevContainer) {
          resizeObserver.unobserve(prevContainer);
        }

        updateCanvasVisibleArea();
      }
    )

    reaction(
      () => this.agentPanelContainer,
      (container, prevContainer) => {
        if (container) {
          resizeObserver.observe(container);
        }
        if (prevContainer) {
          resizeObserver.unobserve(prevContainer);
        }

        updateCanvasVisibleArea();
      }
    )
  }
}
