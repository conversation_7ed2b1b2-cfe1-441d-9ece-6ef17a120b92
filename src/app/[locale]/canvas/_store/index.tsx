"use client";

import { useContext, useEffect, useState } from "react";
import { ProjectsStore } from "./projects";
import { ImageEditStore } from "./imageEdit";
import { MobXProviderContext } from "mobx-react";
import { RenderStore } from "./render";
import { ConfigStore } from "./config";
import { ParamsEditorStore } from "./paramsEditor";
import { GenerateRecordStore } from "./generateRecord";
import { EditorStatusStore } from "./editorStatus";
import { SelectionStore } from "./selection";
import {
  PollingManager,
  PollingManagerContext,
} from "@/app/[locale]/canvas/_context/PollingManagerContext";
import { PosterConfigStore } from "@/stores/PosterConfigStore";
import { HeaderActionStore } from "./headerAction";
import { TasksPollingManager } from "../_utils/TasksPollingManager";
import { useStore } from "@/contexts/StoreContext";
import {
  setDefaultFontPreset,
  setFramePreset,
  setInFrameFontPreset,
} from "@meitu/whee-infinite-canvas";
import { MaxFrameLimit, MinFrameLimit } from "../_constant/params";
import { useI18n } from "@/locales/client";

export class RootStore {
  projectsStore: ProjectsStore;
  renderStore: RenderStore;
  configStore: ConfigStore;
  paramsEditorStore: ParamsEditorStore;
  generateRecordStore: GenerateRecordStore;
  editorStatusStore: EditorStatusStore;
  selectionStore: SelectionStore;
  headerActionStore: HeaderActionStore;
  imageEditStore: ImageEditStore;

  constructor(
    public pollingManagers: PollingManager,
    posterConfigStore: PosterConfigStore,
    public t: ReturnType<typeof useI18n>
  ) {
    this.projectsStore = new ProjectsStore(this);
    this.renderStore = new RenderStore(this);
    this.configStore = new ConfigStore(this, posterConfigStore);
    this.paramsEditorStore = new ParamsEditorStore(this);
    this.generateRecordStore = new GenerateRecordStore(this);
    this.editorStatusStore = new EditorStatusStore(this);
    this.selectionStore = new SelectionStore(this);
    this.headerActionStore = new HeaderActionStore(this);
    this.imageEditStore = new ImageEditStore();
  }
}

export function useRootStore() {
  const rootStore = useContext(MobXProviderContext);

  if (!rootStore && process.env.NODE_ENV === "development") {
    throw new Error("没有注入RootStore");
  }

  return rootStore as RootStore;
}

type RootStoreProviderProps = React.PropsWithChildren;
export function RootStoreProvider({ children }: RootStoreProviderProps) {
  const [pollingManagerContextValue] = useState(() => ({
    tasksPollingManager: new TasksPollingManager(),
  }));

  const t = useI18n();

  const { posterConfigStore } = useStore();
  const [rootStore] = useState(
    () => new RootStore(pollingManagerContextValue, posterConfigStore, t)
  );

  useEffect(() => {
    // 设置默认的frame预设
    setFramePreset({
      maxWidth: MaxFrameLimit,
      minWidth: MinFrameLimit,
      maxHeight: MaxFrameLimit,
      minHeight: MinFrameLimit,
    });
    // 设置默认的字体预设
    setDefaultFontPreset({
      fill: "#fff",
    });
    setInFrameFontPreset({
      fill: "#000",
    });

    rootStore.configStore.init();
  }, []);

  return (
    <MobXProviderContext.Provider value={rootStore}>
      <PollingManagerContext.Provider value={pollingManagerContextValue}>
        {children}
      </PollingManagerContext.Provider>
    </MobXProviderContext.Provider>
  );
}
