import { makeAutoObservable, observable, reaction, runInAction } from 'mobx';
import {
  ElementName,
  ElementOptions,
  FabricObject,
  getElementOptions,
  ImageOptions,
  ImageCustomOptions
} from '@meitu/whee-infinite-canvas';
import { RootStore } from '.';
import { debounce } from 'lodash';

export type CommonImageOptions = ImageOptions & ImageCustomOptions;
export class SelectionStore {
  rootStore: RootStore;

  /**
   * ！！！不要手动设置
   */
  activeObjects: FabricObject[] = [];
  /**
   * ！！！不要手动设置
   */
  objectsOptions: ElementOptions[] = [];

  get singleImage() {
    const activeObjects = this.activeObjects;
    const objectsOptions = this.objectsOptions;

    if (activeObjects.length !== 1) {
      return null;
    }

    const active = activeObjects[0];

    if (active._name_ === ElementName.IMAGE) {
      return {
        image: active,
        options: objectsOptions[0] as CommonImageOptions
      };
    }

    return null;
  }

  private updateOptions() {
    const render = this.rootStore.renderStore.render;
    if (!render) return;
    this.objectsOptions = this.activeObjects.map((o) =>
      getElementOptions.call(render, o)
    );
  }

  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      activeObjects: observable.ref,
      objectsOptions: observable.ref
    });

    this.rootStore = rootStore;

    this.activeObjectsEffects();
    this.activeOptionsEffects();
  }

  private activeObjectsEffects() {
    const cleanups = [] as Array<() => void>;
    const flushCleanups = () => {
      while (cleanups.length) {
        cleanups.pop()?.();
      }
    };
    reaction(
      () => this.rootStore.renderStore.render,
      (render) => {
        flushCleanups();

        if (!render) {
          return;
        }

        const handleSelectedChange = debounce(
          () => {
            const activeObjects = render._FC.getActiveObjects();
            runInAction(() => {
              this.activeObjects = activeObjects.slice(0);
              this.updateOptions();
            });
          },
          50,
          {
            leading: false,
            trailing: true
          }
        );

        const handlerMap = {
          'selection:updated': handleSelectedChange,
          'selection:created': handleSelectedChange,
          'selection:cleared': handleSelectedChange
        };

        render._FC.on(handlerMap);

        cleanups.push(() => {
          render._FC.off(handlerMap);
        });
      }
    );
  }

  private activeOptionsEffects() {
    const cleanups = [] as Array<() => void>;
    const flushCleanups = () => {
      while (cleanups.length) {
        cleanups.pop()?.();
      }
    };
    reaction(
      () => this.rootStore.renderStore.render,
      (render) => {
        flushCleanups();

        if (!render) {
          return;
        }

        const handleSelectedChange = debounce(
          (payload: { target?: FabricObject }) => {
            if (!payload.target) {
              return;
            }

            const needsUpdateOptions = !!this.activeObjects.find(
              (obj) => obj._id_ === payload.target?._id_
            );
            if (needsUpdateOptions) {
              this.updateOptions();
            }
          },
          50,
          {
            leading: false,
            trailing: true
          }
        );

        const handlerMap = {
          'object:changed': handleSelectedChange
        };

        render._FC.on(handlerMap);

        cleanups.push(() => {
          render._FC.off(handlerMap);
        });
      }
    );
  }
}
