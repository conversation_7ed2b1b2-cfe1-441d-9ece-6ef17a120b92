import { AIPoster } from "@/api/types/aiPoster/task";
import { Group } from "@meitu/whee-infinite-canvas";
import { HandleTaskFinish } from "./types";
import { dispatcherHelper } from "../helper";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "../../../_constant/track";

export function handleTextToImageFinish({
  task,
  abortController,
  rootStore,
  t,
}: HandleTaskFinish) {
  const helper = dispatcherHelper({ rootStore, msgId: task.id });
  if (!helper) {
    return;
  }

  const { findShapeNode, render } = helper;

  const shape = findShapeNode();
  if (!shape) {
    return;
  }

  const image = shape.node;

  const firstResult = task.resultImages.find(
    (item) => item.imageStatus === AIPoster.ImageStatus.Success
  );
  // 如果没有找到图片
  const src = firstResult?.urlSign;
  const urlShort = firstResult?.urlShort;
  const msgId = task.id;

  if (!firstResult || !src || !urlShort) {
    render?.Actions?.setError(image?._id_, t("Image generate failed"));
    // 全部失败
    image?.set({
      _custom_data_history_: {
        ...image._custom_data_history_,
        imageStatus: AIPoster.ImageStatus.AuditFailed,
        isHaveCompletedTaskId: msgId,
      },
    });
    return Promise.reject("没找到图片");
  }

  trackEvent("ai_create_image_success", {
    function: Track.FunctionEnum.TextToImage,
    task_id: msgId,
    is_leave: rootStore?.editorStatusStore.hasActiveTask(msgId) ? 0 : 1,
  });

  image?.set({
    _custom_data_history_: {
      ...image._custom_data_history_,
      imageStatus: AIPoster.ImageStatus.Success,
    },
  });

  return render.Actions.replaceImage(
    image as Group,
    {
      src,
      // 设置为画板容器的宽高
      width: image?._custom_data_history_?.params?.width,
      height: image?._custom_data_history_?.params?.height,
      _custom_data_history_: {
        ...image._custom_data_history_,
        msgId,
        urlShort: urlShort,
        isHaveCompletedTaskId: msgId,
      },
      _parent_id_: shape.options._parent_id_,
    },
    "center",
    abortController?.signal
  );
}
