import { AIPoster } from '@/api/types/aiPoster/task';
import { TasksPollingManager } from '../TasksPollingManager';

type PollingTaskParams = {
  tasksPollingManager: TasksPollingManager;
  msgId: string;
  abortController?: AbortController;
};

/**
 * 轮训生成结果 不处理生成失败的逻辑 将Task交给finishHandler处理
 */
export function pollingQuery({
  tasksPollingManager,
  abortController,
  msgId
}: PollingTaskParams) {
  return new Promise<AIPoster.Task | null>((resolve, reject) => {
    // 监听任务轮训
    const handleTaskResolved = (payload: { resolved: AIPoster.Task[] }) => {
      // 如果任务被取消 则不需要再监听轮训了
      if (abortController?.signal.aborted) {
        tasksPollingManager.removeTaskResolvedListener(handleTaskResolved);
        return resolve(null);
      }

      // 有任务完成后 判断当前任务是否完成
      const finish = payload.resolved.find((task) => task.id === msgId);
      if (!finish) {
        return;
      }

      // 当前任务完成后 不需要再继续监听
      tasksPollingManager.removeTaskResolvedListener(handleTaskResolved);

      resolve(finish);
    };

    tasksPollingManager.addTaskResolvedListener(handleTaskResolved);
  });
}
