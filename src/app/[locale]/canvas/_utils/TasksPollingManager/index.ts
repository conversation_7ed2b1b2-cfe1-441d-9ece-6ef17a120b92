import { AIPoster } from '@/api/types/aiPoster/task';
import { BasePollingManager, QueryStatus } from './BasePollingManager';
import { queryTask } from '@/api/aiPoster/task';

export class TasksPollingManager extends BasePollingManager<
  string,
  AIPoster.Task
> {
  protected query(ids: string[], projectId: number): Promise<AIPoster.Task[]> {
    return queryTask({ ids, projectId });
  }
  protected getQueryStatus(res: AIPoster.Task): QueryStatus {
    return res.loadingStatus === AIPoster.LoadingStatus.Loading
      ? QueryStatus.Pending
      : QueryStatus.Resolved;
  }
  protected getQueryId(res: AIPoster.Task): string {
    return res.id;
  }
}
