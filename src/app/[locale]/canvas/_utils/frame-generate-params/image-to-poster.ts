import { ContainerElementOptions, ElementName, ElementOptions, FabricObject, FrameElementOptions, getElementOptions, Group, ImageElementParams, Render, TextElementOptions } from "@meitu/whee-infinite-canvas";
import { createImage } from '@/utils/cropImage';
import { upload } from "@/utils/upload";
import { createPureUpload } from "@/utils/uploader";

const layoutTextColor = '#ffffff';

const initImageMaskTextColor = '#000000';
const initImageMaskImageColor = '#ffffff';


function getLayoutImage(frame: Group, render: Render) {

  const configureNode = (nodeOptions: ElementOptions) => {
    if (nodeOptions._name_ === ElementName.CONTAINER || nodeOptions._name_ === ElementName.FRAME) {
      
      const childrenOptions = (nodeOptions as ContainerElementOptions).children ?? [];

      const nextNodeOptions = {
        ...nodeOptions as ContainerElementOptions,
        children: childrenOptions.map(configureNode),
      } as ContainerElementOptions;

      return nextNodeOptions;
    }

    if (nodeOptions._name_ !== ElementName.TEXT) {
      return nodeOptions;
    }

    const nextNodeOptions: TextElementOptions = {
      ...nodeOptions as TextElementOptions,
      fill: layoutTextColor,
    }

    return nextNodeOptions;
  }
  
  
  return render.Actions.exportElementToBlob(frame, {
    includeType: [ElementName.TEXT],
    exportType: 'jpeg',
    
    callBack(elements) {
      if (elements._name_ === ElementName.FRAME) {
        (elements as FrameElementOptions).backgroundColor = 'transparent';
      }

      return configureNode(elements);
    }
  });
}

function getInitImage(frame: Group, render: Render) {  
  return render.Actions.exportElementToBlob(frame, {
    includeType: [ElementName.IMAGE],
    exportType: 'jpeg',
    callBack(elements) {
      if (elements._name_ === ElementName.FRAME) {
        (elements as FrameElementOptions).backgroundColor = 'transparent';
      }

      return elements;
    }
  });
}

async function getInitImageMask(frame: Group, render: Render): Promise<Blob> {
  const configureNode = (nodeOptions: ElementOptions) => {
    if (nodeOptions._name_ === ElementName.CONTAINER || nodeOptions._name_ === ElementName.FRAME) {
      
      const childrenOptions = (nodeOptions as ContainerElementOptions).children ?? [];

      const nextNodeOptions = {
        ...nodeOptions as ContainerElementOptions,
        children: childrenOptions.map(configureNode),
      } as ContainerElementOptions;

      return nextNodeOptions;
    }

    if (nodeOptions._name_ === ElementName.TEXT) {
      return {
        ...nodeOptions,
        fill: initImageMaskTextColor,
      } as TextElementOptions;
    }

    if (nodeOptions._name_ === ElementName.IMAGE) {
      return {
        ...nodeOptions,
        globalCompositeOperation: 'destination-out',
      } as ElementOptions;
    }

    return nodeOptions;
  }

  const textCanvas = await render.Actions.exportElementToCanvas(frame, {
    includeType: [ElementName.TEXT, ElementName.IMAGE],
    callBack(elements) {
      if (elements._name_ === ElementName.FRAME) {
        (elements as FrameElementOptions).backgroundColor = 'transparent';
      }

      return configureNode(elements);
    }
  });

  const imageCanvas = await render.Actions.exportElementToCanvas(frame, {
    includeType: [ElementName.IMAGE],
    callBack(elements) {
      if (elements._name_ === ElementName.FRAME) {
        (elements as FrameElementOptions).backgroundColor = 'transparent';
      }

      return elements;
    }
  });

  const width = imageCanvas.width;
  const height = imageCanvas.height;
  const ctx = imageCanvas.getContext('2d');

  if (!ctx) {
    throw 'Canvas context error';
  }

  ctx.save();
  ctx.globalCompositeOperation = 'source-in';
  ctx.fillStyle = initImageMaskImageColor;
  ctx.fillRect(0, 0, width, height);

  ctx.globalCompositeOperation = 'source-over';
  ctx.drawImage(textCanvas, 0, 0, width, height);
  ctx.restore();

  return await new Promise((resolve, reject) => {
    imageCanvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject('图片导出失败');
        }
      },
      'image/jpeg',
      1
    );
  });
}

/**
 * 导出图生海报需要的图片参数
 *
 * @param frame 
 * @param render 
 * @param signal 
 * @returns 
 * 1. 如果frame图片中没有图片，仅返回[layoutImageBlob]
 * 2. 否则，返回[layoutImageBlob, initImageBlob, initImageMaskBlob]
 */
async function getImageParamsBlob(frame: FabricObject, render: Render, signal?: AbortSignal) {
  const options = getElementOptions.call(render, frame) as FrameElementOptions;
  if (options._name_ !== ElementName.FRAME) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('仅frame生图时才需要导出图片');
    }
    throw new Error('仅frame生图时才需要导出图片');
  }

  const layoutBlob = await getLayoutImage(frame as Group, render);
  signal?.throwIfAborted();

  /**
   * 检查子元素中是否有图片 如果存在图片 还需要initImage和maskImage
   * @param root 
   * @returns 
   */
  const traverse = (root: ElementOptions) => {
    if (root._name_ === ElementName.IMAGE) {
      return true;
    }

    if ((root as ContainerElementOptions).children?.length) {
      for(const child of (root as ContainerElementOptions).children) {
        const childHasImage = traverse(child);
        if (childHasImage) {
          return true;
        }
      }
    }

    return false;
  }
  if (!traverse(options)) {
    return [layoutBlob] as const;
  }

  const initImageBlob = await getInitImage(frame as Group, render);
  signal?.throwIfAborted();

  const initImageMaskBlob = await getInitImageMask(frame as Group, render);
  signal?.throwIfAborted();

  return [
    layoutBlob,
    initImageBlob,
    initImageMaskBlob,
  ] as const;
}

/**
 * 导出图生海报需要的图片参数并上传
 *
 * @param frame 
 * @param render 
 * @param signal 
 * @returns 
 * 1. 如果frame图片中没有图片，仅返回[layoutImage]
 * 2. 否则，返回[layoutImage, initImage, initImageMask]
 */
export async function getImageParams(frame: FabricObject, render: Render, signal?: AbortSignal) {
  const imageBlobs = await getImageParamsBlob(frame, render, signal);
  const upload = createPureUpload();

  const images = await Promise.all(imageBlobs.map(async (blob) => {
    const image = await upload({ file: blob, signal });
    if (!image.result?.url) {
      throw new Error('图片上传失败')
    }
    signal?.throwIfAborted();

    return image.result.url;
  }));

  return images;
}

export async function debugImages(frame: Group, render: Render) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    return null;
  }

  {
    const blob = await getLayoutImage(frame, render);
    const url = URL.createObjectURL(blob);
    const image = await createImage(url);

    canvas.width = image.width * 2;
    canvas.height = image.height * 2;
    
    ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, image.width, image.height);
    URL.revokeObjectURL(url);
  }

  {
    const blob = await getInitImage(frame, render);
    const url = URL.createObjectURL(blob);
    const image = await createImage(url);    
    ctx.drawImage(image, 0, 0, image.width, image.height, 0, image.height, image.width, image.height);
    URL.revokeObjectURL(url);
  }

  {
    const blob = await getInitImageMask(frame, render);
    const url = URL.createObjectURL(blob);
    const image = await createImage(url);    
    ctx.drawImage(image, 0, 0, image.width, image.height, image.width, image.height, image.width, image.height);
    URL.revokeObjectURL(url);
  }

  const blob = await new Promise<Blob | null>(res => {
    canvas.toBlob(res);
  })

  if (!blob) {
    return;
  }

  const url = URL.createObjectURL(blob);
  window.open(url, '_blank');

  setTimeout(() => {
    URL.revokeObjectURL(url);
  }, 1000)
}
