import { ElementOptions } from '@meitu/whee-infinite-canvas';

export function getLinkTaskMessage(
  options: Omit<ElementOptions, 'width' | 'height'>
): null | {
  linkMsgId?: string;
  initWatermarkImage: string;
} {
  const customData = options._custom_data_history_ ?? {};
  const params = customData.params;

  if (!params || !params.initWatermarkImage) {
    return null;
  }

  return {
    linkMsgId: params.linkMsgId,
    initWatermarkImage: params.initWatermarkImage
  };
}

/**
 * 获取图形的带水印原图
 */
export function getInitWatermarkImage(
  options: Omit<ElementOptions, 'width' | 'height'>
) {
  return getLinkTaskMessage(options)?.initWatermarkImage;
}
