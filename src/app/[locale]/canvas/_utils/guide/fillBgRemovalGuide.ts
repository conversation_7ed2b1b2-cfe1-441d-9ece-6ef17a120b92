import { GuideDescription } from "./types";
import assets from "./assets-index.json";
import { fillGuideElements } from "./fillGuideElements";
import { ElementName, Render } from "@meitu/whee-infinite-canvas";
import { tipsColor, titleColor } from "./constants";
import { useI18n } from "@/locales/client";

type FillBgRemovalGuideOptions = {
  render: Render;
  t: ReturnType<typeof useI18n>;
  defaultFont: string;
};
export async function fillBgRemovalGuide({
  render,
  t,
  defaultFont,
}: FillBgRemovalGuideOptions) {
  const bgRemovalGuideDesc: GuideDescription = {
    title: {
      icon: {
        src: assets["bg-removal"]["title-icon"],
      },
      text: {
        content: t("guide.bg-removal.title"),
        fontSize: 24,
        fontFamily: defaultFont,
        color: titleColor,
      },
      gap: 12,
    },

    steps: {
      list: [
        [
          {
            type: ElementName.TEXT,
            content: t("guide.bg-removal.step1"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["image-icon"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.bg-removal.step2"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["bg-removal"]["bg-removal-icon"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["bg-removal"]["preview"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.bg-removal.step3"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["download"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["download-dropdown"],
            topOffset: 16,
          },
        ],
      ],
      gap: 48,
      topOffset: 40,
    },
  };

  return await fillGuideElements(bgRemovalGuideDesc, { render });
}
