import { createImage } from "@/utils/cropImage";
import { getWorkerPool } from "../../_workers/pool";

const pool = getWorkerPool();

export async function upscalerReplaceAlpha(initSrc: string, maskSrc: string) {
  let initImage = null as null | HTMLImageElement;
  let maskImage = null as null | HTMLImageElement;
  // console.log("replace alpha", initSrc, maskSrc);

  try {
    initImage = await createImage(initSrc);
    maskImage = await createImage(maskSrc);
  } catch (e) {
    if (process.env.NODE_ENV === "development") {
      console.warn("图片加载失败", e);
    }
  }

  if (!initImage || !maskImage) {
    return;
  }

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx) {
    if (process.env.NODE_ENV === "development") {
      console.warn("canvas context 获取失败");
    }
    return;
  }

  ctx.imageSmoothingEnabled = false;
  const drawImage = (
    image: HTMLImageElement,
    width: number,
    height: number
  ) => {
    canvas.width = width;
    canvas.height = height;
    ctx.clearRect(0, 0, width, height);
    ctx.drawImage(
      image,
      0,
      0,
      image.naturalWidth,
      image.naturalHeight,
      0,
      0,
      width,
      height
    );
    return ctx.getImageData(0, 0, width, height);
  };

  const initImageData = drawImage(initImage, initImage.width, initImage.height);
  const maskImageData = drawImage(maskImage, maskImage.width, maskImage.height);

  const resultImageData = await pool.exec(
    "upscaleReplaceAlpha",
    [initImageData, maskImageData],
    { transfer: [initImageData.data.buffer, maskImageData.data.buffer] }
  );
  canvas.width = resultImageData.width;
  canvas.height = resultImageData.height;
  ctx.putImageData(resultImageData, 0, 0);

  return await new Promise<Blob>((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject("导出blob失败");
        }
      },
      "image/png",
      1
    );
  });
}

export async function hasAlphaExecuteInWorker(url: string) {
  let img: HTMLImageElement | null = null;
  try {
    img = await createImage(url);
  } catch (e) {
    if (process.env.NODE_ENV === "development") {
      console.log(`图片加载错误 url[${url}]`, e);
    }
    return null;
  }

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx || !img) {
    return null;
  }

  // 设置 canvas 尺寸与图片一致
  canvas.width = img.width;
  canvas.height = img.height;

  // 将图片绘制到 canvas 上
  ctx.drawImage(img, 0, 0);

  // 获取图片像素数据
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

  return pool.exec("hasAlpha", [imageData], {
    transfer: [imageData.data.buffer],
  });
}
