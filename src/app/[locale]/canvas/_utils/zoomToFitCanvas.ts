import { FabricObject, Render } from '@meitu/whee-infinite-canvas';
const leftPanelWidth = 319;
const rightPanelWidth = 254;
const topPanelHeight = 48;

type ZoomToFitCanvasOptions = {
  limitScale?: number,
  left?: number,
  right?: number,
  top?: number,

  targets?: Array<FabricObject>
}

const defaultZoomOptions = {
  left: leftPanelWidth,
  right: rightPanelWidth,
  top: topPanelHeight,
}

export default function zoomToFitCanvas(render: Render, options?: ZoomToFitCanvasOptions): void {

  const { limitScale, left, right, top, targets } = {
    ...defaultZoomOptions,
    ...options,
  }

  if (!targets || !targets.length) {
    return render.zoomToFitCanvas(limitScale, left, right, top); 
  }

  render.focusObjects(targets, limitScale, left, right, top);
}
