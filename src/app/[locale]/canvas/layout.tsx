import { AgentContextProvider } from "@/components/Agent/context";
import { GuideCreateProjectContextProvider } from "./_context/GuideCreateProjectContext";
import { RootStoreProvider } from "./_store";
import { FormContextProvider } from "@/app/[locale]/canvas/_context/FormContext";
export default function EditorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <RootStoreProvider>
      <GuideCreateProjectContextProvider>
        <FormContextProvider>
          <AgentContextProvider>
            {children}
          </AgentContextProvider>
          {/* <IntercomFeedBack  /> */}
        </FormContextProvider>
      </GuideCreateProjectContextProvider>
    </RootStoreProvider>
  );
}
