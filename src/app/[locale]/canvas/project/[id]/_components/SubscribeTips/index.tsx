import { observer } from "mobx-react-lite";
import styles from "./index.module.scss";
import { useI18n } from "@/locales/client";
import { ChevronRightBlack } from "@meitu/candy-icons";
import { Fragment } from "react";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { useRootStore } from "@/app/[locale]/canvas/_store";
function SubscribeTIps() {
  const t = useI18n();
  const { projectsStore } = useRootStore();
  const { open } = useSubscribeModal();

  // console.log('current can edit', projectsStore.currentCanEdit)

  if (projectsStore.currentCanEdit) {
    return null;
  }

  const handleUpgradeButtonClick = () => {
    open({ productType: SubscribeModalType.Basic });
  };

  return (
    <Fragment>
      <section className={styles.tips}>
        <span className="tips-content">{t("guide.not-vip-tips")}</span>
        <button className="upgrade-button" onClick={handleUpgradeButtonClick}>
          <span className="upgrade-button-label">
            {t("guide.upgrade-to-vip")}
          </span>
          <ChevronRightBlack className="upgrade-button-icon" />
        </button>
      </section>
    </Fragment>
  );
}

export default observer(SubscribeTIps);
