"use client";

import { observer } from "mobx-react-lite";
import { useEffect } from "react";
import { useRootStore } from "../../../_store";
import { useI18n } from "@/locales/client";
import { fillRemoverGuide } from "../../../_utils/guide/fillRemoverGuide";
import zoomToFitCanvas from "../../../_utils/zoomToFitCanvas";
import { fillEditTextGuide } from "../../../_utils/guide/fillEditTextGuide";
import { fillHighDefinitionGuide } from "../../../_utils/guide/fillHighDefinitionGuide";
import { fillImageEditorGuide } from "../../../_utils/guide/fillImageEditorGuide";
import { useRouter } from "next/navigation";
import { useStore } from "@/contexts/StoreContext";
import NotVipTips from "./_components/NotVipTips";
import { notFound } from "next/navigation";
import { fillBgRemovalGuide } from "../../../_utils/guide/fillBgRemovalGuide";
import { loadRemoteFont } from "@/utils/loadRemoteFont";
import { useQuestionModal } from "@/contexts/QuestionContext";

enum GuideFeature {
  // 抠图
  BgRemoval = "bg-removal",
  // 改字
  EditText = "edit-text",
  // 超清
  HighDefinition = "high-definition",
  // 改图
  ImageEditor = "image-editor",
  // 消除
  Remover = "remover",
}

const guideFeatureMap = new Map([
  [GuideFeature.BgRemoval as string, fillBgRemovalGuide],
  [GuideFeature.EditText, fillEditTextGuide],
  [GuideFeature.HighDefinition, fillHighDefinitionGuide],
  [GuideFeature.ImageEditor, fillImageEditorGuide],
  [GuideFeature.Remover, fillRemoverGuide],
]);

type GuideFeatureProps = {
  params: {
    feature: string;
  };
};

function GuideFeaturePage({ params }: GuideFeatureProps) {
  const { userStore, globalProjectStore } = useStore();
  const { renderStore, editorStatusStore, configStore } = useRootStore();

  const { open: openQuestion } = useQuestionModal();
  const t = useI18n();

  const router = useRouter();
  const fillFeatureElements =
    params.feature && guideFeatureMap.get(params.feature);
  if (!fillFeatureElements) {
    notFound();
  }

  useEffect(() => {
    if (editorStatusStore.initFinish) {
      return;
    }

    if (!userStore.isReady || !globalProjectStore.isReady) {
      return;
    }

    let ignore = false;
    const render = renderStore.render;
    if (render && fillFeatureElements) {
      configStore.init()
        .then(() => {
          return fillFeatureElements({ render, t, defaultFont: configStore.config?.name ?? ''})
        })
        .then(() => {
          if (ignore) {
            return;
          }

          zoomToFitCanvas(render, { left: 0, top: 64 });

          if (globalProjectStore.canCreateProject) {
            editorStatusStore.globalEnable();
          } else {
            editorStatusStore.globalDisable();
          }
          editorStatusStore.setInitFinish(true);
        }).then(() => {
          let key = 'hasQuestionnaire' + userStore?.currentUser?.id;
         
          if(localStorage.getItem(key) !== 'true') {
            openQuestion();
          }
        });

      return () => {
        ignore = true;
        editorStatusStore.setInitFinish(true);
      };
    }
   // 忽略eslint
   // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userStore.isReady, globalProjectStore.isReady, globalProjectStore.canCreateProject]);

  return <NotVipTips/>;
}

export default observer(GuideFeaturePage);
