"use client";

import { Fragment, useCallback, useEffect, useRef } from "react";
import { FullScreenLoading } from "../_components/FullScreenLoading";
import styles from "./index.module.scss";
import Editor from "../_components/Editor";
import ActionBar from "../_components/ActionBar";
import { ShortcutHelper } from "../_components/ShortcutHelper";
import { useRootStore } from "../_store";
import { observer } from "mobx-react-lite";
import { useStore } from "@/contexts/StoreContext";
import { createPortal } from "react-dom";
import Questionnaire from "@/components/Questionnaire";
import { trackEvent } from "@meitu/subscribe-intl";
import { useTutorials } from "../_hooks/useTutorials";
import "driver.js/dist/driver.css";
import { Driver } from "driver.js";
import { NoSSR } from "@/components";
import TemplatePanel from "../_components/TemplatePanel";
import AgentPanel from "../_components/AgentPanel";
import AddToChatButton from "../_components/AddToChatButton";
import { editorLayoutContent } from "../_constant/element";

type ProjectLayoutProps = React.PropsWithChildren;

function ProjectLayout({ children }: ProjectLayoutProps) {
  const { editorStatusStore } = useRootStore();
  const { globalProjectStore, userStore } = useStore();
  const driverObjRef = useRef<Driver | null>(null);

  const loading =
    !globalProjectStore.isReady ||
    !userStore.isReady ||
    !editorStatusStore.initFinish ||
    editorStatusStore.changeProjectLoading;

  const QuestionnaireModal =
    typeof window !== "undefined"
      ? createPortal(<Questionnaire />, document.body)
      : null;

  // 初始化首次引导教程
  const handleTutorialInitialized = useCallback((driver: Driver) => {
    driverObjRef.current = driver;
  }, []);
  useTutorials(
    editorStatusStore,
    userStore,
    loading,
    handleTutorialInitialized
  );

  useEffect(() => {
    // mtstat 还没有初始化
    setTimeout(() => {
      trackEvent("edit_page_enter");
    }, 50);

    // 组件卸载时销毁 driver 实例
    return () => {
      driverObjRef.current?.destroy();
    };
  }, []);

  return (
    <Fragment>
      <FullScreenLoading loading={loading} />
      <div className={styles.contentBox} id={editorLayoutContent}>
        <TemplatePanel />
        <AgentPanel />
        <Editor />
        <ActionBar />
        <AddToChatButton rangePadding={[
          editorStatusStore.canvasVisibleArea.top + 16,
          editorStatusStore.canvasVisibleArea.right + 16,
          editorStatusStore.canvasVisibleArea.bottom + 16,
          editorStatusStore.canvasVisibleArea.left + 16,
        ]}/>
        <ShortcutHelper />
        {/* <ImageBar /> */}
        {children}
        <NoSSR>{QuestionnaireModal}</NoSSR>
        {/* <SubscribeModal onClose={() => {}} onSubscribe={() => {}} type="basic" /> */}
      </div>
    </Fragment>
  );
}

export default observer(ProjectLayout);
