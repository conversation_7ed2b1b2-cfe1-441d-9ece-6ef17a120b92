'use client'

import { Fragment, useEffect } from "react";
import Header from "./_components/Header";
import styles from './index.module.scss';
import UploadLoading from "./_components/UploadLoading";
import { observer } from "mobx-react-lite";
import { AIPoster } from "@/api/types/aiPoster/task";
import { useRootStore } from "./_store";
import { useStore } from "@/contexts/StoreContext";

type EditorTemplateProps = React.PropsWithChildren;

function EditorTemplate({ children }: EditorTemplateProps) {

  const { userStore } = useStore();
  const { pollingManagers } = useRootStore();

  // 任务失败后刷新美豆余额
  useEffect(() => {
    const updateMtBalanceAtTaskFailed = (payload: { resolved: AIPoster.Task[]; }) => {
      const failedTasks = payload.resolved.filter(task => {
        // 任务状态为失败的任务
        if (task.loadingStatus !== AIPoster.LoadingStatus.Success) {
          return true;
        }
  
        // 任务状态为成功 但是所有结果图都没有过审核
        const successImages = task.resultImages.filter(result => result.imageStatus === AIPoster.ImageStatus.Success);
        return successImages.length === 0;
      });
  
      if (failedTasks.length) {
        userStore.refreshMtBeanBalance();
      }
    }
  
    pollingManagers.tasksPollingManager.addTaskResolvedListener(updateMtBalanceAtTaskFailed);

    return () => {
      pollingManagers.tasksPollingManager.removeTaskResolvedListener(updateMtBalanceAtTaskFailed);
    }
  }, [pollingManagers.tasksPollingManager])


  return (
    <Fragment>
      <Header/>
      <main className={styles.editorMainBox}>{children}</main>
      <UploadLoading />
    </Fragment>
  )
}

export default observer(EditorTemplate);