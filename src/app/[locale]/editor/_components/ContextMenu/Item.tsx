import { ReactNode, useEffect, useRef, useState } from 'react';
import { HotKey } from '../../_constant/hotKey';

export enum MenuType {
  feature = 'feature',
  divider = 'divider'
}

export interface MenuItem {
  type: MenuType;
  name?: ReactNode;
  nameIcon?: ReactNode;
  icon?: ReactNode;
  key?: HotKey;
  disabled?: boolean;
  onClick?: () => void;
  hotKey?: string;
  childrenMenu?: ReactNode;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const childMenuDefaultTop = -2; // 子菜单默认相对父菜单顶部位置
const childMenuDefaultPadding = 20; // 子菜容器默认padding  实现距离父菜单的间距 同时切换到子菜单时菜单依然可以展示
const childMenuDefaultWidth = 200; // 子菜单默认宽度
const childMenuDefaultStyle = {
  top: `${childMenuDefaultTop}px`,
  left: `${childMenuDefaultWidth}px`,
  paddingLeft: `0px`,
  paddingRight: `0px`
};

const ContextMenuItem = (props: MenuItem) => {
  const { disabled, name, onClick, hotKey, childrenMenu, icon, nameIcon } = props;
  const [childMenuShow, setChildMenuShow] = useState(false);
  const [childMenuStyle, setChildMenuStyle] = useState(childMenuDefaultStyle);

  const handleClick = () => {
    if (disabled) return;
    onClick?.();
  };

  useEffect(() => {
    if (childMenuShow) {
      const windowHeight = window.innerHeight;
      const windowWidth = window.innerWidth;
      const div = document.querySelector('.custom-menu-item-children');
      if (!div) return;
      const bound = div.getBoundingClientRect();
      // 当子菜单高度超出屏幕时，重新计算子菜单的位置保证在屏幕内
      const top = Math.min(
        childMenuDefaultTop,
        windowHeight - bound.y - bound.height
      );
      // 当子菜单宽度超出屏幕时，重新计算子菜单的位置保证在屏幕内
      const isOverFlow =
        windowWidth - bound.x - childMenuDefaultPadding < bound.width;
      let left = isOverFlow
        ? -bound.width - childMenuDefaultPadding
        : childMenuDefaultWidth;
      setChildMenuStyle({
        top: `${top}px`,
        left: `${left}px`,
        paddingLeft: isOverFlow ? '0px' : `${childMenuDefaultPadding}px`,
        paddingRight: isOverFlow ? `${childMenuDefaultPadding}px` : '0px'
      });
    } else {
      setChildMenuStyle(childMenuDefaultStyle);
    }
  }, [childMenuShow]);

  const handleMouseEnter = () => {
    setChildMenuShow(true);
  };
  const handleMouseLeave = () => {
    setChildMenuShow(false);
  };

  return (
    <div
      className={`custom-menu-item ${
        disabled ? 'custom-menu-item-disabled' : ''
      }`}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="custom-menu-item-name-wrapper">
        {nameIcon && <div className="custom-menu-item-icon">{nameIcon}</div>}
        <div className="custom-menu-item-name">{name}</div>
      </div>
      {hotKey && <div className="custom-menu-item-hotKey">{hotKey}</div>}
      {icon && <div className="custom-menu-item-icon">{icon}</div>}
      {childrenMenu && childMenuShow && (
        <div className="custom-menu-item-children" style={childMenuStyle}>
          <div className="custom-menu-container">{childrenMenu}</div>
        </div>
      )}
    </div>
  );
};

const ContextMenuDivider = () => {
  return <div className="custom-menu-divider"></div>;
};

export { ContextMenuItem, ContextMenuDivider };
