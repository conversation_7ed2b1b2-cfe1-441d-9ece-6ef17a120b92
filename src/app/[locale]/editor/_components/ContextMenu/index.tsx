/* eslint-disable @next/next/no-img-element */
import {
  ElementName,
  ContextMenuComponentProps,
  Point,
  Group,
  Canvas,
} from "@meitu/whee-infinite-canvas";
import "./index.scss";
import { HotKey, getHotKeyMap } from "@/app/[locale]/editor/_constant/hotKey";
import { ChevronRightBlack } from "@meitu/candy-icons";
import { ContextMenuDivider, ContextMenuItem, MenuType } from "./Item";
import useAction from "@/app/[locale]/editor/_hooks/useAction";
import { RootStore } from "@/app/[locale]/editor/_store";
import VIPIcon from "@/assets/icons/icon-vip.png";
import { useExportShape } from "@/app/[locale]/editor/_hooks/useExportShape";
import { useEffect, useState } from "react";
import { useI18n, useScopedI18n } from "@/locales/client";
import { UserStore } from "@/stores/UserStore";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";

export default function ContextMenu({
  targets,
  render,
  destroy,
  position,
  rootStore,
  userStore,
  t,
  scopedT,
  subscribeModal
}: ContextMenuComponentProps & {
  rootStore: RootStore;
  userStore: UserStore;
  subscribeModal: ReturnType<typeof useSubscribeModal>
  t: ReturnType<typeof useI18n>;
  scopedT: ReturnType<typeof useScopedI18n>;
}) {
  const {
    pasteAction,
    deleteAction,
    cutAction,
    bringToFrontAction,
    sendToBackAction,
    mergeAction,
    splitAction,
  } = useAction(rootStore);
  const { selectionStore, projectsStore, renderStore } = rootStore;
  const { download, downloading } = useExportShape({
    selectionStore,
    projectsStore,
    renderStore,
    userStore,
    subscribeModal
  });
  const [hotKeyMap, setHotKeyMap] = useState<Record<HotKey, string> | null>(
    null
  );

  useEffect(() => {
    setHotKeyMap(getHotKeyMap());
  }, []);

  const handleCopy = () => {
    if (!targets) return;
    render.Actions.copyStash(...targets);
    destroy();
  };
  const handlePaste = () => {
    if (!targets) return;
    const point = new Point(position);
    let parent: Group | Canvas = render._FC;
    if (targets.length === 1 && targets[0]?._name_ === ElementName.FRAME) {
      parent = targets[0] as Group;
    }
    pasteAction(point, parent);
    destroy();
  };
  const handleDelete = () => {
    if (!targets) return;
    deleteAction(targets);
    destroy();
  };
  const handleCut = () => {
    if (!targets) return;
    cutAction(targets);
    destroy();
  };
  const handleFront = () => {
    if (!targets) return;
    bringToFrontAction(targets);
    destroy();
  };
  const handleBack = () => {
    if (!targets) return;
    sendToBackAction(targets);
    destroy();
  };
  const handleMerge = () => {
    if (!targets) return;
    mergeAction(targets);
    destroy();
  };
  const handleSplit = () => {
    if (!targets) return;
    splitAction(targets);
    destroy();
  };

  const handleDownloadPNG = () => {
    if (!targets) return;
    if (downloading) return;
    download({ removeWatermark: false });
    destroy();
  };
  const handleDownloadWithoutWatermark = () => {
    if (!targets) return;
    if (downloading) return;
    download({ removeWatermark: true });
    destroy();
  };

  const isContainer =
    targets?.length === 1 && targets[0]?._name_ === ElementName.CONTAINER;
  const isFrameInEls = targets?.some((el) => el._name_ === ElementName.FRAME);
  const isImage =
    targets?.length === 1 && targets[0]?._name_ === ElementName.IMAGE;

  return !targets?.length && !render.Actions.copyElement ? null : (
    <div className="custom-menu-container">
      {/* 复制 */}
      {!!targets?.length && (
        <ContextMenuItem
          type={MenuType.feature}
          name={scopedT("Copy")}
          // nameIcon={<Copy />}
          key={HotKey.copy}
          onClick={handleCopy}
          hotKey={hotKeyMap?.[HotKey.copy]}
        />
      )}
      {/* 粘贴 */}
      {render.Actions.copyElement && (
        <ContextMenuItem
          type={MenuType.feature}
          name={scopedT("Paste")}
          // nameIcon={<Copy />}
          key={HotKey.paste}
          onClick={handlePaste}
          hotKey={hotKeyMap?.[HotKey.paste]}
        />
      )}
      {/* 剪切 */}
      {!!targets?.length && (
        <ContextMenuItem
          type={MenuType.feature}
          name={scopedT("Crop")}
          // nameIcon={<Copy />}
          key={HotKey.cut}
          onClick={handleCut}
          hotKey={hotKeyMap?.[HotKey.cut]}
        />
      )}
      {/* 分割线 */}
      {!!targets?.length && <ContextMenuDivider />}
      {/* 置顶 */}
      {!!targets?.length && (
        <ContextMenuItem
          type={MenuType.feature}
          name={scopedT("Bring to front")}
          // nameIcon={<Copy />}
          key={HotKey.front}
          onClick={handleFront}
          hotKey={hotKeyMap?.[HotKey.front]}
        />
      )}
      {/* 置底 */}
      {!!targets?.length && (
        <ContextMenuItem
          type={MenuType.feature}
          name={scopedT("Send to back")}
          // nameIcon={<Copy />}
          key={HotKey.back}
          onClick={handleBack}
          hotKey={hotKeyMap?.[HotKey.back]}
        />
      )}
      {/* 删除 */}
      {!!targets?.length && (
        <ContextMenuItem
          type={MenuType.feature}
          name={t("Delete")}
          // nameIcon={<TrashCan />}
          key={HotKey.delete}
          onClick={handleDelete}
          hotKey={hotKeyMap?.[HotKey.delete]}
        />
      )}
      {/* 分割线 */}
      {!!targets?.length && <ContextMenuDivider />}
      {/* 合并 */}
      {!!targets?.length && (
        <ContextMenuItem
          type={MenuType.feature}
          name={t("Combination")}
          // nameIcon={<Merge />}
          key={HotKey.merge}
          onClick={handleMerge}
          disabled={(targets?.length ?? 0) < 2 || isFrameInEls}
          hotKey={hotKeyMap?.[HotKey.merge]}
        />
      )}
      {/* 拆分 */}
      {!!targets?.length && (
        <ContextMenuItem
          type={MenuType.feature}
          name={t("Cancel combination")}
          // nameIcon={<Split />}
          key={HotKey.split}
          onClick={handleSplit}
          disabled={!isContainer}
          hotKey={hotKeyMap?.[HotKey.split]}
        />
      )}
      {/* 下载 */}
      {isImage && (
        <ContextMenuItem
          type={MenuType.feature}
          name={t("Download")}
          // nameIcon={<Download />}
          icon={<ChevronRightBlack />}
          childrenMenu={
            <>
              <ContextMenuItem
                type={MenuType.feature}
                name={t("PNG")}
                onClick={handleDownloadPNG}
              />
              <ContextMenuItem
                type={MenuType.feature}
                name={
                  <div className="watermark-item-display">
                    {t("Remove watermarks")}
                    <img
                      className="watermark-item-icon"
                      src={VIPIcon.src}
                      alt=""
                    />
                  </div>
                }
                onClick={handleDownloadWithoutWatermark}
              />
            </>
          }
        />
      )}
    </div>
  );
}
