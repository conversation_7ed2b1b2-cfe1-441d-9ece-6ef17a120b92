.createTipModalMask {
  background: rgba(0, 0, 0, 0.70);
  backdrop-filter: blur(20px);
}

.createTipModal {
  :global {
    .ant-modal-content {
      padding: 0 !important;

      .ant-modal-close {
        width: 30px;
        height: 30px;
        top: 12px;
        right: 12px;
        background: #303741;
        border-radius: 50%;

        svg {
          width: 14px;
          height: 14px;
          color: #fff;
        }
      }
    }
  }

  .modalContent {
    height: 432px;
    display: flex;

    .leftBox {
      width: 50%;
      flex-shrink: 0;
    }

    .rightBox {
      width: 50%;
      flex-shrink: 0;
      padding: 42px 24px 32px;
      position: relative;
      box-sizing: border-box;

      h3 {
        color: var(--system-content-primary, #FFF);
        font-family: "Source Serif 4";
        font-size: 30px;
        font-style: normal;
        font-weight: 400;
        line-height: 120%;
      }

      p {
        color: var(--system-content-secondary, #A3AEBF);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 130%;
        margin-top: 12px;
        max-height: 280px;
        overflow-y: auto;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          display: none;
        }
      }

      .btnBox {
        position: absolute;
        left: 24px;
        bottom: 32px;
        width: 276px;
        height: 40px;
        border-radius: var(--radius-10, 10px);
        background: var(--system-content-brandPrimary, #53F6B4);
        color: var(--system-content-onPrimary, #181818);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
  }
}