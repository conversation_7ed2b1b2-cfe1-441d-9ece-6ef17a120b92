@import '@/styles/variable.scss';
@import '@/app/[locale]/editor/_components/zIndex.scss';

.createBox {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: $z-index-create;
  overflow: hidden;
  overflow-y: auto;
  background-color: #0c0e10;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('./bg.png');
    background-repeat: no-repeat;
    background-position: left bottom;
    background-size: 100% auto;
    filter: blur(150px);
    z-index: -1;
  }

  .contentBox {
    width: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .title {
      margin-top: 50px;
      font-family: var(--font-poppins);
      font-size: 38px;
      font-style: normal;
      font-weight: 600;
      color: #fff;
    }

    .optBox {
      display: flex;
      gap: 16px;
      margin: 24px 0 40px;

      .addBox {
        width: 550px;
        height: 238px;
        border-radius: var(--radius-12, 12px);
        border: 1px dashed var(--system-stroke-input-default, #22272E);
        background: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(20px);
        background-blend-mode: overlay;
        color: var(--system-content-secondary, #A3AEBF);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        svg {
          width: 48px;
          height: 48px;
          margin-bottom: 8px;
        }
      }
    }

    .templateBox {
      margin-bottom: 50px;

      h3 {
        color: #FFF;
        text-align: center;
        font-family: var(--font-poppins);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px;
        margin-bottom: 16px;
      }

      :global {
        .ant-tabs {
          width: 100%;
          height: 100%;
          overflow: hidden;

          .template-category-list {
            width: 100%;
            display: flex;
            justify-content: center;
            flex: 0 0 auto;
            gap: 8px;
            overflow-x: auto;
            margin-bottom: 20px;
            scrollbar-width: none;
            -ms-overflow-style: none;

            &::-webkit-scrollbar {
              display: none;
            }

            .template-category-tab {
              display: flex;
              flex: 0 0 auto;
              justify-content: center;
              align-items: center;
              height: 26px;
              padding: 0 8px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
              color: var(--system-content-secondary, #A3AEBF);
              user-select: none;
              cursor: pointer;

              &.active {
                color: var(--system-content-primary, #FFF);
                background: var(--system-background-thirdary, #272C33);
              }
            }
          }

          .ant-tabs-content-holder {
            display: block;
            flex: none;
            width: 100%;
            // height: calc(100% - $template-tab-btn-height - $template-tabs-margin-bottom);
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;

            &::-webkit-scrollbar {
              display: none;
            }

            .template-list-container {
              // margin-bottom: $bottom-empty-height;

              .template-list-content {
                display: flex;
                gap: 8px;
              }

              .template-item {
                width: 107px;
                // height: 181px;
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                z-index: 0;
                cursor: pointer;

                :global {
                  .template-item-loading {
                    width: 107px;
                    height: 141px;
                    position: absolute;
                    top: 0;
                    left: 0;
                    background: rgba(0, 0, 0, 0.30);
                    backdrop-filter: blur(10px);
                    border-radius: 6px;
                    overflow: hidden;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .loading-box {
                      width: 18px;
                      height: 18px;
                    }
                  }

                  .template-item-image {
                    width: 107px;
                    height: 141px;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 0 0 1px var(--system-stroke-secondary, #464957) inset;

                    &>img.ant-image-img {
                      width: 100%;
                      height: 100%;
                      object-fit: cover;
                      position: relative;
                      z-index: -1;
                    }
                  }

                  .template-item-name {
                    margin-top: 8px;
                    height: 32px;
                    line-height: 16px;
                    font-size: 12px;
                    color: $system-content-secondary;
                    color: var(--system-content-secondary, #A3AEBF);
                    font-weight: 500;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2; // 显示的行数
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: break-all;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}