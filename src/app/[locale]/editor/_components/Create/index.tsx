import styles from "./index.module.scss";
import { Tabs, TabsProps, Image } from "antd";
import { useRootStore } from "../../_store";
import { startTransition, useEffect, useMemo, useState } from "react";
import classNames from "classnames";
import { useSaveCanvas } from "../../_hooks/useSaveProjectHistory";
import { toAtlasImageView2URL } from "@meitu/util";
import { Loading } from "@/components";
import { HeaderAction } from "../../_store/headerAction";
import { observer } from "mobx-react";
import CreateTipModal from "./CreateTipModal";
import { fetchPosterConfig } from "@/api/aiPoster/editor";
import {
  TemplateFuncType,
  TemplateItemType,
  TemplateListType,
} from "@/api/types/poster";
import { useI18n } from "@/locales/client";
import zoomToFitCanvas from "../../_utils/zoomToFitCanvas";
import { trackEvent } from "@/services/tracer";
import Lottie from "lottie-react";
import loadingAnimation from "@/assets/lottie/poster/imageLoading/data.json";
import { NewCanvas, NewImage } from "@/assets/icons";
import { TemplateExposureLocation } from "@/types/tracking";
import { useTemplateExpo } from "@/hooks/useTrack/useTemplateExpo";
import { useInsertTemplateWithGuide } from "../../_hooks/useInsertTemplate";
import { useStore } from "@/contexts/StoreContext";
import { needShowGuide } from "../../_utils/frame-guide/record";
import { useInsertFirstFrameGuide } from "../../_hooks/first-frame-guide";

interface TemplateCardProps {
  template: TemplateItemType;
  handleClick: () => void;
  loadingTemplates: Map<number, boolean>;
}

const TemplateCard = observer(
  ({ template, handleClick, loadingTemplates }: TemplateCardProps) => {
    const cardExpo = useTemplateExpo<HTMLDivElement>(
      template.id,
      template.categoryId,
      TemplateExposureLocation.FunctionDock
    );
    return (
      <div
        ref={cardExpo}
        className={"template-item"}
        onClick={handleClick}
        key={template.id}
      >
        <Image
          rootClassName={classNames("template-item-image")}
          src={toAtlasImageView2URL(template.pic, {
            mode: 2,
            width: 107 * window.devicePixelRatio,
            height: 141 * window.devicePixelRatio,
          })}
          placeholder={<Loading />}
          alt=""
          preview={false}
        />

        {loadingTemplates.get(template.id) === true && (
          <div className={"template-item-loading"}>
            <Lottie
              className={"loading-box"}
              animationData={loadingAnimation}
              autoplay
              loop
            />
          </div>
        )}
        <div className="template-item-name">{template.name}</div>
      </div>
    );
  }
);

function Create() {
  const t = useI18n();
  const rootStore = useRootStore();
  const {
    renderStore,
    configStore,
    editorStatusStore,
    paramsEditorStore,
    headerActionStore,
  } = useRootStore();

  const { userStore } = useStore();

  const render = renderStore.render;
  const historyPlugins = renderStore.historyPlugins;
  const { submitSaveElements } = useSaveCanvas(rootStore);

  const [templateList, setTemplateList] = useState<TemplateListType[]>([]);
  const [activeTab, setActiveTab] = useState(0);

  const [loadingTemplates, setLoadingTemplates] = useState<
    Map<number, boolean>
  >(new Map());

  const insertTemplateWithGuide = useInsertTemplateWithGuide({
    configStore,
    renderStore,
    userStore,
  });
  const insertFirstFrameGuide = useInsertFirstFrameGuide({
    configStore,
    renderStore,
    userStore,
  });

  useEffect(() => {
    trackEvent("transition_page_expo");
  }, []);

  // 获取模版列表
  useEffect(() => {
    fetchPosterConfig({ funcType: TemplateFuncType.Create }).then((res) => {
      const templateList = res.template;

      setTemplateList(templateList);
      const firstTab = templateList?.[0];
      if (firstTab?.categoryId) {
        setActiveTab(firstTab.categoryId);
      }
    });
  }, []);

  // 生成图片
  const handleGenerate = () => {
    trackEvent("transition_page_click", {
      click_type: "create_image",
      location: "center",
    });

    editorStatusStore.setShowCreate(false);
    paramsEditorStore.setIsOpenImageParams(true);
  };

  const handleInsertFirstFrame = async () => {
    const render = renderStore.render;
    const historyPlugins = renderStore.historyPlugins;
    if (!render || !historyPlugins) {
      return;
    }

    const elements = await insertFirstFrameGuide();
    const frame = elements && elements[0];

    if (frame) {
      const operation = historyPlugins?.baseAction.getAddOperation({
        objects: elements,
      });
      if (!operation) return;
      historyPlugins.submit(operation);

      render._FC.setActiveObject(frame);
      if (elements.length > 1) {
        zoomToFitCanvas(render, { targets: elements });
      } else {
        render.backToOriginPosition({ target: frame });
      }
      render._FC.requestRenderAll();

      await submitSaveElements(elements);
    }
  };

  // 新建画布
  const handleCanvas = () => {
    const uid = userStore.UID;
    if (!uid) {
      return;
    }

    trackEvent("transition_page_click", {
      click_type: "new_canvas",
      location: "center",
    });

    if (needShowGuide(uid, "frame")) {
      handleInsertFirstFrame();
    } else {
      headerActionStore.setActiveHeaderAction(HeaderAction.InsertFrame);
    }

    editorStatusStore.setShowCreate(false);
    paramsEditorStore.setIsOpenImageParams(false);
  };

  const items = useMemo(() => {
    return templateList.map((category) => {
      return {
        // Tabs组件的key要求string类型
        key: category.categoryId + "",
        label: category.categoryName,
        children: (
          <div className="template-list-container">
            <div className="template-list-content">
              {category.list.map((template) => {
                // 模版点击事件
                const handleClick = async () => {
                  // 有在loading中的模版不允许点击
                  if (
                    !render ||
                    Array.from(loadingTemplates.values()).some(
                      (loading) => loading === true
                    )
                  ) {
                    return;
                  }
                  trackEvent("template_feed_click", {
                    template_id: template.id,
                    template_category_id: category.categoryId,
                    location: TemplateExposureLocation.FunctionDock,
                  });
                  setLoadingTemplates((prev) =>
                    new Map(prev).set(template.id, true)
                  );

                  const elements = await insertTemplateWithGuide(template);
                  const frame = elements && elements[0];
                  if (frame) {
                    const operation =
                      historyPlugins?.baseAction.getAddOperation({
                        objects: elements,
                      });
                    if (!operation) return;
                    historyPlugins?.submit(operation);
                    submitSaveElements(elements);

                    render._FC.setActiveObject(frame);
                    zoomToFitCanvas(render);
                    // render.backToOriginPosition({ target: frame });
                    render._FC.requestRenderAll();

                    setLoadingTemplates((prev) =>
                      new Map(prev).set(template.id, false)
                    );
                    // 加载完之后关闭创建面板
                    editorStatusStore.setShowCreate(false);
                  }
                };
                return (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    handleClick={handleClick}
                    loadingTemplates={loadingTemplates}
                  />
                );
              })}
            </div>
          </div>
        ),
      };
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templateList, render, historyPlugins, loadingTemplates]);

  const renderTabBar: TabsProps["renderTabBar"] = (props) => (
    <div className="template-category-list">
      {templateList?.map((item) => {
        return (
          <div
            key={String(item.categoryId)}
            className={classNames(
              "template-category-tab",
              item.categoryId === activeTab && "active"
            )}
            onClick={() => {
              startTransition(() => {
                setActiveTab(item.categoryId);
              });
            }}
          >
            {item.categoryName}
          </div>
        );
      })}
    </div>
  );

  return (
    <>
      <div className={styles.createBox}>
        <div className={styles.contentBox}>
          <h1 className={styles.title}>
            {t("Inspiration Ready! Time to Create.")}
          </h1>
          <div className={styles.optBox}>
            <div className={styles.addBox} onClick={handleCanvas}>
              <NewCanvas />
              <span>Create poster in Canvas</span>
            </div>
            <div className={styles.addBox} onClick={handleGenerate}>
              <NewImage />
              <span>Generate Image</span>
            </div>
          </div>
          <div className={styles.templateBox}>
            <h3>{t("Pick a template and fuel your creativity!")}</h3>
            <Tabs
              items={items}
              activeKey={String(activeTab)}
              renderTabBar={renderTabBar}
            />
          </div>
        </div>
      </div>
      <CreateTipModal />
    </>
  );
}

export default observer(Create);
