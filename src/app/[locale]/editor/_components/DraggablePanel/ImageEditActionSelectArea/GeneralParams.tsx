import { observer } from "mobx-react-lite";
import { AreaMode, AreaModeRadio } from "../SelectArea/ToolParams/AreaModeRadio";
import { useState } from "react";

type GeneralParamsProps = {
  mode: AreaMode,
  onModeChange: (mode: AreaMode) => void;
}
function GeneralParams({mode, onModeChange}: GeneralParamsProps) {


  return (
    <AreaModeRadio value={mode} onChange={onModeChange}/>
  )
}

export default observer(GeneralParams);