import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { FabricObject, Path, PathBrush, SmartMask } from "@meitu/whee-infinite-canvas";
import { AreaMode, AreaModeRadio } from "../SelectArea/ToolParams/AreaModeRadio";
import { transformMaskImageData, transformSmartSwipeParams, validateHasMaskArea } from "./utils";
import { useRootStore } from "../../../_store";
import { imageEditBrushOptions } from "./constants";
import { createPureUpload } from "@/utils/uploader";
import { createSmartSwipeTask, querySmartSwipeTaskResult } from "@/api/aiPoster/intelligent";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import { Fetch, SmartSwipeTaskStatus } from "@/api/types/aiPoster/intelligent";
import { useRequest } from "ahooks";
import { useFullLoading } from "./fullLoading";
import { useI18n } from "@/locales/client";

type SwipeParamsProps = {
    mode: AreaMode,
    onModeChange: (mode: AreaMode) => void;
}

function SmartSwipeParams({ mode, onModeChange }: SwipeParamsProps) {
    const rootStore = useRootStore();
    const { renderStore, selectionStore, projectsStore } = useRootStore();
    const singleImage = selectionStore.singleImage;
    const render = renderStore.render;
    const image = singleImage?.image;
    const historyPlugins = renderStore.historyPlugins;
    const [queryId, setQueryId] = useState<string>("");
    const t = useI18n();
    const { startLoading, endLoading } = useFullLoading(t("Smart Swipe Loading"));



    /**
     * 转换mask图
     */
    const transformMaskImageByMaskUrl = async (maskUrl: string) => {
        const brush = render?._FC.freeDrawingBrush as PathBrush | undefined;
        if (!image) return
        if (!singleImage?.image || !brush) {
            return;
        }
        const img = await transformMaskImageData(maskUrl, [83, 264, 180])
        const revertImage = await transformMaskImageData(maskUrl, [255, 255, 255])
        if (!img || !revertImage) {
            return;
        }
        const smartMask = new SmartMask({
            img,
            revertImage,
            left: singleImage.image.getCenterPoint().x,
            top: singleImage.image.getCenterPoint().y,
            width: singleImage.image.width,
            height: singleImage.image.height,
            angle: singleImage.image.angle,
            scaleX: singleImage.image.scaleX,
            scaleY: singleImage.image.scaleY,
        })
        const beforeData = historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
        brush.clearTargetElement()
        brush.addSmartMask(smartMask)
        endLoading()
        const afterData = historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
        const operation = historyPlugins?.baseAction.getModifiedOperation({
            beforeData,
            afterData,
        });
        if (!operation) return;
        historyPlugins?.submit(operation);
    }



    const handleQuerySmartSwipeTaskResult = async (ids: string) => {
        if (!ids) return;
        try {
            return await querySmartSwipeTaskResult({ ids })
        } catch (error) {
            defaultErrorHandler(error)
            return false
        }
    }

    const { data, error, cancel, run } = useRequest(handleQuerySmartSwipeTaskResult, {
        pollingInterval: 3000,
    });

    useEffect(() => {
        if (!data) return;
        const task = data.find(item => item.id === queryId)
        if (!task) {
            cancel()
            return;
        };
        const isFailed = [
            SmartSwipeTaskStatus.OPEN_PLATFORM_DELIVERY_FAILED,
            SmartSwipeTaskStatus.OPEN_PLATFORM_PROCESS_FAILED,
            SmartSwipeTaskStatus.INPUT_FILE_AUDIT_FAILED,
            SmartSwipeTaskStatus.OUTPUT_FILE_AUDIT_FAILED,
            SmartSwipeTaskStatus.INPUT_FILE_BLOCKED,
            SmartSwipeTaskStatus.OUTPUT_FILE_BLOCKED,
            SmartSwipeTaskStatus.UNDEFINED_INTERRUPTION,
        ].includes(task.status);
        const isSuccess = task.status === SmartSwipeTaskStatus.SUCCESS;
        // console.log("task.status", task.status)
        if (isFailed) {
            // defaultErrorHandler(task.fail_msg)
            endLoading()
            cancel()
            return;
        }
        if (isSuccess) {
            endLoading()
            cancel()
            const maskUrl = task.resultImages[0]
            if (!maskUrl) {
                return;
            }
            transformMaskImageByMaskUrl(maskUrl)
        }
    }, [data, cancel, error]);

    /**
     * 处理智能涂抹
     */
    const handleSmartSwipe = async (params: Fetch.SmartSwipeRequestParams) => {
        try {
            const { id: msgId } = await createSmartSwipeTask(params)
            if (!msgId) {
                return;
            }
            setQueryId(msgId)
            run(msgId)
        } catch (error) {
            defaultErrorHandler(error)
            endLoading()
        }

        // const result = 'https://obs.mtlab.meitu.com/mtopen/c522c727cee24b17a93a58b1bd643f3f/ba2b525c-9bc5-4ee8-a70b-ae60e4a74b85_MTc0NTExNDQwMA==/80f501b1-26ca-4746-9eef-a037c5e216dc.jpg'
        // await transformMaskImageByMaskUrl(result)
    }

    /**
     * 处理智能选择mask
     */
    const handleSmartSwipeChange =
        async ({ target }: { target: FabricObject }) => {
            if (!render || !image) {
                return;
            }
            const brush = render._FC.freeDrawingBrush as PathBrush | undefined;
            if (!brush) {
                return;
            }
            const path = target as Path;
            brush.setShowShape(path._id_, false)
            let mask = ''
            startLoading()
            const result = await validateHasMaskArea(brush)
            if (result) {
                const p = await brush.exportShapesToBlob({
                    isMerge: true,
                    ext: "png",
                    exportContainerType: imageEditBrushOptions.shapeContainerName,
                    backgroundFill: "#000",
                    shapeFill: "#fff",
                });
                if (!p) return;
                const upload = createPureUpload();
                const blob = Array.isArray(p) ? p[0] : p;
                const imageRes = await upload({ file: blob });
                mask = imageRes.result?.previewUrl || "";
            }
            const params = transformSmartSwipeParams(
                path,
                singleImage.image,
                singleImage.options,
                mode
            )
            handleSmartSwipe({
                projectId: projectsStore.activeProjectId,
                initImage: singleImage.options.src,
                maskImage: mask,
                ...params
            })
        }

    useEffect(() => {
        if (!render) {
            return;
        }
        render._FC.on({
            'mask:path:created': handleSmartSwipeChange,
        });

        return () => {
            render._FC.off({
                'mask:path:created': handleSmartSwipeChange,
            });
        }
    }, []);


    return (
        <div>
            <AreaModeRadio value={mode} onChange={onModeChange} />
        </div>
    )
}

export default observer(SmartSwipeParams);
