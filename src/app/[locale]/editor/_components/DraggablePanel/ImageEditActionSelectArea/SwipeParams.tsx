import { observer } from "mobx-react-lite";
import { useEffect, useRef, useState } from "react";
import PaintSize from "../SelectArea/ToolParams/PaintSize";
import { FabricObject, IImage, Path, PathBrush } from "@meitu/whee-infinite-canvas";
import { AreaMode, AreaModeRadio } from "../SelectArea/ToolParams/AreaModeRadio";
import { transformSmartSwipeParams, validateHasMaskArea } from "./utils";
import { useRootStore } from "../../../_store";
import { imageEditBrushOptions } from "./constants";
import { createPureUpload } from "@/utils/uploader";

type SwipeParamsProps = {
  brushWidth: number,
  onBrushWidthChange: (value: number) => void;
  mode: AreaMode,
  onModeChange: (mode: AreaMode) => void;
}

function SwipeParams({ brushWidth, onBrushWidthChange, mode, onModeChange }: SwipeParamsProps) {

  return (
    <div>
      <AreaModeRadio value={mode} onChange={onModeChange} />
      <PaintSize value={brushWidth} onChange={onBrushWidthChange} />
    </div>
  )
}

export default observer(SwipeParams);

