import { useEffect, useRef, useState } from "react";
import toast from "@/components/Toast";
const fixedLoadingKey = "fixedSmartSwipeLoadingKey";
export const useFullLoading = (loadingText: string) => {
  const maskRef = useRef<HTMLDivElement>(document.createElement("div"));
  const [loading, setLoading] = useState(false);
  const startLoading = () => {
    setLoading(true);
  }

  const endLoading = () => {
    setLoading(false);
  }

  useEffect(() => {
    if (!document.body.contains(maskRef.current)) {
      document.body.appendChild(maskRef.current);
      maskRef.current.style.position = "fixed";
      maskRef.current.style.top = "0";
      maskRef.current.style.left = "0";
      maskRef.current.style.width = "100vw";
      maskRef.current.style.height = "100vh";
      maskRef.current.style.backgroundColor = "transparent";
      maskRef.current.style.zIndex = "1000";
    }
      if (loading) {
        toast.show({
          title: loadingText,
          duration: 0,
          type: "info",
          key: fixedLoadingKey,
        });
        maskRef.current.style.display = "block";
      } else {
        toast.destroy(fixedLoadingKey);
        maskRef.current.style.display = "none";
      }
  }, [ loading ]);

  return {
    startLoading,
    endLoading,
  };
};