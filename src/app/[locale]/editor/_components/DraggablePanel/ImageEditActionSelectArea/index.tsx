import { observer } from "mobx-react-lite"
import { SelectArea } from "../SelectArea";
import { DraggableItemRenderFunctionParams } from "@/components/DragContainer";
import { Box } from "../SelectArea/AreaTool/Box";
import { Swipe } from "../SelectArea/AreaTool/Swipe";
import { SmartSwipe } from "../SelectArea/AreaTool/SmartSwipe";
import { useEffect, useRef, useState } from "react";
import SwipeParams from "./SwipeParams";
import { Lasso } from "../SelectArea/AreaTool/Lasso";
import CancelAreaButton from "../SelectArea/CancelAreaButton";
import { useRootStore } from "../../../_store";
import { IImage, LassoBrush, ownDefaultsMouseStyle, PathBrush, RectBrush, RenderMaskCursor } from "@meitu/whee-infinite-canvas";
import { imageEditBrushOptions } from "./constants";
import { useGetParamsForm } from "../../../_context/FormContext";
import { AreaMode } from "../SelectArea/ToolParams/AreaModeRadio";
import GeneralParams from "./GeneralParams";
import { validateHasMaskArea } from "./utils";
import SmartSwipeParams from "./SmartSwipeParams";
type SelectAreaProps = {
  /**
   * 拖拽控制需要绑定的事件
   */
  handlers?: DraggableItemRenderFunctionParams['handlers'],
  onClose?: () => void,
}

enum ToolKey {
  // 智能涂抹
  // SmartSwipe = 'smart-swipe',
  // 框选
  Box = 'box',
  // 涂抹
  Swipe = 'swipe',
  // 套索
  Lasso = 'lasso',
}

function ImageEditActionSelectArea({ handlers, onClose }: SelectAreaProps) {

  const [activeKey, setActiveKey, ] = useState<string>(ToolKey.Box);
  const { renderStore, selectionStore, imageEditStore } = useRootStore();

  const historyPlugins = renderStore.historyPlugins;

  // mask模式: 添加/擦除
  const [mode, setMode] = useState(AreaMode.Add);
  const singleImage = selectionStore.singleImage;
  const render = renderStore.render;
  const renderStyle = renderStore.renderStyle;
  const image = singleImage?.image;
  // path宽度
  const [brushWidth, setBrushWidth] = useState(30);

  const getForm = useGetParamsForm();

  const brushes = useRef({
    [ToolKey.Box]: null as null | RectBrush,
    [ToolKey.Lasso]: null as null | LassoBrush,
    [ToolKey.Swipe]: null as null | PathBrush,
    // [ToolKey.SmartSwipe]: null as null | PathBrush,
  });

  // 获取/创建mask工具
  const getBrush = (key: ToolKey) => {

    if (!render || !image) {
      return;
    }

    const brush = brushes.current[key];
    if (brush) {
      return brush;
    }

    switch (key) {
      case ToolKey.Box: {
        const brush = brushes.current[ToolKey.Box] = new RectBrush(render._FC, {
          ...imageEditBrushOptions,
          targetElement: image as IImage,
        });
        return brush;
      }

      case ToolKey.Lasso: {
        const brush = brushes.current[ToolKey.Lasso] = new LassoBrush(render._FC, {
          ...imageEditBrushOptions,
          strokeDashArray: [5, 5],
          targetElement: image as IImage,
        });
        return brush;
      }


      case ToolKey.Swipe: {
        const brush = brushes.current[ToolKey.Swipe] = new PathBrush(render._FC, {
          ...imageEditBrushOptions,
          width: brushWidth,
          targetElement: image as IImage,
          eraseShapeColor: '#FA99FF',
          eraseShapeOpacity: 0.3,
        });
        return brush;
      }

      // case ToolKey.SmartSwipe: {
      //   const brush = brushes.current[ToolKey.SmartSwipe] = new PathBrush(render._FC, {
      //     ...imageEditBrushOptions,
      //     width: brushWidth,
      //     targetElement: image as IImage,
      //     eraseShapeColor: '#FA99FF',
      //     eraseShapeOpacity: 0.3,
      //   });
      //   return brush;
      // }
    }
  }

  // 销毁所有mask工具
  const destroyBrushes = () => {
    Object.keys(brushes.current).forEach(key => {
      brushes.current[key as ToolKey]?.destroy();
      brushes.current[key as ToolKey] = null
    })
  }




  // 创建/更新mask工具
  useEffect(() => {
    const selected = singleImage?.image;
    if (!render || !selected) {
      return;
    }

    const brush = getBrush(activeKey as ToolKey);
    if (!brush) {
      return;
    }

    render._FC.isDrawingMode = true;
    render._FC.freeDrawingBrush = brush;
    render._FC.freeDrawingCursor = getCursor();
    renderStyle?.setCursorStyle({
      mousedown: getCursor(),
      move: getCursor(),
      hover: getCursor(),
      defaults: getCursor(),
    })
    return () => {
      render._FC.isDrawingMode = false
      render._FC.freeDrawingBrush = undefined
      renderStyle?.setCursorStyle({
        mousedown: ownDefaultsMouseStyle.mousedown,
        move: ownDefaultsMouseStyle.move,
        hover: ownDefaultsMouseStyle.hover,
        defaults: ownDefaultsMouseStyle.defaults,
      })
    }

  }, [render, activeKey]);

  // 处理是否存在Mask区域
  const handleMaskChange = async () => {
    if (!render || !image) {
      return;
    }

    const brush = render._FC.freeDrawingBrush as PathBrush;
    if (!brush) {
      return;
    }
    const result = await validateHasMaskArea(brush);
    imageEditStore.setHasArea(result);
  }
  


  /**
 * 处理涂抹区状态同步
 * 1. 涂抹区变化时 更新hasArea
 * 2. 涂抹区变化时 更新表单
 */
  useEffect(() => {
    if (!render) {
      return () => {
        destroyBrushes();
      };
    }



    handleMaskChange();

    render._FC.on({
      'mask:rect:created': handleMaskChange,
      'mask:path:created': handleMaskChange,
      'mask:lasso:created': handleMaskChange,
      'mask:smart:created': handleMaskChange,
      'mask:rect:deleted': handleMaskChange,
      'mask:path:deleted': handleMaskChange,
      'mask:lasso:deleted': handleMaskChange,
      'mask:smart:deleted': handleMaskChange,
    });

    return () => {
      render._FC.off({
        'mask:rect:created': handleMaskChange,
        'mask:path:created': handleMaskChange,
        'mask:lasso:created': handleMaskChange,
        'mask:rect:deleted': handleMaskChange,
        'mask:path:deleted': handleMaskChange,
        'mask:lasso:deleted': handleMaskChange,
      });
      destroyBrushes();
    }
  }, []);

  const getCursor = () => {
    const isErase = mode === AreaMode.Reduce;
    switch (activeKey) {
      // case ToolKey.SmartSwipe: {
      //   return isErase ? RenderMaskCursor.intelligentMinus : RenderMaskCursor.intelligentPlus;
      // }
      case ToolKey.Box: {
        return isErase ? RenderMaskCursor.rectMinus : RenderMaskCursor.rectPlus;
      }
      case ToolKey.Lasso: {
        return isErase ? RenderMaskCursor.lassoMinus : RenderMaskCursor.lassoPlus;
      }
      case ToolKey.Swipe: {
        return isErase ? RenderMaskCursor.pathMinus(brushWidth) : RenderMaskCursor.pathPlus(brushWidth);
      }
    }
    return 'default';
  }


  useEffect(() => {
    getBrush(activeKey as ToolKey)?.setErase(mode === AreaMode.Reduce);
    if (render?._FC) {
      render._FC.freeDrawingCursor = getCursor();
      renderStyle?.setCursorStyle({
        mousedown: getCursor(),
        move: getCursor(),
        hover: getCursor(),
        defaults: getCursor(),
      })
    }
  }, [activeKey, mode, brushWidth])

  useEffect(() => {
    setMode(AreaMode.Add);
  }, [activeKey])

  const renderParams = () => {

    const handleBrushChange = (value: number) => {
      setBrushWidth(value);
      const brush = getBrush(ToolKey.Swipe) as null | PathBrush;
      brush?.setWidth(value);
    }

 

    switch (activeKey) {
      // case ToolKey.SmartSwipe: {
      //   return <SmartSwipeParams
      //     mode={mode}
      //     onModeChange={setMode}
      //   />
      // };
      case ToolKey.Box: {
        return <GeneralParams
          mode={mode}
          onModeChange={setMode}
        />
      };
      case ToolKey.Lasso: {
        return <GeneralParams
          mode={mode}
          onModeChange={setMode}
        />
      };
      case ToolKey.Swipe: {
        return <SwipeParams
          brushWidth={brushWidth}
          onBrushWidthChange={handleBrushChange}
          mode={mode}
          onModeChange={setMode}
        />;
      }
    }

    return null;
  }

  const renderCancelAreaButton = () => {

    if (!imageEditStore.hasArea) {
      return;
    }

    const handleClick = () => {
      if (!render?._FC || !image) return
      const beforeData = historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      getBrush(ToolKey.Swipe)?.clearTargetElement();
      render?._FC.requestRenderAll();
      const afterData = historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      const operation = historyPlugins?.baseAction.getModifiedOperation({
        beforeData,
        afterData,
      });
      if (!operation) return;
      historyPlugins?.submit(operation);
    }

    return (
      <CancelAreaButton onClick={handleClick} />
    )
  }

  return (
    <SelectArea
      handlers={handlers}
      onClose={onClose}
      activeKey={activeKey}
      onActiveKeyChange={setActiveKey}
      selectTools={[
        // {
        //   key: ToolKey.SmartSwipe,
        //   element: <SmartSwipe />
        // },
        {
          key: ToolKey.Box,
          element: <Box />
        },
        {
          key: ToolKey.Lasso,
          element: <Lasso />
        },
        {
          key: ToolKey.Swipe,
          element: <Swipe />
        }
      ]}
      paramsElement={renderParams()}
      cancelAreaButton={renderCancelAreaButton()}
    />
  )
}

export default observer(ImageEditActionSelectArea);