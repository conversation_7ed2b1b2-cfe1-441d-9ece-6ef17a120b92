import { useEffect, useRef } from "react";

import { useState } from "react";

interface PollingProps<T> {
    reqFunction: () => Promise<T>;
    interval: number;
}
export function usePolling<T>({
     reqFunction,
     interval,
}: PollingProps<T>) {
    const [data, setData] = useState<T | null>(null);
    const [error, setError] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [abortController, setAbortController] = useState<AbortController>(new AbortController());

    const animationFrameRef = useRef<number | null>(null);
    const startTimeRef = useRef<number>(0);

    const cancelPolling = () => {
        abortController.abort();
        if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
        }
    }
    
    const startPolling = async (timestamp: number) => {
        if (!startTimeRef.current) return;
        if (timestamp - startTimeRef.current > interval) {
            setLoading(true);
            try {
                const data = await reqFunction();
                setData(data);
            } catch (error) {
                setError(error);
            }
        }
        animationFrameRef.current = requestAnimationFrame(startPolling);

    }
    const start = () => {
        const fetchData = async () => {
            if (abortController.signal.aborted) return;
            animationFrameRef.current = requestAnimationFrame(startPolling);
        }
        fetchData();
    }
    return {
        data,
        error,
        loading,
        cancelPolling,
        start,
    }
}