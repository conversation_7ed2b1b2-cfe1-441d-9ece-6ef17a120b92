@import '@/styles/variable.scss';

.radio {
  display: flex;
  :global {
    .radio-item {
      display: flex;
      height: 64px;
      flex: 1 1 0;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      word-break: keep-all;
      color: $system-content-secondary;
      cursor: pointer;
      transition: color .3s;

      & > input {
        display: none;
      }

      &-icon {
        svg {
          width: 18px;
          height: 18px;
        }
      }

      &-display {
        margin-top: 4px;
        font-size: 12px;
      }

      &.checked {
        color: #fff;
      }
    }
  }
}