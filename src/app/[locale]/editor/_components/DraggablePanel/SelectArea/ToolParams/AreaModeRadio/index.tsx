import { MinusCircleBold, PlusCircleBoldFill } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import classNames from "classnames";
import { useI18n } from "@/locales/client";

export enum AreaMode {
  Add = "add",
  Reduce = "reduce",
}

type AreaModeRadioProps = {
  value?: AreaMode;
  onChange?(mode: AreaMode): void;
  className?: string;
};

export function AreaModeRadio({
  value,
  onChange,
  className,
}: AreaModeRadioProps) {
  const t = useI18n();
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    onChange?.(value as AreaMode);
  };

  return (
    <div className={classNames(styles.radio, className)}>
      <label
        className={classNames(
          "radio-item",
          value === AreaMode.Add && "checked"
        )}
      >
        <PlusCircleBoldFill className="radio-item-icon" />
        <span className="radio-item-display">{t("editor.Add Area")}</span>
        <input
          type="radio"
          name="area-mode"
          value={AreaMode.Add}
          onChange={handleChange}
        />
      </label>

      <label
        className={classNames(
          "radio-item",
          value === AreaMode.Reduce && "checked"
        )}
      >
        <MinusCircleBold className="radio-item-icon" />
        <span className="radio-item-display">
          {t("editor.Reduce Selection")}
        </span>
        <input
          type="radio"
          name="area-mode"
          value={AreaMode.Reduce}
          onChange={handleChange}
        />
      </label>
    </div>
  );
}
