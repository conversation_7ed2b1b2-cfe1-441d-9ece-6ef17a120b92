@import '@/styles/variable.scss';

.size {
  display: flex;
  padding: 8px 16px;
  justify-content: space-between;
  align-items: center;
  :global {
    .size-label {
      font-size: 12px;
      color: $system-content-secondary;
    }

    .size-input-container {
      width: 190px;

      .ant-input-number {
        width: 50px;
        input {
          text-align: center;
        }
      }
    }
  }
}