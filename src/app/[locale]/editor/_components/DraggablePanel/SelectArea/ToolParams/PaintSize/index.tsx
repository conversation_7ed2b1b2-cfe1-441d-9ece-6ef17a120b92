import { SliderInput } from '@/components';
import styles from './index.module.scss';
import classNames from 'classnames';
import { observer } from 'mobx-react-lite';
import { useEffect } from 'react';
import { useRootStore } from '@/app/[locale]/editor/_store';

type PaintSizeProps = {
  value?: number,
  onChange?(size: number): void,
  min?: number,
  max?: number,
  className?: string,
}

function PaintSize({
  value = 50,
  onChange,
  className,
  min = 1,
  max = 100,
}: PaintSizeProps) {

  const { renderStore } = useRootStore();

  useEffect(() => {
    const hotkey = renderStore.renderHotkey;
    if (!hotkey) {
      return;
    }

    const clamped = (value: number) => {
      return Math.min(max, Math.max(min, value));
    }

    const increaseBrushWidthHotkey = () => {
      onChange?.(clamped(value + 1));
    }

    const decreaseBrushWidthHotKey = () => {
      onChange?.(clamped(value - 1));
    }

    hotkey.registerHotKey("=", increaseBrushWidthHotkey);
    hotkey.registerHotKey("-", decreaseBrushWidthHotKey);

    return () => {
      hotkey.unregisterHotKey("=", increaseBrushWidthHotkey);
      hotkey.unregisterHotKey("-", decreaseBrushWidthHotKey);
    }
  }, [renderStore.renderHotkey, min, max, value, onChange]);

  
  return (
    <div className={classNames(styles.size, className)}>
      <span className="size-label">Size</span>
      <div className="size-input-container">
        <SliderInput
          value={value}
          onChange={onChange}
          onKeydown={e => {
            if (e.key === '=' || e.key === '-') {
              e.preventDefault();
              return;
            }

            e.nativeEvent.stopImmediatePropagation();
          }}
          // suffix="%"
          direction="horizontal"
          controls={false}
          markNum={0}
          min={min}
          max={max}
        />
      </div>
    </div>
  );
}

export default observer(PaintSize)