@import '@/styles/variable.scss';

.panel {
  box-sizing: content-box;
  padding: 16px;
  position: relative;
  display: inline-block;
  width: 268px;
  border-radius: var(--radius-12, 12px);
  border: 1px solid $system-stroke-input-default;
  background: $system-background-secondary;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);
  user-select: none;

  :global {
    .panel-close {
      position: absolute;
      right: 16px;
      top: 19px;
      min-width: 0;
      display: inline-block;
      background: transparent;
      border: none;
      padding: 0;
      display: flex;
      cursor: pointer;

      svg {
        width: 14px;
        height: 14px;
        color: $system-content-fourth;
      }

      &:hover {
        svg {
          color: $system-content-secondary;
        }
      }
    }

    .panel-header {
      .drag-handler {
        cursor: move;
        display: flex;
        align-items: center;
        &-icon {
          color: $system-content-fourth;
          width: 20px;
          height: 20px;
        }

        &-text {
          color: #fff;
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }

    .panel-body {
      .tool-tab {
        margin-top: 12px;
        height: 49px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        position: relative;
        &-item {
          flex: 1 1 0;
          color: $system-content-secondary;
          border: none;
          background: transparent;
          cursor: pointer;

          transition: color .3s;
          &.active {
            color: #fff;
          }
        }
      }

      .tool-params {
        padding-top: 16px;
        z-index: 0;
        --arrow-left: 0;
        position: relative;
        &::before {
          z-index: 0;
          position: absolute;
          display: block;
          content: '';
          width: 14px;
          height: 14px;
          left: -7px;
          top: 10px;
          border-radius: 4px;
          background: #272b32;
          transform: translate(var(--arrow-left), 0) rotate(-45deg);
          transition: transform .3s;
        }

        &-content {
          z-index: 1;
          width: 100%;
          background: #272b32;
          position: relative;
          border-radius: 12px;
        }
      }
    }

    .tool-tips-container {
      margin-top: 16px;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 4px;
      .tool-tips {
        color: $system-content-secondary;
        font-size: 12px;
      }
    }

    .panel-footer {
      margin-top: 16px;
    }
  }
}