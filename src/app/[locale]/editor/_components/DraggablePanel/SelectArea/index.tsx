import classNames from "classnames";
import styles from "./index.module.scss";
import { CrossBlack, InfoCircleBold, LayerDrag } from "@meitu/candy-icons";
import { DraggableItemRenderFunctionParams } from "@/components/DragContainer";
import { SelectToolOptions } from "./type";
import { useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import { useI18n } from "@/locales/client";

type SelectAreaProps = {
  /**
   * 拖拽控制需要绑定的事件
   */
  handlers?: DraggableItemRenderFunctionParams["handlers"];
  /**
   * 点击关闭按钮触发的事件
   */
  onClose?: () => void;
  /**
   * 选区工具
   */
  selectTools?: Array<SelectToolOptions>;

  activeKey?: string;
  onActiveKeyChange?(key: string): void;

  paramsElement?: React.ReactNode;

  cancelAreaButton?: React.ReactNode;
  /**
   * 工具提示
   */
  toolTips?: React.ReactNode;
};

export function SelectArea({
  handlers,
  onClose,
  selectTools,
  activeKey,
  onActiveKeyChange,
  paramsElement,
  cancelAreaButton,
  toolTips,
}: SelectAreaProps) {
  const t = useI18n();
  const tabElements = useRef<Map<string, HTMLButtonElement>>(new Map());
  const [paramsArrowLeft, setParamsArrowLeft] = useState(0);

  const toolParamsStyle = useMemo(() => {
    return {
      "--arrow-left": paramsArrowLeft + "px",
    } as any;
  }, [paramsArrowLeft]);

  useLayoutEffect(() => {
    const activeItemEl = activeKey && tabElements.current.get(activeKey);
    if (!activeItemEl) {
      return;
    }

    const bbox = activeItemEl.getBoundingClientRect();
    setParamsArrowLeft(bbox.width / 2 + activeItemEl.offsetLeft);
  }, [activeKey]);

  return (
    <section className={classNames(styles.panel, "panel")}>
      <button className="panel-close" onClick={onClose}>
        <CrossBlack className="panel-close-icon" />
      </button>
      <header className="panel-header">
        <div className="drag-handler" {...handlers}>
          <LayerDrag className="drag-handler-icon" />
          <span className="drag-handler-text">{t("editor.Select Area")}</span>
        </div>
      </header>
      <main className="panel-body">
        <div className="tool-tab">
          {selectTools?.map((tool) => {
            return (
              <button
                ref={(el) => {
                  if (el) {
                    tabElements.current.set(tool.key, el);

                    return () => {
                      tabElements.current.delete(tool.key);
                    };
                  }
                }}
                key={tool.key}
                className={classNames(
                  "tool-tab-item",
                  activeKey === tool.key && "active"
                )}
                onClick={() => {
                  onActiveKeyChange?.(tool.key);
                }}
              >
                {tool.element}
              </button>
            );
          })}
        </div>

        {!!paramsElement && (
          <div className="tool-params" style={toolParamsStyle}>
            <div className="tool-params-content">{paramsElement}</div>
          </div>
        )}
      </main>
      {!!toolTips && (
        <div className="tool-tips-container">
          {toolTips}
        </div>
      )}
      {!!cancelAreaButton && (
        <footer className="panel-footer">{cancelAreaButton}</footer>
      )}
    </section>
  );
}
