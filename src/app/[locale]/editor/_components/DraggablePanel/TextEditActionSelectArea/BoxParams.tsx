import { observer } from "mobx-react-lite";
import { useEffect } from "react";
import { IImage, RectBrush } from "@meitu/whee-infinite-canvas";
import { useRootStore } from "../../../_store";

function BoxParams() {

  const { renderStore, selectionStore } = useRootStore();
  const singleImage = selectionStore.singleImage;
  const image = singleImage?.image;
  const render = renderStore.render;
  

  useEffect(() => {
    if (!render || !singleImage?.image) {
      return;
    }

    const brush = new RectBrush(render._FC, {
      shapeColor: '#4053FF',
      shapeContainerName: 'fixText_mask_container', // 承载图形容器名称
      shapeContainerOpacity: 0.8, // 承载图形容器透明度
      showShapeTitle: false, // 是否显示图形标题
      showShapeCloseBtn: false, // 是否显示图形删除按钮
      targetElement: singleImage.image as IImage, // 承载图形元素
      maxMaskCount: 3, // 最大承载图形数量
      maxBoundsNumber: 512, // 最大承载图形数量
      erase: false, // 是否擦除
    });
    render._FC.isDrawingMode = true;
    render._FC.freeDrawingBrush = brush;


    return () => {
      brush.destroy();
    }
  }, [])


  // return (
  //   <AreaModeRadio value={mode} onChange={setMode}/>
  // )

  return null;
}

export default observer(BoxParams);