import { observer } from "mobx-react-lite";
import PaintSize from "../SelectArea/ToolParams/PaintSize";

type SwipeParamsProps = {
  brushWidth: number,
  onBrushWidthChange: (value: number) => void;
}

function SwipeParams({brushWidth, onBrushWidthChange}: SwipeParamsProps) {
  return (
    <div>
      <PaintSize value={brushWidth} onChange={onBrushWidthChange}/>
    </div>
  )
}

export default observer(SwipeParams);