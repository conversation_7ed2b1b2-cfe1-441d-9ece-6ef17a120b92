import { observer } from "mobx-react-lite"
import { SelectArea } from "../SelectArea";
import { DraggableItemRenderFunctionParams } from "@/components/DragContainer";
import { Box } from "../SelectArea/AreaTool/Box";
import { Swipe } from "../SelectArea/AreaTool/Swipe";
import { useEffect, useRef, useState } from "react";
import SwipeParams from "./SwipeParams";
import { Lasso } from "../SelectArea/AreaTool/Lasso";
import CancelAreaButton from "../SelectArea/CancelAreaButton";
import { useRootStore } from "../../../_store";
import { getElementOptions, IImage, LassoBrush, PathBrush, RectBrush, RenderMaskCursor, WarningType } from "@meitu/whee-infinite-canvas";
import { textEditBrushOptions } from "./constants";
import { useGetParamsForm } from "../../../_context/FormContext";
import toast from "@/components/Toast";
import { useI18n } from "@/locales/client";
import { InfoCircleBold } from "@meitu/candy-icons";

type SelectAreaProps = {
  /**
   * 拖拽控制需要绑定的事件
   */
  handlers?: DraggableItemRenderFunctionParams['handlers'],
  onClose?: () => void,
}

enum ToolKey  {
  Box = 'box',
  Swipe = 'swipe',
  Lasso = 'lasso',
}

function TextEditActionSelectArea({ handlers, onClose }: SelectAreaProps) {

  const [activeKey, setActiveKey] = useState<string>(ToolKey.Box);
  const { renderStore, selectionStore } = useRootStore();
  const [hasArea, setHasArea] = useState(false);
  const singleImage = selectionStore.singleImage;
  const render = renderStore.render;
  const renderStyle = renderStore.renderStyle;
  const image = singleImage?.image;
  const [brushWidth, setBrushWidth] = useState(50);
  const t = useI18n();

  const getForm = useGetParamsForm();

  const brushes = useRef({
    [ToolKey.Box]: null as null | RectBrush,
    [ToolKey.Lasso]: null as null | LassoBrush,
    [ToolKey.Swipe]: null as null | PathBrush,
  });

  const getBrush = (key: ToolKey) => {

    if (!render || !image) {
      return;
    }

    const brush = brushes.current[key];
    if (brush) {
      return brush;
    }

    switch(key) {
      case ToolKey.Box: {
        const brush = brushes.current[ToolKey.Box] = new RectBrush(render._FC, {
          ...textEditBrushOptions,
          targetElement: image as IImage,
        });
        return brush;
      }

      case ToolKey.Lasso: {
        const brush = brushes.current[ToolKey.Lasso] = new LassoBrush(render._FC, {
          ...textEditBrushOptions,
          strokeDashArray: [5, 5],
          targetElement: image as IImage,
        });
        return brush;
      }


      case ToolKey.Swipe: {
        const brush = brushes.current[ToolKey.Swipe] = new PathBrush(render._FC, {
          ...textEditBrushOptions,
          width: brushWidth,
          targetElement: image as IImage,
          eraseShapeColor: '#FA99FF',
          eraseShapeOpacity: 0.3,
        });
        return brush;
      }
    }
  }

  const destroyBrushes = () => {
    Object.keys(brushes.current).forEach(key => {
      brushes.current[key as ToolKey]?.destroy();
      brushes.current[key as ToolKey] = null
    })
  }
  useEffect(() => {
    const selected = singleImage?.image;
    if (!render || !selected) {
      return;
    }

    const brush = getBrush(activeKey as ToolKey);
    if (!brush) {
      return;
    }

    render._FC.isDrawingMode = true;
    render._FC.freeDrawingBrush = brush;
    return () => {
      render._FC.isDrawingMode = false
      render._FC.freeDrawingBrush = undefined
    }

  }, [render, activeKey]);

  useEffect(() => {
    const getCursor = (activeKey: ToolKey) => {
      switch (activeKey) {
  
        case ToolKey.Box: {
          return RenderMaskCursor.rectPlus;
        }
        case ToolKey.Lasso: {
          return RenderMaskCursor.lassoPlus;
        }
        case ToolKey.Swipe: {
          return RenderMaskCursor.pathPlus(brushWidth);
        }
      }
      return 'default';
    }

    if (!render || !renderStyle) {
      return;
    }

    const cursor = getCursor(activeKey as ToolKey)
    render._FC.freeDrawingCursor = cursor;
    renderStyle?.setCursorStyle({
      mousedown: cursor,
      move: cursor,
      hover: cursor,
      defaults: cursor,
    })
  }, [render, renderStyle, activeKey, brushWidth]);

  // 将图形上记录的涂抹区与提示词同步到表单
  const syncPromptList = () => {
    const brush = render?._FC.freeDrawingBrush;
    const image = singleImage?.image;
    if (!brush || !image || !render) {
      return;
    }
    const options = getElementOptions.call(render, image);
    const promptMap = new Map(options._custom_data_?.editTextContentList?.map((p: any) => [p.id, p.text]));
   

    const nextContentList = brush.getMasks().map((mask) => {
      const id = mask._id_;
      const index = mask.get('index');
      return {
        id,
        index,
        text: promptMap.get(id) ?? '',
      }
    });

    nextContentList.sort((c1, c2) => c1.index - c2.index);

    image.set({
      _custom_data_: {
        ...options._custom_data_,
        editTextContentList: nextContentList,
      }
    });

    const form = getForm();
    form?.setFieldsValue({
      contentList: nextContentList,
    });
  }

  /**
   * 处理涂抹区状态同步
   * 1. 涂抹区变化时 更新hasArea
   * 2. 涂抹区变化时 更新表单
   */
  useEffect(() => {
    if (!render) {
      return () => {
        destroyBrushes();
      };
    }

    const handleMaskChange = () => {
      if (!render || !image) {
        return;
      }

      const brush = render._FC.freeDrawingBrush as PathBrush;
      if (!brush) {
        return;
      }

      setHasArea(brush.getMaskCount() > 0);
      syncPromptList();
    }

    handleMaskChange();

    render._FC.on({
      'mask:rect:created': handleMaskChange,
      'mask:path:created': handleMaskChange,
      'mask:lasso:created': handleMaskChange,
      'mask:rect:deleted': handleMaskChange,
      'mask:path:deleted': handleMaskChange,
      'mask:lasso:deleted': handleMaskChange,
    });

    return () => {
      render._FC.off({
        'mask:rect:created': handleMaskChange,
        'mask:path:created': handleMaskChange,
        'mask:lasso:created': handleMaskChange,
        'mask:rect:deleted': handleMaskChange,
        'mask:path:deleted': handleMaskChange,
        'mask:lasso:deleted': handleMaskChange,
      });
      destroyBrushes();
    }
  }, []);

  // 选区个数提示
  useEffect(() => {
    if (!render) {
      return;
    }
    const handleExceedMaskLimit = (payload: {
      type: WarningType;
    }) => {
      if (payload.type !== WarningType.MASK_MAX_COUNT) {
        return;
      }

      toast.show({
        type: "info",
        title: t('text-edit.exceed-mask-limit'),
        duration: 3000,
      });
    };

    render._FC.on('warning', handleExceedMaskLimit);

    return () => {
      render._FC.off('warning', handleExceedMaskLimit);
    };
  }, [render]);


  const renderParams = () => {

    const handleBrushWidthChange = (value: number) => {  
      setBrushWidth(value);
      const brush = getBrush(ToolKey.Swipe) as null | PathBrush;
      brush?.setWidth(value);
    }


    switch(activeKey) {
      case ToolKey.Box: return null;
      case ToolKey.Lasso: return null;
      case ToolKey.Swipe: {
        return <SwipeParams brushWidth={brushWidth} onBrushWidthChange={handleBrushWidthChange}/>;
      }
    }

    return null;
  }

  const renderCancelAreaButton = () => {

    if (!hasArea) {
      return;
    }

    const handleClick = () => {
      getBrush(ToolKey.Swipe)?.clearTargetElement();
      render?._FC.requestRenderAll();
    }

    return (
      <CancelAreaButton onClick={handleClick}/>
    )
  }

  const renderToolTips = () => {
    return (
      <div className="tool-tips-container">
        <InfoCircleBold style={{ color: 'var(--system-content-secondary, rgba(163, 174, 191, 1))' }} />
        <div className="tool-tips">
          {t('TextEditActionSelectArea.tool-tips')}
        </div>
      </div>
    )
  }
  return (
    <SelectArea
      handlers={handlers}
      onClose={onClose}
      activeKey={activeKey}
      onActiveKeyChange={setActiveKey}
      selectTools={[
        {
          key: ToolKey.Box,
          element: <Box/>
        },
        {
          key: ToolKey.Lasso,
          element: <Lasso/>
        },
        {
          key: ToolKey.Swipe,
          element: <Swipe/> 
        }
      ]}
      paramsElement={renderParams()}
      cancelAreaButton={renderCancelAreaButton()}
      toolTips={renderToolTips()}
    />
  )
}

export default observer(TextEditActionSelectArea);