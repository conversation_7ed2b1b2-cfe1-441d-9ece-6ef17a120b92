import { DragContainer } from "@/components/DragContainer";
import { observer } from "mobx-react-lite";
import styles from './index.module.scss'
import classNames from "classnames";
import { useRootStore } from "../../_store";
import TextEditActionSelectArea from "./TextEditActionSelectArea";
import { HeaderAction } from "../../_store/headerAction";
import ImageEditActionSelectArea from "./ImageEditActionSelectArea";
import { useEffect } from "react";

function DraggablePanel() {

  const { editorStatusStore, headerActionStore, selectionStore, renderStore } = useRootStore();
  const { isRightSiderOpen } = editorStatusStore;
  const { activeHeaderAction } = headerActionStore;
  const { singleImage } = selectionStore;

  useEffect(() => {
    // console.log('singleImage', singleImage)
    if (
      (!singleImage &&
        (activeHeaderAction === HeaderAction.ModifyImage ||
         activeHeaderAction === HeaderAction.ModifyText
        )) ||
        (
          (singleImage?.image._id_ ?? '') !== (renderStore.render?._FC.freeDrawingBrush?.options.targetElement?._id_ ?? '') &&
          (activeHeaderAction === HeaderAction.ModifyImage ||
           activeHeaderAction === HeaderAction.ModifyText
          )
        )
    ) {
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    }
  }, [singleImage])

  //#region 改字的可移动浮层
  if (activeHeaderAction === HeaderAction.ModifyText) {
    const handleExitTextEdit = () => {
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    }

    return (
      <DragContainer
        className={classNames(styles.container, isRightSiderOpen && 'right-open')}
        draggableItems={[
          {
            key: 'change-text',
            render({ handlers }) {
              return <TextEditActionSelectArea handlers={handlers} onClose={handleExitTextEdit}/>
            }
          }
        ]}
      />
    )
  }
  //#endregion

  //#region 改图的可移动浮层
  if (activeHeaderAction === HeaderAction.ModifyImage) {
    const handleExitTextEdit = () => {
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    }

    return (
      <DragContainer
        className={classNames(styles.container, isRightSiderOpen && 'right-open')}
        draggableItems={[
          {
            key: 'change-image',
            render({ handlers }) {
              return <ImageEditActionSelectArea handlers={handlers} onClose={handleExitTextEdit}/>
            }
          }
        ]}
      />
    )
  }
  //#endregion

  return null;
}

export default observer(DraggablePanel)