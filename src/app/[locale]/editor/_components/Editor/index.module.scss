@import '@/app/[locale]/editor/_components/zIndex.scss';
@import '@/styles/variable.scss';
.editor {
  height: calc(100vh - $header-height);
  z-index: $z-index-editor;
  width: 100%;
  // height: 100%;
  background-color: #000;
}

:where(:global(.override-ant)):global(.ant-modal-root) {
  :global(.ant-modal-wrap.ant-modal-centered) {
    .images-number-limit-modal:global(.ant-modal):where(:global(.override-ant)) {
      :global {
        .ant-modal-content {
          padding: 24px 16px 16px;

          .ant-modal-close {
            color: $system-content-thirdary;
            
            &:hover {
              color: $system-content-tertiary;
            }
          }
    
          .modal-content {
            .title {
              color: $system-content-secondary;
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 22px;
              margin: 20px 0 28px 0;
            }
        
            .desc {
              color: $system-content-secondary;
              text-align: center;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
              margin: 8px 0 28px;
            }
        
            .button-box {
              display: flex;
              justify-content: space-between;
              align-items: center;
        
              .btn {
                width: 128px;
                height: 36px;
                border-radius: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
                // color: var(--content-btnPrimary, #fff);
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;
              }
        
              // .confirm {
              //   background: var(--system-content-brandPrimary, #53F6B4);
        
              //   &:hover {
              //     // background: @background-btn-ai-hover;
              //   }
              // }
        
              // .cancel {
              //   border: 1px solid var(--stroke-btnSecondary, #d0d2d6);
              //   background: var(--background-btnSecondary, #fff);
              //   color: var(--content-btnSecondary, #1c1d1f);
              //   cursor: pointer;
              //   &:hover {
              //     color: #53F6B4;
              //     border-color: #53F6B4;
              //   }
              // }
            }
        
            .checkbox-box {
              margin-top: 16px;
        
              span {
                color: var(--content-systemTertiary, #939599);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
                margin-left: 4px;
              }
            }
          }
        }
      }
    }
  }
}
