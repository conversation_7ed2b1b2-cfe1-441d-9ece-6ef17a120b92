@import '../zIndex.scss';
@import '@/styles/variable.scss';

$header-height: 56px;
.skeleton {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: $z-index-skeleton-loading;
  display: flex;
  flex-direction: column;

  :global {
    header {
      height: $header-height;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      background: $system-background-secondary;
      flex: 0 0 auto;
      .block {
        width: 32px;
        height: 32px;
        background: $system-background-thirdary;
        border-radius: 8px;
      }

      .left,
      .center,
      .right {
        display: flex;
        align-items: center;
      }

      .left {
        .block {
          &:nth-child(1) {
            width: 28px;
            height: 28px;
            margin-left: 16px;
          }

          &:nth-child(2) {
            width: 68px;
            margin-left: 8px;
          }

          &:nth-child(3) {
            width: 85px;
            margin: 0 24px;
          }

          &:nth-child(n + 4) {
            margin-right: 20px;
          }
        }
      }

      .center {
        display: flex;
        justify-content: center;
        .block {
          width: 36px;
          height: 36px;
          margin-left: 8px;
        }
      }

      .right {
        display: flex;
        justify-content: flex-end;

        .block {
          &:nth-child(1) {
            width: 68px;
            margin-right: 16px;
          }

          &:nth-child(2) {
            width: 135px;
            margin-right: 16px;
          }

          &:nth-child(3) {
            border-radius: 50%;
            border: 1px solid $stroke-editor-border-overlay;
            margin-right: 24px;
          }
        }
      }
    }

    main {
      flex: 1;
      min-height: 0;
      display: flex;
      justify-content: center;
      align-items: center;

      .center-logo {
        width: 96px;
        height: 96px;
      }
    }
  }
}
