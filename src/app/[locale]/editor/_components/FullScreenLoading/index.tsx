import styles from "./index.module.scss";
import { range } from "lodash";
import Lottie from "lottie-react";
import loadingAnimation from "@/assets/lottie/poster/loading/data.json";
import loading from "@/assets/images/loadingbrand.gif";

type FullScreenLoadingProps = {
  loading?: boolean;
};

export function FullScreenLoading({ loading }: FullScreenLoadingProps) {
  if (!loading) {
    return;
  }

  return <SkeletonScreen />;
}

function SkeletonScreen() {
  return (
    <div className={styles.skeleton}>
      <header>
        <div className="left">
          {range(0, 7).map((i) => (
            <div key={i} className="block"></div>
          ))}
        </div>

        <div className="center">
          {range(0, 5).map((i) => (
            <div key={i} className="block"></div>
          ))}
        </div>

        <div className="right">
          {range(0, 3).map((i) => (
            <div key={i} className="block"></div>
          ))}
        </div>
      </header>

      <main>
        <div className="center-logo">
          {/* <Lottie animationData={loadingAnimation} autoplay loop /> */}
          <img src={loading.src} alt="" />
        </div>
      </main>
    </div>
  );
}
