import { TextFont } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import classNames from "classnames";
import { Tooltip } from "antd";
import { observer } from "mobx-react-lite";
import { useRootStore } from "../../../_store";
import { HeaderAction } from "../../../_store/headerAction";
import { useEffect } from "react";
import {
  CoreMouseMode,
  ElementName,
  FabricObject,
} from "@meitu/whee-infinite-canvas";
import { useSubmitAddHistory } from "../../../_hooks/useSaveProjectHistory";
import { useI18n } from "@/locales/client";

type AddTextProps = {
  disabled?: boolean;
};

function AddText({ disabled }: AddTextProps) {
  const rootStore = useRootStore();
  const { headerActionStore, renderStore, editorStatusStore } = rootStore;
  const isActive =
    headerActionStore.activeHeaderAction === HeaderAction.InsertText;
  const activateInsertText = () => {
    editorStatusStore.setShowCreate(false);
    headerActionStore.activateActionAndResetCursor(HeaderAction.InsertText);
  };

  const submitAddHistory = useSubmitAddHistory(rootStore);
  const t = useI18n();

  useEffect(() => {
    const render = renderStore.render;
    const renderSelection = renderStore.renderSelection;

    if (!render || !isActive || !renderSelection) {
      return;
    }

    renderSelection.setMode(CoreMouseMode.TEXT);
    const handleAfterInsert = ({ objects }: { objects: FabricObject[] }) => {
      if (objects.length !== 1 || objects[0]._name_ !== ElementName.TEXT) {
        return;
      }

      submitAddHistory(objects);
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    };
    render._FC.on("object:insert", handleAfterInsert);
    return () => {
      render._FC.off("object:insert", handleAfterInsert);
      renderSelection.setMode(CoreMouseMode.SELECTION);
    };
  }, [isActive, renderStore]);

  return (
    <Tooltip title={`${t("Text")} T`}>
      <button
        disabled={disabled}
        className={classNames(
          styles.text,
          isActive && "selected",
          disabled && "disabled"
        )}
        role="button"
        onClick={activateInsertText}
      >
        <TextFont />
      </button>
    </Tooltip>
  );
}

export default observer(AddText);
