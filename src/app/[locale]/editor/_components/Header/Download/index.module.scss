@import '../header-common.scss';
@import '@/styles/variable.scss';

.download {
  display: flex;
  height: 32px;
  padding: 0px var(--spacing-10, 10px);
  align-items: center;
  border-radius: var(--radius-8, 8px);
  background: $system-content-brand-primary;
  color: $system-content-on-primary;
  cursor: pointer;
  margin-right: 16px;
  user-select: none;

  &:hover {
    opacity: 0.9;
  }

  &:global(.disabled) {
    cursor: not-allowed;
    border-radius: var(--radius-6, 6px);
    background: linear-gradient(0deg, var(--system-background-thirdary, #272C33) 0%, var(--system-background-thirdary, #272C33) 100%), var(--system-background-disabled, #393D48);
    color: var(--system-content-disabled, #4A5564);
    border: 1px solid var(--system-stroke-button, #323B48);
  }

  :global {
    .download-icon {
      svg {
        width: 16px;
        height: 16px;
      }
    }

    .download-label {
      font-size: 14px;
      margin: 0 5px 0 4px;
    }

    .download-suffix {
      svg {
        width: 12px;
        height: 12px;
      }
    }
  }
}

.download-popover {
  @include popover-mixin;
}

.download-dropdown-item {
  @include dropdown-item-mixin;
  display: flex;
  justify-content: space-between;
  width: 233px;
  word-break: keep-all;
  white-space: nowrap;

  &:global(.watermark-item) {

    :global {
      .watermark-item-display {
        display: flex;
        height: 100%;
        align-items: center;

        .watermark-item-label {
          margin-right: 8px;
        }

        .watermark-item-icon {
          width: 29px;
          height: 14px;
        }
      }
    }
  }
}

.watermark-item-switch {
  &:global(.ant-switch-checked) {
    background: var(--background-btnAi, #53F6B4) !important;
  }

  :global {
    .ant-switch-handle {
      &::before {
        background-color: #272C33 !important;
      }
    }
  }
}