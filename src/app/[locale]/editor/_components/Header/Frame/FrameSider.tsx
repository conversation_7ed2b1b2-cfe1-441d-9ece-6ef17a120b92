import { Collapse, Drawer } from "antd";
import styles from "./index.module.scss";
import { CollapseProps } from "antd/lib";
import { useMemo } from "react";
import { PointingTrianglelRight } from "@meitu/candy-icons";
import classNames from "classnames";

type FrameSiderProps = {
  show?: boolean;
  onSelect?: (dimension: [number, number]) => void;
}

type DimensionConfig = {
  category: string;
  dimensions: Array<{
    name: string;
    value: [number, number];
  }>
}

const config: Array<DimensionConfig> = [
  {
    category: 'Phone',
    dimensions: [
      {
        name: 'Android Compact',
        value: [412, 917],
      },
      {
        name: 'Android Medium',
        value: [700, 840],
      },
      {
        name: 'iPhone 16',
        value: [393, 852],
      },
      {
        name: 'iPhone 16 Pro',
        value: [402, 874],
      },
      {
        name: 'iPhone 16 Pro Max',
        value: [440, 956],
      },
      {
        name: 'iPhone 16 Plus',
        value: [430, 932],
      },
      {
        name: 'iPhone 14 & 15 Pro Max',
        value: [430, 932],
      },
      {
        name: 'iPhone 14 & 15 Pro',
        value: [393, 852],
      },
      {
        name: 'iPhone 13 & 14',
        value: [390, 844],
      },
      {
        name: 'iPhone 14 Plus',
        value: [428, 926],
      },
      {
        name: 'iPhone 13 mini',
        value: [375, 812],
      },
      {
        name: 'iPhone SE',
        value: [320, 568],
      },
    ]
  },
  {
    category: 'Tablet',
    dimensions: [
      {
        name: 'Android Expanded',
        value: [1280, 800]
      },
      {
        name: 'iPad mini 8.3"',
        value: [744, 1133],
      },
      {
        name: 'iPad Pro 11"',
        value: [834, 1194],
      }
    ]
  },
  {
    category: 'Paper',
    dimensions: [
      {
        name: 'A4',
        value: [595, 842]
      },
      {
        name: 'A5',
        value: [420, 595],
      },
      {
        name: 'A6',
        value: [297, 420],
      },
      {
        name: 'Letter',
        value: [612, 792],
      },
      {
        name: 'Tabloid',
        value: [792, 1224],
      }
    ]
  },
  {
    category: 'Social media',
    dimensions: [
      {
        name: 'Twitter post',
        value: [1200, 675],
      },
      {
        name: 'Facebook post',
        value: [1200, 630],
      },
      {
        name: 'Facebook cover',
        value: [820, 312],
      },
      {
        name: 'Instagram post',
        value: [1080, 1080],
      },
      {
        name: 'Dribbble shot',
        value: [400, 300],
      },
      {
        name: 'Dribbble shot HD',
        value: [800, 600],
      }
    ]
  }
];

export function FrameSider({show, onSelect}: FrameSiderProps) {

  const items: CollapseProps['items'] = useMemo(() => config.map(({ category, dimensions }) => {
    
    const renderDimensions = () => {
      return (
        <ul className="dimensions">
          { dimensions.map(({ name, value }) => {
            return (
              <li key={name} className="dimensions-item" onClick={() => onSelect?.(value)}>
                <span className="name">{name}</span>
                <span className="value">{value[0]} × {value[1]}</span>
              </li>
            )
          }) }
        </ul>
      );
    }
    
    return {
      key: category,
      label: category,
      children: renderDimensions(),
    }
  }), [onSelect])

  return (
    <Drawer
      rootClassName={styles.sider}
      open={show}
      mask={false}
      closable={false}
      placement={"left"}
      width={320}
      destroyOnClose
    >
      <Collapse 
        bordered={false}
        items={items}
        rootClassName="dimensions-collapse"
        defaultActiveKey={config.map(({ category }) => category)}
        expandIcon={({ isActive }) => (
          <PointingTrianglelRight className={classNames('expand-icon', isActive && 'active')}/>
        )}
      />
    </Drawer>
  )
}