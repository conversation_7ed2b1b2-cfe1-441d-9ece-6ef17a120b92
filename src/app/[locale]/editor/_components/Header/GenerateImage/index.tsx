import { PictureGenenrate } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import classNames from "classnames";
import { Tooltip } from "antd";
import { observer } from "mobx-react-lite";
import { useRootStore } from "../../../_store";
import { HeaderAction } from "../../../_store/headerAction";
import { useI18n } from "@/locales/client";
import { trackEvent } from "@meitu/subscribe-intl";

type GenerateImageProps = {
  disabled?: boolean;
};

function GenerateImage({ disabled }: GenerateImageProps) {
  const rootStore = useRootStore();
  const {
    headerActionStore,
    renderStore,
    editorStatusStore,
    paramsEditorStore,
  } = rootStore;
  const t = useI18n();

  const handleGenerateImage = () => {
    if (editorStatusStore.showCreate) {
      trackEvent("transition_page_click", {
        click_type: "create_image",
        location: "top",
      });
    }

    // 清空选择
    renderStore.render?._FC.discardActiveObject();
    renderStore.render?._FC.requestRenderAll();
    headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    // 设置打开图片参数栏
    editorStatusStore.setShowCreate(false);
    paramsEditorStore.setIsOpenImageParams(true);
  };

  return (
    <Tooltip title={`${t("header.Generate")} G`}>
      <button
        id="generate-image"
        disabled={disabled}
        className={classNames(styles.generate, disabled && "disabled")}
        role="button"
        onClick={handleGenerateImage}
      >
        <PictureGenenrate />
      </button>
    </Tooltip>
  );
}

export default observer(GenerateImage);
