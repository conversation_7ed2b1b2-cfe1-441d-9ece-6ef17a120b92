import { <PERSON><PERSON>enerate } from "@meitu/candy-icons";
import { Tooltip } from "antd";
import classNames from "classnames";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { HeaderAction } from "@/app/[locale]/editor/_store/headerAction";
import { AIPoster } from "@/api/types/aiPoster/task";
import { useEffect } from "react";
import { isTextEditing } from "@/app/[locale]/editor/_utils/isTextEditing";
import { useI18n } from "@/locales/client";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "@/app/[locale]/editor/_constant/track";

function TextEditAction() {
  const t = useI18n();
  const rootStore = useRootStore();
  const { selectionStore, headerActionStore, renderStore } = rootStore;

  const image = selectionStore.singleImage?.image;
  const imageOptions = selectionStore.singleImage?.options;
  const objectLoading = imageOptions?._loading_;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const disabled =
    !image || objectLoading || imageStatus === AIPoster.ImageStatus.AuditFailed;

  //快捷键注册
  useEffect(() => {
    const renderHotkey = renderStore.renderHotkey;
    if (!renderHotkey || disabled) return;

    const hotkeyHandler = (event: KeyboardEvent) => {
      if (isTextEditing(renderStore.render)) {
        return;
      }
      event.preventDefault();
      headerActionStore.setActiveHeaderAction(HeaderAction.ModifyText);
    };

    renderHotkey.registerHotKey("shift + t", hotkeyHandler);

    return () => {
      renderHotkey.unregisterHotKey("shift + t", hotkeyHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.renderHotkey, disabled]);

  if (!image || !imageOptions) {
    return null;
  }

  const isActive =
    headerActionStore.activeHeaderAction === HeaderAction.ModifyText;

  const handleActiveTextEdit = () => {
    if (disabled) {
      return;
    }

    trackEvent("edit_page_top_function_click", {
      function: Track.FunctionEnum.ModifyText,
    });

    if (headerActionStore.activeHeaderAction !== HeaderAction.ModifyText) {
      headerActionStore.setActiveHeaderAction(HeaderAction.ModifyText);
    } else {
      headerActionStore.activateActionAndResetCursor();
    }
  };

  return (
    <Tooltip title={t("Image Text Editor")}>
      <div
        className={classNames("actions-item", isActive && "selected", {
          disabled,
        })}
        onClick={handleActiveTextEdit}
      >
        <div className="icon-box">
          <TexGenerate />
        </div>
        <span className="icon-desc">{t("Image Text Editor")}</span>
      </div>
    </Tooltip>
  );
}

export default observer(TextEditAction);
