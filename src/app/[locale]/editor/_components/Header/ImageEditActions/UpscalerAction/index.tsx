import { submitTask } from "@/api/aiPoster/task";
import { AIPoster } from "@/api/types/aiPoster/task";
import { LosslessZoom2, Mt<PERSON>ean<PERSON>ill, UltraHd } from "@meitu/candy-icons";
import { message, Tooltip } from "antd";
import classNames from "classnames";
import { observer } from "mobx-react";
import { useEffect, useRef, useState } from "react";
import {
  FabricObject,
  getElementOptions,
  LoadingType,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { isTextEditing } from "@/app/[locale]/editor/_utils/isTextEditing";
import { isHasAlpha } from "@/app/[locale]/editor/_utils/imageEdit";
import { MtccFuncCode } from "@/types";
import { handleSubmitError } from "@/app/[locale]/editor/_utils/submit";
import { dispatch } from "@/app/[locale]/editor/_utils/TaskDispatcher";
import { MaxUpscalerLimit } from "@/app/[locale]/editor/_constant/params";
import useConfirmModal from "@/hooks/useConfirmModal";
import { HeaderAction } from "@/app/[locale]/editor/_store/headerAction";
import { useGuideCreateProject } from "@/app/[locale]/editor/_context/GuideCreateProjectContext";
import { useI18n } from "@/locales/client";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { fetchPriceDesc } from "@/api/meidou";
import { FunctionCode, MeiDouFetchPriceDescResponse } from "@/api/types/meidou";
import { toSnakeCase } from "@meitu/util";
import styles from "../index.module.scss";
import useSizeExceedConfirmModal from "@/app/[locale]/editor/_hooks/useSizeExceedConfirmModal";
import { useMeiDouBalance } from "@/hooks/useMeidou";
import { trackEvent } from "@/services/tracer";
import { Track } from "@/app/[locale]/editor/_constant/track";

export const HDLimitSize = 2560 * 2560;
export const SuperHDLimitSize = 1280 * 1280;
export enum srValue {
  HD = 2,
  superHD = 4,
  disable = 1,
}

function UpscalerAction() {
  const rootStore = useRootStore();
  const {
    renderStore,
    projectsStore,
    selectionStore,
    headerActionStore,
    editorStatusStore,
  } = rootStore;
  const t = useI18n();
  const image = selectionStore.singleImage?.image;
  const imageOptions = selectionStore.singleImage?.options;

  const objectLoading = imageOptions?._loading_;

  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const [upscalerMode, setUpscalerMode] = useState<srValue>(srValue.disable);
  const { createGuideProject, isInGuidePage } = useGuideCreateProject();

  const { open: openSubscribeModal } = useSubscribeModal();
  const { userStore } = useStore();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const [price, setPrice] = useState<MeiDouFetchPriceDescResponse | null>(null);
  const { availableAmount } = useMeiDouBalance({ userStore });

  // const openSubscribePopup = useOpenSubscribePopup();

  const handleUpscalerRef = useRef<any>(null);

  useEffect(() => {
    if (!image) {
      return;
    }

    let ignore = false;

    // 获取美豆数量
    fetchPriceDesc({
      functionCode: FunctionCode.aiPosterMagicsr,
      functionBody: JSON.stringify(
        toSnakeCase({
          srNum: srValue.superHD,
        })
      ),
    }).then((res) => {
      if (ignore) {
        return;
      }

      setPrice(res);
    });

    return () => {
      ignore = true;
    };
  }, [image]);

  // 获取当前图片超清模式
  // const getUpscalerMode = () => {
  //   const area = Number(imageOptions?.width) * Number(imageOptions?.height);

  //   if (area <= SuperHDLimitSize) {
  //     return srValue.superHD;
  //   } else if (area > SuperHDLimitSize && area <= HDLimitSize) {
  //     return srValue.HD;
  //   }
  //   return srValue.disable;
  // };

  const customData = imageOptions?._custom_data_history_ ?? {};

  const imageSrc = imageOptions?.src;
  // 超清的原图优先使用urlShort（无水印） 对于上传的图片 没有urlShort 使用src即可
  const initImage = customData?.urlShort ?? imageOptions?.src;

  const disabled =
    objectLoading || imageStatus === AIPoster.ImageStatus.AuditFailed;

  //快捷键注册
  useEffect(() => {
    const renderHotkey = renderStore.renderHotkey;
    if (disabled || !renderHotkey) return;

    const hotkeyHandler = (event: KeyboardEvent) => {
      if (isTextEditing(renderStore.render)) {
        return;
      }
      event.preventDefault();
      handleUpscalerRef.current?.();
    };

    renderHotkey.registerHotKey("shift + u", hotkeyHandler);

    return () => {
      renderHotkey.unregisterHotKey("shift + u", hotkeyHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [renderStore.renderHotkey, disabled]);

  const onConfirm = () => {
    handleUpscaler();
  };

  const { open, contextHolder, getNeedsOpen } = useSizeExceedConfirmModal({
    feature: AIPoster.TaskCategory.Upscaler,
    title: "Do you want to continue?",
    description:
      "The image is too large, processing it will affect the resolution.",
    onConfirm,
  });

  if (!image || !imageOptions || !imageSrc) {
    return null;
  }

  const handleUpscalerPreset = async () => {
    if (objectLoading) return;

    //#region 埋点
    trackEvent("edit_page_top_function_click", {
      function: Track.FunctionEnum.ImageEnhancer,
    });

    const deficit = (availableAmount ?? 0) < (price?.amount ?? 0);
    const options =
      renderStore.render && getElementOptions.call(renderStore.render, image);
    const imageIsCreate =
      !!options?._custom_data_history_?.msgId ||
      options?._custom_data_history_?.isFromTemplatePreview;
    trackEvent("create_btn_click", {
      function: Track.FunctionEnum.ImageEnhancer,
      board_info: Track.BoardInfo.NoFrame,
      is_picture_upload: imageIsCreate
        ? Track.IsPictureUpload.Create
        : Track.IsPictureUpload.Upload,
      width: image.getScaledWidth(),
      height: image.getScaledHeight(),
      is_vip: userStore.vipLevel,
      // 消耗美豆为0 上报免费
      credit_balance_sufficient:
        price?.amount === 0
          ? Track.CreditBalanceSufficient.Free
          : // 小号不为0 判断是否足够
          deficit
          ? Track.CreditBalanceSufficient.NotEnough
          : Track.CreditBalanceSufficient.Enough,
    });
    //#endregion

    if (!projectsStore.activeProjectId) {
      if (!isInGuidePage) {
        return;
      }

      await createGuideProject();
    }

    if (
      Math.max(imageOptions?.width, imageOptions?.height) > MaxUpscalerLimit &&
      getNeedsOpen()
    ) {
      open();
    } else {
      handleUpscaler();
    }
  };
  const handleUpscaler = async () => {
    if (objectLoading) return;

    // if (upscalerMode === srValue.disable) {
    //   message.error("图片过大，不支持进行超清～");
    //   return;
    // }

    const projectId = projectsStore.activeProjectId;
    const render = renderStore.render;
    const historyPlugins = renderStore.historyPlugins;
    if (!projectId || !render || !historyPlugins) {
      return;
    }

    // 退出其他操作的编辑态
    headerActionStore.setActiveHeaderAction(HeaderAction.Cursor);

    // 图形loading结束
    const loaded = () => {
      render.Actions.setLoaded(imageOptions._id_);
    };

    // 图形loading
    const operation = historyPlugins.baseAction.getLoadOperation([image]);

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== image
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    operation && historyPlugins.submit(operation);
    render.Actions.setLoading({
      type: LoadingType.FADE_IN_TO_OUT,
      text: "Generating...",
      id: imageOptions._id_,
    });

    let msgId = "";
    let imageParams = {} as Record<string, any>;
    try {
      const isAlpha = await isHasAlpha(imageSrc);
      const params = {
        imageFile: initImage,
        srNum: srValue.superHD,
        alphaStatus: [Number(isAlpha)],
        initWatermarkImage: imageSrc,
        linkMsgId: customData?.msgId,
      };
      imageParams = params;
      // 创建任务
      const res = await submitTask({
        taskCategory: AIPoster.TaskCategory.Upscaler,
        projectId,
        params,
        functionName: MtccFuncCode.FuncCodePosterSuper,
      });
      editorStatusStore.addActiveTask(res.id);
      msgId = res.id;
    } catch (err) {
      handleSubmitError(err, rootStore, {
        subscribeModal: {
          open: openSubscribeModal,
        },
        userStore: userStore,
        openMeiDouRecordsPopup,
      });
      loaded();
      return;
    } finally {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }

    // 如果任务创建失败 或者任务被中断 不继续执行
    if (!msgId || abortController.signal.aborted) {
      return;
    }

    return dispatch({
      msgId,
      rootStore,
      shape: image,
      expandCustomData: {
        params: imageParams,
      },
      t,
    }).finally(() => {
      // 避免超分的图显示过大
      render?.backToOriginPosition();

      // 更新美豆
      userStore.refreshMtBeanBalance();
    });
  };
  handleUpscalerRef.current = handleUpscalerPreset;

  return (
    <>
      <Tooltip
        arrow={false}
        rootClassName={styles.actionsItemTips}
        align={{
          offset: [0, -10],
        }}
        title={
          <div className={styles.actionsItemTipsContent}>
            <div className={styles.leftBox}>
              <span>{t("Image Enhancer")}</span>
              {price?.amount !== 0 && (
                <div className={styles.price}>
                  <MtBeanFill />
                  <span className={"eraseCount"}>{price?.amount}</span>
                </div>
              )}
            </div>
            <div className={styles["key-wrapper"]}>
              <div className={styles["key-container"]}>Shift</div>
              <div className={styles.sign}>+</div>
              <div className={styles["key-container"]}>U</div>
            </div>
          </div>
        }
      >
        <div
          className={classNames("actions-item", { disabled })}
          onClick={handleUpscalerPreset}
        >
          <div className="icon-box">
            <UltraHd />
          </div>
          <span className="icon-desc">{t("Image Enhancer")}</span>
        </div>
      </Tooltip>
      {contextHolder}
    </>
  );
}

export default observer(UpscalerAction);
