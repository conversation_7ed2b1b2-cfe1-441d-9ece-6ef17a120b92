@import '../header-common.scss';

.actions {
  display: flex;
  gap: 14px;

  :global {
    .actions-item {
      @include header-button-mixin;

      flex-direction: column;
      width: auto;

      .icon-box {
        width: 28px;
        height: 28px;
        min-height: 28px;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        flex-grow: 0;

        svg {
          width: 17.5px;
          height: 17.5px;
        }
      }

      .icon-desc {
        margin-top: 2px;
        color: var(--system-content-secondary, #A3AEBF);
        font-family: "PingFang SC";
        font-size: 8px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      &:hover:not(:global(.disabled)):not(:global(.selected)) {
        background: none;
        box-shadow: none;

        .icon-box {
          background: $system-background-thirdary;
          box-shadow: 0px 0px 0px 1px var(--system-stroke-button, #323B48) inset;

          svg {
            color: #fff;
          }
        }
      }

      &.selected {
        background: none;

        .icon-box {
          background: $system-content-brand-primary;

          svg {
            color: $system-content-on-primary;
          }
        }
      }
    }
  }
}

.actionsItemTips {
  &:global(.ant-tooltip) {
    // width: 280px;
    height: 56px;
    max-width: max-content;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-button, #323B48);
    background: var(--background-editorPopup-default, #1D1E23);
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);
    padding: 16px;

    :global {
      .ant-tooltip-inner {
        background: transparent;
        padding: 0;
        color: var(--system-content-primary, #FFF);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
      }
    }

    .actionsItemTipsContent {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 12px;

      .leftBox {
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;

        .price {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 2px;

          svg {
            width: 16px;
            height: 16px;
            color: #FBCD6F;
          }
        }
      }
    }

    .key-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .key-container {
        display: flex;
        min-width: 24px;
        height: 24px;
        padding: 5px 8px;
        box-sizing: border-box;
        justify-content: center;
        align-items: center;
        border-radius: var(--radius-6, 6px);
        border: 1px solid var(--system-stroke-button, #323B48);
        background: var(--system-content-fifth, #303741);
      }

      .sign {
        color: var(--system-content-thirdary, #6B7A8F);
        font-size: 14px;
        margin: 0 6px;
      }
    }
  }
}