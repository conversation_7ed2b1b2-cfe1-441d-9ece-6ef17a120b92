import toast from '@/components/Toast';
import { useI18n } from '@/locales/client';
import { App, Form, Input, InputRef } from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import { useEffect, useRef } from 'react';
import runes from 'runes2';

export enum TextBoxStatus {
  Default = 'default',
  Inputting = 'inputting',
  Submitting = 'submitting'
}

export type TextBoxProps = {
  text: string;
  onTextChange(value: string): void;
  status: TextBoxStatus;
  onStatusChange(status: TextBoxStatus): void;
  className?: string;
  disabled?: boolean;
};

export function TextBox({
  text,
  onTextChange,
  status,
  onStatusChange,
  className,
  disabled
}: TextBoxProps) {
  const inputRef = useRef<InputRef>(null);

  useEffect(() => {
    if (status === TextBoxStatus.Inputting) {
      inputRef.current?.focus();
      inputRef.current?.select();
    }
  }, [status]);

  const renderBox = () => {
    return (
      <div
        className={classNames("text-box-content", disabled && 'disabled')}
        onClick={() => {
          if (disabled) {
            return;
          }
          onStatusChange(TextBoxStatus.Inputting);
        }}
      >
        {text}
      </div>
    );
  };

  const renderInput = () => {
    return (
      <Form
        initialValues={{
          text
        }}
        onFinish={({ text }) => {
          onTextChange(text);
          onStatusChange(TextBoxStatus.Default);
        }}
      >
        <Form.Item noStyle name="text">
          <ProjectNameInput className="text-box-input" inputRef={inputRef} />
        </Form.Item>
      </Form>
    );
  };

  return (
    <div className={className}>
      {status === TextBoxStatus.Default ? renderBox() : renderInput()}
    </div>
  );
}

type ProjectNameInputProps = {
  onChange?(value: string): void;
  value?: string;
  className?: string;
  inputRef?: React.Ref<InputRef>;
};

function ProjectNameInput({
  inputRef,
  className,
  onChange,
  value
}: ProjectNameInputProps) {
  const t = useI18n();

  const debouncedErrorMessage = useRef(
    _.debounce(
      () => {
        toast.error(t('Project name cannot exceed 50 characters'))
      },
      1000,
      {
        leading: true,
        trailing: false
      }
    )
  );
  const errorMessage = () => debouncedErrorMessage.current();

  const isComposing = useRef(false);
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (isComposing.current || !value) {
      onChange?.(value);
      return;
    }

    // const reg = /^[\u4e00-\u9fa5a-zA-Z0-9_()\[\]{}（）【】｛｝「」]+$/;

    // const words = runes(value);
    // const validWords = words.filter((word) => reg.test(word));
    // if (words.length !== validWords.length) {
    //   errorMessage();
    //   onChange?.(validWords.join(''));
    //   return;
    // }

    onChange?.(value);
  };

  const handleCompositionStart = () => {
    isComposing.current = true;
  };
  const handleCompositionEnd = (
    event: React.CompositionEvent<HTMLInputElement>
  ) => {
    isComposing.current = false;
    handleChange(event as unknown as React.ChangeEvent<HTMLInputElement>);
  };

  const form = Form.useFormInstance();

  return (
    <Input
      className={className}
      ref={inputRef}
      // 阻止事件冒泡到document 导致触发快捷键
      onKeyDown={(e) => e.nativeEvent.stopImmediatePropagation()}
      count={{
        max: 50,
        strategy: (txt: string) => runes(txt).length,
        exceedFormatter: (txt: string, { max }: { max: number }) => {
          errorMessage();
          return runes(txt).slice(0, max).join('');
        }
      }}
      value={value}
      onBlur={() => form.submit()}
      onChange={handleChange}
      onCompositionStart={handleCompositionStart}
      onCompositionEnd={handleCompositionEnd}
    />
  );
}
