@import '@/styles/variable.scss';
@import '../header-common.scss';
$project-name-width: 94px;
$project-name-height: 24px;

.projects {
  margin-left: 14px;
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  border-radius: var(--radius-4, 4px);

  :global {
    .project-name {
      .text-box-content,
      .text-box-input {
        box-sizing: border-box;
        font-size: 14px;
        // font-family: system-ui;
        line-height: $project-name-height;
        height: $project-name-height;
        width: $project-name-width;
        margin: 0;
        padding: 0 5px 0 4px;
        background: transparent;
        cursor: text;

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .text-box-content {
        color: $system-content-secondary;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin: 0;
      }

      .text-box-input {
        border-radius: 4px;
        border: none;
        outline: none;
        box-shadow: none;
        color: $system-content-secondary;
        &::selection {
          background: $system-content-brand-primary;
          color: $system-content-on-primary;
        }
      }
    }

    .project-change {
      margin-left: 5px;
      @include dropdown-trigger-mixin;
    }
  }
}

.projects-popover {
  @include popover-mixin;

  :global {
    .ant-popover-content {
      .ant-popover-inner {
        .projects-divider {
          margin: 6px 0;
          border-color: $system-stroke-input;
          // 延长分割线 8为左右两边padding的宽度 220为popover的宽度
          transform: scaleX(calc(1 + 8 / 210));
        }
      }
    }
  }
}

.project-item {
  @include dropdown-item-mixin;
  :global {
    .dropdown-item {
      &-name {
        width: 128px;
      }
    }
  }
}
