"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import { useMemo, useState } from "react";
import { TextBox, TextBoxStatus } from "./TextBox";
import { ProjectItem } from "@/app/[locale]/editor/_store/projects";
import { useRootStore } from "@/app/[locale]/editor/_store";
import styles from "./index.module.scss";
import {
  CheckBlack,
  ChevronDownBlack,
  GridGroupBold,
  PlusBold,
} from "@meitu/candy-icons";
import classNames from "classnames";
import { toAtlasImageView2URL } from "@meitu/util";
import { useCreateProjectInEditor } from "@/app/[locale]/editor/_hooks/useCreateProjectInEditor";
import Link from "next/link";
import { observer } from "mobx-react-lite";
import { useRouter } from "next/navigation";
import { useGuideCreateProject } from "../../../_context/GuideCreateProjectContext";
import { useI18n } from "@/locales/client";
import toast from "@/components/Toast";
import { useStore } from "@/contexts/StoreContext";
/**
 * 仅展示3条数据
 */
const projectsCountsLimit = 3;

type ProjectsProps = {
  disabled?: boolean;
};

function Projects({ disabled }: ProjectsProps) {
  const { projectsStore, headerActionStore } = useRootStore();
  const { globalProjectStore } = useStore();
  const projects = projectsStore.projectsList;
  const currentProjectId = projectsStore.activeProjectId;

  const project = useMemo(() => {
    return projects.find((p) => p.id === currentProjectId);
  }, [projects, currentProjectId]);

  const [textBoxStatus, setTextBoxStatus] = useState(TextBoxStatus.Default);

  const [isOpen, setIsOpen] = useState(false);
  const [openLoading, setOpenLoading] = useState(false);

  const { createGuideProject, isInGuidePage } = useGuideCreateProject();
  const { createProject } = useCreateProjectInEditor();
  const t = useI18n();

  const router = useRouter();

  const handleProjectNameChange = async (newName: string) => {
    setTextBoxStatus(TextBoxStatus.Submitting);

    if (!newName) {
      toast.error(t("Project name is required"));
      return;
    }

    try {
      if (!project) {
        // return null;
        if (!isInGuidePage) {
          return;
        }
        return await createGuideProject({ projectName: newName || t("Unnamed") });
      }
      await projectsStore.renameProject(currentProjectId, newName);
    } catch (e) {
      if (process.env.NODE_ENV === "development") {
        console.error(e);
      }

      // if(e.)
    } finally {
      setTextBoxStatus(TextBoxStatus.Default);
      globalProjectStore.setProjectSaveSuccess(true);
    }
  };

  const renderProjects = () => {
    return projects.slice(0, projectsCountsLimit).map((p) => {
      let previewUrl = "";
      try {
        previewUrl = toAtlasImageView2URL(p.previewImage, {
          mode: 2,
          width: 24 * window.devicePixelRatio,
          height: 24 * window.devicePixelRatio,
        });
      } catch (e) {
        if (process.env.NODE_ENV === "development") {
          // console.log(
          //   `获取预览图失败: projectId[${p.id}], image[${p.previewImage}]`
          // );
        }
      }

      const checked = p.id === currentProjectId;

      return (
        <div
          key={p.id}
          className={styles["project-item"]}
          onClick={() => {
            projectsStore.changeProject(p.id);
            router.replace(`./${p.id}`);
            setIsOpen(false);
            headerActionStore.activateActionAndResetCursor();
          }}
        >
          {previewUrl ? (
            <Image
              rootClassName="dropdown-item-image"
              preview={false}
              src={previewUrl}
              alt=""
            />
          ) : (
            <div className="dropdown-item-image"></div>
          )}
          <Tooltip title={p.name}>
            <div className="dropdown-item-name">{p.name}</div>
          </Tooltip>
          {checked && <CheckBlack className="dropdown-item-checked-icon" />}
        </div>
      );
    });
  };

  const renderToAllProjectsLink = () => {
    return (
      <Link
        className={classNames(styles["project-item"])}
        // to={generateRouteTo(AppModule.Personal, { tab: 'posters' })}
        href="/workspace/projects"
        onClick={() => {
          setIsOpen(false);
        }}
        target="_blank"
      >
        <span className="dropdown-item-icon">
          <GridGroupBold />
        </span>
        <span className="dropdown-item-name">{t("All canvas")}</span>
      </Link>
    );
  };

  const renderCreateProjectBtn = () => {
    return (
      <div
        className={styles["project-item"]}
        onClick={async () => {
          try {
            if (isInGuidePage) {
              await createGuideProject();
            } else {
              await createProject();
            }
          } catch (e) {
            if (process.env.NODE_ENV === "development") {
              console.error(e);
            }
          } finally {
            setIsOpen(false);
          }
        }}
      >
        <span className="dropdown-item-icon">
          <PlusBold />
        </span>
        <span className="dropdown-item-name">{t("header.New")}</span>
      </div>
    );
  };

  const renderPopover = () => {
    return (
      <div>
        {renderProjects()}
        {renderToAllProjectsLink()}
        <Divider className="projects-divider" />
        {renderCreateProjectBtn()}
      </div>
    );
  };

  const handleOpen = async (open: boolean) => {
    if (!open) {
      setIsOpen(false);
      return;
    }

    if (openLoading) {
      return;
    }

    try {
      setOpenLoading(true);

      if (!projectsStore.activeProjectId && isInGuidePage) {
        await createGuideProject();
      }

      await projectsStore.refreshProjects();
      setIsOpen(true);
    } catch (e) {
    } finally {
      setOpenLoading(false);
    }
  };
  return (
    <div className={styles.projects}>
      <TextBox
        className="project-name"
        status={textBoxStatus}
        onStatusChange={setTextBoxStatus}
        text={project?.name || t("Unnamed")}
        onTextChange={handleProjectNameChange}
        disabled={disabled}
      />
      <Popover
        classNames={{
          root: styles["projects-popover"],
        }}
        title={renderPopover()}
        trigger="click"
        open={isOpen}
        onOpenChange={handleOpen}
        mouseLeaveDelay={0.2}
        arrow={false}
        align={{
          offset: [0, -20],
        }}
        destroyTooltipOnHide
      >
        <button
          disabled={disabled}
          className={classNames(
            "project-change",
            isOpen && "open",
            disabled && "disabled"
          )}
        >
          <ChevronDownBlack />
        </button>
      </Popover>
    </div>
  );
}

export default observer(Projects);
