import { Button, Form, FormProps, Input, Modal, Select, Space, Switch } from 'antd';
import { observer } from 'mobx-react';
import { Fragment, useEffect, useState } from 'react';
import { getCategoryList, UpdateOrCreateTemplate } from '@/api/aiPoster/template';
import { CategoryItem, UpdateOrCreateTemplateRequest } from '@/api/types/aiPoster/template';
import { useRootStore } from '../../../_store';
import toast from '@/components/Toast';
import { useStore } from '@/contexts/StoreContext';
interface FieldType {
    name: string;
    categoryId: string;
    isCreate: boolean;
}

function Template() {
    const rootStore = useRootStore();
    const { userStore, posterConfigStore } = useStore();
    const { selectionStore } = rootStore;

    const image = selectionStore.singleImage?.image;
    const imageOptions = selectionStore.singleImage?.options;

    const isCreate = !imageOptions?._custom_data_history_.templateId;

    const isWhiteUser = posterConfigStore.config?.generateTemplatePermission;
    const [form] = Form.useForm<FieldType>();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [categoryList, setCategoryList] = useState<CategoryItem[]>([]);

    const getCategoryListHandler = async () => {
        const { list } = await getCategoryList();
        setCategoryList(list);
    };
    useEffect(() => {
        getCategoryListHandler();
    }, []);

    useEffect(() => {
        // console.log('=================', imageOptions?._custom_data_history_.templateCategoryId)
            form.setFieldsValue({
                name: imageOptions?._custom_data_history_.templateName,
                categoryId: imageOptions?._custom_data_history_.templateCategoryId,
            });
    }, [image])
    if (!isWhiteUser) return null;
    const onFinish: FormProps<FieldType>['onFinish'] = async (values) => {
        const fixedLoadingKey = "updateTemplateByWhitelist";
        const picRatio = (imageOptions?._custom_data_history_?.params?.picRatio ?? '')
        const ratio = picRatio === 'custom' ? 'free' : picRatio
        try {
            const params: UpdateOrCreateTemplateRequest = {
                templateId: imageOptions?._custom_data_history_.templateId ? imageOptions?._custom_data_history_.templateId : 0,
                categoryId: values.categoryId,
                name: values.name,
                pic: imageOptions?.src ?? '',
                ratio,
                prompt: imageOptions?._custom_data_history_?.params?.prompt ?? '',
                msgId: imageOptions?._custom_data_history_?.msgId ?? '',
            }
            toast.show({
                title: `${isCreate ? "创建" : "更新"}模版中...`,
                duration: 0,
                type: "info",
                key: fixedLoadingKey,
              });
            const res = await UpdateOrCreateTemplate(params);
            if (res.result) {
                toast.destroy(fixedLoadingKey);
                setIsModalOpen(false);
            } else {
                toast.destroy(fixedLoadingKey);
                toast.show({
                    title: "更新失败",
                    type: "error",
                });
            }
        } catch (error) {
            toast.destroy(fixedLoadingKey);
            
        }
    };

    const handleCreate = async (isOverwrite: boolean) => {
        const categoryId = form.getFieldValue('categoryId')
        const name = form.getFieldValue('name')
        const picRatio = (imageOptions?._custom_data_history_?.params?.picRatio ?? '')
        const ratio = picRatio === 'custom' ? 'free' : picRatio
        const fixedLoadingKey = "updateTemplateByWhitelist";
        try {
            const params: UpdateOrCreateTemplateRequest = {
                templateId: isOverwrite ? imageOptions?._custom_data_history_.templateId ? imageOptions?._custom_data_history_.templateId : 0 : 0,
                categoryId: categoryId,
                name: name,
                pic: imageOptions?.src ?? '',
                ratio,
                prompt: imageOptions?._custom_data_history_?.params?.prompt ?? '',
                msgId: imageOptions?._custom_data_history_?.msgId ?? '',
            }
            toast.show({
                title: `${isCreate ? "创建" : "覆盖"}模版中...`,
                duration: 0,
                type: "info",
                key: fixedLoadingKey,
              });
            const res = await UpdateOrCreateTemplate(params);
            if (res.result) {
                toast.destroy(fixedLoadingKey);
                setIsModalOpen(false);
            } else {
                toast.destroy(fixedLoadingKey);
                toast.show({
                    title: "更新失败",
                    type: "error",
                });
            }
        } catch (error) {
            toast.destroy(fixedLoadingKey);
            
        }
    }



    const handleCancel = () => {
        setIsModalOpen(false);
    };


    return (
        <Fragment>
            
            <button
                onClick={() => setIsModalOpen(true)}
                className="rounded-full mr-2 bg-gray-950 px-2.5 py-0.5 text-sm/6 font-medium text-white focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-950">
                {isCreate ? "创建模版" : "更新模版"}
            </button>
            <Modal title={isCreate ? "创建模版" : "更新模版"} open={isModalOpen} footer={false}>
                <Form
                    form={form}
                    onFinish={onFinish}
                    initialValues={{
                        name: "",
                        categoryId: "",
                    }}
                    onKeyDown={(e) => {
                        e.nativeEvent.stopImmediatePropagation();
                    }}
                >
                    <Form.Item<FieldType>
                        label="模版名称"
                        name="name"
                        rules={[{ required: true, message: '请输入模版名称' }]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item name="categoryId" label="模版分类" rules={[{ required: true }]}>
                        <Select
                            placeholder="请选择模版分类"
                            allowClear
                        >
                            {categoryList.map((item) => (
                                <Select.Option key={item.categoryId} value={item.categoryId}>{item.categoryName}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item>
                        <Space>
                            <button
                             type="button"
                             className="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                             onClick={() => {
                                handleCreate(false)
                             }}
                             >
                                {"创建"}
                            </button>
                            {
                                !isCreate && <button
                                type="button"
                                className="focus:outline-none text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                                   onClick={() => {
                                       handleCreate(true)
                                   }}
                                >
                                   {"覆盖"}
                               </button>
                            }
                            <button
                             type="button"
                             className="py-2.5 px-5 me-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                             onClick={handleCancel}
                             >
                                取消
                            </button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </Fragment>
    );
}

export default observer(Template);