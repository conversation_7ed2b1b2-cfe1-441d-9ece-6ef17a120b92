@import '@/styles/variable.scss';

$icon-size: 20px;
$button-size: 32px;
$button-border-radius: 8px;
$button-icon-color: #A3AEBF;

@mixin header-button-mixin {
  width: $button-size;
  height: $button-size;
  border-radius: $button-border-radius;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;

  svg {
    width: $icon-size;
    height: $icon-size;
    color: $system-content-secondary;
  }

  &:hover:not(:global(.disabled)):not(:global(.selected)) {
    background: $system-background-thirdary;
    box-shadow: 0px 0px 0px 1px var(--system-stroke-button, #323B48) inset;

    svg {
      color: #fff;
    }
  }

  &:focus-visible, &:focus, &:focus-within {
    outline: none;
  }

  &:global(.selected) {
    background: $system-content-brand-primary;

    svg {
      color: $system-content-on-primary;
    }
  }

  &:global(.disabled) {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin dropdown-trigger-mixin {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  cursor: pointer;
  transform: rotate(0);
  transition: transform 0.3s;

  svg {
    width: 8px;
    height: 8px;
    color: $system-content-secondary;
  }

  &:global(.open) {
    transform: rotate(180deg);
  }

  &:global(.disabled) {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 使用Dropdown实现下拉菜单时 浮层的混入样式
@mixin dropdown-overlay-mixin {
  box-sizing: content-box;
  width: 200px;
  padding: 4px;
  border-radius: 12px;
  border: 1px solid $system-stroke-input;
  background: $background-editor-popup-default;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);
  overflow: hidden;
}

$dropdown-item-image-size: 24px;
$dropdown-item-image-name-gap: 8px;

@mixin dropdown-item-mixin {
  box-sizing: content-box;
  width: 184px;
  height: 40px;
  padding: 0 8px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  cursor: pointer;
  background: transparent;
  border: none;
  color: $system-content-secondary;
  user-select: none;
  position: relative;
  font-weight: 400;

  &:hover {
    font-weight: 500;
    color: $system-content-primary;
    background: $background-editor-popup-hover;

    :global {
      .dropdown-item-icon svg,
      .dropdown-item-name {
        color: #fff;
        font-weight: 500;
      }
    }
  }

  :global {

    .dropdown-item-icon,
    .dropdown-item-image {
      width: $dropdown-item-image-size;
      height: $dropdown-item-image-size;
      border-radius: 4px;
      background-color: #eee;

      &>img {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover;
      }
    }

    .dropdown-item-icon {
      display: inline-flex;
      flex: 0 0 auto;
      justify-content: center;
      align-items: center;
      background-color: transparent;

      svg {
        width: 18px;
        height: 18px;
        color: $system-content-secondary;
      }
    }

    .dropdown-item-name {
      margin-left: $dropdown-item-image-name-gap;
      width: calc(100% - $dropdown-item-image-name-gap - $dropdown-item-image-size);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: $system-content-secondary;
    }

    .dropdown-item-checked-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 11px;

      svg {
        width: 16px;
        height: 16px;
        color: $system-content-brand-primary;
      }
    }
  }
}

// 使用Popover实现下拉菜单时 浮层的混入样式
@mixin popover-mixin {
  :global {
    .ant-popover-content {
      .ant-popover-inner {
        padding: 4px;
        border-radius: 12px;
        border: 1px solid $system-stroke-input;
        background: $background-editor-popup-default;
        box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);

        .ant-popover-title {
          margin: 0;
        }
      }
    }
  }
}