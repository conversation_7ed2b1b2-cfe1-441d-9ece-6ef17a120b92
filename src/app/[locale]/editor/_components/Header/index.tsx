"use client";

import CommonHeader, { HeaderElementType } from "@/components/Header";
import styles from "./index.module.scss";
import Download from "./Download";
import { observer } from "mobx-react-lite";
import ImageEditActions from "./ImageEditActions";
import Cursor from "./Cursor";
import Frame from "./Frame";
import AddText from "./AddText";
import Projects from "./Projects";
import { useRootStore } from "../../_store";
import Template from "./Template";
import FixTemplate from "./fixTemplate";
import UploadImage from "./UploadImage";
import GenerateImage from "./GenerateImage";

type HeaderProps = {
  disabled?: boolean;
};

function Header({ disabled: propsDisabled }: HeaderProps) {
  const { editorStatusStore } = useRootStore();

  const disabled = propsDisabled || editorStatusStore.globalDisabled;

  return (
    <CommonHeader
      className={styles.layout}
      leftSlot={[
        { type: HeaderElementType.Logo },
        { type: HeaderElementType.BackButton },
        {
          type: HeaderElementType.Custom,
          key: "projects",
          element: <Projects disabled={disabled} />,
        },
        {
          type: HeaderElementType.Custom,
          key: "cursor",
          element: (
            // 创建页禁用鼠标切换
            <Cursor disabled={disabled || editorStatusStore.showCreate} />
          ),
        },
        {
          type: HeaderElementType.Custom,
          key: "frame",
          element: <Frame disabled={disabled} />,
        },
        {
          type: HeaderElementType.Custom,
          key: "generate-image",
          element: <GenerateImage disabled={disabled} />,
        },
        {
          type: HeaderElementType.Custom,
          key: "text",
          element: <AddText disabled={disabled} />,
        },
        {
          type: HeaderElementType.Custom,
          key: "upload-image",
          element: <UploadImage disabled={disabled} />,
        },
      ]}
      centerSlot={[
        {
          type: HeaderElementType.Custom,
          key: "editor",
          element: <ImageEditActions />,
        },
      ]}
      rightSlot={[
        {
          type: HeaderElementType.Custom,
          key: "fixTemplate",
          element: <FixTemplate />,
        },
        {
          type: HeaderElementType.Custom,
          key: "template",
          element: <Template />,
        },
        {
          type: HeaderElementType.Custom,
          key: "download",
          element: <Download />,
        },
        { type: HeaderElementType.MtBean },
        { type: HeaderElementType.AccountAvatar },
      ]}
    />
  );
}

export default observer(Header);
