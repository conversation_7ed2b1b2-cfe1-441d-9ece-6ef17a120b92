import { Form, message, Button } from "antd";
import { observer } from "mobx-react";
import styles from "./index.module.scss";
import commonStyles from "../common.module.scss";
import { RatioSection } from "../components/RatioSection";
import {
  ContainerElementOptions,
  ElementName,
  ElementOptions,
  FabricObject,
  FrameElementOptions,
  getElementOptions,
  Group,
  LoadingType,
  TextElementOptions,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { PosterRatio } from "@/api/types/poster";
import { useParamsForm } from "@/app/[locale]/editor/_context/FormContext";
import { useI18n } from "@/locales/client";
import QuantitySection from "../components/QuantitySection";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { useEffect, useMemo, useRef, useState } from "react";
import { useSaveCanvas } from "@/app/[locale]/editor/_hooks/useSaveProjectHistory";
import { dimensionList } from "@/constants/ratio";
import { AIPoster, FormType } from "@/api/types/aiPoster/task";
import { insertImageByOptions } from "@/app/[locale]/editor/_utils/insertImage";
import { tempBaseImage } from "..";
import { submitTask } from "@/api/aiPoster/task";
import { MtccFuncCode } from "@/types";
import { handleSubmitError } from "@/app/[locale]/editor/_utils/submit";
import { dispatch } from "@/app/[locale]/editor/_utils/TaskDispatcher";
import { PromptSection } from "../components/PromptSection";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { disabledKeyboardScale } from "../../../_utils/disabledScreenScale";
import MeiDouButton from "@/components/MeiDou";
import {
  FrameTransformX,
  MaxFrameLimit,
  MinFrameLimit,
} from "../../../_constant/params";
import { throttle } from "lodash";
import { toSnakeCase } from "@meitu/util";
import { getImageParams } from "@/app/[locale]/editor/_utils/frame-generate-params/image-to-poster";
import toast from "@/components/Toast";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { FunctionCode } from "@/api/types/meidou";
import { Track } from "../../../_constant/track";
import { trackEvent } from "@meitu/subscribe-intl";

type FrameParamsType = {
  activeObject: FabricObject;
  objectsOptions: ElementOptions[];
};

const FrameParams = ({ activeObject, objectsOptions }: FrameParamsType) => {
  const { form } = useParamsForm();
  const t = useI18n();
  const rootStore = useRootStore();
  const { renderStore, projectsStore, editorStatusStore } = useRootStore();
  const [submitLoading, setSubmitLoading] = useState(false);
  const render = renderStore.render;
  const historyPlugins = renderStore.historyPlugins;
  const { submitSaveElements } = useSaveCanvas(rootStore);
  const { open: openSubscribeModal } = useSubscribeModal();
  const { userStore } = useStore();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const [hasImage, setHasImage] = useState(false);

  // 回填参数
  useEffect(() => {
    const params = activeObject?._custom_data_history_?.params;
    const width = Math.round(activeObject?.getScaledWidth());
    const height = Math.round(activeObject?.getScaledHeight());
    const temp = dimensionList.find(
      (item) => String(item.size) === String([width, height])
    );
    const picRatio = temp ? temp.value : PosterRatio.FREE;

    if (params) {
      form.setFieldsValue({
        ...params,
        width,
        height,
        picRatio,
      });
    } else {
      form.setFieldsValue({
        prompt: "",
        picRatio,
        width,
        height,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeObject._id_, form]);

  const hasShownMaxError = useRef(false);
  const hasShownMinError = useRef(false);

  // 根据画布里的尺寸回填参数栏
  useEffect(() => {
    const handleSizeChange = throttle(() => {
      if (!activeObject) return;

      const width = Math.round(activeObject?.getScaledWidth());
      const height = Math.round(activeObject?.getScaledHeight());

      if (width >= MaxFrameLimit || height >= MaxFrameLimit) {
        if (!hasShownMaxError.current) {
          toast.error(
            t("Maximum dimension must be under px. Resize image." as any, {
              maxFrame: MaxFrameLimit,
            })
          );
          hasShownMaxError.current = true;
        }
      } else {
        hasShownMaxError.current = false;
      }

      if (width <= MinFrameLimit || height <= MinFrameLimit) {
        if (!hasShownMinError.current) {
          toast.error(
            t("Minimum dimension must exceed px. Adjust and retry." as any, {
              minFrame: MinFrameLimit,
            })
          );
          hasShownMinError.current = true;
        }
      } else {
        hasShownMinError.current = false;
      }

      const temp = dimensionList.find(
        (item) => String(item.size) === String([width, height])
      );
      const picRatio = temp ? temp.value : PosterRatio.FREE;

      if (
        width !== form.getFieldValue("width") ||
        height !== form.getFieldValue("height")
      ) {
        form.setFieldsValue({
          picRatio,
          width,
          height,
        });
      }
    }, 300);
    const handlerMap = {
      scaling: handleSizeChange,
      resizing: handleSizeChange,
    };

    activeObject?.on(handlerMap);

    return () => {
      activeObject?.off(handlerMap);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeObject?._id_, hasShownMaxError, hasShownMinError]);

  useEffect(() => {
    const options =
      render && activeObject && getElementOptions.call(render, activeObject);

    const hasImageElement = (root: ElementOptions): boolean => {
      // 判断当前节点是否为图片
      if (root._name_ === ElementName.IMAGE) {
        return true;
      }

      // 如果有子节点，递归检查子节点
      if ((root as ContainerElementOptions).children?.length) {
        return (root as ContainerElementOptions).children.some((child) =>
          hasImageElement(child)
        );
      }

      return false;
    };

    setHasImage(options ? hasImageElement(options) : false);
  }, [activeObject?._id_]);

  // 获取文字元素的信息
  const getFrameParams = (eleOpt: FrameElementOptions) => {
    const getChildCorners = (
      frame: FrameElementOptions,
      child: TextElementOptions
    ) => {
      const x1 =
        child.left -
        Number(child?.width) / 2 -
        (frame.left - Number(frame?.width) / 2);
      const y1 =
        child.top -
        Number(child.height) / 2 -
        (frame.top - Number(frame.height) / 2);
      const x2 = x1 + (child?.width || 0);
      const y2 = y1;
      const x3 = x1;
      const y3 = y1 + (child?.height || 0);
      const x4 = x2;
      const y4 = y3;

      return { x1, y1, x2, y2, x3, y3, x4, y4 };
    };

    const textEle = eleOpt?.children?.filter(
      (item) => item._name_ === ElementName.TEXT
    ) as TextElementOptions[];

    return textEle?.map((child) => {
      const { x1, y1, x2, y2, x3, y3, x4, y4 } = getChildCorners(
        eleOpt,
        child as TextElementOptions
      );
      return {
        text: child.text,
        font: child.fontFamily,
        position: [
          [x1, y1],
          [x2, y2],
          [x3, y3],
          [x4, y4],
        ].map((subArr) => subArr.map((num) => Math.round(num))),
      };
    });
  };

  // 画布loading
  const startLoading = async (
    eleOpt: FrameElementOptions,
    values: FormType.FrameParamsForm
  ) => {
    if (!render) return;
    // 插入占位图片
    const insertedImageEle = await insertImageByOptions(
      {
        src: tempBaseImage,
        width: eleOpt.width * Math.abs(eleOpt.scaleX),
        height: eleOpt.height * Math.abs(eleOpt.scaleY),
        left: eleOpt.left,
        top: eleOpt.top,
      },
      render
    );

    // 设置画板生图参数
    insertedImageEle?.set({
      _custom_data_history_: {
        params: toQueryParams(values),
        imageStatus: AIPoster.ImageStatus.AuditFailed,
      },
    });

    // 设置画板生图参数
    activeObject?.set({
      _custom_data_history_: {
        ...activeObject?._custom_data_history_,
        params: toQueryParams(values),
      },
    });

    if (!historyPlugins) return;

    const addOperation = historyPlugins.baseAction.getAddOperation({
      objects: [insertedImageEle],
      selectable: false,
    });

    const loadingOperation = historyPlugins.baseAction.getLoadOperation([
      insertedImageEle as FabricObject,
    ]);

    const beforeData =
      historyPlugins.baseAction.getElementData({
        target: activeObject,
      }) ?? [];

    // 原本的画板右移
    activeObject.set(
      "left",
      eleOpt.left + eleOpt.width * eleOpt.scaleX + FrameTransformX
    );

    const afterData =
      historyPlugins.baseAction.getElementData({
        target: activeObject,
      }) ?? [];

    const moveOperation = historyPlugins.baseAction.getModifiedOperation({
      beforeData,
      afterData,
    });

    addOperation &&
      moveOperation &&
      historyPlugins.submit([addOperation, moveOperation]);
    loadingOperation && historyPlugins.submit(loadingOperation);
    submitSaveElements([activeObject]);

    // 设置图片loading
    insertedImageEle?._id_ &&
      render?.Actions.setLoading({
        type: LoadingType.CIRCLE_DANCE,
        text: "Generating...",
        id: insertedImageEle._id_,
      });

    activeObject.setCoords();
    render?._FC.requestRenderAll();

    return insertedImageEle;
  };

  // 画布结束loading
  const loadingEnd = (ele: Group | undefined) => {
    render?.Actions.setLoaded(ele?._id_ || "");
  };

  // 立即生成
  const onFinish = async (values: FormType.FrameParamsForm) => {
    if (!values.prompt) {
      toast.error(t("text-edit.no-prompt-tips"));
      return;
    }
    if (!render || !activeObject) return;

    let msgId = "";
    let insertedImageEle = {} as Group | undefined;
    const eleOpt = getElementOptions.call(
      render,
      activeObject
    ) as FrameElementOptions;
    const textInfo = getFrameParams(eleOpt);
    const projectId = projectsStore.activeProjectId;

    if (!projectId || !historyPlugins) {
      return;
    }

    setSubmitLoading(true);

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== insertedImageEle
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    try {
      // 1. 图片参数
      const images = await getImageParams(activeObject, render);
      abortController.signal.throwIfAborted();

      // 2. 提交任务
      const params = {
        ...toQueryParams(values),
        textGeneration: textInfo,
        tgLayoutImageUrl: images[0],
        templateId: eleOpt._custom_data_history_?.template?.id,
        templateCategoryId: eleOpt._custom_data_history_?.template?.categoryId,
        tg_editor_params: JSON.stringify({
          data: eleOpt,
          v2: true, // 是否使用新版编辑器
        }),
      };
      if (images.length > 2) {
        Object.assign(params, {
          initImages: [images[1], images[2]],
          inputImageIndex: 0,
          inpaintingMaskImageIndex: 1,
        });
      }
      const res = await submitTask({
        projectId,
        layerId: activeObject._id_,
        taskCategory:
          images.length > 2
            ? AIPoster.TaskCategory.PosterImage
            : AIPoster.TaskCategory.Poster,
        params,
        // functionName: MtccFuncCode.FuncCodePosterFrame,
      });
      editorStatusStore.addActiveTask(res.id);
      // 更新美豆
      userStore.refreshMtBeanBalance();
      abortController.signal.throwIfAborted();
      setSubmitLoading(false);

      // 图片占位元素
      insertedImageEle = await startLoading(eleOpt, values);
      msgId = res.id;

      //3. 开始轮询
      await dispatch({
        msgId,
        rootStore,
        shape: insertedImageEle,
        abortController,
        t,
        expandCustomData: {
          templateId: eleOpt._custom_data_history_?.template?.id ?? 0,
          templateCategoryId:
            eleOpt._custom_data_history_?.template?.categoryId ?? 0,
          templateName: eleOpt._custom_data_history_?.template?.name ?? "",
        },
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === "development") {
        console.warn("frame生图出错", e);
      }

      if (abortController.signal.aborted) {
        return;
      }

      if (uploadErrorhandler(e, t)) {
        return;
      }

      if (
        handleSubmitError(e, rootStore, {
          subscribeModal: {
            open: openSubscribeModal,
          },
          userStore: userStore,
          openMeiDouRecordsPopup,
        })
      ) {
        return;
      }
      return;
    } finally {
      loadingEnd(insertedImageEle);
      setSubmitLoading(false);
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }
  };

  // 将表单数据转换为请求参数
  const toQueryParams = (values: FormType.FrameParamsForm) => {
    return {
      ...values,
      width: Math.round(values.width),
      height: Math.round(values.height),
    };
  };

  const formInitValues = {
    prompt: "",
    batchSize: 4,
    picRatio: PosterRatio.FREE,
    width: activeObject?.getScaledWidth(),
    height: activeObject?.getScaledHeight(),
  };

  const batchSize = Form.useWatch("batchSize", form);
  const functionBody = useMemo(() => {
    return JSON.stringify(toSnakeCase(form.getFieldsValue()));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchSize]);

  return (
    <>
      <h3 className={styles.title}>{t("frame-params.title")}</h3>
      <Form
        className={styles.frameParams}
        form={form}
        onFinish={onFinish}
        // onFinish={() => debugImages(activeObject as Group, render!)}
        scrollToFirstError={{
          behavior: "smooth",
          block: "nearest",
        }}
        initialValues={formInitValues}
        // 阻止事件冒泡到document 导致触发快捷键
        onKeyDown={(e) => {
          e.stopPropagation();
          e.nativeEvent.stopImmediatePropagation();
          //如果是回车禁止默认行为
          if (e.code === "Enter") {
            e.preventDefault();
          }
          disabledKeyboardScale(e as unknown as KeyboardEvent);
        }}
      >
        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Prompt")}</h3>
          <PromptSection onPressEnter={onFinish} />
        </div>

        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Size")}</h3>
          <RatioSection
            min={MinFrameLimit}
            max={MaxFrameLimit}
            onSizeChange={(width: number, height: number) => {
              const beforeData =
                renderStore?.historyPlugins?.baseAction.getElementData({
                  target: activeObject,
                }) ?? [];

              const scaleX = activeObject.getScaledWidth() / activeObject.width;
              const scaleY =
                activeObject.getScaledHeight() / activeObject.height;

              // 同步设置画布尺寸
              activeObject.set({
                width: width / scaleX,
                height: height / scaleY,
              });
              activeObject.setCoords();
              render?._FC.requestRenderAll();

              const afterData =
                renderStore?.historyPlugins?.baseAction.getElementData({
                  target: activeObject,
                }) ?? [];
              const operation =
                renderStore?.historyPlugins?.baseAction.getModifiedOperation({
                  beforeData,
                  afterData,
                });
              if (operation) {
                renderStore?.historyPlugins?.submit(operation);
                submitSaveElements([activeObject]);
              }
            }}
          />
        </div>

        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Batch Size")}</h3>
          <QuantitySection />
        </div>

        <MeiDouButton
          functionCode={
            hasImage ? FunctionCode.aiPosterImage : FunctionCode.aiPoster
          }
          functionId={
            hasImage
              ? MtccFuncCode.FuncCodePosterImage2Image
              : MtccFuncCode.FuncCodePosterText2Image
          }
          fetchPriceLoading={submitLoading}
          functionBody={functionBody}
          onClick={({ deficit, price }) => {
            const values = form.getFieldsValue();

            const options =
              render &&
              activeObject &&
              getElementOptions.call(render, activeObject);

            const params = toQueryParams(values);

            let hasUploadImage = false;
            let hasCreateImage = false;
            let hasText = false;

            const traverse = (root: ElementOptions) => {
              if (root._name_ === ElementName.IMAGE) {
                const customData = root._custom_data_history_ ?? {};
                if (!!customData.msgId || !!customData.isFromTemplatePreview) {
                  hasCreateImage = true;
                } else {
                  hasUploadImage = true;
                }
                return;
              }

              if (root._name_ === ElementName.TEXT) {
                hasText = true;
                return;
              }

              if ((root as ContainerElementOptions).children?.length) {
                for (const child of (root as ContainerElementOptions)
                  .children) {
                  traverse(child);
                }
              }
            };

            options && traverse(options);

            const imageSource = [];
            if (hasCreateImage) {
              imageSource.push(Track.IsPictureUpload.Create);
            }
            if (hasUploadImage) {
              imageSource.push(Track.IsPictureUpload.Upload);
            }

            const hasImage = hasCreateImage || hasUploadImage;

            const getBoardInfo = () => {
              if (hasImage && hasText) {
                return Track.BoardInfo.TextAndImage;
              }

              if (hasImage) {
                return Track.BoardInfo.OnlyImage;
              }

              if (hasText) {
                return Track.BoardInfo.OnlyText;
              }

              return Track.BoardInfo.Empty;
            };

            trackEvent("create_btn_click", {
              function: hasImage
                ? Track.FunctionEnum.ImageToPoster
                : Track.FunctionEnum.TextToPoster,
              board_info: getBoardInfo(),
              is_picture_upload: imageSource.join(","),
              // base_model_id: params.baseModelId,
              prompt: params.prompt,
              // style_model_config: JSON.stringify(params.styleModel),
              // controlnet: JSON.stringify(params.controlnetUnits),
              templateId: options?._custom_data_history_?.params?.id || "",
              batch_size: params.batchSize,
              free_batch_size: price?.useFreeNum,
              ratio: params.picRatio,
              width: params.width,
              height: params.height,
              is_vip: userStore.vipLevel,
              // 消耗美豆为0 上报免费
              credit_balance_sufficient:
                price?.amount === 0
                  ? Track.CreditBalanceSufficient.Free
                  : // 消耗不为0 判断是否足够
                  deficit
                  ? Track.CreditBalanceSufficient.NotEnough
                  : Track.CreditBalanceSufficient.Enough,
            });
          }}
          getTemplateId={() => {
            const options =
              render &&
              activeObject &&
              getElementOptions.call(render, activeObject);
            return options?._custom_data_history_?.params?.id;
          }}
        />
      </Form>
    </>
  );
};

export default observer(FrameParams);
