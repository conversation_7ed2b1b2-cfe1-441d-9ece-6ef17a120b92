.GroupParams {
  padding-bottom: 120px !important;

  :global(.ant-collapse-item) {
    border-radius: 0 !important;
    box-shadow: none !important;

    &:not(:last-child) {
      margin-bottom: 0 !important;
      border-bottom: 6px solid #f1f5f9;
    }
  }

  .btnBox {
    width: 100%;
    height: 36px;
    border-radius: var(--radius-8, 8px);
    border: 1px solid rgba(255, 255, 255, 0.50);
    background: var(--system-content-brandPrimary, #53F6B4);
    box-shadow: 0px 0px 14.7px 1px rgba(255, 255, 255, 0.00) inset;
    color: var(--system-content-onPrimary, #181818);
    font-size: 14px;
    font-weight: 700;
    line-height: 36px;
    text-align: center;
    cursor: pointer;

    &:hover {
      background: linear-gradient(0deg, #38D294 44.44%, #01331F 181.94%);
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancelBox {
    margin-top: 12px;
    border-radius: var(--radius-8, 8px);
    border: 1px solid var(--system-stroke-button, #323B48);
    background: var(--system-background-thirdary, #272C33);
    box-shadow: 0px 0px 14.7px 1px rgba(255, 255, 255, 0.00) inset;
    color: var(--system-content-secondary, #A3AEBF);
    font-weight: 500;

    &:hover {
      background: var(--system-background-thirdary, #272C33);
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .alignBox {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .iconBox {
      width: 32px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      cursor: pointer;

      &:hover {
        box-shadow: 0px 0px 0px 1px var(--system-stroke-button, #323B48) inset;
        background: linear-gradient(0deg, var(--system-background-thirdary, #272C33) 0%, var(--system-background-thirdary, #272C33) 100%), #F5F7FA;
      }

      svg {
        font-size: 20px;
      }
    }
  }
}