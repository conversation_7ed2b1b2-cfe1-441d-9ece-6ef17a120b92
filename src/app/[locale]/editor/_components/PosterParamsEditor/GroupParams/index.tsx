import { observer } from "mobx-react";
import styles from "./index.module.scss";
import commonStyles from "../common.module.scss";
import classNames from "classnames";
import {
  AlignBottom,
  AlignHorizontalCenter,
  AlignLeft,
  AlignRight,
  AlignTop,
  AlignVerticalCenter,
} from "@meitu/candy-icons";
import {
  AlignMode,
  ElementName,
  FabricObject,
  Group,
} from "@meitu/whee-infinite-canvas";
import { useRootStore } from "@/app/[locale]/editor/_store";
import useAction from "@/app/[locale]/editor/_hooks/useAction";
import { useI18n } from '@/locales/client';

const alignList = [
  {
    value: AlignMode.LEFT,
    label: <AlignLeft />,
  },
  {
    value: AlignMode.CENTER,
    label: <AlignHorizontalCenter />,
  },
  {
    value: AlignMode.RIGHT,
    label: <AlignRight />,
  },
  {
    value: AlignMode.TOP,
    label: <AlignTop />,
  },
  {
    value: AlignMode.MIDDLE,
    label: <AlignVerticalCenter />,
  },
  {
    value: AlignMode.BOTTOM,
    label: <AlignBottom />,
  },
];

type GroupParamsType = {
  activeObjects: FabricObject[];
};

const GroupParams = ({ activeObjects }: GroupParamsType) => {
  const rootStore = useRootStore();

  const { mergeAction, splitAction, alignAction } = useAction(rootStore);

  const t = useI18n()

  const isContainer =
    activeObjects?.length === 1 &&
    activeObjects[0]?._name_ === ElementName.CONTAINER;
  const isFrameInEls = activeObjects?.some(
    (el) => el._name_ === ElementName.FRAME
  );
  // 非组合的不能拆分
  const spiltDisabled = !isContainer;
  // 含有画板、只有一个元素  不能组合
  const mergeDisabled = activeObjects.length < 2 || isFrameInEls;

  // 组合
  const handleMerge = () => {
    if (mergeDisabled) return;
    mergeAction(activeObjects);
  };
  // 取消组合
  const handleSplit = () => {
    if (!activeObjects || spiltDisabled) return;
    splitAction(activeObjects);
  };
  // 设置对齐
  const handleAlign = (mode: AlignMode) => {
    alignAction(mode);
  };

  return (
    <div className={styles.GroupParams}>
      <div className={commonStyles.formItemBox}>
        <h3 className={commonStyles.formItemTitle}>{t("Combination")}</h3>
        <div
          className={classNames(
            styles.btnBox,
            mergeDisabled && styles.disabled
          )}
          onClick={handleMerge}
        >
          {t("Combination")}
        </div>
        <div
          className={classNames(
            styles.btnBox,
            styles.cancelBox,
            spiltDisabled && styles.disabled
          )}
          onClick={handleSplit}
        >
          {t("Cancel combination")}
        </div>
      </div>

      <div className={commonStyles.formItemBox}>
        <h3 className={commonStyles.formItemTitle}>{t("Align")}</h3>
        <div className={styles.alignBox}>
          {alignList.map((item) => (
            <div
              className={styles.iconBox}
              key={item.value}
              onClick={() => {
                handleAlign(item.value);
              }}
            >
              {item.label}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default observer(GroupParams);
