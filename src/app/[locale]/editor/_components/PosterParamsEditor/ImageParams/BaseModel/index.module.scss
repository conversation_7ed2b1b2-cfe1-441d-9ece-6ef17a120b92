.modelSelector {

  &,
  * {
    outline: none !important;
    box-shadow: none !important;
  }

  :global {
    .ant-select {
      width: 100%;
      height: 32px;
    }

    .ant-select-dropdown {
      border: 1px solid var(--system-stroke-input, #22272E);
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30) !important;

      .ant-select-item{
        border-radius: 10px;
      }

      .custom-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .rc-virtual-list-scrollbar-thumb {
      width: 50% !important;
      background: var(--system-content-fifth, #303741) !important;
      right: -1px !important;
    }

    .ant-select-arrow {
      color: #6B7A8F !important;

      .suffix-icon {
        pointer-events: none !important;

        svg {
          width: 10px;
          height: 10px;
        }
      }
    }
  }
}