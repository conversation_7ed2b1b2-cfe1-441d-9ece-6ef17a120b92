import { CheckBlack, ChevronDownBlack } from "@meitu/candy-icons";
import { Form, Select } from "antd";
import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { TabItem } from "../../components/ControlNetSection";
import { ControlNetUtils } from "../../components/ControlNetSection/controlNetUtils";
import { useI18n } from "@/locales/client";

const BaseModel = () => {
  const { paramsEditorStore } = useRootStore();
  const form = Form.useFormInstance();
  const t = useI18n();

  const controlNetTabs: TabItem[] = [
    { label: `${t("Guide")} 1`, value: "0" },
    { label: `${t("Guide")} 2`, value: "1" },
    { label: `${t("Guide")} 3`, value: "2" },
  ];

  return (
    <div className={styles.modelSelector} id="posterModelSelect">
      <Form.Item noStyle name="baseModelId">
        <Select
          getPopupContainer={() =>
            document.getElementById("posterModelSelect")!
          }
          options={paramsEditorStore.baseModelList}
          optionRender={(option) => (
            <div className="custom-option">
              {option.label}
              {option.value === form.getFieldValue("baseModelId") && (
                <CheckBlack />
              )}
            </div>
          )}
          suffixIcon={<ChevronDownBlack className="suffix-icon" />}
          dropdownStyle={{ width: "288px", borderRadius: '12px' }}
          listHeight={230}
          fieldNames={{
            label: "title",
            value: "id",
          }}
          onChange={(val) => {
            // 重置风格模型 重置controlnet列表
            form.setFieldValue("styleModel", []);
            controlNetTabs.forEach((_, index) => {
              const namePath = ControlNetUtils.getImageProcessParamsPath(index);
              form.setFieldValue(namePath, {});
            });
            // 改变基础模型，重新获取风格模型，controlnet列表
            paramsEditorStore.getStyleModelList({
              baseModelId: val,
              reset: true,
            });
            paramsEditorStore.getControlNetList(val);
          }}
        />
      </Form.Item>
    </div>
  );
};

export default observer(BaseModel);
