.titleBox {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .formItemTitle {
    color: var(--system-content-secondary, #A3AEBF);
     font-family: var(--font-poppins);
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
  }
}

.imageSwitch {
  &:global(.ant-switch-checked) {
    background: var(--background-btnAi, #53F6B4) !important;

    &:hover {
      // background: linear-gradient(0deg, #38D294 44.44%, #01331F 181.94%) !important;
    }
  }

  :global {
    .ant-switch-handle {
      &::before {
        background-color: #272C33 !important;
      }
    }
  }
}

.posterUpload {
  width: 100%;
  height: 66px;
  border-radius: 8px;

  :global(.ant-upload-drag) {
    border: 1px solid #e2e8f0 !important;
    background: #fff !important;

    :global(.ant-upload-btn) {
      padding: 0 !important;
    }
  }
}

.imageBox {
  height: 66px;
  border-radius: var(--radius-12, 12px);
  border: 1px solid var(--system-stroke-button, #323B48);
  background: var(--system-background-thirdary, #272C33);
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;

  .leftBox {
    width: 58px;
    height: 58px;
    background: #f6f7fa;
    border-radius: var(--radius-8, 8px);
    border: 1px solid var(--system-stroke-button, #323B48);
    background: var(--system-content-fifth, #303741);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;

    img {
      border-radius: var(--radius-8, 8px);
      object-fit: cover;
    }

    svg {
      width: 26px;
      height: 26px;
      color: #A3AEBF;
      cursor: pointer;
    }
  }

  .centerBox {
    width: 180px;
    color: var(--system-content-secondary, #A3AEBF);
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
  }

  .rightIcon {
    cursor: pointer;
  }
}

.posterSliderInput {
  margin-top: 12px;

  :global(.ant-input-number-affix-wrapper) {
    width: 58px !important;
  }
}