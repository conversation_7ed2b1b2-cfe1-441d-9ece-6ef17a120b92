import { Loading, SliderInput } from "@/components";
import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { Form, Spin, Switch, Image, message } from "antd";
import { ChevronRightBlack, PlusBold, TrashCanBold } from "@meitu/candy-icons";
import { FabricObject, Group } from "@meitu/whee-infinite-canvas";
import { ImageParamsForm } from "@/api/types/aiPoster/task";
import { useI18n } from "@/locales/client";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { useSaveCanvas } from "@/app/[locale]/editor/_hooks/useSaveProjectHistory";
import FilePicker from "@/components/FilePicker";
import { useUpload } from "@/app/[locale]/editor/_hooks/useUpload";
import { useState } from "react";
import { toAtlasImageView2URL } from "@meitu/util";
import { trackEvent } from "@/services/tracer";
import CardLoading from "@/components/Loading/card-loading";

const ImageSection = () => {
  const form = Form.useFormInstance();
  // const { paramsEditorStore } = useRootStore();
  const imageChecked = Form.useWatch("imageChecked", form);
  const t = useI18n();

  return (
    <>
      <div className={styles.titleBox}>
        <h3 className={styles.formItemTitle}>{t("Image Variation")}</h3>
        <Form.Item name={"imageChecked"} noStyle valuePropName="checked">
          <Switch
            className={styles.imageSwitch}
            // onChange={(val) => {
            //   // 当前选中的模型
            //   const baseModel = getAllModelByConfig(
            //     paramsEditorStore.baseModelList
            //   ).find((item) => item.id === form.getFieldValue("baseModelId"));

            //   // 打开图生图开关，如果当前选择的是mv5，需设置成默认的模型
            //   if (val && baseModel?.mvType === ModelMvType.Special) {
            //     form.setFieldValue(
            //       "baseModelId",
            //       paramsEditorStore.baseModelList?.[0]?.list?.[0]?.id
            //     );
            //     message.warning("MV5模型暂不支持图生图，已为您切换为MV4模型～");
            //   }
            // }}
          />
        </Form.Item>
      </div>

      {imageChecked && (
        <Form.Item name={"image"} noStyle>
          <PosterUploader />
        </Form.Item>
      )}
    </>
  );
};

const PosterUploader = (props: {
  value?: string;
  onChange?: (value: string) => void;
}) => {
  const { value, onChange } = props;
  // const { renderStore, editorStatusStore, projectsStore } = useRootStore();
  // const rootStore = useRootStore();
  // const { submitSaveElements } = useSaveCanvas(rootStore);
  // const form = Form.useFormInstance();
  const t = useI18n();
  const upload = useUpload();
  const [uploadLoading, setUploadLoading] = useState(false);

  // const submitAddHistory = (objects: FabricObject[]) => {
  //   const { historyPlugins, render } = renderStore;
  //   if (!historyPlugins || !render) {
  //     return;
  //   }
  //   const operation = historyPlugins.baseAction.getAddOperation({
  //     objects,
  //   });
  //   if (!operation) return;
  //   historyPlugins.submit(operation);
  //   return submitSaveElements(objects);
  // };

  // async function handleChange(url: string, previewUrl = "") {
  //   // 上传图片的时候，保存生图参数到params里。否则传完之后会清空参数
  //   const formData: ImageParamsForm = form.getFieldsValue();
  //   const customData = {
  //     ...formData,
  //     styleModelConfig: formData.styleModel?.map(
  //       ({ id, strength, categoryIds }) => {
  //         return {
  //           styleModelId: id,
  //           styleModelCategories: categoryIds,
  //           styleModelWeight: strength,
  //         };
  //       }
  //     ),
  //   };

  //   previewUrl && onChange?.(previewUrl);
  //   const onUploadFinish = getCommonUploadFinishHandler({
  //     renderStore,
  //     editorStatusStore,
  //     projectsStore,
  //     customData: {
  //       params: {
  //         ...customData,
  //       },
  //     },
  //   });
  //   const groups = await onUploadFinish([{ url, previewUrl }]);
  //   submitAddHistory(groups?.filter(Boolean) as Group[]);
  // }

  const handleFileChange = async (fileList: FileList | null) => {
    const file = fileList?.[0];
    if (!file) {
      return;
    }
    setUploadLoading(true);
    await upload(file);
    setUploadLoading(false);

    trackEvent("upload_image_success", {
      location: "edit_component",
    });
  };

  return (
    <>
      {value ? (
        <>
          <div className={styles.imageBox}>
            <div className={styles.leftBox}>
              <Image
                alt=""
                width={58}
                height={58}
                src={toAtlasImageView2URL(value, {
                  mode: 2,
                  width: 120,
                })}
                preview={false}
                // placeholder={<Loading />}
                placeholder={<CardLoading />}
              />
            </div>
            <div className={styles.centerBox}>{t("Reference drawing")}</div>
            <TrashCanBold
              className={styles.rightIcon}
              onClick={() => {
                onChange?.("");
              }}
            />
          </div>
          <Form.Item name={"userDenoisingStrength"} noStyle>
            <SliderInput
              controls={false}
              suffix="%"
              label={t("Similarity")}
              direction="horizontal"
              min={0}
              max={100}
              step={1}
              markNum={3}
              className={styles.posterSliderInput}
            />
          </Form.Item>
        </>
      ) : (
        <FilePicker
          accept={[".png", ".jpg", ".jpeg"]}
          onChange={handleFileChange}
        >
          {({ openPicker }) => {
            return (
              <Spin spinning={uploadLoading}>
                <div
                  className={styles.imageBox}
                  onClick={() => {
                    trackEvent("upload_image_click", {
                      location: "edit_component",
                    });
                    openPicker();
                  }}
                >
                  <div className={styles.leftBox}>
                    <PlusBold />
                  </div>
                  <div className={styles.centerBox}>{t("Upload")}</div>
                  <ChevronRightBlack className={styles.rightIcon} />
                </div>
              </Spin>
            );
          }}
        </FilePicker>

        // <Upload.Dragger
        //   className={styles.posterUpload}
        //   renderChildren={(loading: boolean) => {
        //     if (loading) {
        //       return <Spin spinning />;
        //     }
        //     return (
        //       <div className={styles.uploadButton}>
        //         <div className={styles.leftBox}>
        //           <PlusBold />
        //         </div>
        //         <div className={styles.centerBox}>上传图片</div>
        //         <ChevronRightBlack className={styles.rightIcon} />
        //       </div>
        //     );
        //   }}
        //   value={value}
        //   onChange={handleChange}
        //   limit={30}
        //   limitMaxEdge={9500}
        //   limitAspectRatio={null}
        //   customErrorToast={customUploadErrorToast}
        //   // taskCategory={props.taskCategory}
        // />
      )}
    </>
  );
};

export default observer(ImageSection);
