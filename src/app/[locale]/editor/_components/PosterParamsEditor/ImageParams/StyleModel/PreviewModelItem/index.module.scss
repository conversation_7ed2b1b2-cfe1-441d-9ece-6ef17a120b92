.itemBox {
  border-radius: var(--radius-12, 12px);
  border: 1px solid var(--system-stroke-button, #323B48);
  background: var(--system-background-thirdary, #272C33);
  padding: 4px 4px 12px;

  .topBox {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .detailBox {
      display: flex;

      img {
        border-radius: var(--radius-8, 8px);
        border: 1px solid rgba(255, 255, 255, 0.20);
        background: var(--background-systemSpaceHolder, #F6F7FA);
        object-fit: cover;
      }

      .textBox {
        margin-left: 12px;

        h3 {
          margin-top: 8px;
          color: var(--system-content-secondary, #A3AEBF);
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
        }

        .typeBox {
          margin-top: 4px;
          width: 38px;
          height: 18px;
          border-radius: var(--radius-4, 4px);
          background: var(--system-content-fifth, #303741);
          display: flex;
          justify-content: center;
          align-items: center;
          color: var(--system-content-thirdary, #6A7B94);
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
        }
      }
    }
  }

  .sliderBox {
    padding: 0 8px;

    .sliderInput {
      :global(.ant-input-number-affix-wrapper) {
        width: 58px !important;
      }
    }
  }
}