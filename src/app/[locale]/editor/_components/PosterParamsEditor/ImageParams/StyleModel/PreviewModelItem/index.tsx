import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { Loading, SliderInput } from "@/components";
import { TrashCanBold } from "@meitu/candy-icons";
import { Form, Image } from "antd";
import { EditorConfigModelListResponse } from "@/api/types/editorConfig";
import { useEffect } from "react";
import { useI18n } from "@/locales/client";
import { toAtlasImageView2URL } from "@meitu/util";

type ModelListItemType = {
  item: EditorConfigModelListResponse;
  onClick: () => void;
  remove(): void;
  onWeightChange(val: number): void;
};

const PreviewModelItem = ({
  item,
  onClick,
  remove,
  onWeightChange,
}: ModelListItemType) => {
  const form = Form.useFormInstance();
  const t = useI18n();

  // useEffect(() => {
  //   form.setFieldValue("strength", item.styleModelWeight);
  // }, []);

  return (
    <div className={styles.itemBox} onClick={onClick}>
      <div className={styles.topBox}>
        <div className={styles.detailBox}>
          <Image
            className={styles.image}
            width={58}
            height={58}
            alt={item.name}
            src={
              typeof item.images === "string"
                ? toAtlasImageView2URL(item.images, {
                    mode: 2,
                    width: 120,
                  })
                : toAtlasImageView2URL(item.images?.[0], {
                    mode: 2,
                    width: 120,
                  })
            }
            preview={false}
            placeholder={<Loading />}
          />
          <div className={styles.textBox}>
            <h3>{item.name}</h3>
            <span className={styles.typeBox}>{item.typeName}</span>
          </div>
        </div>
        <TrashCanBold
          onClick={(e) => {
            e.stopPropagation();
            e.nativeEvent.stopImmediatePropagation();
            remove?.();
          }}
        />
      </div>
      <div className={styles.sliderBox}>
        <SliderInput
          value={item.styleModelWeight}
          onChange={onWeightChange}
          controls={false}
          suffix="%"
          label={t("Strength")}
          direction="horizontal"
          min={0}
          max={100}
          step={1}
          markNum={3}
          className={styles.sliderInput}
        />
      </div>
    </div>
  );
};

export default observer(PreviewModelItem);
