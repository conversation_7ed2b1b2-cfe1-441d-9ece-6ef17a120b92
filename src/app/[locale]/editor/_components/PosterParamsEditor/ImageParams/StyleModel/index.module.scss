.previewListBox {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .itemBox {
    position: relative;
    width: 90px;
    height: 115px;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 0;

    &.active {
      :global(.ant-image) {
        box-shadow: 0 0 0 2px #53F6B4 inset;
      }
    }

    :global(.ant-image) {
      width: 90px;
      height: 90px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 0 0 1px var(--system-stroke-button, #323B48) inset;
      background: transparent;
      border-radius: 12px;
    }

    .itemImage {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
      position: relative;
      z-index: -1;
    }

    .itemImageTag {
      position: absolute;
      top: 1px;
      left: 1px;
      height: 14px;
      width: auto;
      border-radius: 8px 0 8px 0;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .itemName {
      margin-top: 8px;
      width: 100%;
      height: 16px;
      text-align: center;
      color: var(--system-content-secondary, #A3AEBF);
      text-align: center;
      font-size: 13px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
    }
  }
}

.addBtnBox {
  position: relative;

  .addModelBtn {
    margin-top: 12px;
    border-radius: var(--radius-10, 10px) !important;
    border: 1px solid var(--system-stroke-button, #323B48) !important;
    background: var(--system-background-thirdary, #272C33) !important;
    color: var(--system-content-secondary, #A3AEBF) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }

  .addBtnTag {
    position: absolute;
    top: 12px;
    right: 0;
    height: 14px;
    width: auto;

    img {
      object-fit: contain;
      width: auto;
      height: 100%;
    }
  }
}