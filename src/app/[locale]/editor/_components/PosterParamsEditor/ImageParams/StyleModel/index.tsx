import { Button, Form, Space, message, Image } from "antd";
import PreviewModelItem from "./PreviewModelItem";
import { useRef, useCallback, useMemo, useState, useEffect } from "react";
import { BasicParams } from "@/types";
import { observer } from "mobx-react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { getCategoryIds } from "@/app/[locale]/editor/_utils/modelConfigUtils";
import { toJS } from "mobx";
import {
  EditorConfigModelListResponse,
  ModelMvType,
} from "@/api/types/editorConfig";
import classNames from "classnames";
import styles from "./index.module.scss";
import { useI18n } from "@/locales/client";
import ModelModal, {
  ModalRef,
} from "@/app/[locale]/editor/_components/PosterParamsEditor/components/ModelModal";
import { produce } from "immer";
import { COLLECTION_CATEGORY_ID } from "@/constants/model";
import { Loading } from "@/components";
import toast from "@/components/Toast";
import _ from "lodash";
import { toAtlasImageView2URL } from "@meitu/util";

const STYLE_MODEL_NUMS_LIMIT = 3;
export const STRENGTH = 50;

const StyleModel = () => {
  const modelModalRef = useRef<ModalRef>(null);
  const form = Form.useFormInstance<BasicParams>();
  const t = useI18n();
  const chooseStyleModel = Form.useWatch("styleModel");
  const { paramsEditorStore } = useRootStore();
  // const [previewStyleModelList, setPreviewStyleModelList] = useState<
  //   EditorConfigModelListResponse[]
  // >([]);

  const previewStyleModelList = useMemo(() => {
    const allModels = paramsEditorStore.styleModelList.flatMap(
      (item) => item.list
    );
    /**
     * 同一个模型可能出现在不同的分类中，直接拉平会存在重复的模型
     * 因此，这里需要去重
     */
    return _.uniqBy(allModels, (model) => model.id).slice(0, 6);
  }, [paramsEditorStore.styleModelList]);

  // useEffect(() => {
  //   const temp = paramsEditorStore.styleModelList
  //     .flatMap((item) => item.list)
  //     .slice(0, 6);
  //   setPreviewStyleModelList(temp);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [paramsEditorStore.styleModelList]);

  // const previewStyleModelList = useMemo(
  //   () =>
  //     paramsEditorStore.styleModelList.flatMap((item) => item.list).slice(0, 6),
  //   [paramsEditorStore.styleModelList]
  // );

  const setModalVisible = useCallback((visible: boolean) => {
    modelModalRef.current?.setVisible(visible);
  }, []);

  const onModelClick = useCallback(
    (item: EditorConfigModelListResponse) => {
      const styleList = form.getFieldValue(
        "styleModel"
      ) as BasicParams["styleModel"];
      if (styleList?.some((s) => s.id === item.id)) {
        // message.warning("该模型已经设置在风格模型中啦~");
        toast.warning("The model has already been set in the style model.");
        return;
      }

      const categoryIds = getCategoryIds(
        paramsEditorStore.styleModelList,
        item.id
      );
      const val = [...(styleList ?? []), { ...item, categoryIds }];
      form.setFieldValue("styleModel", val);
      setModalVisible(false);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form, paramsEditorStore.styleModelList]
  );

  const updateModelItem = useCallback(
    (modelId: number, props: Partial<EditorConfigModelListResponse>) => {
      paramsEditorStore.setStyleModelList(
        produce(toJS(paramsEditorStore.styleModelList), (draft) => {
          draft.forEach((styleModel) => {
            const modelIndex = styleModel.list.findIndex(
              (model) => model.id === modelId
            );
            if (modelIndex < 0) return;

            if (
              COLLECTION_CATEGORY_ID === styleModel.categoryId &&
              props.isCollect === false
            ) {
              styleModel.list.splice(modelIndex, 1);
              return;
            }

            Object.assign(styleModel.list[modelIndex], props);
          });
        })
      );
    },
    [paramsEditorStore]
  );

  const onCollectMutation = useCallback(
    async (item: EditorConfigModelListResponse) => {
      const { isCollect, id } = item;
      updateModelItem(id, { isCollect: !isCollect });
    },
    [updateModelItem]
  );

  const handleRemoveModel = useCallback(
    (modelId: number) => {
      const styleList = form.getFieldValue(
        "styleModel"
      ) as BasicParams["styleModel"];
      const val = styleList?.filter((s) => s.id !== modelId);
      form.setFieldValue("styleModel", val);
    },
    [form]
  );

  const handleWeightChange = useCallback(
    (index: number, weight: number) => {
      const updatedModels = chooseStyleModel.map((item: any, idx: number) =>
        idx === index ? { ...item, styleModelWeight: weight } : item
      );
      form.setFieldValue("styleModel", updatedModels);
    },
    [chooseStyleModel, form]
  );

  const renderAddButton = useCallback(() => {
    const selectedNums = form.getFieldValue("styleModel")?.length ?? 0;
    const exceedLimit = selectedNums - STYLE_MODEL_NUMS_LIMIT;
    const needsDelete = Math.max(exceedLimit + 1, 0);

    const handleAddClick = () => {
      const baseModel = paramsEditorStore.baseModelList.find(
        (item) => item.id === form.getFieldValue("baseModelId")
      );

      // if (baseModel?.mvType === ModelMvType.Special) {
      //   message.warning("MV5不支持风格模型～");
      //   return;
      // }

      if (needsDelete) {
        const numsTip = needsDelete > 1 ? `${needsDelete}个` : "";
        // message.warning(`风格模型已达到上限，请删除${numsTip}后添加。`);
        toast.warning(
          `The style model has reached the upper limit, please delete ${numsTip} and then add it.`
        );
        return;
      }

      setModalVisible(true);
    };

    return (
      <div className={styles.addBtnBox}>
        <Button className={styles.addModelBtn} onClick={handleAddClick} block>
          {t("Add model")}
        </Button>
        {paramsEditorStore.addStyleBtnIcon && (
          <Image
            alt={""}
            src={paramsEditorStore.addStyleBtnIcon}
            placeholder={<Loading />}
            wrapperClassName={styles.addBtnTag}
            preview={false}
          />
        )}
      </div>
    );
  }, [form, paramsEditorStore.baseModelList, setModalVisible]);

  const renderPreviewList = useCallback(
    () => (
      <>
        <div className={styles.previewListBox}>
          {previewStyleModelList?.map((item, index) => (
            <div
              key={`${item.id}-${index}`}
              className={classNames(styles.itemBox)}
              onClick={() => onModelClick(item)}
            >
              <Image
                alt={item.name}
                src={
                  typeof item.images === "string"
                    ? toAtlasImageView2URL(item.images, {
                        mode: 2,
                        width: 180,
                      })
                    : toAtlasImageView2URL(item.images?.[0], {
                        mode: 2,
                        width: 180,
                      })
                }
                placeholder={<Loading />}
                className={styles.itemImage}
                preview={false}
              />
              {item.tag?.url && (
                <Image
                  alt={item.tag?.name}
                  src={item.tag?.url}
                  placeholder={<Loading />}
                  wrapperClassName={styles.itemImageTag}
                  preview={false}
                />
              )}
              <div className={styles.itemName}>{item.name}</div>
            </div>
          ))}
        </div>
        <div className={styles.addBtnBox}>
          <Button
            className={styles.addModelBtn}
            onClick={() => setModalVisible(true)}
            block
          >
            {t("Add model")}
          </Button>
          {paramsEditorStore.addStyleBtnIcon && (
            <Image
              alt={""}
              src={paramsEditorStore.addStyleBtnIcon}
              placeholder={<Loading />}
              wrapperClassName={styles.addBtnTag}
              preview={false}
            />
          )}
        </div>
      </>
    ),
    [previewStyleModelList, onModelClick, setModalVisible, t]
  );

  return (
    <>
      <Form.Item name="styleModel" noStyle>
        {chooseStyleModel && chooseStyleModel.length > 0 ? (
          <Space direction="vertical" style={{ width: "100%" }} size={12}>
            {chooseStyleModel.map(
              (item: EditorConfigModelListResponse, index: number) => (
                <PreviewModelItem
                  key={item.id}
                  item={item}
                  onClick={() => {}}
                  remove={() => handleRemoveModel(item.id)}
                  onWeightChange={(val: number) =>
                    handleWeightChange(index, val)
                  }
                />
              )
            )}
            <Form.Item
              noStyle
              shouldUpdate={(prev, cur) =>
                prev.styleModel?.length !== cur.styleModel?.length
              }
            >
              {renderAddButton}
            </Form.Item>
          </Space>
        ) : (
          renderPreviewList()
        )}
      </Form.Item>

      <ModelModal
        ref={modelModalRef}
        modelList={paramsEditorStore.styleModelList}
        onModelSelect={onModelClick}
        onCollectMutation={onCollectMutation}
        title={t("Style Model")}
      />
    </>
  );
};

export default observer(StyleModel);
