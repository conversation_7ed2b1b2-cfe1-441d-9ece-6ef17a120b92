import { Form, message, Button } from "antd";
import { observer } from "mobx-react";
import styles from "./index.module.scss";
import commonStyles from "../common.module.scss";
import { RatioSection } from "../components/RatioSection";
import { toSnakeCase } from "@meitu/util";
import {
  createImage,
  ElementName,
  ElementOptions,
  FabricObject,
  FrameElementOptions,
  getElementOptions,
  Group,
  ImageOptions,
  LoadingType,
  TextElementOptions,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { PosterRatio } from "@/api/types/poster";
import { useParamsForm } from "@/app/[locale]/editor/_context/FormContext";
import { useI18n } from "@/locales/client";
import QuantitySection from "../components/QuantitySection";
import ImageSection from "./ImageSection";
import BaseModel from "./BaseModel";
import StyleModel from "./StyleModel";
import { <PERSON><PERSON>oster, ImageParamsForm, Submit } from "@/api/types/aiPoster/task";
import { disabledKeyboardScale } from "@/app/[locale]/editor/_utils/disabledScreenScale";
import { useEffect, useMemo, useState } from "react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { useSaveCanvas } from "@/app/[locale]/editor/_hooks/useSaveProjectHistory";
import { dimensionList } from "@/constants/ratio";
import { ModelMvType, StyleModelConfigType } from "@/api/types/editorConfig";
import { isUndefined } from "lodash";
import { submitTask } from "@/api/aiPoster/task";
import { ControlNetParams, ControlNetZoomMode, MtccFuncCode } from "@/types";
import { handleSubmitError } from "@/app/[locale]/editor/_utils/submit";
import { dispatch } from "@/app/[locale]/editor/_utils/TaskDispatcher";
import { insertImageByOptions } from "@/app/[locale]/editor/_utils/insertImage";
import { PromptSection } from "../components/PromptSection";
import { ControlNetSection } from "../components/ControlNetSection";
import MeiDouButton from "@/components/MeiDou";
import { MaxImageLimit } from "../../../_constant/params";
import useConfirmModal from "@/hooks/useConfirmModal";
import {
  formatControlNet,
  getControlNet,
} from "../../../_utils/modelConfigUtils";
import { useGuideCreateProject } from "../../../_context/GuideCreateProjectContext";
import toast from "@/components/Toast";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { HeaderAction } from "../../../_store/headerAction";
import { FunctionCode } from "@/api/types/meidou";
import useSizeExceedConfirmModal from "../../../_hooks/useSizeExceedConfirmModal";
import { trackEvent } from "@/services/tracer";
import { Track } from "../../../_constant/track";

type ImageParamsType = {
  activeObject: FabricObject;
  objectsOptions: ImageOptions[];
};
// 服务端返回的params转换为表单格式
export const transformResponseToImageParams = (params: any) => {
  return {
    ...params,
    /** 风格模型 */
    styleModel: params?.styleModelConfig?.map((item: StyleModelConfigType) => {
      return {
        /** 风格模型id */
        id: item.styleModelId,
        /** 强度 */
        styleModelWeight: item.styleModelWeight,
        /** 分类ids */
        categoryIds: item.styleModelCategories,
        images: item.styleModelImage,
        name: item.styleModelName,
        typeName: item.styleModelTypeName,
      };
    }),
    /** 控制参数 */
    controlNet: params?.controlnetUnits
      ? getControlNet(params?.controlnetUnits, {
          enable: false,
          weight: 0.6,
          interventionTiming: [0, 0],
          zoomMode: ControlNetZoomMode.Cropping,
        } as ControlNetParams)
      : undefined,
  };
};

// 按照比列缩放到最大尺寸
export const resizeDimensions = (
  width: number,
  height: number,
  maxLimit: number
) => {
  const long = Math.max(width, height);
  if (long <= maxLimit) return { width, height };

  const scale = maxLimit / long;
  return {
    width: Math.round(width * scale),
    height: Math.round(height * scale),
  };
};

const transFormX = 50;

export const tempBaseImage =
  "https://titan-h5.meitu.com/whee/assets/qrCode/invisible-graph.jpg";

const ImageParams = ({ activeObject, objectsOptions }: ImageParamsType) => {
  const rootStore = useRootStore();
  const {
    projectsStore,
    renderStore,
    selectionStore,
    paramsEditorStore,
    headerActionStore,
    editorStatusStore,
  } = useRootStore();
  const { form } = useParamsForm();
  const t = useI18n();
  const render = renderStore.render;
  const historyPlugins = renderStore.historyPlugins;
  const { submitSaveElements } = useSaveCanvas(rootStore);
  const previewImg = Form.useWatch("image", form);
  const [submitLoading, setSubmitLoading] = useState(false);
  const { createGuideProject, isInGuidePage } = useGuideCreateProject();
  const { open: openSubscribeModal } = useSubscribeModal();
  const { userStore } = useStore();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const formInitialValues = {
    prompt: "",
    batchSize: 4,
    picRatio: PosterRatio.RATIO_3_4,
    width: 768,
    height: 1024,
    imageChecked: false,
    userDenoisingStrength: 50,
    styleModel: undefined,
    controlNet: undefined,
  };

  // 回填表单数据
  useEffect(() => {
    const activeOptions = selectionStore.singleImage?.options;
    const params = activeOptions?._custom_data_history_?.params;
    const width = Math.round(activeObject?.getScaledWidth());
    const height = Math.round(activeObject?.getScaledHeight());
    const temp = dimensionList.find(
      (item) => String(item.size) === String([width, height])
    );
    const picRatio = temp ? temp.value : PosterRatio.FREE;

    if (params) {
      const imageUrl =
        activeOptions?._custom_data_history_?.imageStatus ===
        AIPoster.ImageStatus.AuditFailed
          ? ""
          : activeOptions.src;

      const isMv5 =
        paramsEditorStore.baseModelList.find(
          (item) => item.id === params.baseModelId
        )?.mvType === ModelMvType.Special;

      form.setFieldsValue({
        ...params,
        userDenoisingStrength: params.imageChecked
          ? params.userDenoisingStrength || 50
          : 50,
        baseModelId:
          isMv5 || isUndefined(params.baseModelId)
            ? paramsEditorStore.baseModelList?.[0]?.id
            : params.baseModelId,
        imageChecked:
          isUndefined(params.imageChecked) || imageUrl
            ? true
            : params.imageChecked,
        image: imageUrl,
        styleModel: params.styleModel ? params.styleModel : undefined,
        prompt: params.prompt ? params.prompt : "",
        width: width || 768,
        height: height || 1024,
        picRatio,
      });
    } else {
      const baseModelId = paramsEditorStore.baseModelList?.[0]?.id;

      form.setFieldsValue({
        ...formInitialValues,
        width: width || 768,
        height: height || 1024,
        picRatio: previewImg ? PosterRatio.FREE : picRatio,
        baseModelId,
        imageChecked: activeOptions?.src ? true : false,
        image: activeOptions?.src,
      });
    }

    const baseModelId = form.getFieldValue("baseModelId");
    // 改变基础模型，重新获取风格模型，controlnet列表
    paramsEditorStore.getStyleModelList({
      baseModelId,
      reset: true,
    });
    paramsEditorStore.getControlNetList(baseModelId);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    form,
    activeObject?._id_,
    selectionStore.singleImage?.options.src,
    paramsEditorStore.baseModelList,
  ]);

  // 根据画布里的尺寸回填参数栏
  useEffect(() => {
    const handleSizeChange = () => {
      if (!activeObject || !render) return;

      const eleOpt = getElementOptions.call(render, activeObject);
      const width = Math.round((eleOpt?.width || 0) * Math.abs(eleOpt.scaleX));
      const height = Math.round(
        (eleOpt?.height || 0) * Math.abs(eleOpt.scaleY)
      );

      const temp = dimensionList.find(
        (item) => String(item.size) === String([width, height])
      );
      const picRatio = temp ? temp.value : PosterRatio.FREE;

      if (
        width !== form.getFieldValue("width") ||
        height !== form.getFieldValue("height")
      ) {
        form.setFieldsValue({
          picRatio,
          width,
          height,
        });
      }
    };
    const handlerMap = {
      scaling: handleSizeChange,
      resizing: handleSizeChange,
    };

    activeObject?.on(handlerMap);
    render?._FC.on("object:changed", handleSizeChange);

    return () => {
      activeObject?.off(handlerMap);
      render?._FC.off("object:changed", handleSizeChange);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeObject?._id_]);

  // 将表单数据转换为请求参数
  const toImage2ImageQueryParams = (values: ImageParamsForm) => {
    if (!activeObject) {
      // eslint-disable-next-line no-throw-literal
      throw "没有选中图片，触发了图生图";
    }

    const activeOptions =
      render && getElementOptions.call(render, activeObject);
    return {
      ...values,
      styleModelConfig: values.styleModel?.map(
        ({ id, styleModelWeight, categoryIds }) => {
          return {
            styleModelId: id,
            styleModelCategories: categoryIds,
            styleModelWeight,
          };
        }
      ),
      initImages: [
        activeOptions?._custom_data_history_?.urlShort ?? values.image,
      ],
      linkMsgId: activeOptions?._custom_data_history_?.msgId,
      initWatermarkImage: values.image ?? "",
      controlnetUnits: formatControlNet(values.controlNet),
    } as unknown as Submit.Image2ImageParams;
  };

  const toText2ImageQueryParams = (values: ImageParamsForm) => {
    return {
      ...values,
      styleModelConfig: values.styleModel?.map(
        ({ id, styleModelWeight, categoryIds }) => {
          return {
            styleModelId: id,
            styleModelCategories: categoryIds,
            styleModelWeight,
          };
        }
      ),
      controlnetUnits: formatControlNet(values.controlNet),
    } as unknown as Submit.Text2ImageParams;
  };

  // 创建图生图
  const createImageToImage = async (values: ImageParamsForm) => {
    if (!activeObject) {
      return;
    }

    // 图形loading结束
    const imageToImageLoaded = () => {
      render?.Actions.setLoaded(activeObject?._id_ || "");
    };

    const imageToImageLoading = () => {
      activeObject?._id_ &&
        render?.Actions.setLoading({
          type: LoadingType.CIRCLE_DANCE,
          blur: true,
          maskOpacity: 0.2,
          text: "Generating...",
          id: activeObject._id_,
        });
    };

    let msgId = "";
    const projectId = projectsStore.activeProjectId;
    if (!projectId || !render || !historyPlugins) {
      return;
    }
    // 图形loading
    const operation = historyPlugins.baseAction.getLoadOperation([
      activeObject,
    ]);

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== activeObject
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);
    operation && historyPlugins.submit(operation);

    setSubmitLoading(true);
    // 图形loading
    imageToImageLoading();

    const params = toImage2ImageQueryParams(values);

    try {
      // 生图参数用图片的宽高，不是参数栏的宽高
      const { width, height } = resizeDimensions(
        objectsOptions?.[0].imageWidth,
        objectsOptions?.[0].imageHeight,
        MaxImageLimit
      );
      const res = await submitTask({
        projectId,
        // TODO
        // layerId: activeObject?._id_,
        taskCategory: AIPoster.TaskCategory.Image2Image,
        params: { ...toImage2ImageQueryParams(values), width, height },
        functionName: MtccFuncCode.FuncCodePosterImage2Image,
      });
      setSubmitLoading(false);
      editorStatusStore.addActiveTask(res.id);
      msgId = res.id;
    } catch (err) {
      handleSubmitError(err, rootStore, {
        subscribeModal: {
          open: openSubscribeModal,
        },
        userStore: userStore,
        openMeiDouRecordsPopup,
      });
      imageToImageLoaded();
      setSubmitLoading(false);
      return;
    } finally {
      // 更新美豆
      userStore.refreshMtBeanBalance();

      // restore();
    }

    // 如果任务创建失败 或者任务被中断 不继续执行
    if (!msgId || abortController.signal.aborted) {
      return;
    }

    return dispatch({
      msgId,
      rootStore,
      shape: activeObject,
      abortController,
      expandCustomData: {
        params,
      },
      t,
    }).finally(() => {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    });
  };

  // 文生图
  const createTextToImage = async (values: ImageParamsForm) => {
    // 画布loading
    const startLoading = async (values: ImageParamsForm) => {
      if (!render) return;
      // 插入占位图片
      const insertedImageEle = await createImage(
        "",
        {
          src: tempBaseImage,
          width: values.width,
          height: values.height,
        },
      );

      render.addToViewPortCenterByArrangement(insertedImageEle);
      render._FC.setActiveObject(insertedImageEle);
      render.backToOriginPosition({ target: insertedImageEle });
      render._FC.requestRenderAll();

      // 给插入的元素设置生成参数
      insertedImageEle?.set({
        _custom_data_history_: {
          params: toText2ImageQueryParams(values),
          imageStatus: AIPoster.ImageStatus.AuditFailed,
        },
      });

      if (!historyPlugins) return;

      const addOperation = historyPlugins.baseAction.getAddOperation({
        objects: [insertedImageEle],
      });

      const loadingOperation = historyPlugins.baseAction.getLoadOperation([
        insertedImageEle as FabricObject,
      ]);

      addOperation && historyPlugins.submit(addOperation);
      loadingOperation && historyPlugins.submit(loadingOperation);

      // 设置图片loading
      insertedImageEle?._id_ &&
        render?.Actions.setLoading({
          type: LoadingType.FADE_IN_TO_OUT,
          text: "Generating...",
          id: insertedImageEle._id_,
        });

      return insertedImageEle;
    };

    let msgId = "";
    let insertedImageEle = {} as Group | undefined;
    const projectId = projectsStore.activeProjectId;
    if (!projectId || !render || !historyPlugins) {
      return;
    }

    setSubmitLoading(true);

    // 画布结束loading
    const loadingEnd = () => {
      render?.Actions.setLoaded(insertedImageEle?._id_ || "");
    };

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== insertedImageEle
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    try {
      const { width, height } = resizeDimensions(
        values.width,
        values.height,
        MaxImageLimit
      );
      const res = await submitTask({
        projectId,
        // layerId: insertedImageEle?._id_,
        taskCategory: AIPoster.TaskCategory.Text2Image,
        // 生图参数最长边1024
        params: { ...toText2ImageQueryParams(values), width, height },
        functionName: MtccFuncCode.FuncCodePosterText2Image,
      });
      editorStatusStore.addActiveTask(res.id);
      setSubmitLoading(false);

      // 图片占位元素
      insertedImageEle = await startLoading(values);

      msgId = res.id;
    } catch (err) {
      handleSubmitError(err, rootStore, {
        subscribeModal: {
          open: openSubscribeModal,
        },
        userStore: userStore,
        openMeiDouRecordsPopup,
      });
      loadingEnd();
      setSubmitLoading(false);
      return;
    } finally {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }

    // 如果任务创建失败 或者任务被中断 不继续执行
    if (!msgId || abortController.signal.aborted) {
      return;
    }

    return dispatch({
      msgId,
      rootStore,
      shape: insertedImageEle,
      abortController,
      t,
    }).finally(() => {
      // 更新美豆
      userStore.refreshMtBeanBalance();
    });
  };

  const onConfirm = async () => {
    if (!projectsStore.activeProjectId) {
      if (!isInGuidePage) {
        return;
      }

      await createGuideProject();
    }

    form.getFieldValue("imageChecked")
      ? createImageToImage(form.getFieldsValue())
      : createTextToImage(form.getFieldsValue());
  };

  const {
    open: openTextToImageModal,
    contextHolder: textToImageContextHolder,
    getNeedsOpen: getTextToImageModalNeedsOpen,
  } = useSizeExceedConfirmModal({
    feature: AIPoster.TaskCategory.Text2Image,
    title: "Do you want to continue?",
    description: t("text-to-image.size-exceed-modal-content{size}" as any, {
      size: 1280,
    }),
    onConfirm,
  });

  const {
    open: openImageToImageModal,
    contextHolder: imageToImageContextHolder,
    getNeedsOpen: getImageToImageModalNeedsOpen,
  } = useSizeExceedConfirmModal({
    feature: AIPoster.TaskCategory.Image2Image,
    title: "Do you want to continue?",
    description:
      "The image is too large, processing it will affect the resolution.",
    onConfirm,
  });

  const onFinish = async (values: ImageParamsForm) => {
    if (!projectsStore.activeProjectId) {
      if (!isInGuidePage) {
        return;
      }

      await createGuideProject();
    }

    // 是否有打开生图开关来区分任务类型。创建任务后的画布状态，记录历史等操作不不同，分开来写
    if (values.imageChecked) {
      if (!values.image) {
        toast.error(t("Upload an image to start editing."));
        return;
      }
    } else {
      if (!values.prompt) {
        toast.error(t("Enter a prompt to generate content."));
        return;
      }
    }

    // 如果是图生图
    if (values.imageChecked) {
      // 判断是否超过最长边 且需要打开弹窗
      if (
        Math.max(values?.width, values?.height) > MaxImageLimit &&
        getImageToImageModalNeedsOpen()
      ) {
        return openImageToImageModal();
      }
      // 不需要打开弹窗 直接创建任务
      return createImageToImage(values);
    }

    // 如果是文生图

    // 判断是否超过最长边 且需要打开弹窗
    if (
      Math.max(values?.width, values?.height) > MaxImageLimit &&
      getTextToImageModalNeedsOpen()
    ) {
      return openTextToImageModal();
    }
    // 不需要打开弹窗 直接创建任务
    return createTextToImage(values);
  };

  const batchSize = Form.useWatch("batchSize", form);
  const functionBody = useMemo(() => {
    return JSON.stringify(toSnakeCase(form.getFieldsValue()));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchSize, previewImg]);

  return (
    <>
      <h3 className={styles.title}>{t("image-params.title")}</h3>
      <Form
        className={styles.imageParams}
        form={form}
        onFinish={onFinish}
        scrollToFirstError={{
          behavior: "smooth",
          block: "nearest",
        }}
        initialValues={formInitialValues}
        // 阻止事件冒泡到document 导致触发快捷键
        onKeyDown={(e) => {
          e.stopPropagation();
          e.nativeEvent.stopImmediatePropagation();
          //如果是回车禁止默认行为
          if (e.code === "Enter") {
            e.preventDefault();
          }
          disabledKeyboardScale(e as unknown as KeyboardEvent);
        }}
      >
        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Prompt")}</h3>
          <PromptSection onPressEnter={onFinish} />
        </div>

        <div className={commonStyles.formItemBox}>
          <ImageSection />
        </div>

        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Models")}</h3>
          <BaseModel />
        </div>

        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Style Model")}</h3>
          <StyleModel />
        </div>

        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>
            {t("Screen Reference")}
          </h3>
          <ControlNetSection moduleList={paramsEditorStore.controlNetList} />
        </div>

        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Size")}</h3>
          <RatioSection
            onSizeChange={(width: number, height: number) => {
              if (!activeObject) return;

              const beforeData =
                renderStore?.historyPlugins?.baseAction.getElementData({
                  target: activeObject,
                }) ?? [];

              render?.Actions.replaceImage(
                activeObject as Group,
                {
                  width,
                  height,
                  scaleX: 1,
                  scaleY: 1,
                },
                "center"
              );

              const afterData =
                renderStore?.historyPlugins?.baseAction.getElementData({
                  target: activeObject,
                }) ?? [];
              const operation =
                renderStore?.historyPlugins?.baseAction.getModifiedOperation({
                  beforeData,
                  afterData,
                });
              if (operation) {
                renderStore?.historyPlugins?.submit(operation);
                submitSaveElements([activeObject]);
              }
            }}
          />
        </div>

        <div className={commonStyles.formItemBox}>
          <h3 className={commonStyles.formItemTitle}>{t("Batch Size")}</h3>
          <QuantitySection />
        </div>

        <MeiDouButton
          functionCode={
            previewImg
              ? FunctionCode.aiPosterImg2img
              : FunctionCode.aiPosterText2img
          }
          functionId={
            previewImg
              ? MtccFuncCode.FuncCodeImage2Image
              : MtccFuncCode.FuncCodeText2Image
          }
          fetchPriceLoading={submitLoading}
          functionBody={functionBody}
          // 生成中的禁止生成
          disabled={
            selectionStore.singleImage?.options._loading_ ||
            headerActionStore.activeHeaderAction === HeaderAction.Eraser
          }
          onClick={({ deficit, price }) => {
            const values = form.getFieldsValue();

            const options =
              render &&
              activeObject &&
              getElementOptions.call(render, activeObject);
            /**
             * 被选中的图片是生成的：
             * 1. 图片有msgId
             * 2. 是模版的预览图
             */
            const imageIsCreate =
              !!options?._custom_data_history_?.msgId ||
              options?._custom_data_history_?.isFromTemplatePreview;

            const params = previewImg
              ? toImage2ImageQueryParams(values)
              : toText2ImageQueryParams(values);

            trackEvent("create_btn_click", {
              function: previewImg
                ? Track.FunctionEnum.ImageToImage
                : Track.FunctionEnum.TextToImage,
              board_info: Track.BoardInfo.NoFrame,
              is_picture_upload: previewImg
                ? imageIsCreate
                  ? Track.IsPictureUpload.Create
                  : Track.IsPictureUpload.Upload
                : "",
              base_model_id: params.baseModelId,
              prompt: params.prompt,
              style_model_config: JSON.stringify(params.styleModel),
              controlnet: JSON.stringify(params.controlnetUnits),
              batch_size: params.batchSize,
              free_batch_size: price?.useFreeNum,
              ratio: params.picRatio,
              width: params.width,
              height: params.height,
              is_vip: userStore.vipLevel,
              // 消耗美豆为0 上报免费
              credit_balance_sufficient:
                price?.amount === 0
                  ? Track.CreditBalanceSufficient.Free
                  : // 小号不为0 判断是否足够
                  deficit
                  ? Track.CreditBalanceSufficient.NotEnough
                  : Track.CreditBalanceSufficient.Enough,
            });
          }}
        />
      </Form>
      {textToImageContextHolder}
      {imageToImageContextHolder}
    </>
  );
};

export default observer(ImageParams);
