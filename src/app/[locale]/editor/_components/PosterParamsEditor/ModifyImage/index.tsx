import { Form } from "antd";
import { observer } from "mobx-react-lite";
import commonStyles from "../common.module.scss";
import QuantitySection from "../components/QuantitySection";
import { useI18n } from "@/locales/client";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/MeiDou";
import { toSnakeCase } from "@meitu/util";
import { PromptSection } from "../components/PromptSection";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { handleSubmitError } from "../../../_utils/submit";
import { useRootStore } from "../../../_store";
import {
  ElementName,
  FabricObject,
  getElementOptions,
  Group,
} from "@meitu/whee-infinite-canvas";
import { WarningType } from "@meitu/whee-infinite-canvas";
import { LoadingType } from "@meitu/whee-infinite-canvas";
import { createPureUpload } from "@/utils/uploader";
import { imageEditBrushOptions } from "../../DraggablePanel/ImageEditActionSelectArea/constants";
import { AIPoster } from "@/api/types/aiPoster/task";
import { Submit } from "@/api/types/aiPoster/task";
import { submitTask } from "@/api/aiPoster/task";
import { dispatch } from "../../../_utils/TaskDispatcher";
import { useMeiDouBalance } from "@/hooks/useMeidou";
import { useStore } from "@/contexts/StoreContext";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { HeaderAction } from "../../../_store/headerAction";
import { useMemo, useState } from "react";
import { FunctionCode } from "@/api/types/meidou";
import { trackEvent } from "@/services/tracer";
import { Track } from "../../../_constant/track";
import { MtccFuncCode } from "@/types";

function ModifyImage() {
  const [form] = Form.useForm();
  const t = useI18n();
  const { userStore } = useStore();
  const rootStore = useRootStore();
  const {
    renderStore,
    selectionStore,
    projectsStore,
    headerActionStore,
    imageEditStore,
    editorStatusStore,
  } = rootStore;
  const singleImage = selectionStore.singleImage;
  const { updateMeiDouBalance } = useMeiDouBalance({
    userStore,
  });
  const historyPlugins = renderStore.historyPlugins;
  const { open: openSubscribeModal } = useSubscribeModal();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const [submitLoading, setSubmitLoading] = useState(false);
  const image = selectionStore.singleImage?.image;
  const imageOptions = selectionStore.singleImage?.options;
  const objectLoading = imageOptions?._loading_;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const disabled =
    !image ||
    objectLoading ||
    imageStatus === AIPoster.ImageStatus.AuditFailed ||
    !imageEditStore.hasArea;

  const prompt = t("Enter prompt for selected area");

  const onFinish = async ({
    prompt,
    batchSize,
  }: {
    prompt: string;
    batchSize: number;
  }) => {
    const render = renderStore.render;
    const historyPlugins = renderStore.historyPlugins;
    const brush = render?._FC.freeDrawingBrush;
    const image = singleImage?.image;
    const imageOptions = singleImage?.options;
    const projectId = projectsStore.activeProjectId;
    if (
      !render ||
      !brush ||
      !image ||
      !projectId ||
      !imageOptions ||
      !historyPlugins
    ) {
      return;
    }

    // 图形loading
    const loadingOperation = historyPlugins.baseAction.getLoadOperation([
      image,
    ]);

    // 图形开始loading
    const startLoading = () => {
      render.Actions.setLoading({
        type: LoadingType.FADE_IN_TO_OUT,
        text: "Generating...",
        id: imageOptions._id_,
      });
      setSubmitLoading(true);
    };

    // 图形loading结束
    const loaded = () => {
      render.Actions.setLoaded(imageOptions._id_);
      setSubmitLoading(false);
    };

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== image
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    startLoading();
    const upload = createPureUpload();
    try {
      // 1. 导出画布上的mask
      const masksBlob = await brush.exportShapesToBlob({
        isMerge: true,
        ext: "jpeg",
        shapeFill: "#fff",
        backgroundFill: "#000000",
        exportContainerType: imageEditBrushOptions.shapeContainerName, // 导出容器类型
      });
      const beforeData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      render._FC.freeDrawingBrush?.clearTargetElement();
      const afterData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      const operation = historyPlugins?.baseAction.getModifiedOperation({
        beforeData,
        afterData,
      });
      abortController.signal.throwIfAborted();
      if (!masksBlob) {
        return;
      }

      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
      if (!operation) return;
      historyPlugins?.submit(operation);
      loadingOperation && historyPlugins.submit(loadingOperation);

      // 2. 导出画布上原图的blob
      const clippedInitImageBlob = await render.Actions.exportElementToBlob(
        image as Group,
        {
          includeType: [ElementName.IMAGE, ElementName.TEXT],
          exportType: "png",
          multiplier: 1,
        }
      );
      abortController.signal.throwIfAborted();
      if (!clippedInitImageBlob) {
        return;
      }

      // 3. 上传原图
      const initRes = await upload({ file: clippedInitImageBlob });
      abortController.signal.throwIfAborted();
      const clippedInitImageUrl = initRes.result;
      if (!clippedInitImageUrl?.previewUrl) {
        return;
      }

      // 4. 上传mask图
      const uploadContext = await Promise.all(
        masksBlob.map((blob) => {
          return upload({ file: blob });
        })
      );

      const maskUrls = uploadContext.map(
        (context) => context.result?.url ?? ""
      );

      const params: Submit.ImageEditParams = {
        batchSize: batchSize,
        initImage: clippedInitImageUrl.previewUrl,
        maskImage: maskUrls[0],
        prompt: prompt,
      };

      const res = await submitTask({
        projectId,
        params,
        taskCategory: AIPoster.TaskCategory.ImageEdit,
      });
      editorStatusStore.addActiveTask(res.id);

      const msgId = res.id;
      userStore.refreshMtBeanBalance();
      await dispatch({
        msgId,
        rootStore,
        shape: image,
        abortController,
        expandCustomData: {
          ...params,
          initWatermarkImage: imageOptions.src,
        },
        t,
      }).finally(() => {
        updateMeiDouBalance();
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === "development") {
        console.warn("改图", e);
      }

      if (abortController.signal.aborted) {
        return;
      }

      if (uploadErrorhandler(e, t)) {
        return;
      }

      if (
        handleSubmitError(e, rootStore, {
          subscribeModal: {
            open: openSubscribeModal,
          },
          userStore: userStore,
          openMeiDouRecordsPopup,
        })
      ) {
        return;
      }
    } finally {
      loaded();
      userStore.refreshMtBeanBalance();
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);
    }
  };

  const batchSize = Form.useWatch("batchSize", form);
  const functionBody = useMemo(() => {
    return JSON.stringify(toSnakeCase(form.getFieldsValue()));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchSize]);

  return (
    <Form
      form={form}
      onFinish={onFinish}
      initialValues={{
        batchSize: 2,
        prompt: "",
      }}
      onKeyDown={(e) => {
        e.nativeEvent.stopImmediatePropagation();
      }}
    >
      <div className={commonStyles.formItemBox}>
        <h3 className={commonStyles.formItemTitle}>{t("Prompt")}</h3>
        <PromptSection needDeepSeek={false} prompt={prompt} />
      </div>
      <div className={commonStyles.formItemBox}>
        <h3 className={commonStyles.formItemTitle}>{t("Batch Size")}</h3>
        <QuantitySection />
      </div>
      <MeiDouButton
        functionCode={FunctionCode.aiPosterInpaint}
        functionId={MtccFuncCode.FuncCodePosterInpaint}
        functionBody={functionBody}
        fetchPriceLoading={submitLoading}
        disabled={disabled}
        onClick={({ deficit, price }) => {
          const render = renderStore.render;
          const values = form.getFieldsValue();

          const options =
            render && image && getElementOptions.call(render, image);
          /**
           * 被选中的图片是生成的：
           * 1. 图片有msgId
           * 2. 是模版的预览图
           */
          const imageIsCreate =
            !!options?._custom_data_history_?.msgId ||
            options?._custom_data_history_?.isFromTemplatePreview;

          trackEvent("create_btn_click", {
            function: Track.FunctionEnum.ModifyImage,
            board_info: Track.BoardInfo.NoFrame,
            is_picture_upload: imageIsCreate
              ? Track.IsPictureUpload.Create
              : Track.IsPictureUpload.Upload,
            // base_model_id: params.baseModelId,
            prompt: values?.prompt,
            // style_model_config: JSON.stringify(params.styleModel),
            // controlnet: JSON.stringify(params.controlnetUnits),
            batch_size: values?.batchSize,
            free_batch_size: price?.useFreeNum,
            // ratio: params.picRatio,
            width: image?.getScaledWidth(),
            height: image?.getScaledHeight(),
            is_vip: userStore.vipLevel,
            // 消耗美豆为0 上报免费
            credit_balance_sufficient:
              price?.amount === 0
                ? Track.CreditBalanceSufficient.Free
                : // 小号不为0 判断是否足够
                deficit
                ? Track.CreditBalanceSufficient.NotEnough
                : Track.CreditBalanceSufficient.Enough,
          });
        }}
      />
    </Form>
  );
}

export default observer(ModifyImage);
