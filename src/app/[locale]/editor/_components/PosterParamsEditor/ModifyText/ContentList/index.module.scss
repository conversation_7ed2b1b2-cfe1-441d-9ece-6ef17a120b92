@import "@/styles/variable.scss";

.list {
  :global {
    .list-item {
      &:not(:nth-child(1)) {
        margin-top: 32px;
      }

      .textarea {
        resize: none;
        width: 100%;
        height: 90px;
        padding: 8px 8px 4px;
        border-radius: 12px;
        font-size: 14px;
        color: $system-content-thirdary;

        &::placeholder {
          color: $system-content-fourth;
        }

        &:focus,&:focus-within {
          outline: none;
          box-shadow: none;
        }
      }
    }
  }
}