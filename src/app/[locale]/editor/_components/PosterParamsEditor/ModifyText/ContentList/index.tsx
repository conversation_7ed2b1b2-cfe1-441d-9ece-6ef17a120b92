import { Form } from "antd";
import { observer } from "mobx-react-lite"
import TextArea from "antd/es/input/TextArea";
import styles from './index.module.scss';
import commonStyles from '../../common.module.scss';
import { useRootStore } from "@/app/[locale]/editor/_store";
import { getElementOptions } from "@meitu/whee-infinite-canvas";
import { useI18n } from "@/locales/client";

type ContentListValueItem = {
  id: string;
  index: number;
  text: string;
}

export type ContentListValueType = Array<ContentListValueItem>;


function ContentList() {

  const t = useI18n();
  const { selectionStore, renderStore } = useRootStore();
  const { singleImage } = selectionStore;
  const render = renderStore.render;

  const form = Form.useFormInstance()

  return (
    <section className={commonStyles.formItemBox}>
      <Form.List name="contentList">
        {(contentListFields, { add, remove }) => {

          const contentList = form.getFieldValue('contentList');
          return (
            <ul className={styles.list}>
              {contentListFields.map((field, i) => {
                const valueItem = contentList[field.name] as ContentListValueItem;
                return (
                  <li className="list-item" key={valueItem.id}> 
                    <h3 className={commonStyles.formItemTitle}>{t('text-edit.edit-content' as any, { index: valueItem.index })}</h3>
                    <Form.Item name={[field.name, 'text']} noStyle>
                      <TextArea
                        className="textarea"
                        placeholder={t('text-edit.edit-content-placeholder')}
                        onChange={event => {
                          const nextText = event.target.value;
                          if (!singleImage?.image || !render) {
                            return;
                          }

                          const options = getElementOptions.call(render, singleImage.image);

                          singleImage.image.set({
                            _custom_data_: {
                              ...options._custom_data_,
                              editTextContentList: options._custom_data_.editTextContentList?.map((item: ContentListValueItem) => {
                                if (item.id !== valueItem.id) {
                                  return item;
                                }

                                return {
                                  ...valueItem,
                                  text: nextText
                                }
                              }) 
                            }
                          })
                        }}
                      />
                    </Form.Item>
                  </li>
                )
              })}
            </ul>
          )
        }}
      </Form.List>
    </section>
  )
}

export default observer(ContentList);