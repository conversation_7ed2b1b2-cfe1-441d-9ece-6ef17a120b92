import { Form } from "antd";
import { observer } from "mobx-react-lite";
import commonStyles from "../common.module.scss";
import ContentList, { ContentListValueType } from "./ContentList";
import QuantitySection from "../components/QuantitySection";
import { useI18n } from "@/locales/client";
import <PERSON><PERSON><PERSON><PERSON><PERSON>on from "@/components/MeiDou";
import { toSnakeCase } from "@meitu/util";
import { useParamsForm } from "../../../_context/FormContext";
import { useRootStore } from "../../../_store";
import { createPureUpload } from "@/utils/uploader";
import { textEditBrushOptions } from "../../DraggablePanel/TextEditActionSelectArea/constants";
import {
  ElementName,
  FabricObject,
  getElementOptions,
  Group,
  ImageElementParams,
  LoadingType,
  WarningType,
} from "@meitu/whee-infinite-canvas";
import { AIPoster, Submit } from "@/api/types/aiPoster/task";
import { submitTask } from "@/api/aiPoster/task";
import { dispatch } from "../../../_utils/TaskDispatcher";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { handleSubmitError } from "../../../_utils/submit";
import _ from "lodash";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import toast from "@/components/Toast";
import { useMemo, useState } from "react";
import { FunctionCode } from "@/api/types/meidou";
import { HeaderAction } from "../../../_store/headerAction";
import runes from "runes2";
import { redrawImage } from "@/utils/redraw";
import { trackEvent } from "@meitu/subscribe-intl";
import { Track } from "../../../_constant/track";
import { MtccFuncCode } from "@/types";

type FormValueType = {
  contentList: ContentListValueType;
  batchSize: number;
};

function ModifyText() {
  const { form } = useParamsForm();
  const t = useI18n();
  const rootStore = useRootStore();
  const {
    renderStore,
    selectionStore,
    projectsStore,
    headerActionStore,
    editorStatusStore,
  } = rootStore;
  const { open: openSubscribeModal } = useSubscribeModal();
  const { userStore } = useStore();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const { singleImage } = selectionStore;
  const [submitLoading, setSubmitLoading] = useState(false);
  const image = selectionStore.singleImage?.image;
  const imageOptions = selectionStore.singleImage?.options;
  const objectLoading = imageOptions?._loading_;
  const historyPlugins = renderStore.historyPlugins;
  const imageStatus = imageOptions?._custom_data_history_?.imageStatus;
  const disabled =
    !image || objectLoading || imageStatus === AIPoster.ImageStatus.AuditFailed;

  const handleCommitForm = async (values: FormValueType) => {
    const render = renderStore.render;
    const brush = render?._FC.freeDrawingBrush;
    const image = singleImage?.image;
    const imageOptions = singleImage?.options;
    const projectId = projectsStore.activeProjectId;
    if (!render || !brush || !image || !projectId || !imageOptions) {
      return;
    }

    const masks = brush.getMasks();
    if (!masks.length) {
      return toast.error(t("text-edit.select-area-tips"));
    }

    if (values.contentList.find((content) => !content.text)) {
      return toast.error(t("text-edit.no-prompt-tips"));
    }

    const isBlank = values.contentList.find((content) => {
      const words = runes(content.text);
      const notEmpty = words.filter((c) => c !== " " && c !== "\n");

      return notEmpty.length === 0;
    });

    if (isBlank) {
      return toast.error(t("text-edit.empty-prompt-tips"));
    }

    // 图形loading
    const loadingOperation = historyPlugins?.baseAction.getLoadOperation([
      image,
    ]);

    // 图形开始loading
    const startLoading = () => {
      render.Actions.setLoading({
        type: LoadingType.FADE_IN_TO_OUT,
        text: "Generating...",
        id: imageOptions._id_,
      });

      setSubmitLoading(true);
    };

    // 图形loading结束
    const loaded = () => {
      render.Actions.setLoaded(imageOptions._id_);
      setSubmitLoading(false);
    };

    // 任务可以被取消
    const abortController = new AbortController();
    const handleCancel = (payload: {
      type: WarningType;
      target: FabricObject;
    }) => {
      if (
        payload.type !== WarningType.TASK_CANCEL ||
        payload.target !== image
      ) {
        return;
      }

      abortController.abort();
      render._FC.off("warning", handleCancel);
    };
    render._FC.on("warning", handleCancel);

    startLoading();

    const upload = createPureUpload();

    try {
      const width = Math.round(image.getScaledWidth());
      const height = Math.round(image.getScaledHeight());

      // 1. 导出画布上的mask
      const containerName = textEditBrushOptions.shapeContainerName;
      const masksBlob = await brush.exportShapesToBlob({
        isMerge: false,
        ext: "jpeg",
        shapeFill: "#fff",
        backgroundFill: "#000000",
        exportContainerType: containerName, // 导出容器类型
      });
      const beforeData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      render._FC.freeDrawingBrush?.clearTargetElement();
      const afterData =
        historyPlugins?.baseAction.getElementData({ target: image }) ?? [];
      const operation = historyPlugins?.baseAction.getModifiedOperation({
        beforeData,
        afterData,
      });
      abortController.signal.throwIfAborted();
      if (!masksBlob) {
        return;
      }

      if (!operation) return;
      historyPlugins?.submit(operation);
      loadingOperation && historyPlugins?.submit(loadingOperation);
      // 退出改字
      headerActionStore.activateActionAndResetCursor(HeaderAction.Cursor);

      // 2. 导出画布上原图的blob
      const clippedInitImageBlob = await render.Actions.exportElementToBlob(
        image as Group,
        {
          includeType: [ElementName.IMAGE, ElementName.TEXT],
          exportType: "png",
          multiplier: 1,
        }
      );
      abortController.signal.throwIfAborted();
      if (!clippedInitImageBlob) {
        return;
      }

      // 3. 上传原图
      const initRes = await upload({
        file: await redrawImage(clippedInitImageBlob, { width, height }),
      });
      abortController.signal.throwIfAborted();
      const clippedInitImageUrl = initRes.result;
      if (!clippedInitImageUrl?.previewUrl) {
        return;
      }

      const indexMap = masks.map((mask) => mask.get("index") - 1);

      // 4. 上传mask图
      const uploadContext = await Promise.all(
        masksBlob.map((blob) => {
          return redrawImage(blob, { width, height }).then((blob) =>
            upload({ file: blob })
          );
        })
      );

      const maskUrls = uploadContext.map(
        (context) => context.result?.url ?? ""
      );

      const params: Submit.TextEditParams = {
        batchSize: values.batchSize,
        initImage: clippedInitImageUrl.previewUrl,
        mediaList: indexMap.map((index) => {
          return {
            maskImage: maskUrls[index],
            text: values.contentList[index].text,
          };
        }),
      };

      const res = await submitTask({
        projectId,
        params,
        taskCategory: AIPoster.TaskCategory.TextEdit,
      });
      editorStatusStore.addActiveTask(res.id);
      const msgId = res.id;

      // 更新美豆
      userStore.refreshMtBeanBalance();

      await dispatch({
        msgId,
        rootStore,
        shape: image,
        abortController,
        t,
      }).finally(() => {
        // 更新美豆
        userStore.refreshMtBeanBalance();
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === "development") {
        console.warn("改字", e);
      }

      if (abortController.signal.aborted) {
        return;
      }

      if (uploadErrorhandler(e, t)) {
        return;
      }

      if (
        handleSubmitError(e, rootStore, {
          subscribeModal: {
            open: openSubscribeModal,
          },
          userStore: userStore,
          openMeiDouRecordsPopup,
        })
      ) {
        return;
      }
    } finally {
      loaded();
      // 更新美豆
      userStore.refreshMtBeanBalance();
    }
  };

  const batchSize = Form.useWatch("batchSize", form);
  const functionBody = useMemo(() => {
    return JSON.stringify(toSnakeCase(form.getFieldsValue()));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchSize]);

  return (
    <Form
      form={form}
      initialValues={{
        contentList: [],
        batchSize: 4,
      }}
      onKeyDown={(e) => {
        e.nativeEvent.stopImmediatePropagation();
      }}
      onFinish={handleCommitForm}
    >
      <ContentList />
      <div className={commonStyles.formItemBox}>
        <h3 className={commonStyles.formItemTitle}>{t("Batch Size")}</h3>
        <QuantitySection />
      </div>
      <MeiDouButton
        functionCode={FunctionCode.aiPosterTextEditing}
        functionId={MtccFuncCode.FuncCodePosterTextEditing}
        functionBody={functionBody}
        fetchPriceLoading={submitLoading}
        disabled={disabled}
        onClick={({ deficit, price }) => {
          const render = renderStore.render;
          const values = form.getFieldsValue();

          const options =
            render && image && getElementOptions.call(render, image);
          /**
           * 被选中的图片是生成的：
           * 1. 图片有msgId
           * 2. 是模版的预览图
           */
          const imageIsCreate =
            !!options?._custom_data_history_?.msgId ||
            options?._custom_data_history_?.isFromTemplatePreview;

          const prompts = (
            values?.contentList as ContentListValueType | undefined
          )?.map((content) => content.text);

          trackEvent("create_btn_click", {
            function: Track.FunctionEnum.ModifyText,
            board_info: Track.BoardInfo.NoFrame,
            is_picture_upload: imageIsCreate
              ? Track.IsPictureUpload.Create
              : Track.IsPictureUpload.Upload,
            // base_model_id: params.baseModelId,
            prompt:
              prompts?.length && prompts.length > 1
                ? JSON.stringify(prompts)
                : prompts?.[0],
            // style_model_config: JSON.stringify(params.styleModel),
            // controlnet: JSON.stringify(params.controlnetUnits),
            batch_size: values?.batchSize,
            free_batch_size: price?.useFreeNum,
            // ratio: params.picRatio,
            width: image?.getScaledWidth(),
            height: image?.getScaledHeight(),
            is_vip: userStore.vipLevel,
            // 消耗美豆为0 上报免费
            credit_balance_sufficient:
              price?.amount === 0
                ? Track.CreditBalanceSufficient.Free
                : // 小号不为0 判断是否足够
                deficit
                ? Track.CreditBalanceSufficient.NotEnough
                : Track.CreditBalanceSufficient.Enough,
          });
        }}
      />
    </Form>
  );
}

export default observer(ModifyText);
