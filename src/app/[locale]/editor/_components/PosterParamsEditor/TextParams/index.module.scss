.textParams {

  &,
  * {
    outline: none !important;
    box-shadow: none !important;
  }

  .topBox {
    display: flex;
    gap: 12px;

    :global .ant-image {
      display: flex;
      align-items: center;
      overflow: hidden;

      .font-family-img {
        width: auto !important;
        height: 18px;
        // filter: invert(1);
      }
    }

    &.focus {
      :global .ant-select-selection-item {
        .font-family-img {
          opacity: 0.3 !important;
        }
      }
    }

    :global {
      .ant-select {
        width: 100%;
        height: 100%;

        &.ant-select-focused {
          &> :global(.ant-select-selector) {
            border-color: #64748B !important;
          }
        }
      }

      .ant-select-dropdown {
        border: 1px solid var(--system-stroke-input, #22272E);
        box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30) !important;

        .custom-option {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }

      .rc-virtual-list-scrollbar-thumb {
        width: 50% !important;
        background: var(--system-content-fifth, #303741) !important;
        right: -1px !important;
      }

      .ant-select-item {
        height: 34px;
        align-items: center;

        .ant-select-item-option-content {
          line-height: 24px;
        }
      }
    }

    &>.selector {
      &:global(.ant-select-focused.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer)) {
        &> :global(.ant-select-selector) {
          border-color: #ccc;
        }
      }

      :global {
        .ant-select-selector {
          border-radius: 10px;
        }

        .ant-select-arrow {
          color: #6B7A8F;

          .suffix-icon {
            pointer-events: none !important;

            svg {
              width: 10px;
              height: 10px;
            }
          }
        }
      }
    }

    .fontFamilyPopup {
      width: 234px !important;


      .font-family-img {
        width: auto !important;
        height: 18px !important;
      }

      :global .ant-select-item-option-selected {
        background: transparent !important;

        .custom-option {
          display: flex;
          align-items: center;
          justify-content: space-between;

          svg {
            color: #fff;
          }
        }
      }

      :global .ant-empty-image {
        display: none;

        svg {
          mix-blend-mode: exclusion;
        }
      }
    }

    .sizeInput {
      width: 108px;
      height: 36px;
      border-radius: 10px;
    }
  }

  .centerBox {
    margin-top: 12px;
    width: 100%;
    height: 36px;

    .segmentedBox {
      height: 100%;
      border: 1px solid var(--system-stroke-input-default, #22272E);
      border-radius: 10px;
      padding: 4px;

      :global(.ant-segmented-thumb) {
        border-radius: 8px !important;
      }

      :global(.ant-segmented-item-selected) {
        box-shadow: 0px 0px 0px 1px var(--system-stroke-button, #323B48) inset !important;
        border-radius: 8px;
      }

      :global(.ant-segmented-item-label) {
        line-height: 26px;
      }
    }
  }
}