import { observer } from "mobx-react";
import styles from "./index.module.scss";
import commonStyles from "../common.module.scss";
import { useEffect, useMemo, useRef, useState } from "react";
import { Select, Input, Segmented, Image } from "antd";
import {
  CheckBlack,
  TextAlignmentCenterBold,
  TextAlignmentLeftBold,
  TextAlignmentRightBold,
} from "@meitu/candy-icons";
import { ElementOptions, IText, util } from "@meitu/whee-infinite-canvas";
import { loadRemoteFont } from "@/utils/loadRemoteFont";
import classNames from "classnames";
import { useParamsForm } from "@/app/[locale]/editor/_context/FormContext";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { useSaveCanvas } from "@/app/[locale]/editor/_hooks/useSaveProjectHistory";
import { disabledKeyboardScale } from "@/app/[locale]/editor/_utils/disabledScreenScale";
import { useI18n } from "@/locales/client";
import { Loading } from "@/components";

const segmentedOptions = [
  {
    value: "left",
    label: <TextAlignmentLeftBold />,
  },
  {
    value: "center",
    label: <TextAlignmentCenterBold />,
  },
  {
    value: "right",
    label: <TextAlignmentRightBold />,
  },
];

type TextParamsType = {
  activeObject: IText;
};

const TextParams = ({ activeObject }: TextParamsType) => {
  const { form } = useParamsForm();
  const rootStore = useRootStore();
  const { renderStore, paramsEditorStore } = rootStore;
  const [familyVal, setFamilyVal] = useState("");
  const [fontFamilyLoading, setFontFamilyLoading] = useState(false);
  const [sizeVal, setSizeVal] = useState<number>(16);
  const [alignVal, setAlignVal] = useState("");
  const { submitSaveElements } = useSaveCanvas(rootStore);
  const beforeTextFontSizeOptions = useRef<ElementOptions[] | []>([]);
  const [isFocus, setIsFocus] = useState(false);
  const t = useI18n();

  const familyList = useMemo(() => {
    return paramsEditorStore.familyList.map((item) => ({
      ...item,
      label: (
        <Image
          alt={item.name}
          className="font-family-img"
          placeholder={<Loading />}
          src={item.previewFile}
          preview={false}
        />
      ),
      value: item.name,
    }));
  }, [paramsEditorStore.familyList]);

  useEffect(() => {
    const transform = util.qrDecompose(activeObject.calcTransformMatrix());
    const scale = transform.scaleX;

    // 设置切换后的默认值
    setSizeVal(Math.abs(Math.round(activeObject.fontSize * scale)));
    setAlignVal(activeObject.textAlign);
    setFamilyVal(activeObject.fontFamily);
    // 加载默认字体的全量包
    handleFontFamilyChange(activeObject.fontFamily, false);

    // 监听文字变化
    const handleSizeChange = () => {
      const transform = util.qrDecompose(activeObject.calcTransformMatrix());
      const scale = transform.scaleX;
      setSizeVal(Math.abs(Math.round(activeObject.fontSize * scale)));
      setAlignVal(activeObject.textAlign);
      setFamilyVal(activeObject.fontFamily);
    };

    const handlerMap = {
      scaling: handleSizeChange,
      resizing: handleSizeChange,
    };

    activeObject.on(handlerMap);
    renderStore.render?._FC.on("object:changed", handleSizeChange);

    return () => {
      activeObject.off(handlerMap);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeObject, form, renderStore.render?._FC, familyList]);

  // 设置字体
  const handleFontFamilyChange = (value: string, needHistory: boolean) => {
    const fontFamilyItem = familyList.find((v) => v.name === value);
    if (!fontFamilyItem) return;

    const setActiveFont = () => {
      activeObject.set("fontFamily", value);
      activeObject.set(
        "_font_url_",
        fontFamilyItem.wofFontFile || fontFamilyItem.fontFile
      );
      renderStore.render?._FC.requestRenderAll();
    };

    setFamilyVal(value);
    setFontFamilyLoading(true);
    loadRemoteFont(
      fontFamilyItem.name,
      fontFamilyItem.wofFontFile || fontFamilyItem.fontFile
    )
      .then(() => {
        needHistory ? recordTextChange(setActiveFont) : setActiveFont();
      })
      .finally(() => {
        setFontFamilyLoading(false);
      });
  };

  const recordTextChange = (callback: () => void) => {
    const beforeData =
      renderStore.historyPlugins?.baseAction.getElementData({
        target: activeObject,
      }) ?? [];
    callback();
    const afterData =
      renderStore.historyPlugins?.baseAction.getElementData({
        target: activeObject,
      }) ?? [];
    const operation =
      renderStore.historyPlugins?.baseAction.getModifiedOperation({
        beforeData,
        afterData,
      });
    if (!operation) return;
    renderStore.historyPlugins?.submit(operation);
    submitSaveElements([activeObject]);
  };

  // 更新字体大小
  const updateFontSize = (value1: number) => {
    // 小于3px的字体大小不允许
    const value = value1 < 3 ? 3 : value1;
    setSizeVal(value);

    const transform = util.qrDecompose(activeObject.calcTransformMatrix());
    const scale = transform.scaleX;

    activeObject.set("fontSize", value / scale);

    renderStore.render?._FC.requestRenderAll();
    const afterData =
      renderStore.historyPlugins?.baseAction.getElementData({
        target: activeObject,
      }) ?? [];
    const operation =
      renderStore.historyPlugins?.baseAction.getModifiedOperation({
        beforeData: beforeTextFontSizeOptions.current,
        afterData,
      });
    if (!operation) return;
    renderStore.historyPlugins?.submit(operation);
    beforeTextFontSizeOptions.current = [];
    submitSaveElements([activeObject]);
  };

  return (
    <div className={styles.textParams}>
      <div className={commonStyles.formItemBox}>
        <h3 className={commonStyles.formItemTitle}>{t("Text")}</h3>
        <div
          className={classNames(styles.topBox, isFocus && styles.focus)}
          id="textBox"
        >
          <Select
            // 阻止事件冒泡到document 导致触发快捷键
            onKeyDown={(e) => {
              e.stopPropagation();
              e.nativeEvent.stopImmediatePropagation();
              disabledKeyboardScale(e as unknown as KeyboardEvent);
            }}
            className={styles.selector}
            // suffixIcon={<ChevronDownBlack className="suffix-icon" />}
            getPopupContainer={() => document.getElementById("textBox")!}
            popupClassName={styles.fontFamilyPopup}
            dropdownAlign={{
              points: ["tl", "bl"], // 下拉框的对齐方式：输入框底部与下拉框顶部对齐
              offset: [-4, 12], // 偏移量：[水平, 垂直]
            }}
            style={{ width: 168, height: 36 }}
            value={familyVal}
            onChange={(value) => {
              handleFontFamilyChange(value, true);
            }}
            options={familyList}
            optionRender={(option) => (
              <div className="custom-option">
                {option.label}
                {option.value === familyVal && <CheckBlack />}
              </div>
            )}
            filterOption={(input, option) =>
              option?.defaultName?.includes(input) || false
            }
            showSearch
            loading={fontFamilyLoading}
            onDropdownVisibleChange={(visible) => {
              setIsFocus(visible);
            }}
          />
          <Input
            value={sizeVal}
            onKeyDown={(e) => {
              e.stopPropagation();
              e.nativeEvent.stopImmediatePropagation();
              if (e.code === "Enter" || e.code === "NumpadEnter") {
                const value = Number((e.target as HTMLInputElement).value);
                updateFontSize(value);
              }
              disabledKeyboardScale(e as unknown as KeyboardEvent);
            }}
            onChange={(e) => {
              const value = Number(e.target.value);
              if (typeof value !== "number" || isNaN(value)) return;
              setSizeVal(value);
              // activeObject.set('fontSize', value);
              // renderStore.render?._FC.requestRenderAll();
            }}
            onFocus={() => {
              beforeTextFontSizeOptions.current =
                renderStore.historyPlugins?.baseAction.getElementData({
                  target: activeObject,
                }) ?? [];
            }}
            onBlur={(e) => {
              const value = Number(e.target.value);
              updateFontSize(value);
            }}
            className={styles.sizeInput}
          />
        </div>
        <div className={styles.centerBox}>
          <Segmented
            value={alignVal}
            block
            className={styles.segmentedBox}
            options={segmentedOptions}
            onChange={(value) => {
              // 切换对齐方式  先退出编辑状态
              activeObject.exitEditing();

              recordTextChange(() => {
                setAlignVal(String(value));
                activeObject.set("textAlign", value);
                renderStore.render?._FC.requestRenderAll();
              });
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default observer(TextParams);
