@import '@/styles/variable.scss';
@import '@/app/[locale]/editor/_components/zIndex.scss';

.posterDrawer {
  user-select: none;
  outline: none;

  :global {
    .ant-drawer-content-wrapper {
      box-shadow: none;
      border-right: 1px solid var(--system-stroke-input, #22272E);

      .ant-drawer-body {
        padding: 0;
      }
    }
  }

  .formScrollBox {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .formBox {
      padding-bottom: 126px;
    }
  }
}

.formItemBox {
  background: var(--system-background-secondary, #1D1E23);
  padding: 16px;

  &:not(:last-child) {
    border-bottom: 6px solid #16171C;
  }

  .formItemTitle {
    color: var(--system-content-secondary, #A3AEBF);
     font-family: var(--font-poppins);
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    margin-bottom: 8px;
  }
}