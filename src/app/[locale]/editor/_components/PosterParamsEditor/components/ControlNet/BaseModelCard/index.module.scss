.modelCard {
  transition: all 0.2s;
  position: relative;

  :global {
    .modelCard {
      &-extra {
        position: absolute;
        top: 4px;
        right: 4px;
        background-color: #f5f5f5;
        padding: 4px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
  }

  :global(.ant-space-vertical) {
    gap: 4px 0px;
  }

  &:global(.ant-card) {
    border-radius: var(--radius-10, 10px);
    border: 1px solid var(--system-stroke-button, #323B48);
    background: var(--system-background-thirdary, #272C33);

    // &:hover {
    //   border-color: #059bff;
    //   box-shadow: 0 0 0 2px rgba(5, 155, 255, 0.06);

    //   img {
    //     transform: scale(1.1);
    //   }
    // }

    :global {
      .ant-card {
        &-body {
          padding: 4px;
        }
      }
    }
  }

  :global(.ant-tag) {
    height: 18px;
    line-height: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 6px;
    width: fit-content;

    background-color: #ffefdc;
    color: #ff9a27;
    border-radius: 4px;
    border: none;
  }

  :global(.ant-image) {
    border-radius: 4px;
    overflow: hidden;

    img {
      border-radius: 4px;
      transition: 0.6s;
      object-fit: cover;
    }

    img[src=''],
    img:not([src]) {
      opacity: 0.3;
    }
  }

  .container {
    flex-wrap: nowrap;
    cursor: pointer;

    .imageContainer {
      height: 58px;
    }

    .pr29 {
      padding-right: 29px !important;
    }

    .pr6 {
      padding-right: 6px !important;
    }

    .m0 {
      margin: 0;
    }

    .mAuto {
      margin: auto;
    }

    .f-12 {
      font-size: 12px;
    }
  }

  .children {
    margin: 12px 0;
    width: 100%;
    padding: 0 8px;
  }

  .responsive {
    display: grid;
    grid-template-columns: minmax(auto, max-content) auto;
    gap: 6px;
    align-items: center;

    .cornerLabel {
      padding: 1px 6px;
      border-radius: 4px;
      background-size: cover;
      width: 36px;
      height: 18px;
    }
  }
}