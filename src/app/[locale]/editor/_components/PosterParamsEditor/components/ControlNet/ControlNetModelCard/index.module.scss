.plusBlock {
  border-radius: var(--radius-8, 8px);
  border: 1px solid var(--system-stroke-button, #323B48);
  background: var(--system-content-fifth, #303741);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 26px;
  color: #A3AEBF;
}

.extra {
  position: absolute;
  top: 20px;
  right: 12px;
}

.modelCard {
  overflow: hidden;
  transition: background-color 0.3s ease;
  margin-bottom: 8px !important;

  :global(.modelCard-extra) {
    background-color: transparent;
  }

  &:hover {
    border-color: #d9d9d9;
    box-shadow: none;

    :global(.ant-card-body) {
      background-color: rgba(28, 29, 31, 0.02);

      &>span {
        background-color: transparent;
      }
    }
  }

  .desc {
    width: 180px;
    font-size: 12px;
  }
}