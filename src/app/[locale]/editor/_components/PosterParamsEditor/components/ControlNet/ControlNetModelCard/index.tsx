import { ChevronRightBlack, PlusBold, TrashCanBold } from "@meitu/candy-icons";
import styles from "./index.module.scss";
import { Typography } from "antd";
import { EditorConfigControlNetModel } from "@/api/types/editorConfig";
import { BaseModelCard } from "../BaseModelCard";
import { useI18n } from "@/locales/client";

type ControlNetModelCardProps = Partial<{
  onClick(): void;
  onDelete(): void;
  model?: EditorConfigControlNetModel;
}>;

export function ControlNetModelCard({
  onDelete,
  onClick,
  model,
}: ControlNetModelCardProps) {
  return (
    <BaseModelCard
      // {...model}
      src={model?.coverPic ?? ""}
      desc={model?.desc ?? ""}
      renderDesc={(desc) => {
        return (
          <Typography.Text
            type="secondary"
            className={styles.desc}
            ellipsis={{
              tooltip: {
                title: desc ?? "",
                destroyTooltipOnHide: true,
              },
            }}
          >
            {desc ?? ""}
          </Typography.Text>
        );
      }}
      title={model?.name}
      cornerLabelUrl={model?.tagUrl ?? ""}
      extra={
        <div className={styles.extra}>
          <TrashCanBold />
        </div>
      }
      className={styles.modelCard}
      onClick={onClick}
      onExtraClick={onDelete}
    />
  );
}

type EmptyControlNetModelCardProps = Partial<{
  onClick(): void;
}>;

export function EmptyControlNetModelCard({
  onClick,
}: EmptyControlNetModelCardProps) {
  const t = useI18n();

  return (
    <BaseModelCard
      title={t("Select a reference model")}
      renderPreview={() => (
        <div className={styles.plusBlock}>
          <PlusBold />
        </div>
      )}
      extra={
        <div className={styles.extra}>
          <ChevronRightBlack />
        </div>
      }
      className={styles.modelCard}
      onClick={onClick}
      onExtraClick={onClick}
    />
  );
}
