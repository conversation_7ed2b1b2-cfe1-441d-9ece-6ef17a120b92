.selector {
  display: flex;
  flex-direction: column;

  .selectRow {
    &:not(:first-child) {
      margin-top: 8px;
    }
  }
}

.titleContainer {
  margin-bottom: 12px;
}

.selection {
  width: 51px;
  height: 51px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  opacity: 1;

  &:global(.upload) {
    background: transparent;
    opacity: 1;
  }

  &:global(.active) {
    box-shadow: 0 0 0 2px #38f;
    opacity: 1;
  }

  &:global(.empty) {
    opacity: 1;
    box-sizing: border-box;
    border: 2px dashed #f5f5f5;
  }
}


// .upload {
//   width: 100%;
//   height: 100%;
//   padding: 0 !important;
//   min-width: 100%;
//   border: none;

//   .uploadButtonBox {
//     width: 100%;
//     height: 100%;
//   }

//   .uploadButton {
//     width: 100%;
//     height: 100%;
//     inset: 0;
//     position: absolute;
//     background: transparent !important;
//     border-radius: 0 !important;
//     color: #abadb2 !important;

//     &::before,
//     &::after {
//       background: transparent !important;
//     }

//     &:hover {
//       color: #38f !important;
//     }
//   }

//   :global(.ant-upload-btn) {
//     padding: 0 !important;
//   }
// }

.uploadButton {
  width: 51px;
  height: 51px;
  border-radius: var(--radius-8, 8px);
  box-sizing: border-box;
  border: 1px solid var(--system-stroke-button, #323B48);
  background: var(--system-background-thirdary, #272C33);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  svg {
    width: 24px;
    height: 24px;
    color: #abadb2;
  }
}

.uploaderSelection {
  position: relative;

  .deleteUploadedButton {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    cursor: pointer;

    background-color: #f5f5f5;
    font-size: 12px;
    border-radius: 0px 0px 0px 4px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
  }
}

.imageContainer {
  width: 100%;
  height: 100%;

  .image:global(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}