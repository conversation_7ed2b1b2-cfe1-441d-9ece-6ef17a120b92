import { Col, Row, Image, Spin, Button } from "antd";
import classNames from "classnames";
import styles from "./ImageSelector.module.scss";
import { useEffect, useState } from "react";
import { PlusBold, TrashCan } from "@meitu/candy-icons";
import { EditorConfigControlNetModel } from "@/api/types/editorConfig";
import { ExampleTooltip } from "../ExampleTooltips";
import { containsUrlWithoutQuery } from "@/app/[locale]/editor/_utils/controlNetUtils";
import FilePicker from "@/components/FilePicker";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { useI18n } from "@/locales/client";
import { trackEvent } from "@meitu/subscribe-intl";

type ImageSelectorProps = SelectorProps & {
  examples?: EditorConfigControlNetModel["sampleTips"];
  taskCategory?: string;
};

export function ImageSelector({
  list,
  value,
  onChange,
  examples,
  taskCategory,
}: ImageSelectorProps) {
  const t = useI18n();
  useEffect(() => {
    // 没有生成参数，默认选中第一项
    if (!value && list.length) {
      onChange?.(list[0]);
    }
  }, [list, value, onChange]);

  return (
    <>
      <Row className={styles.titleContainer}>
        <Col span={16}>
          <strong>{t("Upload or select a reference")}</strong>
        </Col>
        <Col span={8}>
          <Row justify="end">
            {!!(examples && Object.keys(examples).length) && (
              <ExampleTooltip examples={examples} />
            )}
          </Row>
        </Col>
      </Row>

      <Row>
        <Col span={24}>
          <Selector
            list={list}
            value={value}
            onChange={onChange}
            taskCategory={taskCategory}
          />
        </Col>
      </Row>
    </>
  );
}

//#region 图片选择相关
type SelectorProps = {
  /**
   * 可以被选的图片
   * 1. controlnet模型相关
   * 2. 如果为图生图 还包括一张原图
   */
  list: Array<string>;
  value?: string;
  onChange?: (url: string) => void;
  taskCategory?: string;
};

const SELECTION_NUMS_IN_LINE = 5;
function Selector({ list, value, onChange, taskCategory }: SelectorProps) {
  const urls = ["upload", ...list];
  const rowNums = Math.ceil(urls.length / SELECTION_NUMS_IN_LINE);

  // 如果value不为空表示选中了一个图片
  // 如果在list中没找到value 则表示当前选中的value是上传的图片
  const valueIsUpload = !!(value && !containsUrlWithoutQuery(list, value));

  return (
    <section className={styles.selector}>
      {new Array(rowNums).fill(0).map((_, i) => {
        const urlsStart = i * SELECTION_NUMS_IN_LINE;
        const urlsEnd = Math.min(
          urlsStart + SELECTION_NUMS_IN_LINE,
          urls.length
        );
        return (
          <SelectRow
            key={i}
            firstRow={0 === i}
            urls={urls.slice(urlsStart, urlsEnd)}
            selectedUrl={value}
            valueIsUpload={valueIsUpload}
            onChange={onChange}
            taskCategory={taskCategory}
            list={list}
          />
        );
      })}
    </section>
  );
}

function SelectRow(props: {
  urls: Array<string>;
  firstRow?: boolean;
  selectedUrl?: string;
  valueIsUpload?: boolean;
  onChange?: (url: string) => void;
  taskCategory?: string;
  list: Array<string>;
}) {
  const urls = props.urls.slice(props.firstRow ? 1 : 0);
  const selections = urls.map((url) => {
    return (
      <ImageSelection
        key={url}
        url={url}
        selected={props.selectedUrl === url}
        onChange={props.onChange}
      />
    );
  });
  const emptysNums = props.firstRow
    ? SELECTION_NUMS_IN_LINE - 1 - selections.length
    : SELECTION_NUMS_IN_LINE - selections.length;
  const emptys = new Array(emptysNums).fill(0).map((_, i) => {
    return <EmptySelection key={i} />;
  });

  return (
    <Row justify="space-between" className={styles.selectRow}>
      {props.firstRow && (
        <Uploader
          selected={props.valueIsUpload}
          onSelect={props.onChange}
          // 不回填controlNet原图
          // defaultValue={props.valueIsUpload ? props.selectedUrl : ""}
          taskCategory={props.taskCategory}
          list={props.list}
        />
      )}
      {selections}
      {emptys}
    </Row>
  );
}

function Uploader(props: {
  selected?: boolean;
  onSelect?: (url: string) => void;
  defaultValue?: string;
  taskCategory?: string;
  list: Array<string>;
}) {
  const t = useI18n();
  const [value, setValue] = useState(props.defaultValue ?? "");
  const upload = useMonitorUploadFunc();
  const [uploadLoading, setUploadLoading] = useState(false);

  if (value && containsUrlWithoutQuery(props.list, value)) {
    setValue("");
  }

  function handleDelete() {
    setValue("");
    props.onSelect?.("");
  }

  const handleFileChange = async (fileList: FileList | null) => {
    const file = fileList?.[0];
    if (!file || !upload) {
      return;
    }
    setUploadLoading(true);
    // 获取图片上传地址
    try {
      const context = await upload({
        file,
      });

      const previewUrl = context.result?.previewUrl ?? "";
      setValue(previewUrl ?? "");
      previewUrl && props.onSelect?.(previewUrl);

      trackEvent("upload_image_success", {
        location: "controlnet",
      });
    } catch (e: any) {
      uploadErrorhandler(e, t);
    } finally {
      setUploadLoading(false);
    }
  };

  return value ? (
    <div className={styles.uploaderSelection}>
      <ImageSelection
        url={value}
        selected={!!props.selected}
        onChange={props.onSelect}
      />
      <span className={styles.deleteUploadedButton} onClick={handleDelete}>
        <TrashCan />
      </span>
    </div>
  ) : (
    <FilePicker
      accept={[".png", ".jpg", ".jpeg"]}
      onChange={handleFileChange}
      className="uploadButtonBox"
    >
      {({ openPicker }) => {
        return (
          <Spin spinning={uploadLoading}>
            <div
              className={classNames(styles.uploadButton)}
              onClick={() => {
                trackEvent("upload_image_click", {
                  location: "controlnet",
                });
                openPicker();
              }}
            >
              <PlusBold />
            </div>
          </Spin>
        );
      }}
    </FilePicker>
  );
}

function ImageSelection(props: {
  url: string;
  selected: boolean;
  onChange?: (url: string) => void;
}) {
  return (
    <div
      className={classNames(styles.selection, props.selected && "active")}
      onClick={() => {
        props.onChange?.(props.url);
      }}
    >
      <Image
        alt=""
        src={props.url}
        preview={false}
        rootClassName={styles.imageContainer}
        className={styles.image}
      />
    </div>
  );
}

function EmptySelection() {
  return <div className={classNames(styles.selection, "empty")}></div>;
}
//#endregion
