import {
  EditorConfigControlNetModel,
  EditorConfigModuleListResponse,
} from "@/api/types/editorConfig";
import {
  ControlNetModelCard,
  EmptyControlNetModelCard,
} from "../ControlNetModelCard";
import { useEffect, useMemo, useRef } from "react";
import { ImageSelector } from "./ImageSelector";
import styles from "./index.module.scss";
import { containsUrlWithoutQuery } from "@/app/[locale]/editor/_utils/controlNetUtils";
import ModelModal, { ModalRef } from "../../ModelModal";
import { useI18n } from "@/locales/client";
import cn from 'classnames'

export interface ImageProcessingParams {
  image?: string;
  module?: string;
  model?: string;
  modelId?: number;

  modelConfig?: EditorConfigControlNetModel;
}

export interface ImageProcessingProps {
  /**
   * 可供选择的模型
   */
  moduleList: EditorConfigModuleListResponse[];
  /**
   * 向表单提交的值
   */
  value?: ImageProcessingParams;
  onChange?: (value: ImageProcessingParams) => void;
  /**
   * 选择图片时 可以向选项前放入其他图片
   */
  choosablePicBeforePreset?: string[];
  /**
   * 通过modal选择模型后触发
   */
  onSelectModel?: (model: EditorConfigControlNetModel) => void;

  taskCategory?: string;
}

export function ControlNetModelImage(props: ImageProcessingProps) {
  const {
    value,
    onChange,
    moduleList,
    choosablePicBeforePreset,
    onSelectModel,
    taskCategory,
  } = props;
  const selectedModel = value?.modelConfig;

  const t = useI18n();

  const choosablePics = useMemo(() => {
    return [
      ...(choosablePicBeforePreset ?? []),
      ...(selectedModel?.choosablePics ?? []),
    ];
  }, [choosablePicBeforePreset, selectedModel]);

  const modelModalRef = useRef<ModalRef>(null);

  function openSelectModal() {
    modelModalRef.current?.setVisible(true);
  }

  function closeModal() {
    modelModalRef.current?.setVisible(false);
  }

  function handleDelete() {
    onChange?.({});
  }

  useEffect(() => {
    // 数据回填时 进行同步
    if (!selectedModel && value?.image && value?.modelId) {
      // 根据回填的数据找到模型
      const model = moduleList
        .flatMap(({ list }) => list)
        .find((model) => {
          return model.id === value.modelId;
        });

      if (model) {
        onChange?.({
          ...value,
          modelConfig: model,
        });
      } else {
        onChange?.({
          image: "",
          modelId: undefined,
          model: "",
          module: "",
          modelConfig: undefined,
        });
      }
    }
  }, [selectedModel, value, moduleList, onChange]);

  return (
    <>
      <div className={cn(styles.modelSelector, selectedModel && styles.hasSelectModel)}>
        <strong className="title">{t("Reference Type")}</strong>

        {selectedModel ? (
          <ControlNetModelCard
            onClick={openSelectModal}
            onDelete={handleDelete}
            model={selectedModel}
          />
        ) : (
          <EmptyControlNetModelCard onClick={openSelectModal} />
        )}
      </div>

      {selectedModel && (
        <ImageSelector
          examples={selectedModel.sampleTips}
          list={choosablePics}
          onChange={(url: string) => {
            onChange?.({
              ...value,
              image: url,
            });
          }}
          value={value?.image}
          taskCategory={taskCategory}
        />
      )}

      <ModelModal
        title={t("Screen Reference")}
        ref={modelModalRef}
        modelList={moduleList as any}
        onModelSelect={async (model: any) => {
          const preModel = value?.modelConfig;
          const imageIsUpload =
            // 1. 当前选中了图片
            value?.image &&
            // 2. 在候选的图片中没有找到选择的图片
            !containsUrlWithoutQuery(
              preModel?.choosablePics ?? [],
              value.image
            );

          onChange?.({
            ...value,
            // 切换controlnet模型时
            // 1. 如果图片为上传的图片，则保持不变
            // 2. 如果图片为预设的图片，则设置image为空，然后会默认选中第一项
            //    这里不默认选中model.choosablePics中的第一项
            //    因为在图生图中，存在原图应该选中原图，设置为空会自动处理这一逻辑
            image: imageIsUpload ? value.image : "",
            model: model.model,
            module: model.module,
            modelId: model.id,
            modelConfig: model,
          });
          onSelectModel?.(model as any);
          closeModal();
        }}
      />
    </>
  );
}
