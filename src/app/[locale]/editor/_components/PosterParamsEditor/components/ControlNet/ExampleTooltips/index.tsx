import { EditorConfigControlNetModel } from "@/api/types/editorConfig";
import { Image, Tooltip } from "antd";
import styles from "./index.module.scss";
import { useI18n } from "@/locales/client";

export function ExampleTooltip(props: {
  className?: string;
  examples: EditorConfigControlNetModel["sampleTips"];
}) {
  const t = useI18n();
  return (
    <Tooltip
      className={props.className}
      overlayClassName={styles.overlay}
      title={
        <div className={styles.tooltipPopup}>
          {props.examples?.samplePic && (
            <ExampleImage
              label={t("Reference drawing")}
              src={props.examples.samplePic}
            />
          )}
          {props.examples?.prePic && (
            <ExampleImage
              label={t("Pre-Process")}
              src={props.examples.prePic}
            />
          )}
          {props.examples?.resultPic && (
            <ExampleImage
              label={t("Output Preview")}
              src={props.examples.resultPic}
            />
          )}
        </div>
      }
    >
      <span className={styles.tooltip}>{t("Example")}</span>
    </Tooltip>
  );
}

function ExampleImage(props: { label: string; src: string }) {
  return (
    <div className={styles.exampleItem}>
      <Image
        alt=""
        src={props.src}
        preview={false}
        wrapperClassName={styles.exampleImage}
      />
      <span>{props.label}</span>
    </div>
  );
}
