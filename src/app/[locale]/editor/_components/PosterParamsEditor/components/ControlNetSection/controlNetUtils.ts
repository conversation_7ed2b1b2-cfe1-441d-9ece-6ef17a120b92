export function containsUrlWithoutQuery(list: string[], maybeInList: string) {
  function removeQuery(url: string) {
    return url.split("?").shift();
  }
  const withoutQuery = removeQuery(maybeInList);

  return !!list.find((u) => removeQuery(u) === withoutQuery);
}

export namespace ControlNetUtils {
  export function getNamePath(index: number, key: string) {
    return ["controlNet", index, key];
  }

  export function getImageProcessParamsPath(index: number) {
    return getNamePath(index, "imageProcessParams");
  }

  export function getWeightPath(index: number) {
    return getNamePath(index, "weight");
  }

  export function getTimingPath(index: number) {
    return getNamePath(index, "interventionTiming");
  }

  export function createNamePathGetter(index: number) {
    const _getNamePath = (key: string) => getNamePath(index, key);
    const _getImageProcessParamsPath = () => getImageProcessParamsPath(index);
    const _getWeightPath = () => getWeightPath(index);
    const _getTimingPath = () => getTimingPath(index);

    return {
      getNamePath: _getNamePath,
      getImageProcessParamsPath: _getImageProcessParamsPath,
      getWeightPath: _getWeightPath,
      getTimingPath: _getTimingPath,
    };
  }
}
