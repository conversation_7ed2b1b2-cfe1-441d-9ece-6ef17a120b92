.segmented-tabs {
  :global {
    .ant-tabs-nav {
      border-radius: var(--radius-10, 10px);
      border: 1px solid var(--system-stroke-input-default, #22272E);
      background: var(--system-background-input, #16171C);

      &::before {
        display: none;
      }

      .ant-tabs-nav-list {
        margin: 0;
        padding: 2px;
        flex: 1;

        .ant-tabs-tab {
          position: relative;
          z-index: 9;
          flex: 1;
          margin: 0;
          padding: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 30px;
          color: var(--system-content-secondary, #A3AEBF);

          &.ant-tabs-tab-disabled {
            color: rgba(28, 29, 31, 0.25);
          }

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: var(--system-content-primary, #FFF);
              font-weight: 500;
            }
          }
        }

        .ant-tabs-ink-bar {
          height: 30px;
          bottom: auto;
          border-radius: var(--radius-8, 8px);
          border: 1px solid var(--system-stroke-button, #323B48);
          background: var(--system-background-thirdary, #272C33);
          box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 6px 24px 0px rgba(0, 0, 0, 0.06);
        }
      }
    }
  }
}

.controlNetModelImageFormItem{
  margin-bottom: 0px !important;
}