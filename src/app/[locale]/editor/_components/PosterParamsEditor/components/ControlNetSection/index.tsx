import { forwardRef, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { Form, Tabs, TabsProps } from "antd";
import { RangeSliderInput, SliderInput } from "@/components";
import { usePrevious } from "react-use";
import {
  DraftType,
  EditorConfigControlNetModel,
  EditorConfigResponse,
} from "@/api/types/editorConfig";
import { ControlNetModelImage } from "../ControlNet/ControlNetModelImage";
import { ControlNetParams } from "@/types";
import { EnableLabel } from "../ControlNet/EnableLabel";
import { memo } from "react";
import styles from "./index.module.scss";
import { ControlNetUtils } from "./controlNetUtils";
import { useI18n } from "@/locales/client";
import cn from "classnames";
interface ControlNetSectionProps {
  moduleList: EditorConfigResponse["moduleList"];
}

export interface TabItem {
  label: string;
  value: string;
}

export interface ControlNetSectionTabPaneProps
  extends Omit<ControlNetSectionProps, "renderTabPane"> {
  tab: TabItem;
  index: number;
  onChangeModel?: (model?: EditorConfigControlNetModel) => void;
}

export type ControlNetSectionHandle = {
  activateTab(index: number): void;
};

type ControlNetImageProcessParams = ControlNetParams["imageProcessParams"];

const isValidControlNetParams = (
  params?: ControlNetImageProcessParams
): boolean => {
  if (!params?.modelId) return false;
  return !!(params.image && params.model && params.module);
};

// const useControlNetForm = (index: number) => {
//   const {
//     getNamePath,
//     getWeightPath,
//     getTimingPath,
//     getImageProcessParamsPath,
//   } = ControlNetUtils.createNamePathGetter(index);
//   const form = Form.useFormInstance();

//   const imageProcessParamsPath = getImageProcessParamsPath();
//   const initImageUrl = Form.useWatch("image", form);
//   const originImage = form.getFieldValue("image") ?? initImageUrl;
//   const preOriginImage = usePrevious(originImage);

//   const imageNamePath = [...imageProcessParamsPath, "image"];
//   useEffect(() => {
//     if (!initImageUrl) {
//       const currentUrl = form.getFieldValue(imageNamePath);
//       if (currentUrl === preOriginImage) {
//         form.setFieldValue([...imageProcessParamsPath, "image"], "");
//       }
//     }
//   }, [
//     initImageUrl,
//     imageNamePath,
//     imageProcessParamsPath,
//     preOriginImage,
//     form,
//   ]);

//   const value = Form.useWatch(imageProcessParamsPath, form);
//   const selectedModel = value?.modelConfig as
//     | EditorConfigControlNetModel
//     | undefined;
//   const choosablePicBeforePreset = originImage ? [originImage] : [];

//   const weightNamePath = getWeightPath();
//   const timingNamePath = getTimingPath();

//   const handleSelectModel = useCallback(
//     (model: EditorConfigControlNetModel | undefined) => {
//       form.setFieldValue(timingNamePath, [
//         model?.guidanceStart,
//         model?.guidanceEnd,
//       ]);
//       form.setFieldValue(weightNamePath, model?.weight);
//     },
//     [form, timingNamePath, weightNamePath]
//   );

//   return {
//     form,
//     imageProcessParamsPath,
//     selectedModel,
//     choosablePicBeforePreset,
//     weightNamePath,
//     timingNamePath,
//     handleSelectModel,
//   };
// };

const ControlNetSectionTabPane = memo(
  ({
    tab,
    index,
    moduleList,
    onChangeModel,
  }: ControlNetSectionTabPaneProps) => {
    const { getNamePath, getWeightPath, getTimingPath } =
      ControlNetUtils.createNamePathGetter(index);
    const form = Form.useFormInstance();

    const imageProcessParamsPath = getNamePath("imageProcessParams");
    const initImageUrl = Form.useWatch("image", form);

    // HACK 获取上传原图加签后的路径；回填没有image.preview，但image.key会带有加签参数
    const originImage = form.getFieldValue("image") ?? initImageUrl;

    const preOriginImage = usePrevious(originImage);

    const imageNamePath = [...imageProcessParamsPath, "image"];
    useEffect(
      () => {
        if (!initImageUrl) {
          const currentUrl = form.getFieldValue(imageNamePath);
          if (currentUrl === preOriginImage) {
            // 如果当前选中的图片是原图 原图删除时 取消选择
            form.setFieldValue([...imageProcessParamsPath, "image"], "");
          }
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [initImageUrl]
    );
    const choosablePicBeforePreset = originImage ? [originImage] : [];

    const value = Form.useWatch(imageProcessParamsPath, form);
    const selectedModel = value?.modelConfig as
      | EditorConfigControlNetModel
      | undefined;

    const weightNamePath = getWeightPath();
    const timingNamePath = getTimingPath();

    const t = useI18n();

    function handleSelectModel(model?: EditorConfigControlNetModel) {
      form.setFieldValue(timingNamePath, [
        model?.guidanceStart,
        model?.guidanceEnd,
      ]);
      form.setFieldValue(weightNamePath, model?.weight);
      onChangeModel?.(model);
    }
    return (
      <div key={tab.value}>
        <Form.Item
          name={imageProcessParamsPath}
          className={cn({
            [styles.controlNetModelImageFormItem]: !selectedModel,
          })}
        >
          <ControlNetModelImage
            moduleList={moduleList}
            choosablePicBeforePreset={choosablePicBeforePreset}
            onSelectModel={handleSelectModel}
            taskCategory={DraftType.IMAGE_TO_IMAGE}
          />
        </Form.Item>
        {selectedModel && (
          <>
            <Form.Item name={weightNamePath}>
              <SliderInput
                title={t("Similarity")}
                min={0}
                max={selectedModel?.weightMax ?? 2}
                step={selectedModel?.weightStep ?? 0.01}
                markNum={0}
              />
            </Form.Item>
            <Form.Item name={timingNamePath}>
              <RangeSliderInput
                title={t("Reference Time")}
                min={0}
                max={selectedModel?.guidanceEndMax ?? 1}
                step={selectedModel?.guidanceStep ?? 0.01}
                minInterval={selectedModel?.guidanceInterval ?? 0.1}
              />
            </Form.Item>
          </>
        )}
      </div>
    );
  }
);

ControlNetSectionTabPane.displayName = "ControlNetSectionTabPane";

export const ControlNetSection = forwardRef<
  ControlNetSectionHandle,
  ControlNetSectionProps
>((props, ref) => {
  const controlNet = Form.useWatch("controlNet");
  const t = useI18n();

  const tabIsEnable = useCallback(
    (index: number) => {
      const params = controlNet?.[index]
        ?.imageProcessParams as ControlNetImageProcessParams;
      return (
        isValidControlNetParams(params) &&
        props.moduleList
          .flatMap(({ list }) => list)
          .some((item) => item.id === params.modelId)
      );
    },
    [controlNet, props.moduleList]
  );

  const controlNetTabs: TabItem[] = [
    { label: `${t("Guide")} 1`, value: "0" },
    { label: `${t("Guide")} 2`, value: "1" },
    { label: `${t("Guide")} 3`, value: "2" },
  ];

  const items: TabsProps["items"] = useMemo(
    () =>
      controlNetTabs.map((tab, index) => ({
        key: tab.value,
        label: <EnableLabel text={tab.label} enable={tabIsEnable(index)} />,
        children: (
          <ControlNetSectionTabPane {...props} tab={tab} index={index} />
        ),
        forceRender: true,
      })),
    [tabIsEnable, props]
  );

  const [activeKey, setActiveKey] = useState(items[0]?.key);

  return (
    <Tabs
      className={styles["segmented-tabs"]}
      items={items}
      activeKey={activeKey}
      onChange={setActiveKey}
    />
  );
});

ControlNetSection.displayName = "ControlNetSection";
