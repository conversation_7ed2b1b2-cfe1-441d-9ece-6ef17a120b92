.modelModal {

  :global(.ant-modal) {
    :global {
      .ant-modal-content {
        height: 652px;
        padding: 12px 0 0 16px;
        background: var(--system-background-primary, #1E2128);
        border-radius: 16px;

        .ant-modal-header {
          padding-right: 56px;
          margin: 0 !important;
          background: var(--system-background-primary, #1E2128) !important;
          display: flex !important;
          align-items: center !important;
          gap: 16px !important;
          height: 36px;

          .ant-modal-title {
            color: var(--system-content-primary, #FFFFFF);
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            flex: 1;
          }
        }

        .ant-modal-body {
          height: calc(100% - 36px);
          overflow: hidden;

          .ant-tabs-content-holder {
            padding-right: 16px;

            .ant-tabs-content {
              height: 100%;

              .ant-tabs-tabpane {
                height: 100%;
              }
            }
          }

          .ant-tabs {
            height: 100%;
          }
        }

        .ant-modal-close {
          color: var(--system-content-secondary, #A3AEBF);
          top: 14px;
          right: 12px;

          &:hover {
            color: var(--system-content-primary, #FFFFFF);
          }
        }

        .ant-tabs-nav {
          padding: 8px 0 12px;
          margin: 0;

          &,
          * {
            box-shadow: none !important;
            outline: none !important;
          }

          &::before {
            border: none;
          }

          .ant-tabs-tab {
            padding: 4px 0;
            font-size: 14px;
            color: var(--system-content-thirdary, #6A7B94);
            margin: 0 40px 0 0;
            transition: color 0.3s;

            &:hover {
              color: var(--system-content-primary, #FFFFFF);
            }

            &.ant-tabs-tab-active .ant-tabs-tab-btn {
              color: var(--system-content-primary, #FFFFFF);
              font-weight: 600;
            }
          }

          .ant-tabs-ink-bar {
            background: var(--system-content-brandPrimary, #53F6B4);
            width: 16px !important;
            height: 3px;
            border-radius: 2px;
          }
        }
      }
    }
  }

  .search-wrapper {
    margin: 0;
    width: 222px;
    position: relative;

    &,
    * {
      box-shadow: none !important;
    }

    :global(.ant-input-affix-wrapper) {
      border-radius: var(--radius-8, 8px);
      border: 1px solid var(--system-stroke-input, #22272E);
      background: var(--system-background-input, #16171C);
      padding: 7px 12px !important;

      svg {
        color: var(--system-content-fourth, #49576C);
      }

      input {
        background: transparent;
        color: var(--system-content-fourth, #49576C);
        font-size: 14px;
        line-height: 22px;

        &::placeholder {
          color: var(--system-content-fourth, #49576C);
        }
      }

      .anticon {
        color: var(--system-content-fourth, #49576C);
      }

      &:hover,
      &:focus-within {
        border-color: var(--system-stroke-button-hover, #464957);
        background: var(--system-background-secondary, #22262C);

        .anticon {
          color: var(--system-content-secondary, #A3AEBF);
        }
      }
    }
  }

  .model-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
    height: 100%;
    padding-bottom: 12px;
    box-sizing: border-box;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    // &::-webkit-scrollbar {
    //   width: 6px;
    // }

    // &::-webkit-scrollbar-track {
    //   background: rgba(255, 255, 255, 0.05);
    //   border-radius: 3px;
    // }

    // &::-webkit-scrollbar-thumb {
    //   background: rgba(255, 255, 255, 0.2);
    //   border-radius: 3px;

    //   &:hover {
    //     background: rgba(255, 255, 255, 0.3);
    //   }
    // }
  }

  .model-empty-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    svg {
      width: 60px;
      height: 60px;
    }
  }

  .model-card {
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-button, #323B48);
    background: var(--system-background-thirdary, #272C33);
    height: 211px;
    padding: 4px 4px 8px;

    // &:hover {
    //   transform: translateY(-2px);
    //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    //   border-color: rgba(255, 255, 255, 0.2);
    //   background: rgba(255, 255, 255, 0.08);
    // }

    .model-image {
      width: 100%;
      height: 155px;
      object-fit: cover;
      border-radius: 8px;
    }

    .model-content {
      margin-top: 8px;

      .model-title {
        font-size: 12px;
        font-weight: 500;
        color: var(--system-content-secondary, #A3AEBF);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .bottom-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .model-desc {
          font-size: 12px;
          color: var(--system-content-thirdary, #6A7B94);
          line-height: 1.5;
          width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 16px;
        }

        .collect-box {
          width: 20px;
          height: 20px;
          border-radius: var(--radius-6, 6px);
          background: var(--system-content-fifth, #303741);
          display: flex;
          justify-content: center;
          align-items: center;

          .active {
            svg {
              color: #ffd257;
            }
          }
        }
      }
    }
  }
}