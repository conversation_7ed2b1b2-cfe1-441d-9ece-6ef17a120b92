import {
  forwardRef,
  useEffect,
  useImperative<PERSON><PERSON>le,
  useState,
  useCallback,
} from "react";
import { Modal as AntdModal, Image, Input, Spin, Tabs, Typography } from "antd";
import classNames from "classnames";
import styles from "./index.module.scss";
import {
  FivePointedStarBold,
  FivePointedStarBoldFill,
  SearchBold,
  File,
} from "@meitu/candy-icons";
import { debounce } from "lodash";
import {
  EditorConfigModelListResponse,
  StyleModelResponse,
} from "@/api/types/editorConfig";
import { cancelMarkModel, markModel } from "@/api/aiPoster/model";
import { COLLECTION_CATEGORY_ID } from "@/constants/model";
import { useRootStore } from "@/app/[locale]/editor/_store";
import _ from "lodash";
import { Loading } from "@/components";
import { useI18n } from "@/locales/client";
import { toAtlasImageView2URL } from "@meitu/util";

export interface ModalRef {
  setVisible: (visible: boolean) => void;
  visible: boolean;
}

export interface ModalProps {
  className?: string;
  onModelSelect?: (item: EditorConfigModelListResponse) => void;
  modelList: StyleModelResponse[];
  onCollectMutation?: (item: EditorConfigModelListResponse) => Promise<any>;
  title?: string;
}

const ModelModal = forwardRef<ModalRef, ModalProps>((props, ref) => {
  const { paramsEditorStore } = useRootStore();
  const { className, onModelSelect, modelList, onCollectMutation, title } =
    props;
  const [visible, setVisible] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [activeCategory, setActiveCategory] = useState<string>(
    modelList?.[0]?.categoryId?.toString()
  );
  const t = useI18n();

  useImperativeHandle(ref, () => ({
    setVisible,
    visible,
  }));

  // const fetchModelList = useCallback(
  //   debounce((keyword: string, categoryId: number) => {
  //     paramsEditorStore.getStyleModelList({
  //       keyword,
  //       categoryId,
  //       reset: true,
  //     });
  //   }, 300),
  //   [paramsEditorStore]
  // );

  // 切换tab
  // const onCategoryChange = useCallback(
  //   (categoryId: number) => {
  //     fetchModelList(searchValue, categoryId);
  //   },
  //   [fetchModelList, searchValue]
  // );

  // 搜索
  // const onSearch = useCallback(() => {
  //   fetchModelList(searchValue, Number(activeCategory));
  // }, [fetchModelList, searchValue, activeCategory]);

  return (
    <AntdModal
      open={visible}
      onCancel={() => setVisible(false)}
      footer={null}
      centered
      wrapClassName={classNames(styles.modelModal, className)}
      title={
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span>{title}</span>
          {/* 暂无搜索 */}
          {/* <div className={styles["search-wrapper"]} style={{ width: "240px" }}>
            <Input
              // allowClear
              placeholder="搜索模型"
              prefix={<SearchBold />}
              value={searchValue}
              onChange={({ target: { value } }) => {
                setSearchValue(value);
                fetchModelList(value, Number(activeCategory));
              }}
              onPressEnter={onSearch}
              // onBlur={onSearch}
              // onClear={onSearch}
            />
          </div> */}
        </div>
      }
      width={896}
    >
      <Tabs
        defaultActiveKey={`${modelList?.[0]?.categoryId}`}
        activeKey={activeCategory}
        onChange={(id) => {
          setActiveCategory(id);
          // onCategoryChange(+id);
        }}
        items={modelList?.map((category) => ({
          key: `${category.categoryId}`,
          label: category.categoryName,
          children: (
            <TabContainer
              category={category}
              onModelSelect={onModelSelect}
              onCollectMutation={onCollectMutation}
            />
          ),
        }))}
      />
    </AntdModal>
  );
});
export interface TabContainerProps {
  category: StyleModelResponse;
  onModelSelect?: (item: EditorConfigModelListResponse) => void;
  onCollectMutation?: (item: EditorConfigModelListResponse) => Promise<any>;
}
const TabContainer = (props: TabContainerProps) => {
  const { category, onModelSelect, onCollectMutation } = props;
  const [loading, setLoading] = useState(false);
  const [activeModelId, setActiveModelId] = useState<number>();

  const onIconClick = useCallback(
    async (item: EditorConfigModelListResponse) => {
      if (loading) return;
      setLoading(true);
      setActiveModelId(item.id);

      const action = item.isCollect ? cancelMarkModel : markModel;

      try {
        await action({ id: item.id });
        await onCollectMutation?.(item);
      } catch (error) {
        // console.log(error);
      } finally {
        setLoading(false);
        setActiveModelId(undefined);
      }
    },
    [loading, onCollectMutation]
  );

  const renderModelCard = useCallback(
    (model: EditorConfigModelListResponse) => (
      <div
        key={model.id}
        className={styles["model-card"]}
        onClick={() => onModelSelect?.(model)}
      >
        <Image
          className={styles["model-image"]}
          src={
            typeof model.images === "string"
              ? toAtlasImageView2URL(model.images, {
                  mode: 2,
                  width: 300,
                })
              : toAtlasImageView2URL(model.images[0], {
                  mode: 2,
                  width: 300,
                })
          }
          alt={model.name}
          preview={false}
          width={"100%"}
          placeholder={<Loading />}
        />
        <div className={styles["model-content"]}>
          {/* <h3 className={styles["model-title"]}>{model.name}</h3> */}
          <Typography.Text
            className={styles["model-title"]}
            ellipsis={{
              tooltip: {
                title: model.name,
                destroyTooltipOnHide: true,
              },
            }}
          >
            {model.name}
          </Typography.Text>
          <div className={styles["bottom-box"]}>
            {/* <p className={styles["model-desc"]}>{model.desc}</p> */}
            <Typography.Text
              className={styles["model-desc"]}
              ellipsis={{
                tooltip: {
                  title: model.desc,
                  destroyTooltipOnHide: true,
                },
              }}
            >
              {model.desc}
            </Typography.Text>
            {/* 暂没有收藏 */}
            {/* <div
              className={styles["collect-box"]}
              onClick={(e) => {
                e.stopPropagation();
                e.nativeEvent.stopImmediatePropagation();
                onIconClick(model);
              }}
            >
              {loading && model.id === activeModelId ? (
                <Spin spinning size="small" />
              ) : model.isCollect ? (
                <FivePointedStarBoldFill className={styles.active} />
              ) : (
                <FivePointedStarBold />
              )}
            </div> */}
          </div>
        </div>
      </div>
    ),
    [onModelSelect]
  );

  return (
    <>
      {!!category?.list?.length ? (
        <div className={styles["model-grid"]}>
          {category?.list?.map(renderModelCard)}
        </div>
      ) : (
        <div className={styles["model-empty-box"]}>
          <File />
          <span>暂无数据</span>
        </div>
      )}
    </>
  );
};

ModelModal.displayName = "ModelModal";

export default ModelModal;
