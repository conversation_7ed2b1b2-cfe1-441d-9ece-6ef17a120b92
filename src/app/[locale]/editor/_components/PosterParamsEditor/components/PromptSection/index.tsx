import { Form } from "antd";
import { CorpusFilter } from "@/types";
import { ImageParamsForm } from "@/api/types/aiPoster/task";
import { TextArea } from "../TextArea";
import DeepSeek from "@/components/DeepSeek";
import { useWatch } from "antd/es/form/Form";
import { useI18n } from "@/locales/client";

export function PromptSection({
  onPressEnter,
  savePrompt,
  needDeepSeek = true,
  prompt,
}: {
  onPressEnter?: (values: ImageParamsForm) => void;
  savePrompt?: (values: string) => void;
  needDeepSeek?: boolean;
  prompt?: string;
}) {
  const t = useI18n();
  const form = Form.useFormInstance();
  const promptPlaceholder = t("Enter your ideas for poster creation");

  // TODO: 监听prompt的变化, 是否有更优雅的写法
  const promptWatch = useWatch("prompt", {
    form,
    preserve: true,
  });

  return (
    <div id="prompt">
      <Form.Item name="prompt" noStyle>
        <TextArea.SmartTextarea
          hasRandomPromptAction={true}
          onPressEnter={(e) => {
            onPressEnter?.(form.getFieldsValue());
          }}
          hasComplementAction={true}
          style={{ height: "160px", resize: "none" }}
          allowClear
          // maxLength={800}
          showCount={false}
          placeholder={ prompt || promptPlaceholder}
          corpusAction={CorpusFilter.ALL}
          onChange={(val) => {
            savePrompt?.(val);
          }}
        />
      </Form.Item>
      {/* {needDeepSeek && (
        <DeepSeek
          usePrompt={(prompt, content, direct) => {
            form.setFieldValue("prompt", content);
          }}
          prompt={form.getFieldValue("prompt")}
        />
      )} */}
    </div>
  );
}
