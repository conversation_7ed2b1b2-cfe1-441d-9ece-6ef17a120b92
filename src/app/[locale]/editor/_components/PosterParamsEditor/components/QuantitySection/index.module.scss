.genNum:global(.ant-radio-group) {
  width: 100%;
  display: flex;
  justify-content: space-between;

  :global {
    .gen-num-radio.ant-radio-wrapper {
      margin: 0;

      .ant-wave-target {
        display: none;

        &+span {
          padding: 0;
        }
      }

      .gen-num-radio-label {
        display: flex;
        width: 69px;
        height: 32px;
        justify-content: center;
        align-items: center;
        border-radius: var(--radius-8, 8px);
        box-shadow: 0 0 0 1px var(--system-stroke-button, #323B48) inset;
        background: var(--system-background-thirdary, #272C33);
      }

      &.ant-radio-wrapper-checked {
        .gen-num-radio-label {
          box-shadow: 0 0 0 1.5px #53F6B4 inset;
          background: var(--system-content-brandSecondary, #0F221A);
          color: var(--system-content-primary, #FFF);
        }
      }
    }
  }
}