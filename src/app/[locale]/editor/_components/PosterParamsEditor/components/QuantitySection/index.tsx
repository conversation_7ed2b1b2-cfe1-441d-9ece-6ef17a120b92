import { Form, Radio } from "antd";
import styles from "./index.module.scss";
import { observer } from "mobx-react";

type QuantitySectionProps = {
  value?: number;
  onChange?(newValue: number): void;
  options?: number[];
};

function QuantitySection({
  value,
  onChange,
  options = [1, 2, 3, 4],
}: QuantitySectionProps) {
  return (
    <Form.Item name="batchSize" noStyle>
      <Radio.Group className={styles.genNum}>
        {options.map((number) => {
          return (
            <Radio key={number} className="gen-num-radio" value={number}>
              <div className="gen-num-radio-label">{number}</div>
            </Radio>
          );
        })}
      </Radio.Group>
    </Form.Item>
  );
}

export default observer(QuantitySection);
