.radioGroup {
  width: 108px;
  height: 32px;

  &,
  * {
    outline: none !important;
    box-shadow: none !important;
  }

  .selection {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  :global {
    .ant-select {
      width: 100%;
      height: 100%;

      &.ant-select-focused {
        &> :global(.ant-select-selector) {
          border-color: #64748B !important;
        }
      }
    }

    .ant-select-dropdown {
      border: 1px solid var(--system-stroke-input, #22272E);
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30) !important;
      .ant-select-item.ant-select-item-option{
         &:hover{
           color: var(--system-content-primary, #FFF);
           background: var(--background-editorPopup-hover, #272C33);
           position: relative;
           &::after{
             display: block;
             content: '';
             position: absolute;
             top: 0;
             right: 0;
             bottom: 0;
             left: 0;
             border-radius: var(--radius-10, 10px);
             border: 1px solid var(--system-stroke-button, #323B48);

           }
         }
      }
      .custom-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .rc-virtual-list-scrollbar-thumb {
      width: 50% !important;
      background: var(--system-content-fifth, #303741) !important;
      right: -1px !important;
    }

    .ant-select-item {
      height: 34px;

      .ant-select-item-option-content {
        line-height: 24px;
      }
    }
  }

  &>.selector {
    &:global(.ant-select-focused.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer)) {
      &> :global(.ant-select-selector) {
        border-color: #ccc;
      }
    }

    :global {
      .ant-select-arrow {
        color: #6B7A8F;

        .suffix-icon {
          pointer-events: none !important;

          svg {
            width: 10px;
            height: 10px;
          }
        }
      }
    }
  }
}

.size-title {
  color: #ccc;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 8px;
}

.sizeInputNumber:global(.ant-input-number) {
  width: 66px;
  height: 32px;
  z-index: 1;
  box-shadow: none !important;

  :global {
    .ant-input-number-handler-wrap {
      z-index: 2;
    }

    .ant-input-number-input-wrap {
      position: relative;
      z-index: 1;
      width: 100%;
      height: 100%;

      .ant-input-number-input {
        width: 100%;
        height: 100%;
        padding: 0 8px 1px 22px;
        line-height: 100%;
      }

      &::after {
        display: block;
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--content-inputLable, #abadb2);
        font-size: 12px;
      }
    }
  }

  &:global(.width) {
    :global {
      .ant-input-number-input-wrap {
        &::after {
          content: 'W';
          color: var(--system-content-thirdary, #6B7A8F);
        }
      }
    }
  }

  &:global(.height) {
    :global {
      .ant-input-number-input-wrap {
        &::after {
          content: 'H';
          color: var(--system-content-thirdary, #6B7A8F);
        }
      }
    }
  }
}

.linkIcon {
  margin: 0 10px;

  svg {
    width: 12px;
    height: 12px;
    color: #abadb2;
  }
}