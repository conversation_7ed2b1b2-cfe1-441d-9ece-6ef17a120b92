import classNames from "classnames";
import { InputNumberLimitMax } from "@/components/InputNumber";
import {
  CheckBlack,
  ChevronDownBlack,
  DelinkBold,
  LinkBold,
} from "@meitu/candy-icons";
import { Flex, Form, Select } from "antd";
import styles from "./index.module.scss";
import { Dimension, DimensionType, dimensionList } from "@/constants/ratio";
import { PosterRatio } from "@/api/types/poster";
import { observer } from "mobx-react";

export interface RatioProps {
  onSizeChange?: (width: number, height: number) => void;
  min?: number;
  max?: number;
}

export const RatioSection = observer((props: RatioProps) => {
  const form = Form.useFormInstance();

  const picRatioWatch = Form.useWatch("picRatio", {
    form,
    preserve: true,
  });

  const widthWatch = Form.useWatch("width", {
    form,
    preserve: true,
  });
  const heightWatch = Form.useWatch("height", {
    form,
    preserve: true,
  });

  const handleWidthChange = (width: number, preWidth: number) => {
    updateCanvasSize(
      width,
      heightWatch,
      preWidth,
      heightWatch,
      picRatioWatch,
      picRatioWatch
    );
  };
  const handleHeightChange = (height: number, preHeight: number) => {
    updateCanvasSize(
      widthWatch,
      height,
      widthWatch,
      preHeight,
      picRatioWatch,
      picRatioWatch
    );
  };
  // 修改画布尺寸
  const updateCanvasSize = (
    width: number,
    height: number,
    preWidth: number,
    preHeight: number,
    ratio?: PosterRatio,
    preRatio?: PosterRatio
  ) => {
    form.setFieldValue("width", width);
    form.setFieldValue("height", height);
    form.setFieldValue("picRatio", ratio);

    props.onSizeChange?.(width, height);
  };

  // 切换比例
  const ratioChange = (
    val: PosterRatio | undefined,
    preVal: PosterRatio | undefined
  ) => {
    const temp = dimensionList.find((item) => item.value === val);
    const width =
      temp?.type === DimensionType.FIXED_ASPECT
        ? Number(temp?.size?.[0])
        : widthWatch;
    const height =
      temp?.type === DimensionType.FIXED_ASPECT
        ? Number(temp?.size?.[1])
        : heightWatch;
    const preWidth = form.getFieldValue("width");
    const preHeight = form.getFieldValue("height");

    updateCanvasSize(width, height, preWidth, preHeight, val, preVal);
  };

  return (
    <Flex justify="space-between">
      <Form.Item name="picRatio" noStyle>
        <RatioRadio
          dimensions={dimensionList}
          onChange={(val, preVal) => {
            ratioChange(val, preVal);
          }}
        />
      </Form.Item>

      <Flex justify="space-between">
        <Form.Item name="width" noStyle>
          <InputNumberLimitMax
            min={props.min}
            max={props.max}
            step={1}
            onChange={(val, preVal) => {
              handleWidthChange(val, Number(preVal));
            }}
            className={classNames(styles.sizeInputNumber, "width")}
            precision={0}
            changeOnBlur
            disabled={picRatioWatch !== PosterRatio.FREE}
            controls={false}
            formatter={(value) => (value ? Math.round(value) : 0).toString()}
          />
        </Form.Item>
        {picRatioWatch === PosterRatio.FREE ? (
          <DelinkBold className={styles.linkIcon} />
        ) : (
          <LinkBold
            style={{ cursor: "pointer" }}
            className={styles.linkIcon}
            onClick={() => {
              ratioChange(PosterRatio.FREE, picRatioWatch);
            }}
          />
        )}
        <Form.Item name="height" noStyle>
          <InputNumberLimitMax
            min={props.min}
            max={props.max}
            step={1}
            onChange={(val, preVal) => {
              handleHeightChange(val, Number(preVal));
            }}
            className={classNames(styles.sizeInputNumber, "height")}
            precision={0}
            changeOnBlur
            disabled={picRatioWatch !== PosterRatio.FREE}
            controls={false}
            formatter={(value) => (value ? Math.round(value) : 0).toString()}
          />
        </Form.Item>
      </Flex>
    </Flex>
  );
});

type RatioRadioProps = {
  dimensions?: Array<Dimension>;
  value?: PosterRatio;
  onChange: (val: PosterRatio, preVal?: PosterRatio) => void;
};
function RatioRadio({ dimensions, value, onChange }: RatioRadioProps) {
  return (
    <div className={styles.radioGroup} id="posterRatioSelect">
      <Select
        getPopupContainer={() => document.getElementById("posterRatioSelect")!}
        value={value}
        options={dimensions}
        optionRender={(option) => (
          <div className="custom-option">
            {option.label}
            {option.value === value && <CheckBlack />}
          </div>
        )}
        suffixIcon={<ChevronDownBlack className="suffix-icon" />}
        className={styles.selector}
        onChange={(val) => {
          onChange(val, value);
        }}
        dropdownStyle={{ width: "196px", height: "244px" }}
        listHeight={230}
      />
    </div>
  );
}
