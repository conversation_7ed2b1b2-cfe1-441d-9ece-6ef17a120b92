import { Input } from "antd";
import { forwardRef, useCallback } from "react";

const createNoSpace = <T extends React.ElementType<any>>(Component: T) => {
  const Comp = Component as any;

  // eslint-disable-next-line react/display-name
  return forwardRef<React.ComponentRef<T>, React.ComponentPropsWithoutRef<T>>(
    ({ onBlur, onChange, ...rest }, ref) => {
      const handleChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
          e.target.value = e.target.value.trimStart();
          onChange?.(e);
        },
        [onChange]
      );
      const handleBlur = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
          e.target.value = e.target.value.trim();
          onBlur?.(e);
          handleChange?.(e);
        },
        [handleChange, onBlur]
      );

      return (
        <Comp {...rest} ref={ref} onBlur={handleBlur} onChange={handleChange} />
      );
    }
  );
};

const InputNoSpace = createNoSpace(Input);
const TextareaNoSpace = createNoSpace(Input.TextArea);

export { InputNoSpace, TextareaNoSpace };
