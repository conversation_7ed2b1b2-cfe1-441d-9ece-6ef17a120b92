import { Input } from "antd";
import {
  ComponentPropsWithoutRef,
  ComponentRef,
  useEffect,
  useRef,
  useState,
} from "react";
import { TextArea } from "../TextArea";
import styles from "./index.module.scss";
import { fetchRandomPrompt } from "@/api/corpus";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import { AiSparkleBold } from "@meitu/candy-icons";
import classNames from "classnames";

import { CorpusFilter } from "@/types";
import { useI18n } from "@/locales/client";

const { TextArea: AntdTextArea } = Input;

type CorpusOption = {
  visible: boolean;
  corpusFilter: CorpusFilter;
  /**
   * 展开按钮的配置
   */
  expand?: {
    numbers: number;
  };
  /**
   * 触发按钮的文字 默认为“词库”
   * 如果哦设置了expand 该设置不生效
   */
  btnLabel?: string;
};
export interface TextAreaProps
  extends Omit<ComponentPropsWithoutRef<typeof AntdTextArea>, "onChange"> {
  // 智能补全textarea初始值
  complementInitValue?: string;
  onComplementChange?(value: string): void;
  onChange?(value: string): void;
  /** 是否有智能补全按钮 操作按钮 */
  hasComplementAction?: boolean;
  /** 是否有随机 prompt 操作按钮 */
  hasRandomPromptAction?: boolean;
  /** 是否有词库 操作按钮 */
  corpusAction?: CorpusFilter | CorpusOption;
  /** 词库按钮展示的文字 */
  /** 是否展示原来的随机按钮 */
  showInitRandomPrompt?: boolean;
}

export const SmartTextarea = ({
  className,
  complementInitValue,
  onComplementChange,
  onChange,
  hasRandomPromptAction,
  hasComplementAction = true,
  showInitRandomPrompt = true,
  corpusAction,
  ...restProps
}: TextAreaProps) => {
  const textareaRef = useRef<ComponentRef<typeof AntdTextArea>>(null);
  const [prompts, setPrompts] = useState<Array<string>>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const t = useI18n();

  /**
   * 随机生成 Prompt
   */
  const onRandomPrompt = () => {
    // 检查是否已经取完所有关键词，如果是，则重新从头开始
    if (currentIndex >= prompts.length) {
      setCurrentIndex(() => 0);
    }

    // 更新当前索引，以便下次点击可以获取下一个关键词
    setCurrentIndex((prevIndex) => prevIndex + 1);
    // 随机获取关键词
    const promptIndex = currentIndex >= prompts.length ? 0 : currentIndex;
    const prompt = prompts[promptIndex];

    onChange?.(prompt);
  };

  useEffect(() => {
    if (
      (hasRandomPromptAction && prompts.length === 0) ||
      showInitRandomPrompt
    ) {
      // 有配置随机 prompt 功能
      fetchRandomPrompt()
        .then((prompts) => {
          setPrompts(prompts);
        })
        .catch(defaultErrorHandler);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasRandomPromptAction, showInitRandomPrompt]);

  return (
    <TextArea
      ref={textareaRef}
      {...restProps}
      onChange={({ target: { value } }) => {
        onChange?.(value);
      }}
      extra={
        <section className={styles.actions}>
          {hasRandomPromptAction ? (
            <div
              className={classNames(styles.actionItem, styles.button)}
              onClick={onRandomPrompt}
            >
              <AiSparkleBold />
              <span className={styles.itemTitle}>{t("Random")}</span>
            </div>
          ) : null}
        </section>
      }
    />
  );
};
