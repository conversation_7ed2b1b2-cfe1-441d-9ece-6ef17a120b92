.textareaWrapper {
  position: relative;
  border-radius: 12px;
  border: 1px solid var(--system-stroke-input, #22272E);
  background: var(--system-background-input, #16171C);
  transition: all 0.2s ease-in-out;
  padding: 8px;

  &:hover {
    border-color: var(--system-stroke-input-focus, #64748B);
  }

  &.error {
    border-color: red;
  }

  &:focus,
  &:focus-within {
    border-color: var(--system-stroke-input-focus, #64748B);
  }
}

.textarea {
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &:global(.ant-input-affix-wrapper) {
    border: none !important;
    border-radius: 0px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    overflow: hidden;
    padding-bottom: 32px !important;

    &:focus-within {
      box-shadow: none !important;
    }

    textarea {
      border-radius: 0;
      padding: 0;
      padding-right: 0 !important;
      resize: none;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      transition: all 0.3s ease-in-out;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::placeholder {
        color: var(--system-content-fourth, #49576C);
      }

      &::-webkit-scrollbar {
        display: none;
      }

      // &::-webkit-scrollbar {
      //   width: 4px;
      //   height: 4px;
      // }

      // &::-webkit-scrollbar-track {
      //   background-color: transparent;
      // }
    }

    :global .ant-input-suffix {
      .ant-input-clear-icon {
        right: 0;
        bottom: 0;
        visibility: visible !important;
      }
    }

    :global(.ant-input-data-count) {
      bottom: 2px;
      right: 8px;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: var(--system-content-thirdary, #6A7B94);
    }
  }

  &:global(.ant-input-affix-wrapper-disabled) {
    border: none;

    &:hover {
      border: none;
    }
  }

  :global(.ant-input-disabled) {
    background-color: '#6A7B94' !important;
    border: none;
    color: '#6A7B94' !important;

    &:hover {
      border: none;
    }
  }

  &.layoutClear {
    :global(.ant-input-data-count) {
      right: 32px;
    }
  }
}

.remove {
  cursor: pointer;
  color: '#6A7B94';
  bottom: 2px;
  right: 0px;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  min-height: 16px;

  &:hover {
    color: '#6A7B94';
  }

  .divider {
    background: var(--system-stroke-input, #22272E);
    width: 2px;
    top: 0;
    border: none;
  }

  svg {
    color: #6A7B94
  }
}

.extra {
  position: absolute;
  bottom: 8px;
  left: 8px;
  z-index: 20;
}