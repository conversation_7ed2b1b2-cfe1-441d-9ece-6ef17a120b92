// 匹配中英文逗号
export const matchCommaReg = /[，,]/g;

export function hasChinese(str: string): boolean {
  return /[\u4e00-\u9fa5]/gm.test(str);
}
/**
 *  根据光标位置获取关联文本
 * @param str 文本
 * @param index 游标
 * @returns { text: string, startIndex: number, endIndex: number }
 */
export const getAssociationTextByCursor = (str: string, index: number) => {
  if (index === 0) return { text: "", startIndex: 0, endIndex: 0 };

  let matchedValue = "";

  let startIndex = index - 1;
  while (startIndex >= 0) {
    const char = str[startIndex];

    // 如果找到逗号，截取从逗号之后到 endIndex 的值
    if (char.match(matchCommaReg)) {
      matchedValue = str.substring(startIndex + 1, index).trim();
      break;
    } else if (startIndex === 0) {
      matchedValue = str.substring(startIndex, index).trim();
      break;
    }

    startIndex--;
  }

  return {
    text: matchedValue,
    startIndex: startIndex === 0 ? 0 : startIndex + 1,
    endIndex: index,
  };
};

/**
 *  根据光标位置获取关联文本的起始位置 和 结束位置
 * @param text
 * @param endIndex
 * @returns
 */
export const getSelectionRange = (text: string, endIndex: number) => {
  const { startIndex } = getAssociationTextByCursor(text, endIndex);

  return { startIndex, endIndex };
};
