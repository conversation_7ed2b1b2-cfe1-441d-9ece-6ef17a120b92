import { observer } from "mobx-react";
import styles from "./common.module.scss";
import { Drawer } from "antd";
import { useEffect } from "react";
import ImageParams from "./ImageParams";
import FrameParams from "./FrameParams";
import TextParams from "./TextParams";
import GroupParams from "./GroupParams";
import { ElementName, ImageOptions, IText } from "@meitu/whee-infinite-canvas";
import { useRootStore } from "@/app/[locale]/editor/_store";
import { useDelayMemo } from "@/app/[locale]/editor/_hooks/useDelayMemo";
import ModifyText from "./ModifyText";
import ModifyImage from "./ModifyImage";
import { HeaderAction } from "../../_store/headerAction";

export const tempBaseImage =
  "https://titan-h5.meitu.com/whee/assets/qrCode/invisible-graph.jpg";

const PosterParamsEditor = () => {
  const {
    paramsEditorStore,
    selectionStore,
    editorStatusStore,
    renderStore,
    headerActionStore,
  } = useRootStore();
  const { activeObjects, objectsOptions } = selectionStore;

  // console.log(
  //   "activeObjects",
  //   activeObjects,
  //   renderStore.render?._FC.getActiveObjects()
  // );

  // 是否是组和元素
  const isContainer =
    activeObjects?.length === 1 &&
    activeObjects[0]?._name_ === ElementName.CONTAINER;
  // 是否展示组参数栏
  const isShowGroup = activeObjects.length > 1 || isContainer;
  // 是否展示图片参数栏
  let isShowImage =
    activeObjects.length === 1 && activeObjects[0]._name_ === ElementName.IMAGE;
  // 是否展示文字参数栏
  const isShowText =
    activeObjects.length === 1 && activeObjects[0]._name_ === ElementName.TEXT;
  // 是否展示画板参数栏
  const isShowFrame =
    activeObjects.length === 1 && activeObjects[0]._name_ === ElementName.FRAME;
  const isShowModifyText =
    headerActionStore.activeHeaderAction === HeaderAction.ModifyText;
    // 激活改图状态
  const isShowImageEditParams =
    headerActionStore.activeHeaderAction === HeaderAction.ModifyImage;

  const renderImageEditParams = () => {
    if (isShowModifyText) {
      return <ModifyText />;
    }
    if (isShowImageEditParams) {
      return <ModifyImage />;
    }

    if (isShowImage || paramsEditorStore.isOpenImageParams) {
      return (
        <ImageParams
          activeObject={activeObjects?.[0]}
          objectsOptions={objectsOptions as ImageOptions[]}
        />
      );
    }
  };

  // openDrawer 1. 有图形被选中 2. 生图被打开
  const openDrawer = useDelayMemo(() => {
    if(!editorStatusStore.isClearScreenUI) {
      return activeObjects.length > 0 || paramsEditorStore.isOpenImageParams;
    }
    return false
  }, 100);

  useEffect(() => {
    // 有选中的时候，设置header生图打开false
    if (activeObjects.length) {
      paramsEditorStore.setIsOpenImageParams(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [objectsOptions]);

  // 鼠标按下，收起顶部生图唤起的参数栏
  useEffect(() => {
    const handleMouseDown = () => {
      paramsEditorStore.setIsOpenImageParams(false);
    };
    renderStore?.render?._FC.on("mouse:down", handleMouseDown);

    return () => {
      renderStore?.render?._FC.off("mouse:down", handleMouseDown);
    };
  }, [paramsEditorStore, renderStore?.render?._FC]);

  useEffect(() => {
    // 获取字体列表
    paramsEditorStore.getFamilyList();
    // 获取基础模型
    paramsEditorStore.getBaseModelList();
  }, [paramsEditorStore]);

  return (
    
      <Drawer
        open={openDrawer}
        mask={false}
        closable={false}
        placement={"left"}
        width={320}
        getContainer={false}
        rootClassName={styles.posterDrawer}
        afterOpenChange={() => {
          // 插入文字后可直接输入
          const el = activeObjects[0];
          if (!el) return;
          const isTextEditing =
            el._name_ === ElementName.TEXT && (el as IText).isEditing;

          if (isTextEditing) {
            // 获取焦点
            (el as IText).enterEditing();
            (el as IText).hiddenTextarea?.focus();
          }
        }}
      >
        <div className={styles.formScrollBox}>
          <div className={styles.formBox}>
            {renderImageEditParams()}
            {isShowText && (
              <TextParams activeObject={activeObjects[0] as IText} />
            )}
            {isShowFrame && (
              <FrameParams
                activeObject={activeObjects?.[0]}
                objectsOptions={objectsOptions}
              />
            )}
            {isShowGroup && <GroupParams activeObjects={activeObjects} />}
          </div>
        </div>
      </Drawer>
  );
};

export default observer(PosterParamsEditor);
