'use client';

import { QuestionMarkCircleBold } from '@meitu/candy-icons';
import styles from './index.module.scss';
import { Fragment } from 'react';
import useModal from './useModal';
import { useRootStore } from '@/app/[locale]/editor/_store';
import { observer } from 'mobx-react';

export const ShortcutHelper = observer(function () {
  const { editorStatusStore } = useRootStore();

  const { contextHolder, open } = useModal();
  const handleOpen = () => {
    open();
  };
  return !editorStatusStore.isClearScreenUI ? (
    <Fragment>
      <div className={styles.helper} onClick={handleOpen}>
        <QuestionMarkCircleBold />
      </div>
      {contextHolder}
    </Fragment>
  ) : null;
});
