import classNames from 'classnames';
import styles from './index.module.scss';
import { ChevronLeftNarrowBlack } from '@meitu/candy-icons';

export type DrawerProps = React.PropsWithChildren<{
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  disabled?: boolean;
}>;

export function Drawer({ children, open = true, onOpenChange, disabled }: DrawerProps) {
  const handleClickTrigger = () => {
    onOpenChange?.(!open);
  };

  return (
    <div className={classNames(styles.drawer, { open, disabled })}>
      <div className="drawer-trigger" onClick={handleClickTrigger}>
        <ChevronLeftNarrowBlack className="drawer-trigger-icon" />
      </div>
      <div className="drawer-content">{children}</div>
    </div>
  );
}
