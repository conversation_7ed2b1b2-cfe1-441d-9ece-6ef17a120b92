import { AIPoster } from '@/api/types/aiPoster/task';
import { useRootStore } from '@/app/[locale]/editor/_store';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { App, Button, Modal } from 'antd';
import { observer } from 'mobx-react';
import { useState } from 'react';
import { InfoCircleBoldFill } from '@meitu/candy-icons';
import styles from './index.module.scss';
import toast from '@/components/Toast';
import { useI18n } from '@/locales/client';
import buttonStyles from '@/styles/button.module.scss';
import useConfirmModal from '@/hooks/useConfirmModal';

export type RemoveItem = {
  image: AIPoster.ResultImage;
  task: AIPoster.Task;
};

export type RemoveModalProps = {
  removeItems?: RemoveItem[];
  open?: boolean;
  onFinish?: () => any;
  onCancel?: () => any;
};

function RemoveModal({
  removeItems,
  open,
  onFinish,
  onCancel
}: RemoveModalProps) {
  const [loading, setLoading] = useState(false);

  const { generateRecordStore } = useRootStore();
  const t = useI18n();

  const handleConfirmRemove = async () => {
    if (loading || !removeItems?.length) {
      return;
    }

    setLoading(true);
    let result = false;
    try {
      result = await generateRecordStore.delete(
        removeItems?.map((item) => {
          return {
            msgId: item.task.id,
            imageUrl: item.image.urlShort
          };
        })
      );
    } catch (e) {
      // defaultErrorHandler(e);
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }
    } finally {
      setLoading(false);
    }

    if (result) {
      toast.success(t("generate-records.delete-success"));
      onFinish?.();
    } else {
      toast.error(t("generate-records.delete-failed"))
    }
  };

  return (
    <Modal
      centered
      width={300}
      className={styles['remove-modal']}
      open={open}
      footer={false}
      destroyOnClose={false}
      maskClosable={false}
      closeIcon={null}
    >
      <section className={styles.content}>
        <InfoCircleBoldFill className="content-icon" />
        <h1>{t('generate-records.delete-modal-title')}</h1>
        <p>{t('generate-records.delete-modal-content')}</p>
        <div className="content-btns">
          <Button
            rootClassName={buttonStyles.secondary}
            onClick={() => {
              onCancel?.();
            }}
          >
            {t('Cancel')}
          </Button>
          <Button
            rootClassName={buttonStyles.main}
            loading={loading}
            onClick={() => {
              handleConfirmRemove?.();
            }}
          >
            {t('Confirm')}
          </Button>
        </div>
      </section>
    </Modal>
  );
}

export default observer(RemoveModal);
