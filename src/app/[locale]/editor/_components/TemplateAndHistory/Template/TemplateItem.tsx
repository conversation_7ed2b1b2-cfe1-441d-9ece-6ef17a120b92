import { TemplateItemType } from "@/api/types/poster";
import { toAtlasImageView2URL } from "@meitu/util";
import { Image } from "antd";
import classNames from "classnames";
import { Loading } from "@/components";
import styles from "./index.module.scss";
import Lot<PERSON> from "lottie-react";
import loadingAnimation from "@/assets/lottie/poster/imageLoading/data.json";
import { useTemplateExpo } from "@/hooks/useTrack/useTemplateExpo";
import { TemplateExposureLocation } from "@/types/tracking";

export type TemplateItemProps = {
  item: TemplateItemType;
  onClick?: () => any;
  disabled?: boolean;
  loading?: boolean;
};

export function TemplateItem({
  item,
  onClick,
  disabled,
  loading,
}: TemplateItemProps) {
  const cardExpo = useTemplateExpo<HTMLButtonElement>(item.id, item.categoryId, TemplateExposureLocation.Editor)
  return (
    <button
      ref={cardExpo}
      disabled={disabled}
      className={styles["template-item"]}
      onClick={onClick}
    >
      <Image
        rootClassName={classNames("template-item-image")}
        src={toAtlasImageView2URL(item.pic, {
          mode: "0" as any,
          width: 107 * window.devicePixelRatio,
          height: 141 * window.devicePixelRatio,
        })}
        placeholder={<Loading />}
        alt=""
        preview={false}
      />
      {loading && (
        <div className={"template-item-loading"}>
          <Lottie
            className={"loading-box"}
            animationData={loadingAnimation}
            autoplay
            loop
          />
        </div>
      )}
      <div className="template-item-name">{item.name}</div>
    </button>
  );
}
