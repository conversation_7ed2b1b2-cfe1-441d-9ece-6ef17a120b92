@import '../variables.scss';
@import '@/styles/variable.scss';

.template {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  // padding: 0 16px;
  overflow: hidden;

  :global {
    .ant-tabs {
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;

      .template-category-list {
        width: 100%;
        display: flex;
        flex: none;
        gap: 8px;
        overflow-x: auto;
        margin-bottom: $template-tabs-margin-bottom;

        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .template-category-tab {
          display: flex;
          flex: 0 0 auto;
          justify-content: center;
          align-items: center;
          height: $template-tab-btn-height;
          padding: 0 8px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          color: $system-content-secondary;
          user-select: none;
          cursor: pointer;

          &:first-child {
            margin-left: 16px;
          }

          &:last-child {
            margin-right: 16px;
          }

          &:disabled {
            cursor: not-allowed;
          }

          &.active {
            background: $system-background-thirdary;
          }
        }
      }

      .ant-tabs-content-holder {
        display: block;
        flex: 1 !important;
        min-height: 0 !important;
        width: calc(100% - 32px);
        overflow: hidden;

        .ant-tabs-content {
          height: 100%;

          .ant-tabs-tabpane {
            height: 100%;

            .template-list-container {

              height: 100%;
              overflow-x: hidden;
              overflow-y: auto;
              scrollbar-width: none;
              -ms-overflow-style: none;
              padding-bottom: $bottom-empty-height;
              box-sizing: border-box;

              &::-webkit-scrollbar {
                display: none;
              }

              .template-list-content {
                .template-row {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  margin-top: 12px;
                }
              }

              .template-list-tips {
                display: flex;
                justify-content: center;
                font-size: 12px;
                color: $system-content-secondary;
                margin-top: 8px;
              }
            }
          }

        }
      }
    }
  }
}

.template-item {
  width: 107px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 0;
  cursor: pointer;

  &:global(:disabled) {
    cursor: not-allowed;
  }

  :global {
    .template-item-loading {
      width: 107px;
      height: 141px;
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.30);
      backdrop-filter: blur(10px);
      border-radius: 6px;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;

      .loading-box {
        width: 18px;
        height: 18px;
      }
    }

    .template-item-image {
      width: 107px;
      height: 141px;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);

      &>img.ant-image-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: relative;
        z-index: -1;
      }
    }

    .template-item-name {
      width: 100%;
      max-height: 32px;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 13px;
      margin-top: 8px;
      color: $system-content-secondary;
    }
  }

  // &:hover {
  //   :global {
  //     .template-item-image {
  //       box-shadow: inset 0 0 0 2px $system-content-brand-primary,
  //         inset 0 0 0 4px #fff;
  //     }
  //   }
  // }
}