'use client';

import { useI18n } from "@/locales/client";
import { message } from "antd";
import { observer } from "mobx-react-lite";
import { useEffect, useRef } from "react";
import { useRootStore } from "@/app/[locale]/editor/_store";
import toast from "@/components/Toast";

const uploadingMessageKey = 'message-key/uploading'

function UploadLoading() {
  
  const t = useI18n();
  const { editorStatusStore } = useRootStore();
  
  const isUploadingShow = useRef(false);
  
  useEffect(() => {
    if (editorStatusStore.isUploading) {
      if (isUploadingShow.current) {
        return;
      }
      isUploadingShow.current = true;
      toast.show({
        type: "loading",
        title: t('Uploading...'),
        description: '',
        duration: 0,
        key: uploadingMessageKey,
      });
    } else {
      if (isUploadingShow.current) {
        toast.destroy(uploadingMessageKey);
        isUploadingShow.current = false;
      }
    }
  }, [editorStatusStore.isUploading]);

  useEffect(() => {
    return () => {
      if (isUploadingShow.current) {
        toast.destroy(uploadingMessageKey);
        isUploadingShow.current = false;
      }
    }
  }, []);

  return null;
}

export default observer(UploadLoading);