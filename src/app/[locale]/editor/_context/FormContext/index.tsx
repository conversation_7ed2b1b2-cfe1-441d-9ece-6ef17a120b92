'use client'

import { Form, FormInstance } from 'antd';
import {
  PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useRef
} from 'react';

type FormContextType = {
  setForm(form: FormInstance | null): void;
  getForm(): FormInstance | null;
};

function functionPlaceHolder() {
  throw new Error('请实现FormContext的函数');
}

const FormContext = createContext<FormContextType>({
  setForm: functionPlaceHolder,
  getForm: functionPlaceHolder as () => FormInstance
});

type FormContextProviderProps = PropsWithChildren;

export function FormContextProvider({ children }: FormContextProviderProps) {
  const formRef = useRef<FormInstance | null>(null);

  function setForm(form: FormInstance) {
    formRef.current = form;
  }

  function getForm() {
    return formRef.current;
  }

  return (
    <FormContext.Provider value={{ setForm, getForm }}>
      {children}
    </FormContext.Provider>
  );
}

function useFormContext() {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('请在组件树中添加FormContextProvider');
  }

  return context;
}

/**
 * 组件挂载时 将form实例组册到context
 */
export function useParamsForm() {
  const { setForm } = useFormContext();
  const [form] = Form.useForm();
  useEffect(() => {
    setForm(form);

    return () => {
      setForm(null);
    };
  }, [form, setForm]);

  return { form };
}

/**
 * 获取参数表单
 */
export function useGetParamsForm() {
  const { getForm } = useFormContext();
  return getForm;
}
