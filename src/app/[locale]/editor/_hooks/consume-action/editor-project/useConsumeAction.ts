import { useStore } from "@/contexts/StoreContext";
import { Action } from "@/stores/TransferAction/types";
import { FabricObject, Render } from "@meitu/whee-infinite-canvas";
import { useRootStore } from "../../../_store";
import { EditorStatusStore } from "../../../_store/editorStatus";
import { locateTemplate } from "@/utils/locateTemplate";
import { ConfigStore } from "../../../_store/config";
import { insertImageToViewPortCenter } from "../../../_utils/insertImage";
import { useSaveCanvas } from "../../useSaveProjectHistory";
import { saveElements } from "../../../_utils/saveProjectHistory";
import zoomToFitCanvas from "../../../_utils/zoomToFitCanvas";
import { useInsertTemplateWithGuide } from "../../useInsertTemplate";
import { TemplateItemType } from "@/api/types/poster";

export function useConsumeAction() {
  const { transferActionStore, userStore } = useStore();
  const rootStore = useRootStore();
  const { editorStatusStore, configStore, renderStore, projectsStore } =
    rootStore;

  const insertTemplateWithGuide = useInsertTemplateWithGuide({
    configStore,
    renderStore,
    userStore,
  });

  const saveAllElements = async () => {
    const render = renderStore.render;
    const projectId = projectsStore.activeProjectId;
    const targets = render?._FC.getObjects();
    if (!render || !projectId || !targets?.length) {
      return;
    }

    await saveElements({
      render,
      targets,
      projectId,
    });
  };

  return function consumeAction() {
    const render = renderStore.render;
    if (!render) {
      return;
    }

    const action = transferActionStore.consumeAction(
      Action.Consumer.EditorProject
    );
    return Promise.allSettled(
      action.map((a) => {
        switch (a.type) {
          case Action.EditorProject.Type.AddTemplate: {
            const payload =
              a.payload as Action.EditorProject.AddTemplatePayload;
            return consumeAddTemplateAction(payload, {
              render,
              editorStatusStore,
              saveAllElements,
              insertTemplateWithGuide,
            });
          }
          case Action.EditorProject.Type.LocateTemplate: {
            const payload =
              a.payload as Action.EditorProject.LocateTemplatePayload;
            return consumeLocateTemplateAction(payload, {
              editorConfigStore: configStore,
              editorStatusStore,
            });
          }
          case Action.EditorProject.Type.AddImage: {
            const payload = a.payload as Action.EditorProject.AddImagePayload;
            return consumeAddImageAction(payload, {
              render,
              editorStatusStore,
              saveAllElements,
            });
          }
        }
      })
    );
  };
}

type ConsumeAddTemplateActionDeps = {
  render: Render;
  editorStatusStore: EditorStatusStore;
  saveAllElements: () => Promise<void>;
  insertTemplateWithGuide: (
    template: TemplateItemType
  ) => Promise<Array<FabricObject> | undefined>;
};
async function consumeAddTemplateAction(
  payload: Action.EditorProject.AddTemplatePayload,
  {
    render,
    editorStatusStore,
    saveAllElements,
    insertTemplateWithGuide,
  }: ConsumeAddTemplateActionDeps
) {
  editorStatusStore.setShowCreate(false);
  const elements = await insertTemplateWithGuide(payload.detail);
  const frame = elements && elements[0];
  if (frame) {
    render._FC.setActiveObject(frame);
    zoomToFitCanvas(render);
    // render.backToOriginPosition({ target: elements.frame });
    await saveAllElements();
  }
}

type ConsumeLocateTemplateActionDeps = {
  editorStatusStore: EditorStatusStore;
  editorConfigStore: ConfigStore;
};
function consumeLocateTemplateAction(
  payload: Action.EditorProject.LocateTemplatePayload,
  { editorConfigStore, editorStatusStore }: ConsumeLocateTemplateActionDeps
) {
  editorStatusStore.setShowCreate(false);
  return locateTemplate({
    template: payload.detail,
    editorConfigStore,
    editorStatusStore,
  });
}

type ConsumeAddImageActionDeps = {
  render: Render;
  editorStatusStore: EditorStatusStore;
  saveAllElements: () => Promise<void>;
};
async function consumeAddImageAction(
  payload: Action.EditorProject.AddImagePayload,
  { render, editorStatusStore, saveAllElements }: ConsumeAddImageActionDeps
) {
  editorStatusStore.setShowCreate(false);
  const image = await insertImageToViewPortCenter({ src: payload.url }, render);
  render.backToOriginPosition({ target: image });
  await saveAllElements();
}
