import { useEffect, useRef, useState } from "react";
import { driver, Driver } from "driver.js";
import { EditorStatusStore } from "../../_store/editorStatus";
import "./index.scss";
import { useI18n } from "@/locales/client";
import { UserStore } from "@/stores/UserStore";

const TutorialsKey = "poster-editor/tutorials";

type DescriptionMap = {
  [uid in number]: boolean;
};

// 获取存储
function getCreateStorageMap() {
  const descriptionStr = localStorage.getItem(TutorialsKey);
  if (!descriptionStr) {
    return;
  }

  let description: null | DescriptionMap = null;
  try {
    description = JSON.parse(descriptionStr) as DescriptionMap;
  } catch (e) {
    if (process.env.NODE_ENV === "development") {
      console.error("JSON解析失败", e);
      return;
    }
  }

  return description;
}

export const useTutorials = (
  editorStatusStore: EditorStatusStore,
  userStore: UserStore,
  loading: boolean,
  onInitialized?: (driver: Driver) => void
) => {
  const t = useI18n();
  const uid = userStore.UID;
  const driverObjRef = useRef<Driver | null>(null);

  const [isFromCreate, setIsFromCreate] = useState(false);

  useEffect(() => {
    if (editorStatusStore.showCreate) {
      setIsFromCreate(editorStatusStore.showCreate);
    }

    const descriptionMap = getCreateStorageMap() ?? {};
    if (!editorStatusStore || !uid) return;

    // 是从创建页进入，非loading，当前uid没有打开过
    if (
      !editorStatusStore.showCreate &&
      isFromCreate &&
      !loading &&
      descriptionMap[uid] !== true
    ) {
      initTutorials();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editorStatusStore, editorStatusStore.showCreate, loading]);

  const handleClose = () => {
    driverObjRef.current?.destroy();

    if (!uid) {
      return;
    }
    const descriptionMap = getCreateStorageMap() ?? {};
    descriptionMap[uid] = true;
    localStorage.setItem(TutorialsKey, JSON.stringify(descriptionMap));
  };

  const initTutorials = () => {
    const driverObj = driver({
      animate: true,
      showButtons: ["next"],
      showProgress: true,
      allowClose: false,
      nextBtnText: t("tutorial.Next"),
      doneBtnText: t("tutorial.Got it"),
      popoverClass: "poster-tutorials-popover",
      stageRadius: 8,
      stagePadding: 2,
      popoverOffset: 10,
      onDestroyed: () => {
        handleClose();
      },
      steps: [
        {
          element: "#insert-frame",
          popover: {
            title: t("tutorial.Poster generator"),
            description: t(
              "tutorial.Add text and images within the frame, and use prompts to help create a superior poster."
            ),
            progressText: "1/2",
            side: "bottom",
            align: "center",
          },
        },
        {
          element: "#generate-image",
          popover: {
            title: t("tutorial.Image generator"),
            description: t(
              "tutorial.Enter a prompt, then select a model and style to create your image."
            ),
            progressText: "2/2",
            side: "bottom",
            align: "center",
          },
        },
      ],
    });
    driverObjRef.current = driverObj;
    // 初始化完成后调用回调
    onInitialized?.(driverObj);
    driverObj.drive();
  };

  return driverObjRef;
};
