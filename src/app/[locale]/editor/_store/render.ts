import { makeAutoObservable, observable } from 'mobx';
import { RootStore } from '.';
import {
  HistoryPlugins,
  Render,
  RenderStyle,
  Selection,
  MouseAction,
  HotKey
} from '@meitu/whee-infinite-canvas';

export type RenderStoreType = {
  render: Render | null;
  renderSelection: Selection | null;
  renderStyle: RenderStyle | null;
  historyPlugins: HistoryPlugins | null;
  renderMouseAction: MouseAction | null;
  renderHotkey: HotKey | null;
};

export class RenderStore implements RenderStoreType {
  rootStore: RootStore;
  render: Render | null = null;
  renderSelection: Selection | null = null;
  renderStyle: RenderStyle | null = null;
  historyPlugins: HistoryPlugins | null = null;
  renderMouseAction: MouseAction | null = null;
  renderHotkey: HotKey | null = null;

  configRenderStore(renderStore: RenderStoreType) {
    this.render = renderStore.render;
    this.renderSelection = renderStore.renderSelection;
    this.renderStyle = renderStore.renderStyle;
    this.historyPlugins = renderStore.historyPlugins;
    this.renderMouseAction = renderStore.renderMouseAction;
    this.renderHotkey = renderStore.renderHotkey;
  }

  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      render: observable.ref,
      renderSelection: observable.ref,
      renderStyle: observable.ref,
      historyPlugins: observable.ref,
      renderMouseAction: observable.ref,
      renderHotkey: observable.ref
    });

    this.rootStore = rootStore;
  }
}
