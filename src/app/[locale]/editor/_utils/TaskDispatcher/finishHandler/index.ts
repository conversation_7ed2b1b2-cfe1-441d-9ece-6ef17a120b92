import { AIPoster } from "@/api/types/aiPoster/task";
import { HandleFinishParams } from "./types";
import { handleCutoutFinish } from "./cutout";
import { handleUpscalerFinish } from "./upscaler";
import { handlePosterFinish } from "./poster";
import { handleTextToImageFinish } from "./textToImage";
import { handleImageToImageFinish } from "./imageToImage";
import { handleEraserFinish } from "./eraser";
import { handleTextEditFinish } from "./textEdit";
import { handleImageEditFinish } from "./imageEdit";

export function handleFinish({ type, ...params }: HandleFinishParams) {
  switch (type) {
    case AIPoster.TaskCategory.Upscaler:
      return handleUpscalerFinish(params);
    case AIPoster.TaskCategory.Cutout:
      return handleCutoutFinish(params);
    case AIPoster.TaskCategory.Poster:
      return handlePosterFinish(params);
    case AIPoster.TaskCategory.PosterImage:
      return handlePosterFinish(params);
    case AIPoster.TaskCategory.Text2Image:
      return handleTextToImageFinish(params);
    case AIPoster.TaskCategory.Image2Image:
      return handleImageToImageFinish(params);
    case AIPoster.TaskCategory.Eraser:
      return handleEraserFinish(params);
    case AIPoster.TaskCategory.TextEdit:
      return handleTextEditFinish(params);
    case AIPoster.TaskCategory.ImageEdit:
      return handleImageEditFinish(params);
  }

  return Promise.resolve();
}
