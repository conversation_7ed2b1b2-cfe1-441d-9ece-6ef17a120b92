import { ProjectDetailResponse } from "@/api/types/aiPoster/project";
import { ContainerElementOptions, ElementOptions, Render } from "@meitu/whee-infinite-canvas";

type BackfillEditorParamsOptions = {
  render: Render
}

export function backfillEditorParams(
  layers: Exclude<ProjectDetailResponse['editorParams'], undefined>,
  {
    render
  }: BackfillEditorParamsOptions
) {

  let editorParams = layers.map((v) => {
    return JSON.parse(v.params);
  });

  const paramsMap: Record<string, ElementOptions> = editorParams.reduce(
    (prev, current) => {
      prev[current._id_] = current;
      return prev;
    },
    {} as Record<string, ElementOptions>
  );

  Object.values(paramsMap).forEach((v: ElementOptions) => {
    if (v._parent_id_ && paramsMap[v._parent_id_]) {
      const parent = paramsMap[v._parent_id_] as ContainerElementOptions;
      if (!parent.children) {
        parent.children = [];
      }
      const index = parent.children?.findIndex(
        (child) => (child?.zIndex ?? 0) > (v?.zIndex ?? 0)
      );
      if (index === -1) {
        parent.children.push(v);
      } else {
        parent.children.splice(index, 0, v);
      }
    }
  });

  editorParams = Object.values(paramsMap).filter((v) => !v._parent_id_);
  editorParams.sort((a, b) => a.zIndex - b.zIndex);
  return render.setSceneData(editorParams);
}