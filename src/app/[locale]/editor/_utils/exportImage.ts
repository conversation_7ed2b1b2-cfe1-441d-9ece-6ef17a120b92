import { ElementName, Group, Render } from '@meitu/whee-infinite-canvas';
import watermarkSrc from '@/assets/images/watermark.png';
import { createImage } from '@/utils/cropImage';
import { downloadFile } from '@/utils/blob';
import { v4 as uuid } from 'uuid';
import { canvasToBlob } from '@/utils/canvasToBlob';


const baseScale = 4;

/**
 * 水印放在一个512 * 4(水印是4倍图)的正方形中
 */
const watermarkSquare = 512 * baseScale;


const relativePictureSize = 512;

/**
 * 水印图在参照图中的尺寸
 */
const watermarkSquareInRelativePicture = 120;

// 绘制时，将watermarkSquare进行缩放
// 参照的比例：将watermarkSquare放置在relativePictureSize大小的参照图中，水印图的大小为watermarkSquareInRelativePicture

// 水印正方形相对于图片短边的缩放为
const watermarkScale = watermarkSquareInRelativePicture / relativePictureSize;


type Dimension = readonly [number, number];

export type WatermarkOptions = {
  position:
    | 'NorthWest'
    | 'North'
    | 'NorthEast'
    | 'West'
    | 'Center'
    | 'East'
    | 'SourceWest'
    | 'South'
    | 'SouthEast';

  /**
   * 基于512 x 512大小的图片的偏移量
   */
  offset: Dimension;
};

export type ExportImageParams = {
  render: Render;
  shape: Group;
  watermark?: WatermarkOptions;
};
export async function exportImage({
  render,
  shape,
  watermark,
}: ExportImageParams) {
  let canvas = null as null | HTMLCanvasElement;
  try {
    canvas = await render.Actions.exportElementToCanvas(shape, {
      includeType: [ ElementName.IMAGE, ElementName.TEXT],
      exportType: 'png',
      multiplier: 1
    });
  } catch (e) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('图形导出失败', e);
    }
  }
  if (!canvas) {
    return null;
  }

  if (watermark) {
    try {
      await attachWatermark(canvas, watermark);
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('绘制水印失败', e);
      }
      return null;
    }
  }

  try {
    return await canvasToBlob(canvas, { type: "image/png", quality: 1 });
  } catch(e) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('导出blob失败', e);
    }
    return null;
  }
}

async function attachWatermark(
  canvas: HTMLCanvasElement,
  watermark: WatermarkOptions
) {
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('canvas获取context失败');
  }
  const watermarkImage = await createImage(watermarkSrc.src);
  const canvasDimension = [canvas.width, canvas.height] as const;
  const watermarkImageDimension = [
    watermarkImage.naturalWidth,
    watermarkImage.naturalHeight
  ] as const;
  const drawScale = calcDrawScale(canvasDimension);
  const drawDimension = [
    drawScale * watermarkImageDimension[0],
    drawScale * watermarkImageDimension[1]
  ] as const;
  const drawPosition = calcDrawPosition(canvasDimension, drawDimension, watermark, drawScale);

  ctx.drawImage(
    watermarkImage,
    0,
    0,
    watermarkImageDimension[0],
    watermarkImageDimension[1],
    drawPosition[0],
    drawPosition[1],
    drawDimension[0],
    drawDimension[1]
  );
}

function calcDrawScale(
  imageDimension: Dimension
) {

  // 根据短边算出水印正方形在图片中的尺寸
  const watermarkSquareInImage = Math.min(imageDimension[0], imageDimension[1]) * watermarkScale;

  // 算出水印的缩放
  return (watermarkSquareInImage / watermarkSquare);
}

function calcDrawPosition(
  imageDimension: Dimension,
  watermarkDimension: Dimension,
  watermark: WatermarkOptions,
  drawScale: number,
) {
  let base = [0, 0] as [number, number];
  let offset = watermark.offset.map(value => value * baseScale * drawScale) as [number, number];

  const top = 0;
  const bottom = imageDimension[1] - watermarkDimension[1];
  const verticalCenter = bottom / 2;

  const left = 0;
  const right = imageDimension[0] - watermarkDimension[1];
  const horizontalCenter = right / 2;

  switch (watermark.position) {
    case 'NorthWest': {
      base = [left, top];
      break;
    }

    case 'North': {
      base = [horizontalCenter, top];
      break;
    }

    case 'NorthEast': {
      base = [right, top];
      offset = [-offset[0], offset[1]];
      break;
    }

    case 'West': {
      base = [left, verticalCenter];
      break;
    }

    case 'Center': {
      base = [horizontalCenter, verticalCenter];
      break;
    }

    case 'East': {
      base = [right, verticalCenter];
      offset = [-offset[0], offset[1]];
      break;
    }

    case 'SourceWest': {
      base = [left, bottom];
      offset = [offset[0], -offset[1]];
      break;
    }

    case 'South': {
      base = [horizontalCenter, bottom];
      offset = [offset[0], -offset[1]];
      break;
    }

    case 'SouthEast': {
      base = [right, bottom];
      offset = [-offset[0], -offset[1]];
      break;
    }
  }

  return [base[0] + offset[0], base[1] + offset[1]];
}
