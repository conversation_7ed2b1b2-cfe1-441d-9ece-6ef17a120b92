import { createImage, createText, <PERSON>ementName, FabricObject, Render } from "@meitu/whee-infinite-canvas"
import { ElementItem, GuideDescription } from "./types";
import assets from './assets-index.json';

type FillGuideElementsOptions = {
  render: Render,
  stepWidth?: number,
  scale?: number,
  titleStepGap?: number,
  stepArrowGap?: number,
}


export async function fillGuideElements(
  description: GuideDescription,
  {
    render,
    stepWidth = 220,
    stepArrowGap = 24,
    scale = 4
  }: FillGuideElementsOptions
) {

  const fillTitle = async () => {

    const title = description.title;
    const titleText = title.text;
    const titleIcon = title.icon;
  
    const titleIconElement = await createImage('', {
      src: titleIcon.src,
    });

    titleIconElement.set({
      left: titleIconElement.getScaledWidth() / 2,
      top: titleIconElement.getScaledHeight() / 2,
    });
  
    const titleTextElement = createText('', {
      text: titleText.content,
      fill: titleText.color,
      fontSize: titleText.fontSize * scale,
      fontFamily: titleText.fontFamily,
    });
  
    const titleWidth = titleTextElement.getScaledWidth();
    titleTextElement.set({
      left: titleIconElement.left + titleIconElement.getScaledWidth() / 2 + title.gap * scale + titleWidth / 2,
      top: titleIconElement.top,
    });
  
    render._FC.add(titleIconElement, titleTextElement);
  
    const titleHeight = Math.max(titleIconElement.getScaledHeight(), titleTextElement.getScaledHeight());

    return { titleHeight };
  }

  const { titleHeight } = await fillTitle();
  const stepsOffset = titleHeight + (description.steps.topOffset ?? 0) * scale;

  const fillStep = async (index: number) => {

    
    // 1. 绘制“step i”
    const step = description.steps.list[index];
    const elements = Array.isArray(step) ? step : step.elements;
    const stepLeftOffset = Array.isArray(step) ? description.steps.gap : (step.offsetLeft ?? description.steps.gap);
    
    const leftOffset = (stepWidth + stepLeftOffset) * scale * index;

    const stepIcon = await createImage('', {
      src: assets['common']['step-icons'][index],
    });

    stepIcon.set({
      left: leftOffset + stepIcon.getScaledWidth() / 2,
      top: stepsOffset + stepIcon.getScaledHeight() / 2,
    });
    render._FC.add(stepIcon);

    // 2. 不是最后一步 绘制箭头
    if (index < description.steps.list.length - 1) {
      const arrow = await createImage('', {
        src: assets['common']['step-arrow'],
      });

      arrow.set({
        left: stepIcon.left + stepIcon.getScaledWidth() / 2 + stepArrowGap * scale + arrow.getScaledWidth() / 2,
        top: stepIcon.top,
      });

      render._FC.add(arrow);
    }

    // 3. 绘制每一步的元素
    let topOffset = stepIcon.getScaledHeight() + stepsOffset;
    for(const item of elements) {
      
      let element = null as null | FabricObject;
      if (item.type === ElementName.TEXT) {
        element = createText('', {
          text: item.content,
          fill: item.color,
          fontSize: item.fontSize * scale,
          fontFamily: item.fontFamily,
        });

        // console.log(element);
      }

      if (item.type === ElementName.IMAGE) {
        element = await createImage('', {
          src: item.src,
        })
      }

      if (element) {

        const itemTopGap =  (item.topOffset ?? 0) * scale;

        element.set({
          left: element.getScaledWidth() / 2 + leftOffset,
          top: topOffset + element.getScaledHeight() / 2 + itemTopGap,
        });

        topOffset += element.getScaledHeight() + itemTopGap;

        render._FC.add(element);
      }
    }
  }

  await Promise.allSettled(description.steps.list.map((_, index) => fillStep(index)));
}