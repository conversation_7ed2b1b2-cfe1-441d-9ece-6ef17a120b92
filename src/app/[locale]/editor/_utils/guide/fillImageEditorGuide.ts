import { GuideDescription } from "./types";
import assets from "./assets-index.json";
import { fillGuideElements } from "./fillGuideElements";
import { ElementName, Render } from "@meitu/whee-infinite-canvas";
import { tipsColor, titleColor } from "./constants";
import { useI18n } from "@/locales/client";

type FillImageEditorOptions = {
  render: Render;
  t: ReturnType<typeof useI18n>;
  defaultFont: string;
};
export async function fillImageEditorGuide({
  render,
  t,
  defaultFont,
}: FillImageEditorOptions) {
  const bgRemovalGuideDesc: GuideDescription = {
    title: {
      icon: {
        src: assets["image-editor"]["title-icon"],
      },
      text: {
        content: t("guide.image-editor.title"),
        fontSize: 24,
        fontFamily: defaultFont,
        color: titleColor,
      },
      gap: 12,
    },

    steps: {
      list: [
        [
          {
            type: ElementName.TEXT,
            content: t("guide.image-editor.step1"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["common"]["image-icon"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.image-editor.step2"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["image-editor"]["image-editor-icon"],
            topOffset: 16,
          },
          {
            type: ElementName.IMAGE,
            src: assets["image-editor"]["preview"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.image-editor.step3"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["image-editor"]["content-input"],
            topOffset: 16,
          },
        ],
        [
          {
            type: ElementName.TEXT,
            content: t("guide.image-editor.step4"),
            fontSize: 14,
            fontFamily: defaultFont,
            color: tipsColor,
            topOffset: 8,
          },
          {
            type: ElementName.IMAGE,
            src: assets["image-editor"]["result"],
            topOffset: 16,
          },
        ],
      ],
      gap: 48,
      topOffset: 40,
    },
  };

  return await fillGuideElements(bgRemovalGuideDesc, { render });
}
