import { AIPoster } from "@/api/types/aiPoster/task";
import { createImage } from "@/utils/cropImage";
import {
  hasAlphaExecuteInWorker,
  upscalerReplaceAlpha,
} from "./processImageWithWorker";
import { uploaderFunc } from "./upload";
import { createPureUpload } from "@/utils/uploader";

export const getImageEditStatus = (taskCategory: AIPoster.TaskCategory) => {
  switch (taskCategory) {
    case AIPoster.TaskCategory.Cutout:
      return {
        hasCutout: true,
        hasUpscale: false,
      };
    case AIPoster.TaskCategory.Upscaler:
      return {
        hasCutout: false,
        hasUpscale: true,
      };
  }
};

// 判断图片是否有透明通道
export const isHasAlpha = async (url: string) => {
  let img: HTMLImageElement | null = null;
  try {
    img = await createImage(url);
  } catch (e) {
    if (process.env.NODE_ENV === "development") {
      // console.log(`图片加载错误 url[${url}]`, e);
    }
    return null;
  }

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx || !img) {
    return null;
  }

  // 设置 canvas 尺寸与图片一致
  canvas.width = img.width;
  canvas.height = img.height;

  // 将图片绘制到 canvas 上
  ctx.drawImage(img, 0, 0);

  // 获取图片像素数据
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const pixels = imageData.data;

  // 检查透明通道
  let hasTransparency = false;
  for (let i = 3; i < pixels.length; i += 4) {
    if (pixels[i] < 255) {
      hasTransparency = true;
      break;
    }
  }

  return hasTransparency;
  // TODO
  // return await hasAlphaExecuteInWorker(url);
};

// 原图，结果图，替换Alpha通道
export const _replaceAlpha = async (
  hdImgSrc: string,
  originImgSrc: string
): Promise<{ previewUrl: string; url: string } | undefined> => {
  const blob = await upscalerReplaceAlpha(hdImgSrc, originImgSrc);

  if (!blob) return;

  const upload = createPureUpload();

  try {
    const res = await upload({
      file: blob,
    });
    if (!res.result) return;

    return {
      previewUrl: res.result.previewUrl || "",
      url: res.result.url,
    };
  } catch (err) {
    // console.log('_replaceAlpha', err);
  }
};
