import { createImage, Point, Render } from "@meitu/whee-infinite-canvas";

// 上传图片，拖入上传
export const insertImageToViewPortCenter = (
  params: {
    src: string;
    width?: number;
    height?: number;
    _custom_data_history_?: Record<string, any>;
  },
  render: Render,
  dropEvent?: React.DragEvent<HTMLDivElement> | null
) => {
  return createImage("", { ...params }).then((group) => {
    if (dropEvent) {
      const p = render._FC.getScenePoint(dropEvent as any);
      group.setXY(new Point(p.x, p.y));
      render.add(group);
    } else {
      render.addToViewPortCenter(group);
    }

    if (params._custom_data_history_) {
      group.set({
        _custom_data_history_: params._custom_data_history_,
      });
    }

    render?._FC.setActiveObject(group);

    return group;
  });
};

// 画布插入图片，不会自动选中
export const insertImageByOptions = (
  params: {
    src: string;
    width?: number;
    height?: number;
    left?: number;
    top?: number;
    scaleX?: number;
    scaleY?: number;
  },
  render: Render
) => {
  return createImage("", { ...params }).then((group) => {
    render.add(group);

    return group;
  });
};
