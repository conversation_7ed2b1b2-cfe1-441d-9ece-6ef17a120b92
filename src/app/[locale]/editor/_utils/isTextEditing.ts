import { <PERSON><PERSON><PERSON><PERSON>, Render } from '@meitu/whee-infinite-canvas';

export const isTextEditing = (render: Render | null) => {
  if (!render) return false;
  const targets = render?._FC.getActiveObjects();
  if (!targets?.length) return false;
  if (
    targets?.length === 1 &&
    targets[0].get('_name_') === ElementName.TEXT &&
    targets[0].get('isEditing') === true
  ) {
    return true;
  }
  return false;
};
