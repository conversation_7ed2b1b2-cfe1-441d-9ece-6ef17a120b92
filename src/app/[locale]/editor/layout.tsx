import { GuideCreateProjectContextProvider } from "./_context/GuideCreateProjectContext";
import { RootStoreProvider } from "./_store";
import { FormContextProvider } from "@/app/[locale]/editor/_context/FormContext";
import IntercomFeedBack from "@/components/Intercom";
export default function EditorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <RootStoreProvider>
      <GuideCreateProjectContextProvider>
        <FormContextProvider>
          {children}
          <IntercomFeedBack  />
        </FormContextProvider>
      </GuideCreateProjectContextProvider>
    </RootStoreProvider>
  );
}
