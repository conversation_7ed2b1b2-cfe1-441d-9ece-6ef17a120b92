"use client";

import { useEffect } from "react";
import { useRootStore } from "../../_store";
import { fetchProjectDetail } from "@/api/aiPoster/project";
import { observer } from "mobx-react-lite";
import { useConsumeAction } from "../../_hooks/consume-action/editor-project/useConsumeAction";
import { notFound, useRouter } from "next/navigation";
import SubscribeTips from "./_components/SubscribeTips";
import { useStore } from "@/contexts/StoreContext";
import { useQuestionModal } from "@/contexts/QuestionContext";
import { useI18n } from "@/locales/client";
import _ from 'lodash';

function Project({ params }: { params: { id: string } }) {
  const { id } = params;
  const { userStore } = useStore();
  const { projectsStore, editorStatusStore, renderStore, configStore } =
    useRootStore();
  const consumeAction = useConsumeAction();
  configStore.config?.name

  const t = useI18n();

  const { open: openQuestionModal } = useQuestionModal();
  const router = useRouter();
  const projectId = parseInt(id);
  if (!projectId) {
    notFound();
  }


  useEffect(() => {
    if (editorStatusStore.initFinish) {
      return;
    }

    let ignore = false;
    editorStatusStore.globalEnable();
    projectsStore
      .fetchProjects()
      // 判断当前项目id是否是一个合法的项目
      .then(() => {
        return fetchProjectDetail({ projectId });
      })
      // 选择一个项目并加载
      .then((projectDetail) => {
        return projectsStore.setActiveProject(projectDetail);
      })
      .then(consumeAction).then(() => {
        let key = 'hasQuestionnaire' + userStore?.currentUser?.id;
         
        if(localStorage.getItem(key) !== 'true') {
          openQuestionModal();
        }
      }) 
      .catch((e) => {
        if (process.env.NODE_ENV === "development") {
          console.log(e);
        }

        if (ignore) {
          return;
        }

        router.replace('/editor/project');
      })
      .finally(() => {
        if (ignore) {
          return;
        }
        editorStatusStore.setInitFinish(true);
      });

    return () => {
      ignore = true;
    }
  }, []);

  useEffect(() => {

    // 更新项目是否可编辑啊
    const updateProjectsEditable = () => {
      projectsStore.refreshProjects();
    }
    userStore.addUpdateUserInfoListener(updateProjectsEditable);

    return () => {
      userStore.removeUpdateUserInfoListener(updateProjectsEditable);
    }
  }, [projectsStore, userStore])

  return <SubscribeTips/>;
}

export default observer(Project);
