@keyframes gradientRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
    opacity: 0.7;
    filter: blur(20px);
  }

  50% {
    opacity: 0.9;
    filter: blur(25px);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
    opacity: 0.7;
    filter: blur(20px);
  }
}

.chatBox {
  width: 100%;
  margin-top: 24px;
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
  position: relative;

  .contentBox {
    h1 {
      color: rgba(255, 255, 255, 0.50);
      text-align: center;
      font-family: Inter;
      font-size: 40px;
      font-style: normal;
      font-weight: 600;
      line-height: 100%;
      padding: 4px 0;
      mix-blend-mode: hard-light;
      background: linear-gradient(98deg, #00FFB2 14.37%, #FFF 40.78%, #FFB1F3 75.68%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    h3 {
      color: var(--system-content-secondary, #A3AEBF);
      text-align: center;
      font-family: Poppins;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 100%;
      text-transform: capitalize;
      padding: 4px 0;
      margin: 16px 0 48px;
    }

    .animateBox {
      position: relative;
      width: 640px;
      height: auto;
      margin: 0 auto;
      border-radius: 16px;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 141%;
        padding-bottom: 141%;
        transform-origin: center center;
        background: conic-gradient(rgba(0, 255, 178, 0.95),
            rgba(255, 255, 255, 0.95),
            rgba(255, 177, 243, 0.95));
        border-radius: 18px;
        z-index: 0;
        animation: gradientRotate 4s linear infinite;
      }

      .chatAgentInput {
        margin: 2px;
        z-index: 1;
        width: calc(100% - 4px);
      }
    }
  }
}