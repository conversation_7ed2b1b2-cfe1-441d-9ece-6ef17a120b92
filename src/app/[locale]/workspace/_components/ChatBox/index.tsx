"use client";

import { observer } from "mobx-react";
import styles from "./index.module.scss";
import { useStore } from "@/contexts/StoreContext";
import AgentInput from "@/components/Agent/Input";
import { useAgentInput } from "@/components/Agent/hooks/useAgentInput";
import useAddChatToEditor from "@/hooks/useAddChatToEditor";
import { useState } from "react";
import { Agent } from "@/components/Agent/types";
import { set } from "mobx";
import { it } from "node:test";

const ChatBox = () => {
  const { userStore } = useStore();
  const inputContext = useAgentInput();
  const { addChatToEditor } = useAddChatToEditor();

  const [status, setStatus] = useState(Agent.Status.Idle);

  // 跳转编辑器
  const handleSend = async () => {
    setStatus(Agent.Status.Loading);
    try {
      await addChatToEditor({
        text: inputContext.text,
        images: inputContext.images
          .filter((item) => item.type === Agent.InputImageType.Exist)
          .map((item) => item.src),
      });
    } catch (e) {
      setStatus(Agent.Status.Idle);
    }
  };

  return (
    <div className={styles.chatBox}>
      <div className={styles.contentBox}>
        <h1>Hey，{userStore.userInfo?.user?.screenName}</h1>
        <h3>{`What's your inspiration today?`}</h3>
        <div className={styles.animateBox}>
          <AgentInput
            text={inputContext.text}
            onTextChange={inputContext.setText}
            onSend={handleSend}
            status={status}
            images={inputContext.images}
            uploadImage={inputContext.uploadImage}
            removeImage={inputContext.removeImage}
            className={styles.chatAgentInput}
          />
        </div>
      </div>
    </div>
  );
};

export default observer(ChatBox);
