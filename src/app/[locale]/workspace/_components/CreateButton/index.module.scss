@import '@/styles/variable.scss';
.create-button {
  display: flex;
  height: 32px;
  padding: 0px var(--spacing-10, 10px);
  align-items: center;
  border-radius: var(--radius-8, 8px);
  margin-right: 16px;
  border: 1px solid var(--color-primary-100) !important;
  color: $system-content-on-primary;
  :global {    
    .prefix {
      height: 16px;
      svg {
        width: 16px;
        height: 16px;
      }
    }
  
    .label {
      font-size: 14px;
      margin: 0 5px 0 4px;
      white-space: nowrap;
    }
  
    .suffix {
      svg {
        width: 12px;
        height: 12px;
      }
    }
  }
}