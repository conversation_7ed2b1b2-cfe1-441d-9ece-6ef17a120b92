"use client";

import { PlusBold } from "@meitu/candy-icons";
import classNames from "classnames";
import buttonStyles from "@/styles/button.module.scss";
import styles from "./index.module.scss";
import { useI18n } from "@/locales/client";
import { useRouter } from "next/navigation";
import { useCreateProject } from "@/hooks/useCreateProject";
import { useStore } from "@/contexts/StoreContext";
import { observer } from "mobx-react-lite";
export default observer(function CreateButton({
  className,
  onClick,
}: {
  className?: string;
  onClick?: () => void;
}) {
  const t = useI18n();
  const router = useRouter();
  const { globalProjectStore, userStore } = useStore();
  const { createProject } = useCreateProject({
    globalProjectStore,
    userStore,
  });

  const handleClick = async () => {
    onClick?.();
    createProject().then((res) => {
      if (!res) return;
      router.push(`/canvas/project/${res.projectId}`);
    });
  };

  return (
    <button
      className={classNames(
        buttonStyles.main,
        styles["create-button"],
        className
      )}
      onClick={handleClick}
    >
      <PlusBold className="prefix" />
      <span className="label">{t("Create Project")}</span>
    </button>
  );
});
