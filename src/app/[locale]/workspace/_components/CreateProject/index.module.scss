.create-project-wrapper {
  box-sizing: border-box;
  padding-right: 16px;
  width: 100%;
  aspect-ratio: 1182/185;
  min-height: 185px;
  max-height: 250px;
  margin-bottom: 32px;
  position: relative; /* 添加定位 */
  :global {
    .create-project-container {
      position: absolute; /* 改为绝对定位 */
      top: 0;
      left: 0;
      box-sizing: border-box;
      width: calc(100% - 16px);
      height: 100%;
      border-radius: var(--radius-16, 16px);
      border: 1px solid #1D1E23;
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.10) 0%, rgba(0, 0, 0, 0.10) 100%), #1D1E23;
      box-shadow: 0px 0px 0px 0px rgba(83, 246, 180, 0.50) inset;
      overflow: hidden;
      .create-project-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .create-project-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #000;
        opacity: 0.3;
        z-index: 1;
      }
      .create-project-content {
        z-index: 2;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
       
        .create-project-button {
          padding: 0 16px;
          height: 36px;
          background-color: #fff;
          color: #000;
          font-weight: 600;
          font-family: Inter;
          margin-right: 0;
          &:hover {
            background-color: #fff ;
            color: #000;
          }
        }
        .create-project-description {
          margin-top: 16px;
          color: #fff;
          font-family: Inter;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          // opacity: 0.7;
          mix-blend-mode:soft-light;
        }
      }
    }
  }
}
