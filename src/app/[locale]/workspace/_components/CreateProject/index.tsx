"use client";
import FileDropTarget from "@/components/FIleDropTarget";
import CreateButton from "../CreateButton";
import styles from "./index.module.scss";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import toast from "@/components/Toast";
import { useRouter } from "next/navigation";
import { useStore } from "@/contexts/StoreContext";
import { Action } from "@/stores/TransferAction/types";
import { useI18n } from "@/locales/client";
import { useCreateProject } from "@/hooks/useCreateProject";
import { uploadErrorhandler } from "@/utils/error-handler/upload";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import { observer } from "mobx-react-lite";
import { trackEvent } from "@/services/tracer";

const CreateProject = observer(() => {
  const upload = useMonitorUploadFunc();
  const router = useRouter();
  const { transferActionStore, globalProjectStore, userStore } = useStore();
  const { createProject } = useCreateProject({
    globalProjectStore,
    userStore,
  });
  const t = useI18n();
  return (
    <FileDropTarget
      className={styles["file-drop-target"]}
      onFilesDropped={async ({ files, event }) => {
        try {
          const file = files[0];
          if (!file) {
            return;
          }
          toast.show({
            key: "uploading",
            title: t("Uploading..."),
            type: "loading",
            duration: 0,
          });
          const context = await upload?.({ file });
          const res = await createProject();
          if (!res) return;
          transferActionStore.pushAction({
            consumer: Action.Consumer.EditorProject,
            type: Action.EditorProject.Type.AddImage,
            payload: {
              url: context?.result?.previewUrl ?? "",
            },
          });
          router.push(`/editor/project/${res.projectId}`);
        } catch (error: any) {
          setTimeout(() => {
            if (uploadErrorhandler(error, t)) {
              return;
            }
            defaultErrorHandler(error);
          }, 500);
        } finally {
          toast.destroy("uploading");
        }
        
      }}
    >
      <div className={styles["create-project-wrapper"]}>
        <div className={"create-project-container"}>
          <video
            src="https://wheeai-public.stariidata.com/static/workspace/animation.mp4"
            autoPlay
            muted
            loop
            playsInline
            disablePictureInPicture
            className="create-project-video"
          ></video>
          <div className="create-project-mask"> </div>
          <div className="create-project-content">
            <CreateButton className="create-project-button" onClick={() => {
              trackEvent('login_home_page_click', {
                click_type: 'create_project',
              });
            }} />
            <div className="create-project-description">
              Drop your image here
            </div>
          </div>
        </div>
      </div>
    </FileDropTarget>
  );
});

export default CreateProject;
