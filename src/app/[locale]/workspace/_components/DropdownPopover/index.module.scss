.dropdown-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  :global {
    .dropdown-trigger-text {
      margin-right: 8px;
    }
  }
}
.dropdown-container {
  :global {
    .dropdown-menu {
      box-sizing: border-box;
      margin-top: 8px;
      min-width: 164px;
      border-radius: 12px ;
      border-radius: var(--radius-12, 12px);
      border: 1px solid var(--system-stroke-input, #22272E);
      background: var(--background-editorPopup-default, #1D1E23);
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);
      padding: 4px;
      .dropdown-menu-item {
        padding: 8px ;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        margin-bottom: 4px;
        box-sizing: border-box;
        &:hover {
          color: #fff;
          border-radius: var(--radius-10, 10px);
          background: linear-gradient(0deg, var(--background-editorPopup-hover, #272C33) 0%, var(--background-editorPopup-hover, #272C33) 100%), #F5F7FA;
        }
      }
    }
  }
}