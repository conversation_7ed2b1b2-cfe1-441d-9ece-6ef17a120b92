'use client';
import styles from './index.module.scss';

import { Dropdown as AntdDropdown, DropdownProps as AntdDropdownProps } from 'antd';
import { ChevronDownBlack, CheckBlack } from '@meitu/candy-icons';
import { MenuItemType } from 'antd/es/menu/interface';
import type { MenuInfo } from 'rc-menu/lib/interface';
import { useEffect, useState } from 'react';

interface DropdownPopoverProps extends Omit<AntdDropdownProps, 'menu'> {
  items: MenuItemType[];
  onChange?: (item: MenuItemType) => void;
}

const DropdownPopover = ({ overlayClassName, items, onChange, children, trigger = ['click'], ...props }: DropdownPopoverProps) => {
  const [selectedItem, setSelectedItem] = useState<MenuItemType | null>(null);
  const [open, setOpen] = useState(false);

  const handleClick = (item: MenuItemType) => {
    setSelectedItem(item);
    setOpen(false);
    onChange?.(item);
  };

  useEffect(() => {
    items && setSelectedItem(items[0]);
  }, [items]);

  return (
    <AntdDropdown 
      {...props}
      overlayClassName={styles['dropdown-container']}
      trigger={trigger}
      open={open}
      onOpenChange={setOpen}
      menu={{
        items,
      }}
      dropdownRender={() => {
        return (
          <div className={'dropdown-menu'}>
            {items?.map((item) => (
              <div 
                key={item?.key} 
                onClick={(e) => {
                  e.stopPropagation();
                  item?.onClick?.({ key: String(item.key) } as MenuInfo);
                  handleClick(item);
                }} 
                className={'dropdown-menu-item'}
              >
                {item?.label}
              </div>
            ))}
          </div>
        );
      }}
    >
      {children}
    </AntdDropdown>
  );
};

export default DropdownPopover;