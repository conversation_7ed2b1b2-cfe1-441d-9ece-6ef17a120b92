'use client';
import styles from './index.module.scss';

import { Dropdown as AntdDropdown, DropdownProps as AntdDropdownProps } from 'antd';
import { ChevronDownBlack, CheckBlack } from '@meitu/candy-icons';
import { MenuItemType } from 'antd/es/menu/interface';
import { useEffect, useState } from 'react';
import classNames from 'classnames';

interface DropdownSelectProps extends Omit<AntdDropdownProps, 'menu'> {
  items: MenuItemType[];
  onChange?: (item: MenuItemType) => void;
}
const DropdownSelect = ({ overlayClassName, items, onChange, ...props }: DropdownSelectProps) => {
  const [selectedItem, setSelectedItem] = useState<MenuItemType | null>(null);
  const [open, setOpen] = useState(false);
  const handleClick = (item: MenuItemType) => {
    setSelectedItem(item);
    setOpen(false);
    onChange?.(item);
  };

  useEffect(() => {
    items && setSelectedItem(items[0]);
  }, [items]);

  return (
    <AntdDropdown 
      {...props}
      overlayClassName={styles['dropdown-container']}
      trigger={['click']}
      open={open}
      onOpenChange={setOpen}
      menu={{
        items,
      }}
      dropdownRender={() => {
        return (
          <div className={'dropdown-menu'}>
            {items?.map((item) => (
              <div key={item?.key} onClick={() => handleClick(item)} className={classNames('dropdown-menu-item', {
                ['dropdown-menu-item-selected']: selectedItem?.key === item?.key,
              })}>
                {item?.label}
                {selectedItem?.key === item?.key && <CheckBlack />}
              </div>
            ))}
          </div>
        );
      }}
    >
      <div className={styles['dropdown-trigger']}>
        <span className={'dropdown-trigger-text'}>{selectedItem?.label}</span>
        <ChevronDownBlack />
      </div>
    </AntdDropdown>

  );

};

export default DropdownSelect;