.function-module {
  width: 100%;
  margin-bottom: 32px;
  box-sizing: border-box;
  :global {
    .function-title-wrapper {
      padding-right: 16px;
    }
    .function-card-list {
      width: 100%;
      padding-top: 12px;
      padding-bottom: 26px;
      padding-right: 16px;
      box-sizing: border-box;
      display: flex;
      gap: 16px;
      overflow-x: auto;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}
.function-card {
  flex-shrink: 0;
  width: 15.5vw;
  min-width: 214px;
  max-width: 300px;
  aspect-ratio: 224/65;
  border-radius: var(--radius-12, 12px);
  border: 1px solid rgba(255, 255, 255, 0.07);
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
  background-size:cover;
  background-position: center;
  background-color: #1b1c20;
  &:hover {
    background: var(--hover-background-color);
    :global(.function-card-content) {
      :global(.function-card-title) {
        mix-blend-mode: multiply;
        color: #1b1c20;
      }
      :global(.function-card-icon) {
        mix-blend-mode: multiply;
        background-image: var(--hover-icon);
      }
    }
  }
  :global {
    .function-card-content {
      width: 100%;
      height: 100%;
      padding: 0 20px 0 16px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: absolute;
      left: 1px;
      bottom: 1px;
      .function-card-title {
         font-family: var(--font-poppins);
        font-size: clamp(15px, 1.04vw, 20px);
        // font-size: 15px;
        font-style: normal;
        font-weight: 600;
        color: #fff;
      }
      .function-card-icon {
        width: 24px;
        height: 24px;
        // background-image: var(--icon);
        // background-size: cover;
        // background-position: center;
      }
    }
  }
}
