/* eslint-disable @next/next/no-img-element */
"use client";
import {
  UltraHdBold,
  NoCutoutBold,
  EliminateBold,
  TextSquareBold,
  LosslessAmplificationBold,
} from "@meitu/candy-icons";
import Title from "../Title";
import styles from "./index.module.scss";
import classNames from "classnames";
import Link from "@/components/Link";
import { FunctionListItem } from "@/api/types/workspace";
import { useI18n } from "@/locales/client";
import { saveProject } from "@/api/aiPoster/project";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { trackEvent } from "@/services/tracer";
interface FunctionCardProps {
  title: string;
  icon: string;
  id: string;
  type: string;
  url: string;
  pic: string;
  hoverIcon: string;
  hoverBackgroundColor: string;
}

const FunctionCard = ({
  title,
  icon,
  url,
  pic,
  hoverIcon,
  hoverBackgroundColor,
}: FunctionCardProps) => {
  const router = useRouter();
  const [isHover, setIsHover] = useState(false);
  useEffect(() => {
    router.prefetch(url);
  }, [url, router]);

  const handleClick = async () => {
    router.push(url);
    trackEvent('login_home_page_click', {
      click_type: title?.toLowerCase().replace(/\s+/g, '_'),
    });
  };
  const style = {
    backgroundImage: `url(${pic})`,
  };
  const handleMouseEnter = () => {
    setIsHover(true);
  };
  const handleMouseLeave = () => {
    setIsHover(false);
  };
  const Content = (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="function-card-content"
      style={
        {
          "--hover-background-color": hoverBackgroundColor,
        } as React.CSSProperties
      }
    >
      <div className="function-card-title">{title}</div>
      {icon && (
        <>
         <img className="function-card-icon" src={hoverIcon} alt={title} style={{ display: isHover?'block':'none'}}  />

          <img className="function-card-icon" src={icon} alt={title} style={{ display: !isHover?'block':'none'}}   />
        </>
      )}
    </div>
  );
  return (
    <div
      className={classNames(styles["function-card"])}
      style={pic ? { ...style, "--hover-background-color": hoverBackgroundColor } as React.CSSProperties : { "--hover-background-color": hoverBackgroundColor } as React.CSSProperties}
      onClick={handleClick}
    >
      {Content}
      {/* {Content} */}
    </div>
  );
};

const FunctionModule = ({ initData }: { initData: FunctionListItem[] }) => {
  const t = useI18n();

  return (
    <div className={styles["function-module"]}>
      <div className={"function-title-wrapper"}>
        <Title
          title={t("AI Tools")}
          right={
            <Link
              className={styles["all-content-link"]}
              href="/workspace/ai-tools"
              onClick={() => {
                trackEvent('login_home_page_click', {
                  click_type: 'all_content',
                  location: 'all_tools_all_content'
                });
              }}
            >
              {t("All")}
            </Link>
          }
        />
      </div>
      <div className={"function-card-list"}>
        {initData?.length > 0 &&
          initData?.map((item, index) => (
            <FunctionCard
              key={item.id}
              title={item.name}
              icon={item.icon}
              id={item.id.toString()}
              type={item.type}
              url={item.url}
              pic={item.pic}
              hoverIcon={item.hoverIcon}
              hoverBackgroundColor={item.hoverBackgroundColor}
            />
          ))}
      </div>
    </div>
  );
};
export default FunctionModule;
