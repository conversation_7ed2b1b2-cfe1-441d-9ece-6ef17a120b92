import buttonStyles from "@/styles/button.module.scss";
import classNames from "classnames";
import styles from "./index.module.scss";
import { MtBeanFilled } from "@meitu/candy-icons";

import { useMeiDouBalance } from "@/hooks/useMeidou";
import { rootStore } from "@/stores/RootStore";
import { observer } from "mobx-react-lite";
import { useStore } from "@/contexts/StoreContext";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
function MtBean() {
  const { userStore } = useStore();
  const { availableAmount } = useMeiDouBalance({ userStore });
  const { isLogin } = rootStore.userStore;
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  // 修正逻辑 登录后才展示美豆区域
  return isLogin ? (
    <div
      className={classNames(
        styles["mt-bean-container"],
        "mt-bean-container-custom"
      )}
    >
      <div
        role="button"
        className={classNames(buttonStyles.secondary, "mt-bean")}
        onClick={() => {
          openMeiDouRecordsPopup();
        }}
      >
        <MtBeanFilled className="mt-bean-icon" />
        {availableAmount}
      </div>
    </div>
  ) : (
    <></>
  );
}

export default observer(MtBean);
