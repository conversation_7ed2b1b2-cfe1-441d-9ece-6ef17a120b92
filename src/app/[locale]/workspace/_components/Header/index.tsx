"use client";
import CommonHeader, { HeaderElementType } from "@/components/Header";
import styles from "./index.module.scss";
import CreateButton from "../CreateButton";
import MtBean from "@/components/Header/MtBean";
import { trackEvent } from "@/services/tracer";
export default function WorkspaceHeader() {
  return (
    <CommonHeader
      className={styles["header-container"]}
      rightSlot={[
        {
          type: HeaderElementType.Custom,
          key: "download",
          element: (
            <CreateButton
              onClick={() => {
                trackEvent("login_home_page_top_click", {
                  click_type: "create_profile",
                });
              }}
            />
          ),
        },
        {
          type: HeaderElementType.Custom,
          key: "mt-bean",
          element: (
            <MtBean
              onClick={() => {
                trackEvent("login_home_page_top_click", {
                  click_type: "credit",
                });
              }}
            />
          ),
        },
        {
          type: HeaderElementType.AccountAvatar,
          onClick: () => {
            trackEvent("login_home_page_top_click", {
              click_type: "profile",
            });
          },
        },
      ]}
    />
  );
}
