.nav-container {
  height: calc(100vh - 88px);
  user-select: none;
  position: relative;

  :global {
    .ant-menu-dark {
      background: #0C0E0F;

      .ant-menu-item {
        color: var(--system-content-secondary, #A3AEBF);
        border: 1px solid transparent;

      }

      .ant-menu-item-active {
        background: var(--system-background-secondary, #1D1E23) !important;
        border: 1px solid var(--system-stroke-input-default, #22272E) !important;
      }

      .ant-menu-item-selected {
        color: #fff;
        background: var(--system-background-secondary, #1D1E23);
        border: 1px solid var(--system-stroke-input-default, #22272E);
      }
    }
  }

  &-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    // width: 100%;
    width: 208px - 40px;
    height: 155px - 32px;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-input-default, #22272E);
    background: var(--system-background-secondary, #1D1E23);
    padding: 16px;

    .title {
      display: flex;
      flex-direction: row;
      color: var(--system-content-primary, #FFF);
      /* text_14_bold */
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;

      span {
        display: inline-block;
        margin-left: 8px;
      }
    }

    .description {
      color: var(--system-content-secondary, #A3AEBF);
      /* text_12 */
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 125%;
      /* 15px */
      margin-top: 8px;
      margin-bottom: 16px;
      // text-align: center;
    }

    &-button {
      width: 100%;
      height: 40px;
      border-radius: var(--radius-8, 8px);
      background: var(--system-background-primary, #FFF);

      &:hover {
        background: linear-gradient(88deg, #5DF5B9 0%, #D8EAFF 47.22%, #FBA6FF 100%) !important;
        opacity: 0.9;
      }

      :global {
        border: none !important;
      }
    }
  }

  // background-color: #000;
}