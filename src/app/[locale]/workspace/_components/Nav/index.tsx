"use client";
import React, { useState, useEffect } from "react";
import type { MenuProps } from "antd";
import { Menu } from "antd";
import { useI18n, useScopedI18n } from "@/locales/client";

import { observer } from "mobx-react-lite";

import { VipIcons } from "@/assets/icons";
import { VipButton } from "@/components/Button/1VipButton";

import {
  FunctionBold,
  FolderBold,
  TemplateBold,
  UserBold,
  BookBold,
  LightingFill,
  HomeBold,
  HomeBoldFill,
  FunctionBoldFill,
  FolderBoldFill,
  TemplateBoldFill,
  UserBoldFill,
  BookBoldFill,
} from "@meitu/candy-icons";

import styles from "./index.module.scss";
import { usePathname, useRouter } from "next/navigation";
import { useStore } from "@/contexts/StoreContext";
import { VipLevel } from "@/types";
import { useOpenMeiDouRecordsPopup, useSubscribe } from "@/hooks/useSubscribe";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { useQuestionModal } from "@/contexts/QuestionContext";
import { trackEvent } from "@/services/tracer";
type MenuItem = Required<MenuProps>["items"][number] & {
  clickType?: string;
};

const Nav: React.FC<{ isCollapsed?: boolean; isMobile?: boolean }> = ({
  isCollapsed,
  isMobile,
}) => {
  const router = useRouter();
  const pathname = usePathname();

  const { userStore } = useStore();
  const { vipLevel, isVipCurrent } = userStore;
  // const vipLevel = VipLevel.Basic;
  //
  const VIP_TYPE = {
    [VipLevel.Plus]: "Premium",
    [VipLevel.Basic]: "Basic",
    [VipLevel.None]: "Free",
    [VipLevel.Super]: "Super",
  };

  const DescriptionMAP = {
    [VipLevel.Plus]: "3000 monthly credits unlocked. Explore plan benefits.",
    [VipLevel.Basic]:
      "Basic｜Upgrade to Premium for more monthly credits and unlimited project creations.",
    [VipLevel.None]:
      "Free｜Upgrade to unlock advanced features and create commercial-use content.",
    [VipLevel.Super]: "",
  };
  const onClick: MenuProps["onClick"] = (e) => {
    let clickType = e.key
      .replace("/workspace/", "")
      .replace(/-/g, "_")
      .toLowerCase();
    trackEvent("login_home_page_side_bar_click", {
      click_type: clickType == "/workspace" ? "home" : clickType,
    });
    router.push(e.key);
  };
  const t = useScopedI18n("workspace.nav");

  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const { open, close } = useSubscribeModal();

  // const { open: openQuestion } = useQuestionModal();

  // useEffect(() => {
  //     openQuestion();
  // }, []);

  // 图标映射
  const IconMap = {
    "/workspace": {
      active: <HomeBoldFill />,
      default: <HomeBold />,
    },
    "/workspace/ai-tools": {
      active: <FunctionBoldFill />,
      default: <FunctionBold />,
    },
    "/workspace/projects": {
      active: <FolderBoldFill />,
      default: <FolderBold />,
    },
    "/workspace/templates": {
      active: <TemplateBoldFill />,
      default: <TemplateBold />,
    },
    "/workspace/subscribe": {
      active: <UserBoldFill />,
      default: <UserBold />,
    },
    "/workspace/tutorials": {
      active: <BookBoldFill />,
      default: <BookBold />,
    },
  };

  // 定义路由路径常量
  const ROUTES = {
    HOME: "/workspace",
    AI_TOOLS: "/workspace/ai-tools",
    PROJECTS: "/workspace/projects",
    TEMPLATES: "/workspace/templates",
    SUBSCRIBE: "/workspace/subscribe",
    TUTORIALS: "/workspace/tutorials",
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const routes = Object.values(ROUTES);

    // 精确匹配
    if (routes.includes(pathname)) {
      return [pathname];
    }

    // 子路径匹配 - 找到最长匹配的父路径
    const matchingRoutes = routes
      .filter((route) => pathname.startsWith(route))
      .filter(
        (route) =>
          pathname === route ||
          (pathname.length > route.length &&
            pathname.charAt(route.length) === "/")
      );

    // 按路径长度排序，选择最长匹配
    matchingRoutes.sort((a, b) => b.length - a.length);

    return matchingRoutes.length > 0 ? [matchingRoutes[0]] : [ROUTES.HOME];
  };

  const selectedKeys = getSelectedKeys();
  // 菜单配置项
  const items: MenuItem[] = [
    {
      key: "/workspace",
      label: t("Home"),
      icon: selectedKeys.includes("/workspace")
        ? IconMap["/workspace"].active
        : IconMap["/workspace"].default,
    },
    {
      key: "/workspace/ai-tools",
      label: t("AI Tools"),
      icon: selectedKeys.includes("/workspace/ai-tools")
        ? IconMap["/workspace/ai-tools"].active
        : IconMap["/workspace/ai-tools"].default,
    },
    {
      key: "/workspace/projects",
      label: t("Projects"),
      icon: selectedKeys.includes("/workspace/projects")
        ? IconMap["/workspace/projects"].active
        : IconMap["/workspace/projects"].default,
    },
    {
      key: "/workspace/templates",
      label: t("Templates"),
      icon: selectedKeys.includes("/workspace/templates")
        ? IconMap["/workspace/templates"].active
        : IconMap["/workspace/templates"].default,
    },
    {
      key: "/workspace/subscribe",
      label: t("Subscribe"),
      icon: selectedKeys.includes("/workspace/subscribe")
        ? IconMap["/workspace/subscribe"].active
        : IconMap["/workspace/subscribe"].default,
    },
    {
      key: "/workspace/tutorials",
      label: t("Tutorials"),
      icon: selectedKeys.includes("/workspace/tutorials")
        ? IconMap["/workspace/tutorials"].active
        : IconMap["/workspace/tutorials"].default,
    },
  ];

  // 函数监听浏览器窗口大小
  // handleResize()

  const handleClick = () => {
    trackEvent("login_home_page_side_bar_click", {
      click_type: "upgrade_plan",
    });
    if (vipLevel === VipLevel.Plus) {
      openMeiDouRecordsPopup();
    } else {
      open({ productType: SubscribeModalType.Basic });
    }
  };

  return (
    <div className={styles["nav-container"]}>
      <Menu
        onClick={onClick}
        inlineCollapsed={isCollapsed}
        items={items}
        theme="dark"
        selectedKeys={getSelectedKeys()}
      />
      <div
        className={styles["nav-container-bottom"]}
        style={{
          display: isCollapsed ? "none" : "block",
        }}
      >
        <div className={styles["title"]}>
          <VipIcons /> <span>{VIP_TYPE[vipLevel]} Plan</span>
        </div>
        <p className={styles["description"]}>{DescriptionMAP[vipLevel]}</p>
        <VipButton
          className={styles["nav-container-bottom-button"]}
          onClick={handleClick}
        >
          <LightingFill
            style={{
              color: "#000",
              marginRight: 4,
            }}
          />{" "}
          {vipLevel === VipLevel.Plus ? "Buy credits" : "Upgrade Plan"}
        </VipButton>
      </div>
    </div>
  );
};

export default observer(Nav);
