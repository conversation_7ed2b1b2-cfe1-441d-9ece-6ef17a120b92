.card-container {
  display: block;
  width: 100%;
  padding: 12px;
  box-sizing: border-box;
  border-radius: var(--radius-16, 16px);
  cursor: pointer;
  // margin-bottom: 32px;
  transition: background 0.3s ease;
  // background-color: red;
  &:hover {
    background: var(--system-background-input, #16171c);
    :global {
      .card-content {
        .content-right {
          .dropdown-trigger {
            display: flex;
          }
        }
      }
      
    }
  }
  :global {
    .image-container {
      width: 100%;
      border-radius: var(--radius-12, 12px);
      border: 1px solid #22272e;
      background: #1d1e23;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 11px 8px;
      box-sizing: border-box;
      margin: 0 auto;
      margin-bottom: 12px;
      overflow: hidden;
      .image-wrapper {
        width:  100%;
        aspect-ratio: 209.6/157;
        border-radius: var(--radius-8, 8px);
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
        background: #fff;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    .card-content {
      display: flex;
      justify-content: space-between;

      .content-left {
        width: calc(100% - 22px);
        .title {
          width: 100%;
          overflow: hidden;
          color: #fff;
          text-overflow: ellipsis;
          font-family: Inter;
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          margin-bottom: 2px;
          background-color: transparent;
          border: none;
          outline: none;
          caret-color: rgba(83, 246, 180, 1); /* 设置光标颜色 */
          &:focus {
            border: none;
          }
          &::selection {
            color: #dbfbee;
            background: rgba(83, 246, 180, 0.5);
          }
        }
        .description {
          overflow: hidden;
          color: var(--system-content-thirdary, #6a7b94);
          text-overflow: ellipsis;
          font-family: Inter;
          font-size: 12px;
          font-weight: 400;
          line-height: 16px; 
        }
      }
      .content-right {
        .dropdown-trigger {
          cursor: pointer;
          width: 22px;
          height: 22px;
          display: none;
          align-items: center;
          justify-content: center;
          color: var(--system-content-thirdary, #6a7b94);
          border-radius: var(--radius-6, 6px);
          &:hover {
            background: var(--background-editorPopup-hover, #272c33);
          }
        }
      }
    }
  }
}

.card-container-small {
  flex-shrink: 0;
  width: 12.56vw;
  max-width: 244.35px;
  padding: 8px;
  min-width: 181px;
  &:hover {
    :global {
      .image-container {
        .image-card-bg {
          transform: rotate(0deg);
        }
      }
    }
  }
  :global {
    .image-container {
      width:100%;
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 8px;
      padding: 19px;
      box-sizing: border-box;
      position: relative;
      
      .image-wrapper {
        aspect-ratio: 165/123;
        position: relative;
        z-index: 2;
        }
      .image-card-bg {
        position: absolute;
        width: calc(100% - 38px);
        min-width: 124px;
        aspect-ratio: 124/82;
        box-sizing: border-box;
        border-radius: var(--radius-8, 8px);
        border: 1px solid #303239;
        background: #27292f;
        box-shadow: 0px 8px 14.5px 0px rgba(0, 0, 0, 0.16);
        transform: rotate(-10deg);
        z-index: 1;
        transition: transform 0.3s ease;
      }

    }
    .card-content {
      .content-left {
        .title {
          height: 22px;
        }
        .description {
          height: 16px;
          margin-bottom: 8px;
        }
      }
    }
  }
}

.card-container-empty {
  :global {
    .image-container {
      aspect-ratio: 209.6/157;
      height: auto;
      svg {
        width: 32px;
        height: 32px;
        color: var(--system-content-thirdary, #6b7c94);
      }
    }
   .card-content {
    .content-left {
      width: 100% ;
    }
   }
  }
}