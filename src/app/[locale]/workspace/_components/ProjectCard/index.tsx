/* eslint-disable @next/next/no-img-element */
"use client";
import styles from "./index.module.scss";
import { EllipsisVerticalBlack } from "@meitu/candy-icons";
import DropdownPopover from "@/app/[locale]/workspace/_components/DropdownPopover";
import classNames from "classnames";
import TransformImage from "@/components/TransformImage";
import useConfirmModal from "@/hooks/useConfirmModal";
// 静态方式
import toast from "@/components/Toast";
import { useEffect, useRef, useState } from "react";
import { ProjectItem } from "@/api/types/aiPoster/project";
import { saveProject } from "@/api/aiPoster/project";
import Link from "@/components/Link";
import { useI18n } from "@/locales/client";
import { useStore } from "@/contexts/StoreContext";
import { PlusBold } from "@meitu/candy-icons";
import { useCreateProject } from "@/hooks/useCreateProject";
import { useRouter } from "next/navigation";
import { observer } from "mobx-react-lite";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";
import { toAtlasImageView2URL } from "@meitu/util";
interface BaseCardProps {
  size?: "small" | "default";
  onClick?: () => void;
}

interface EmptyCardProps extends BaseCardProps {
  type: "empty";
}

interface DefaultCardProps extends BaseCardProps, ProjectItem {
  handleDelete?: ({ projectId }: { projectId: number }) => void;
  onRename?: ({ projectId }: { projectId: number }) => void;
  type?: "default";
}

type CardProps = EmptyCardProps | DefaultCardProps;

const Card = (props: CardProps) => {
  const { globalProjectStore, userStore } = useStore();
  const { createProject } = useCreateProject({ globalProjectStore, userStore });
  const { size = "default", type = "default" } = props;
  const t = useI18n();
  const router = useRouter();

  const isClient = typeof window !== 'undefined';

  // 处理默认卡片的情况
  const { projectId, projectName, lastEditedTime, picUrl, handleDelete, onClick, onRename } =
    props as DefaultCardProps;

  const onConfirm = async () => {
    try {
      await globalProjectStore.deleteProject(projectId);
      handleDelete?.({ projectId });
      toast.success(t("Project deleted successfully"));
    } catch (error) {
      toast.error(t("Failed to delete project"));
    }
  };

  const { open, contextHolder } = useConfirmModal({
    title: t("Are you sure you want to delete this project？"),
    description: "",
    onConfirm,
    okText: t("Delete"),
  });
  const [isEdit, setIsEdit] = useState(false);
  const titleRef = useRef<HTMLInputElement>(null);
  const [title, setTitle] = useState(projectName);

  useEffect(() => {
    if (isEdit) {
      const titleEl = titleRef.current;
      if (titleEl) {
        titleEl.select();
      }
    }
  }, [isEdit]);

  useEffect(() => {
    setTitle(projectName);
  }, [projectName]);

  const handleRename = async () => {
    setIsEdit(false);
    if (title === projectName) {
      return;
    }
    if (title.length === 0) {
      toast.error(t("Project name is required"));
      setTitle(projectName);
      return;
    }
    try {
      await saveProject({ projectId, projectName: title });
      toast.success(t("Project name updated"));
      onRename?.({ projectId });
    } catch (error) {
      setTitle(projectName);
      defaultErrorHandler(error);
    }
  };

  // 处理空卡片的情况
  if (type === "empty") {
    return (
      <div
        onClick={async () => {
          onClick?.();
          try {
            const project = await createProject();
            router.push(`/editor/project/${project.projectId}`);
          } catch (error) {
            console.error(error);
          }
        }}
        className={classNames(
          styles["card-container"],
          {
            [styles["card-container-small"]]: size === "small",
          },
          styles["card-container-empty"]
        )}
      >
        <div className="image-container">
          <PlusBold />
        </div>
        <div className="card-content">
          <div className="content-left">
            <div className="title">{t("Create new project")}</div>
            <div className="description"> </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Link
      href={`/editor/project/${projectId}`}
      className={classNames(styles["card-container"], {
        [styles["card-container-small"]]: size === "small",
      })}
      onClick={(e) => {
        onClick?.();
        if (isEdit) {
          e.preventDefault();
        }
      }}
    >
      <div className={"image-container"}>
        <div className={"image-wrapper"} style={{
         background: picUrl ? '#1d1e23' : '#fff'
        }}>
          {picUrl && (
            <TransformImage
              src={toAtlasImageView2URL(picUrl, {
                format: 'webp',
                height: 360,
              })}
              alt={projectName}
            />
          )}
        </div>
        <div className={"image-card-bg"}></div>
      </div>
      <div className={"card-content"}>
        <div className="content-left">
          <input
            className="title"
            ref={titleRef}
            value={title}
            disabled={!isEdit}
            style={{ pointerEvents: isEdit ? "auto" : "none" }}
            onBlur={handleRename}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleRename();
              }
            }}
            onChange={(e) => {
              if (e.target.value.length > 50) {
                toast.error(t("Project name cannot exceed 50 characters"));
                return;
              }
              setTitle(e.target.value);
            }}
            onClick={(e) => {
              e.preventDefault();
            }}
            onFocus={(e) => {
              e.stopPropagation();
            }}
          />
          <div className="description">{lastEditedTime}</div>
        </div>
        <div
          className="content-right"
          onClick={(e) => {
            e.preventDefault();
          }}
        >
          <DropdownPopover
            trigger={["hover"]}
            items={[
              {
                label: "Rename",
                key: "rename",
                onClick: () => {
                  setIsEdit(true);
                },
              },
              {
                label: "Remove from list",
                key: "remove",
                onClick: () => {
                  open();
                },
              },
            ]}
            placement="bottomRight"
          >
            <div className="dropdown-trigger">
              <EllipsisVerticalBlack />
            </div>
          </DropdownPopover>
        </div>
      </div>
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {contextHolder}
      </div>
    </Link>
  );
};

export default observer(Card);
