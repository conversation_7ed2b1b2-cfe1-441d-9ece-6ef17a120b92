"use client";
import styles from "./index.module.scss";
import ProjectCard from "../ProjectCard";
import Title from "../Title";
import Link from "@/components/Link";
import { useI18n } from "@/locales/client";
import { getConfigList } from "@/api/servers/workspace";
import useS<PERSON> from "swr";
import { ConfigListResponse } from "@/api/types/workspace";
import { useStore } from "@/contexts/StoreContext";
import { observer } from "mobx-react";
import { useEffect } from "react";
import { getWheelHorizontalScrollRefHandler } from "@/utils/wheelHorizontalScroll";
import { trackEvent } from "@/services/tracer";
import { getHomeConfig } from "@/api/workspace";
const ProjectModule = ({ configList }: { configList: ConfigListResponse }) => {
  const t = useI18n();
  const { globalProjectStore } = useStore();

  const { data, error, isLoading, mutate } = useSWR(
    "/home/<USER>",
    getHomeConfig,
    {
      fallbackData: configList,
      revalidateOnFocus: false,
      revalidateOnReconnect: false
    }
  );

  useEffect(() => {
    if (globalProjectStore.projectSaveSuccess) {
      mutate();
      globalProjectStore.setProjectSaveSuccess(false);
    }
  }, [globalProjectStore.projectSaveSuccess]);

  const projectData = data?.aiPosterProject;
  return (
    <div className={styles["project-module"]}>
      <div className={"project-module-title"}>
        <Title
          title={t("Projects")}
          right={
            <Link
              href="/workspace/projects"
              onClick={() => {
                trackEvent("login_home_page_click", {
                  click_type: "all_content",
                  location: "project_all_content",
                });
              }}
            >
              {t("All")}
            </Link>
          }
        />
      </div>
      <div
        className={"project-card-list"}
        ref={getWheelHorizontalScrollRefHandler({
          alwaysPreventDefault: false,
        })}
      >
        {isLoading || projectData?.length > 0 ? (
          projectData?.map((item, index) => (
            <ProjectCard
              size="small"
              key={item.projectId}
              {...item}
              handleDelete={({ projectId }) => {
                globalProjectStore.setProjectSaveSuccess(true);
              }}
              onClick={() => {
                trackEvent("login_home_page_click", {
                  click_type: "project",
                  project_id: item.projectId,
                });
              }}
              onRename={({ projectId }) => {
                globalProjectStore.setProjectSaveSuccess(true);
              }}
            />
          ))
        ) : (
          <ProjectCard size="small" type="empty" />
        )}
      </div>
    </div>
  );
};
export default observer(ProjectModule);
