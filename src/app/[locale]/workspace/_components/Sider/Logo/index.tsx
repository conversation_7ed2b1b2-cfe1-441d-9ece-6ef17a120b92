'use client';
import styles from './index.module.scss';
import { LogoIcon } from "@/assets/icons";
import { trackEvent } from "@/services/tracer";
import { useRouter } from 'next/navigation';
const Logo = () => {
  const router = useRouter();
  return (
    <div className={styles['logo-container']} onClick={() => {
      router.push('/workspace');
      trackEvent('login_home_page_top_click', {
        click_type: 'whee_logo',
      });
    }}>
      <LogoIcon />
    </div>
  )
}

export default Logo;