'use client'
import Nav from '@/app/[locale]/workspace/_components/Nav'
import { useState, useEffect } from 'react';
import { Layout } from 'antd';
import Logo from './Logo';
import styles from './index.module.scss';
const { Sider } = Layout;

const WorkspaceSider = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
        window.removeEventListener('resize', handleResize);
    };
}, []);

const handleResize = () => {
  const width = window.innerWidth;
  if (width <= 1100) {
      setIsCollapsed(true);
  } else if( width <= 375) {
      setIsMobile(true);
  } else {
      setIsCollapsed(false);
      setIsMobile(false);
  }
}
  return (
    <Sider
      width={isCollapsed ? 72 : 232}
      style={{ padding: isCollapsed ? '16px 8px' : '16px 12px 16px 16px' }}
      className={styles['sider-container']}
    >
      <Logo />
      <div className={'nav-wrapper'}>
        <Nav isCollapsed={isCollapsed} isMobile={isMobile} />
      </div>
    </Sider>
  )
}
export default WorkspaceSider;