.imageCard {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  will-change: transform;
  cursor: pointer;
  &:hover {
    .title_bj{
      display: block;
    }
    // .title {
    //   display: block;
    // }
  }
  .title_bj{ 
    position: absolute;
    display: none;
    background: linear-gradient(to bottom,  rgba(0, 0, 0, 0),rgba(0, 0, 0, 0.5));
    width: 285px;
    height: 54px;
    padding: 8px;
    bottom: 0px;
    left: 0px;
    .title {
      // position: absolute;
      // bottom: 22px;
      // left: 8px;
      // display: none;
      color: #FFF;
      text-shadow:
      -1px -1px 0 rgba(0, 0, 0, 0.10),  
       1px -1px 0 rgba(0, 0, 0, 0.10),
      -1px  1px 0 rgba(0, 0, 0, 0.10),
       1px  1px 0 rgba(0, 0, 0, 0.10); 
      font-family: Inter;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 137.5% */
      
    }
  }
  
  .imageContainer {
    position: relative;
    width: 100%;
    background-color: #1d1e23;
    overflow: hidden;
    .image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      
    }
  }
  
 
  
}


