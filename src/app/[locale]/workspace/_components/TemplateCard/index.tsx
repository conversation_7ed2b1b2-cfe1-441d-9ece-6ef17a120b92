/* eslint-disable @next/next/no-img-element */
import React, { useState } from "react";
import styles from "./index.module.scss";
import TransformImage from "@/components/TransformImage";
import { TemplateItemType } from "@/api/types/poster";
import useAddTemplateToEditor from "@/hooks/useAddTemplateToEditor";
import { useTemplateExpo } from "@/hooks/useTrack/useTemplateExpo";
import { TemplateExposureLocation } from "@/types/tracking";
interface ImageCardProps {
  imageUrl: string;
  title?: string;
  width: number;
  height: number;
  className?: string;
  detail: TemplateItemType;
  onClick?: () => void;
  location: TemplateExposureLocation;
  isActive?: boolean;
}

const TemplateCard: React.FC<ImageCardProps> = ({
  imageUrl,
  title,
  width,
  height,
  className,
  detail,
  onClick,
  location,
  isActive,
}) => {
  const cardExpo = useTemplateExpo<HTMLDivElement>(detail.id, detail.categoryId, location, isActive);

  // 计算宽高比
  const aspectRatio = height / width;
  const paddingBottom = `${aspectRatio * 100}%`; 

  const { addTemplateToEditor } = useAddTemplateToEditor();

  const handleClick = async () => {
    onClick?.();
    try {
      addTemplateToEditor(detail);
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <div ref={cardExpo} className={`${styles.imageCard} ${className}`} onClick={handleClick}>
      <div
        className={styles.imageContainer}
        style={{
          // 使用 paddingBottom 技术设置固定的宽高比，这样元素高度会随宽度变化而自动调整
          paddingBottom,
        }}
      >
        {imageUrl && (
          <TransformImage
            src={imageUrl}
            alt={title || "图片"}
            className={`${styles.image}`}
            loading="lazy"
            width={width}
            height={height}
          />
        )}
        <div className={styles.title_bj}>
          <div className={styles.title}>{title}</div>
        </div>
        
      </div>
    </div>
  );
};

export default TemplateCard;
