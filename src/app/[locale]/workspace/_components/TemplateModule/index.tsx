"use client";
import styles from "./index.module.scss";
import Title from "../Title";
import TemplateCard from "../TemplateCard";
import { TemplateItemType } from "@/api/types/poster";
import { useEffect, useState } from "react";
import Link from "@/components/Link";
import { useI18n } from "@/locales/client";
import { getWheelHorizontalScrollRefHandler } from "@/utils/wheelHorizontalScroll";
import { toAtlasImageView2URL } from "@meitu/util";
import { trackEvent } from "@/services/tracer";
import { TemplateExposureLocation } from "@/types/tracking";

const TemplateModule = ({ initData }: { initData: TemplateItemType[] }) => {
  const t = useI18n();
  return (
    <div className={styles["template-module"]}>
      <div className={"template-module-title"}>
        <Title
          title={t("Templates")}
          right={<Link href="/workspace/templates" onClick={() => {
            trackEvent('login_home_page_click', {
              click_type: 'all_content',
              location: 'template_all_content	'
            });
          }}> {t("All")}</Link>}
        />
      </div>
      <div
        className={"template-module-list"}
        ref={getWheelHorizontalScrollRefHandler({ alwaysPreventDefault: false })}
      >
        {initData?.length > 0 &&
          initData?.map((item) => (
            <div key={item.id} className="template-card-item">
              <TemplateCard
                location={TemplateExposureLocation.LoginHome}
                isActive={true}
                detail={item}
                width={768}
                height={1024}
                imageUrl={toAtlasImageView2URL(item?.pic, {
                  // mode: 2 as any,
                  format: 'webp',
                  // height: 360 * (typeof window !== 'undefined' ? window.devicePixelRatio : 1),  
                  height: 360,

                })}
                title={item?.name}
                className="image-card-wrapper"
                onClick={() => {
                  trackEvent('template_feed_click', {
                    template_id: item.id,
                    location: TemplateExposureLocation.LoginHome,
                  });
                }}
              />
            </div>
          ))}
      </div>
    </div>
  );
};

export default TemplateModule;
