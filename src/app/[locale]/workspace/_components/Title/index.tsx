'use client';
import styles from "./index.module.scss";

interface TitleProps {
  title: string;
  right?: React.ReactNode;
  rightClick?: () => void;
}

const Title = ({ title, right, rightClick }: TitleProps) => {
  return (
    <div className={styles["title-container"]}>
      <h2>{title}</h2>
      <div
        className={"title-right"}
        onClick={() => {
          rightClick?.();
        }}
      >
        {right}
      </div>
    </div>
  );
};
export default Title;
