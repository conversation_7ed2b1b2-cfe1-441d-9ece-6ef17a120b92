.card-container {
  width: 100%;
  aspect-ratio: 1/1;
  border-radius: 24px;
  background: var(--system-background-secondary, #1D1E23);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background-size: cover;
  background-position: center;
  &:hover {
    opacity: 0.8;
  }
  :global {
    .card-content {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      mix-blend-mode: overlay;
      padding-left: 16px;
      padding-right: 20px;
      box-sizing: border-box;
      .card-title {
        flex: 1;
        font-size: 15px;
        font-weight: 600;
        color: #FFF;
         font-family: var(--font-poppins);
        margin-right: 19px;
      }
      .card-icon {
        color: #fff;
        mix-blend-mode: overlay;
        img {
          // width: max(1.66vw, 24px);
          // height: max(1.66vw, 24px);
          width: 24px;
          height: 24px;
        }
      }
    }
  }
}