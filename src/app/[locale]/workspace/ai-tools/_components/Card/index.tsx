/* eslint-disable @next/next/no-img-element */
"use client";
import { toAtlasImageView2URL } from "@meitu/util";
import styles from "./index.module.scss";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { trackEvent } from "@/services/tracer";
interface CardProps {
  title?: string;
  icon?: string;
  url: string;
  pic?: string;
  onClick?: () => void;
}
const Card = ({ title, icon, url, pic, onClick }: CardProps) => {
  const router = useRouter();

  useEffect(() => {
    router.prefetch(url);
  }, [url, router]);

  const handleClick = async () => {
    router.push(url);
    onClick?.();
    trackEvent('all_tools_page_click', {
      click_type: title,
    });
  };

  const style = {
    backgroundImage: `url(${toAtlasImageView2URL(pic ?? '', {
      mode: 2 as any,
      height: 310 * window.devicePixelRatio,
    })})`,
  };
  
  return (
    <div
      className={styles["card-container"]}
      onClick={handleClick}
      style={pic ? style : {}}
    >
      <div className={"card-content"}>
        {title && (
          <div className={"card-title"}>
            <span>{title}</span>
          </div>
        )}
        <div className="card-icon">
          {icon && <img src={icon} alt={title} />}
        </div>
      </div>
      <div className={"card-content"}>
        {title && (
          <div className={"card-title"}>
            <span>{title}</span>
          </div>
        )}
        <div className="card-icon">
          {icon && <img src={icon} alt={title} />}
        </div>
      </div>
    </div>
  );
};

export default Card;
