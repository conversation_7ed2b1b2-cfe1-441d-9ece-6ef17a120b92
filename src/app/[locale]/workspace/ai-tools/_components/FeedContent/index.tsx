'use client'
import Card from "../Card";
import { FunctionListItem } from "@/api/types/workspace";
const FeedContent = ({ initData }: { initData: FunctionListItem[] }) => {
    return (
      <div className={'function-content'}>
        {initData?.map((item) => (
          <Card key={item.id} title={item.name} icon={item.icon} url={item.url} pic={item.pic} />
        ))}
      </div>
    )
}

export default FeedContent;