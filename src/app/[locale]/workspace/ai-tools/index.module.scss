.function-page {
  width: 100%;
  height: 100%;
  padding-right: 16px;
  box-sizing: border-box;
  padding-top: 11px;
  :global {
    .function-content {
      display: grid;
      grid-template-columns: repeat(7, minmax(0px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }
    @media (max-width: 1920px) {
      .function-content {
        grid-template-columns: repeat(5, minmax(0px, 1fr));
      }
    }  
    @media (max-width: 1280px) {
      .function-content {
        grid-template-columns: repeat(4, minmax(0px, 1fr));
      }
    }
    @media (max-width: 1024px) {
      .function-content {
        grid-template-columns: repeat(3, minmax(0px, 1fr));
      }
    } 
    @media (max-width: 768px) {
      .function-content {
        grid-template-columns: repeat(2, minmax(0px, 1fr));
      }
    } 
    
  }
}