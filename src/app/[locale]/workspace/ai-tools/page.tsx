import Title from "../_components/Title";
import styles from "./index.module.scss";
import FeedContent from "./_components/FeedContent";
import { getFunctionList } from "@/api/servers/workspace";
import { PageStartClientTrack } from "@/components/PageStartClientTrack";
import { IsLogin } from "@/hooks/useTrack";
 const FunctionPage =  async ()=> {
  const res = await getFunctionList({
    cursor: "",
    count: 10,
  });
  return (
    <div className={styles["function-page"]}>
      <Title title="AI Tools" />
      <FeedContent initData={res.list} />
      <PageStartClientTrack pageName="ai_tools" isLogin={IsLogin.login} />
    </div>
  );
}
export default FunctionPage;