import WorkspaceSider from "./_components/Sider";
import WorkspaceHeader from "./_components/Header";
import "./layout.scss";

const WorkspaceLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="workspace-layout">
      <WorkspaceSider />
      <div className="workspace-main">
        <WorkspaceHeader />
        <div className="workspace-content">{children}</div>
      </div>
    </div>
  );
};
export default WorkspaceLayout;
