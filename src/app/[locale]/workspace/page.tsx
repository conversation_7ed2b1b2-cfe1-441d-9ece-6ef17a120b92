import CreateProject from "./_components/CreateProject";
import FunctionModule from "./_components/FunctionModule";
import ProjectModule from "./_components/ProjectModule";
import TemplateModule from "./_components/TemplateModule";
import { getConfigList } from "@/api/servers/workspace";
import IntercomFeedBack from "@/components/Intercom";
import { notFound } from "next/navigation";
import { IsLogin } from "@/hooks/useTrack";
import { PageEnterTrack } from "./_components/PageEnterTrack";
import { headers } from "next/headers";
import { getUserHeaders } from "@/utils/request/utils";
import ChatBox from "./_components/ChatBox";
const WorkspacePage = async () => {
  try {
    const customHeaders = getUserHeaders(headers());
    const res = await getConfigList(customHeaders);
    const { funcList, aiPosterTemplate } = res;
    return (
      <>
        {/* <CreateProject /> */}
        <ChatBox />
        <FunctionModule initData={funcList} />
        <ProjectModule configList={res} />
        <TemplateModule initData={aiPosterTemplate} />
        <IntercomFeedBack />
        <PageEnterTrack pageName="workspace" isLogin={IsLogin.login} />
      </>
    );
  } catch (error) {
    notFound();
  }
};

export default WorkspacePage;
