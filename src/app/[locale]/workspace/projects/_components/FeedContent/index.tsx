"use client";
import { useI18n } from "@/locales/client";
import Title from "../../../_components/Title";
import { useCallback, useEffect, useRef, useState } from "react";
import Card from "../../../_components/ProjectCard";
import Waterfall from "@/components/CardWaterfall";
import { getProjectList } from "@/api/aiPoster/project";
import { ProjectItem, ProjectSortType } from "@/api/types/aiPoster/project";
import Empty from "../Empty";
import { useRouter } from "next/navigation";
import { useStore } from "@/contexts/StoreContext";
import { observer } from "mobx-react";
import { trackEvent } from "@/services/tracer";
import useSWRInfinite from "swr/infinite";

const projectSortTypeList = [
  {
    label: "Last opened", // 最后打开
    key: "lastopened",
  },
  {
    label: "Last created", // 最后创建
    key: "lastcreated",
  },
  {
    label: "Alphabetically", // 按字母顺序
    key: "alphabetically",
  },
];

// 定义 API 响应的数据结构，用于 SWRInfinite
interface ProjectListResponse {
  list: ProjectItem[]; // 项目列表
  cursor: string; // 用于分页的游标
}

const FeedContent = () => {
  const t = useI18n();
  const { globalProjectStore } = useStore(); 
  const waterfallWrapperRef = useRef<HTMLDivElement>(null); // 瀑布流容器的引用，用于计算高度
  const [showWaterfall, setShowWaterfall] = useState(false); // 控制瀑布流容器是否显示

  // SWRInfinite 的 getKey 函数：根据页码和上一页数据生成唯一的 key
  const getKey = (
    pageIndex: number, // 当前页码 (从 0 开始)
    previousPageData: ProjectListResponse | null // 上一页的响应数据
  ): [string, string, number, ProjectSortType | undefined] | null => {
    // 如果上一页数据存在且 cursor 为空，表示已到达数据末尾，返回 null 停止加载
    if (previousPageData && !previousPageData.cursor) return null;

    // 第一页：cursor 为空字符串，每页加载 20 条，排序类型暂为 undefined
    if (pageIndex === 0) return ["/api/projects", "", 20, undefined];

    // 后续页：使用上一页数据中的 cursor 进行分页
    return ["/api/projects", previousPageData!.cursor, 20, undefined];
  };

  // SWRInfinite 的 fetcher 函数：实际的数据请求逻辑
  const fetcher = async ([, cursor, count, sortType]: [
    string, // API 路径 (来自 getKey)
    string, // 当前页的游标
    number, // 每页请求数量
    ProjectSortType | undefined // 项目排序类型
  ]): Promise<ProjectListResponse> => {
    return getProjectList({ cursor, count, sortType }); // 调用实际的 API 请求函数
  };

  // 使用 useSWRInfinite Hook 进行数据获取和状态管理
  const { data, error, size, setSize, isValidating, mutate } = // SWR 返回的状态和方法
    useSWRInfinite<ProjectListResponse>(getKey, fetcher, {
      revalidateOnFocus: false,
      revalidateOnReconnect: false
    });
  // 从 SWR 的 data 中派生出扁平化的项目列表
  const projects: ProjectItem[] = data ? data.flatMap((page) => page.list) : [];
  // 是否正在进行初次数据加载 (无数据、无错误、正在验证)
  const isLoadingInitialData = !data && !error && isValidating;
  // 是否正在刷新数据 (有数据、正在验证)
  const isRefreshing = isValidating && !!data;
  // 是否已到达数据末尾 (最后一页的 cursor 为空)
  const isReachingEnd = data ? data[data.length - 1]?.cursor === "" : false;
  // 数据是否为空 (项目列表为空、且非初次加载、且非刷新中)
  const isEmpty =
    projects.length === 0 && !isLoadingInitialData && !isRefreshing;

  // 在编辑器页面保存成功后，刷新数据
  useEffect(() => {
    if (globalProjectStore.projectSaveSuccess) {
      mutate(); // 调用 SWR 的 mutate 方法重新验证（刷新）当前数据
      globalProjectStore.setProjectSaveSuccess(false); // 重置保存成功状态
    }
  }, [
    globalProjectStore.projectSaveSuccess, 
    globalProjectStore.setProjectSaveSuccess, 
    mutate, 
    setSize, 
  ]);

  //检查内容高度是否足以滚动，如果不足则自动加载更多
  useEffect(() => {
    const checkScrollAndLoadMore = () => {
      // 确保容器引用存在、未到达末尾、且当前不在验证中
      if (waterfallWrapperRef.current && !isReachingEnd && !isValidating) {
        const { scrollHeight, clientHeight } = waterfallWrapperRef.current; // 获取容器的滚动高度和可见高度
        // 如果滚动内容的总高度小于视口高度的1.5倍，认为滚动空间不足
        if (scrollHeight < clientHeight * 1.5) {
          setSize(size + 1); // 加载下一页数据
        }
      }
    };

    // 仅当 SWR 返回了数据、且数据数组非空、且 projects 数组也已填充时执行检查
    if (data && data.length > 0 && projects.length > 0) {
      const timeoutId = setTimeout(checkScrollAndLoadMore, 250); // 延迟执行，确保 DOM 更新完毕
      return () => clearTimeout(timeoutId); // 组件卸载或依赖更新时清除定时器
    }
  }, [data, projects, size, setSize, isReachingEnd, isValidating]); // 依赖项数组

  // 渲染单个项目卡片的函数
  const renderItem = (item: ProjectItem) => {
    return (
      <Card
        key={item.projectId} // 项目的唯一标识
        {...item} // 展开项目数据作为 Card 的 props
        handleDelete={async ({ projectId }) => {
          // 处理删除操作，此处简单调用 mutate 刷新列表
          globalProjectStore.setProjectSaveSuccess(true);
        }}
        onClick={() => {
          trackEvent("project_page_click", {
            project_id: item.projectId,
          });
        }}
        onRename={({ projectId }) => {
          globalProjectStore.setProjectSaveSuccess(true);
        }}
      />
    );
  };

  // 加载更多项目的函数，供 Waterfall 组件调用
  const loadMoreProjects = () => {
    // 如果未到达末尾且当前不在验证（加载）中，则加载下一页
    if (!isReachingEnd && !isValidating) {
      setSize(size + 1);
    }
  };

  // useCallback Hook: 计算瀑布流中每个项目的高度
  const getItemHeight = useCallback((item: ProjectItem, itemWidth: number) => {
    return itemWidth; // 示例：返回项目宽度作为其高度 (可能需要根据实际内容调整)
  }, []);

  useEffect(() => {
    let timerId: NodeJS.Timeout | undefined;
    if (!isLoadingInitialData && projects.length > 0) {
      timerId = setTimeout(() => {
        setShowWaterfall(true);
      }, 100);
    } else {
      setShowWaterfall(false);
    }
    return () => clearTimeout(timerId);
  }, [isLoadingInitialData, projects]);


  return (
    <>
      <div className="project-page-header">
        <Title title={t("Projects")} right={null} /> 
      </div>
      {/* 条件渲染：如果数据为空且非加载中，显示 Empty 组件 */}
      {isEmpty && !isLoadingInitialData && !isRefreshing ? (
        <Empty />
      ) : (
        //否则，显示瀑布流容器
        <div
          className={"waterfall-content-wrapper"}
          id="waterfall-content-wrapper"
          ref={waterfallWrapperRef}
          style={{
            opacity: showWaterfall ? 1 : 0,
          }}
        >
          <Waterfall
            items={projects} // 瀑布流数据源
            renderItem={renderItem} // 单项渲染函数
            columnGap={4} // 列间距
            rowGap={4} // 行间距
            loadMore={loadMoreProjects} // 加载更多函数
            hasMore={isValidating || !isReachingEnd} // 是否还有更多数据或正在加载中
            scrollableTarget="waterfall-content-wrapper" // 可滚动父容器的 ID
            getItemHeight={getItemHeight} // 获取项目高度的函数
            itemKey={(item: ProjectItem) => item.projectId} // 项目的唯一 key
            scrollThreshold={0.9} // 滚动到距离底部多少百分比时触发加载更多
            className="custom-waterfall" 
            style={{ marginBottom: "50px" }} 
            endMessageText={""}
          />
        </div>
      )}
    </>
  );
};

export default observer(FeedContent); 
