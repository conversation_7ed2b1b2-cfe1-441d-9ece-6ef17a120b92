import { useI18n } from "@/locales/client";
import styles from "../index.module.scss";
// import { Button } from "@/components/Button";
import buttonStyles from "@/styles/button.module.scss";
import { CheckCircleBold } from "@meitu/candy-icons";
import classNames from "classnames";
const BasicPlan = (props: { handleClick: () => void; className?: string }) => {
  const { className = 'upgrade-button' } = props;

  const t = useI18n();
  return (
    <div className={styles["basic-plan"]}>
      <div className={styles["basic-plan-title"]}>
        <span>Basic Plan Subscription</span>
        <button
          className={classNames(
            buttonStyles.main,
            styles["upgrade-button"],
            className
          )}
          onClick={props.handleClick}
        >
          Upgrade Plan
        </button>
      </div>
      <div className={styles["basic-plan-description"]}>
        Quickly bring your ideas to life with AI for free
      </div>
      <div className={styles["basic-plan-content"]}>
        <div className={styles["basic-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["basic-plan-content-item-text"]}>{t("{count}credits per month" as any, { count: 1000 })}</span>
        </div>
        <div className={styles["basic-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["basic-plan-content-item-text"]}>{t("Generated posters are private")}</span>
        </div>
        <div className={styles["basic-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["basic-plan-content-item-text"]}>
            {t("Generations per batch: {count}" as any, { count: 3 })}
          </span>
        </div>
        <div className={styles["basic-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["basic-plan-content-item-text"]}>10 {t("Projects")}</span>
        </div>
        <div className={styles["basic-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["basic-plan-content-item-text"]}>{t("No restrictions on commercial use")}</span>
        </div>
        <div className={styles["basic-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["basic-plan-content-item-text"]}>{t("Access to all member benefits")}</span>
        </div>
        <div className={styles["basic-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles[" -plan-content-item-text"]}>{t("Watermark-free downloads")}</span>
        </div>
        <div className={styles["basic-plan-content-item"]}>
        
          <span className={styles[" -plan-content-item-text"]}></span>
        </div>
      </div>
    </div>
  );
};

export default BasicPlan;
