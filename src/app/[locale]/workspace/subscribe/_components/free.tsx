import styles from "../index.module.scss";
// import { Button } from "@/components/Button";
import buttonStyles from "@/styles/button.module.scss";
import classNames from "classnames";
import { CheckCircleBold } from "@meitu/candy-icons";
import { useI18n } from "@/locales/client";
const FreePlan = (props: { handleClick: () => void; className?: string }) => {
  const { className = "upgrade-button" } = props;
  const t = useI18n();
  return (
    <div className={styles["free-plan"]}>
      <div className={styles["free-plan-title"]}>
        <span>Free Plan Subscription</span>
        <button
          onClick={props.handleClick}
          className={classNames(
            buttonStyles.main,
            styles["upgrade-button"],
            className
          )}
        >
          Upgrade Plan
        </button>
      </div>
      <div className={styles["free-plan-description"]}>
        Free AI to quickly realize your creativity
      </div>
      <div className={styles["free-plan-content"]}>
        {/* <div className={styles["free-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["free-plan-content-item-text"]}>{t("{count}credits per month" as any, { count: 20 })}</span>
        </div> */}
        <div className={styles["free-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["free-plan-content-item-text"]}>{t("Generated posters are public")}</span>
        </div>
        <div className={styles["free-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["free-plan-content-item-text"]}>
            {t("Generations per batch: {count}" as any, { count: 1 })}
          </span>
        </div>
        <div className={styles["free-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["free-plan-content-item-text"]}>2 {t("Projects")}</span>
        </div>
      </div>
    </div>
  );
};

export default FreePlan;
