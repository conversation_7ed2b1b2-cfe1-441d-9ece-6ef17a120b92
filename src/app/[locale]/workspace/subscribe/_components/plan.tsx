import styles from "../index.module.scss";
// import { Button } from "@/n";
import { useCallback } from "react";
import { VipLevel } from "@/types";

interface PlanProps {
  type: VipLevel;
  handleClick?: () => void;
}

const Plan = ({ type, handleClick }: PlanProps) => {
  const onClickHandler = useCallback(() => {
    if (handleClick) {
      handleClick();
    }
  }, [handleClick]);

  const getTitle = () => {
    switch (type) {
      case VipLevel.None:
        return "Free Plan Subscription";
      case VipLevel.Basic:
        return "Basic Plan Subscription";
      case VipLevel.Plus:
        return "Premium Plan Subscribe";
      default:
        return "Plan Subscription";
    }
  };

  const getDescription = () => {
    switch (type) {
      case VipLevel.None:
        return "Free AI to quickly realize your creativity";
      case VipLevel.Basic:
        return "Quickly bring your ideas to life with AI for free";
      case VipLevel.Plus:
        return "Get more Al credits, unlock all Al tools, and assist professionals in completing design tasks";
      default:
        return "";
    }
  };

  const getClassName = () => {
    switch (type) {
      case VipLevel.None:
        return "free-plan";
      case VipLevel.Basic:
        return "basic-plan";
      case VipLevel.Plus:
        return "premium-plan";
      default:
        return "plan";
    }
  };

  return (
    <div className={styles[getClassName()]}>
      <div className={styles[`${getClassName()}-title`]}>
        <span>{getTitle()}</span>
        {/* {type !== VipLevel.Plus && ( */}
          <button onClick={onClickHandler}>Upgrade Plan</button>
        {/* )} */}
      </div>
      <div className={styles[`${getClassName()}-description`]}>
        {getDescription()}
      </div>
      <div className={styles[`${getClassName()}-content`]}>
        待提供
      </div>
    </div>
  );
};

export default Plan; 