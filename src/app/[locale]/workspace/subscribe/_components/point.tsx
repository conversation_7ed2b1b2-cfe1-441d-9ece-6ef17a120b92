import meidou from "@/assets/images/meidou.png";
import styles from "../index.module.scss";
import { observer } from "mobx-react-lite";
import { VipButton } from "@/components/Button/1VipButton";
import { Divider } from "antd";
import { useStore } from "@/contexts/StoreContext";
import { useI18n } from "@/locales/client";
import { VipLevel } from "@/types";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
const Point = observer(({ onClick }: { onClick?: () => void }) => {
  const { userStore } = useStore();
  const t = useI18n();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const { vipLevel, isVipCurrent } = userStore;
  const { mtBeanBalance } = userStore;

  //   console.log("availableAmount", mtBeanBalance);
  return (
    <div className={styles["point"]}>
      <div className={styles["point-title"]}>Credits</div>
      <div className={styles["point-content"]}>
        <div className={styles["point-content-value"]}>
          <div className={styles["content-value-left"]}>
            <img src={meidou.src} alt="meidou" />
            {mtBeanBalance?.availableAmount}{" "}
          </div>
          <div>
            {/* 只有高级会员才能购买 */}
            {isVipCurrent && vipLevel === VipLevel.Plus ? (
              <VipButton
                onClick={() => {
                  onClick?.();
                  openMeiDouRecordsPopup();
                }}
                style={{ border: "none" }}
              >
                {t("Buy credits")}
              </VipButton>
            ) : null}
          </div>
        </div>
        <Divider
          className={styles["point-content-line"]}
          style={{ color: "#F7F8FA" }}
        />
        <div className={styles["point-content-desc"]}>
          <ul>
            <li>
              ・
              {t(
                "subscribe.New user registration credits are valid for 30 days."
              )}
            </li>
            <li>
              ・
              {t(
                "subscribe.Daily login credits expire within 24 hours of being issued."
              )}
            </li>
            <li>
              ・
              {t(
                "subscribe.Basic members receive 1,000 credits per month, while Premium members receive 3,000 credits per month.All monthly credits expire at the end of the month they are issued."
              )}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
});

export default Point;
