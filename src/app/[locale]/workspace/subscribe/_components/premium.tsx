import styles from "../index.module.scss";
// import { Button } from "@/components/Button";
import buttonStyles from "@/styles/button.module.scss";
import { CheckCircleBold } from "@meitu/candy-icons";
import classNames from "classnames";
import { useI18n } from "@/locales/client";
const PremiumPlan = () => {
  const t = useI18n();
  return (
    <div className={styles["premium-plan"]}>
      <div className={styles["premium-plan-title"]}>
        <span>Premium Plan Subscribe</span>
        {/*  */}
      </div>
      <div className={styles["premium-plan-description"]}>
        Get more Al credits, unlock all Al tools, and assist professionals in
        completing design tasks
      </div>
      <div className={styles["premium-plan-content"]}>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            {t("{count}credits per month" as any, { count: 3000 })}
          </span>
        </div>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            {t("Generated posters are private")}
          </span>
        </div>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            {t("Generations per batch: {count}" as any, { count: 5 })}
          </span>
        </div>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            Unlimited {t("Projects")}
          </span>
        </div>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            {t("No restrictions on commercial use")}
          </span>
        </div>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            {t("Access to all member benefits")}
          </span>
        </div>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            {t("Watermark-free downloads")}
          </span>
        </div>
        <div className={styles["premium-plan-content-item"]}>
          <CheckCircleBold
            style={{
              color: "#fff",
            }}
          />
          <span className={styles["premium-plan-content-item-text"]}>
            {t("Early access to new features")}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PremiumPlan;
