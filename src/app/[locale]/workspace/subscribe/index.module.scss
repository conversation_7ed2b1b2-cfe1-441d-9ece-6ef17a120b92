.subscribe-page {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.point {
    width: calc(100% - 16px);
    margin-right: 16px;
    // margin-right: 16px;
    padding-right: 16px;
    margin-top: 32px;

    &-title {
        color: var(--system-content-primary, #FFF);
        /* text_16_bold */
        font-family: var(--font-poppins);
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        margin-bottom: 16px;
    }

    &-content {
        // width: 100%;

        // height: 100%;
        // aspect-ratio: 1182/185;
        // height: 185px;
        border-radius: var(--radius-12, 12px);
        border: 1px solid var(--system-stroke-input-default, #22272E);
        background: var(--system-background-secondary, #1D1E23);
        display: flex;
        padding: var(--spacing-24, 24px);
        flex-direction: column;
        align-items: flex-start;

        &-value {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 100%;
            justify-content: space-between;
            // margin-bottom: 16px;

            // border-bottom: 1px solid var(--neutral-2, #F7F8FA);
            img {
                width: 32px;
                height: 32px;
            }

            color: var(--system-content-primary, #FFF);
            /* text_24_bold */
            font-family: Inter;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;

        }

        &-line {
            margin: 16px 0 !important;
            width: 1136px;
            height: 1px;
            background: var(--neutral-2, #F7F8FA);
            mix-blend-mode: soft-light;
        }

        .content-value-left {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 400px;
        }

        &-desc {
            // margin-top:16px;
            // padding-top: 16px;
            width: 100%;
            // border-top: 1px solid var(--system-stroke-input-default, #22272E);
            color: var(--system-content-secondary, #A3AEBF);
            font-family: Inter;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;

            ul {
                list-style: none;
                padding-left: 0;

                li {
                    color: var(--system-content-secondary, #A3AEBF);
                    font-family: Inter;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                }
            }
        }
    }
}

.free-plan {
    width: calc(100% - 16px);
    margin-top: 12px;
    margin-right: 16px;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-input-default, #22272E);
    background: #1D1E23;
    box-sizing: border-box;
    padding: 24px;

    &-title {
        color: var(--system-content-primary, #FFF);
        color: var(--system-content-primary, #FFF);
        font-family: var(--font-poppins);
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: normal;
    }

    &-description {
        padding-top: 4px;
        color: var(--system-content-secondary, #A3AEBF);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        line-height: normal;
        font-weight: 400;
        padding-bottom: 16px;
        border-bottom: 2px solid var(--system-stroke-input-default, #22272E);
    }

    &-content {
        color: var(--system-content-secondary, #A3AEBF);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        padding-top: 16px;
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        justify-content: flex-start;
        gap: 16px;

        &-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            width: 260px;

            &-text {
                color: var(--system-content-secondary, #A3AEBF);
                font-family: Inter;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                display: inline-block;
                width: 228px;
            }
        }
    }
}

.basic-plan {
    width: calc(100% - 16px);
    margin-top: 12px;
    margin-right: 16px;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-content-brandPrimary, #53F6B4);
    background: linear-gradient(90deg, rgba(83, 246, 180, 0.07) 0%, rgba(49, 144, 105, 0.07) 100%);
    box-sizing: border-box;
    padding: 24px;

    &-title {
        color: var(--system-content-primary, #FFF);
        color: var(--system-content-primary, #FFF);
        font-family: var(--font-poppins);
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: normal;
    }

    &-description {
        padding-top: 4px;
        color: var(--system-content-secondary, #A3AEBF);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        line-height: normal;
        font-weight: 400;
        padding-bottom: 16px;
        border-bottom: 2px solid var(--system-stroke-input-default, #22272E);
    }

    &-content {
        color: var(--system-content-secondary, #A3AEBF);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        padding-top: 16px;
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 16px;

        &-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 260px;
            gap: 8px;

            &-text {
                color: var(--system-content-secondary, #A3AEBF);
                font-family: Inter;
                font-size: 14px;
                display: inline-block;
                width: 228px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }
        }
    }
}

.premium-plan {
    width: calc(100% - 16px);
    margin-top: 12px;
    margin-right: 16px;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-input-default, #22272E);
    border-radius: var(--radius-0, 12px);
    background: linear-gradient(222deg, #060606 -0.65%, #12202D 19.22%, #18372B 42.23%, #270525 103.4%), var(--system-background-secondary, #1D1E23);
    box-sizing: border-box;
    padding: 24px;

    &-title {
        color: var(--system-content-primary, #FFF);
        color: var(--system-content-primary, #FFF);
        font-family: var(--font-poppins);
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: normal;
    }

    &-description {
        padding-top: 4px;
        color: var(--system-content-secondary, #A3AEBF);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        line-height: normal;
        font-weight: 400;
        padding-bottom: 16px;
        border-bottom: 2px solid var(--system-stroke-input-default, #22272E);
    }

    &-content {
        color: var(--system-content-secondary, #A3AEBF);
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        padding-top: 16px;
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 16px;

        &-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 260px;
            gap: 8px;

            &-text {
                color: var(--system-content-secondary, #A3AEBF);
                font-family: Inter;
                font-size: 14px;
                display: inline-block;
                width: 228px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }
        }
    }
}

.upgrade-button {
    display: flex;
    height: 32px;
    padding: 0px var(--spacing-10, 10px);
    align-items: center;
    gap: var(--spacing-8, 8px);
    border-radius: var(--radius-8, 8px);
    color: var(--system-content-onPrimary, #181818);
    /* text_14_bold */
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;

}