"use client";

import Title from "../_components/Title";
import styles from "./index.module.scss";
import { observer } from "mobx-react-lite";
// import {  useMeiDouBalance } from "@/hooks/useMeidou";

import Point from "./_components/point";
import FreePlan from "./_components/free";
import BasicPlan from "./_components/basic";
import PremiumPlan from "./_components/premium";
import { useStore } from "@/contexts/StoreContext";
import { VipLevel } from "@/types";
// import { SubscribeModal } from "@/components/SubscribeModal";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { IsLogin, usePageStart } from "@/hooks/useTrack";
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { trackEvent } from "@/services/tracer";
const SubscribePage = observer(() => {
  usePageStart('subscribe', IsLogin.login);
  const { userStore } = useStore();
  const { isVipCurrent, isLogin,  userInfo, vipLevel } = userStore;

  // const vipLevel = 2;
 


  const { open, close } = useSubscribeModal();

  const handleClick = () => {
    trackEvent('subscribe_page_click', {
      click_type: 'upgrade_plan',
    });
    open({ productType: SubscribeModalType.Basic, onSuccess: () => {
      // close();
      console.log('onSuccess');
    } });
  };

  return (
    <div className={styles["subscribe-page"]}>
      <Title title="Subscribe" />
      {userInfo?.user?.id && (
        <>
          {isVipCurrent && vipLevel === VipLevel.Plus ? (
            <PremiumPlan />
          ) : isVipCurrent && vipLevel === VipLevel.Basic ? (
            <BasicPlan handleClick={handleClick} />
          ) : (
            <FreePlan handleClick={handleClick} />
          )}
          <Point onClick={() => {
            trackEvent('subscribe_page_click', {
              click_type: 'buy_credits',
            });
          }}  />
        </>
      )}
      {/* {/* <SubscribeModal
        show={showSubscribeModal}
        onClose={() => setShowSubscribeModal(false)}
        onSubscribeSuccess={() => setShowSubscribeModal(false)}
        type="basic"
      /> */}
    </div>
  );
});
export default SubscribePage;
