.tab-bar-container {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
  :global {
    .tab-bar-item {
      height: 33px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding: 8px 16px;
      border-radius: var(--radius-8, 8px);
      border: 1px solid var(--system-stroke-input-default, #22272E);
      background: var(--system-background-secondary, #1D1E23);
      cursor: pointer;
      margin-right: 8px;
      .tab-bar-item-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .tab-bar-item-text {  
        font-size: 13px;
        font-weight: 600;
        color: var(--system-content-secondary, #A3AEBF);
      }
      &.active {
        border: 1px solid var(--system-stroke-secondary, #464957);
        background: var(--system-background-thirdary, #272C33);
       
        .tab-bar-item-text {
          color: var(--system-content-primary, #FFF);
        }
      }
      &:hover {
        border: 1px solid var(--system-stroke-secondary, #464957);
        background: var(--system-background-thirdary, #272C33);
        .tab-bar-item-text {
          color: var(--system-content-primary, #FFF);
        }
      }
    }
  }
}