"use client";
import React, { useState, useEffect, useRef } from "react";
import styles from "./index.module.scss";
import { Drink } from "@meitu/candy-icons";
import { PosterConfigType } from "@/api/types/poster";
export interface TabBarDataItem {
  categoryId: number;
  categoryName: string;
  categoryIcon?: string;
  hoverCategoryIcon?: string;
}

export interface TabBarItemProps extends TabBarDataItem {
  active: boolean;
  onChange: (item: TabBarDataItem, e?: React.MouseEvent) => void;
}

export interface TabBarProps {
  onChange: (item: TabBarDataItem, e?: React.MouseEvent) => void;
  data: PosterConfigType["template"];
}

const TabBarItem = ({ active, onChange, ...item }: TabBarItemProps) => {
  const [hover, setHover] = useState(false);
  return (
    <div
      className={active ? "tab-bar-item active" : "tab-bar-item"}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      onClick={(e) => onChange(item, e)}
    >
      {item.categoryIcon && (
        <div className={"tab-bar-item-icon"}>
          <img
            style={{
              display: (hover || active) ? 'block' : 'none'
            }}
            src={item.hoverCategoryIcon}
            alt={item.categoryName}
          />
          <img
            style={{
              display: (hover || active) ? 'none' : 'block'
            }}
            src={item.categoryIcon}
            alt={item.categoryName}
          />
        </div>
      )}
      <div className={"tab-bar-item-text"}>{item.categoryName}</div>
    </div>
  );
};

const TabBar = ({ onChange, data }: TabBarProps) => {
  const [activeItem, setActiveItem] = useState<TabBarDataItem>(data?.[0]);
  const onceRef = useRef(false);
  useEffect(() => {
    if (!onceRef.current && data?.[0]) {
      setActiveItem(data[0]);
      onceRef.current = true;
    }
  }, [data]);
  const handleChange = (item: TabBarDataItem, e?: React.MouseEvent) => {
    setActiveItem(item);
    onChange(item, e);
  };
  return (
    <div className={styles["tab-bar-container"]}>
      {data.map((item) => (
        <TabBarItem
          key={item.categoryId}
          {...item}
          active={activeItem?.categoryId === item.categoryId}
          onChange={handleChange}
        />
      ))}
    </div>
  );
};

export default TabBar;
