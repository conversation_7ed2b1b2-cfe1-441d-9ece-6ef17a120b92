"use client";
import styles from "./index.module.scss";
import TabBar, { TabBarDataItem } from "../TabBar";
import { useState, useEffect, useRef, useCallback } from "react";
import TemplateCard from "@/app/[locale]/workspace/_components/TemplateCard";
import Waterfall from "@/components/CardWaterfall";
import { TemplateItemType, PosterConfigType } from "@/api/types/poster";
import Title from "@/app/[locale]/workspace/_components/Title";
import { useStore } from "@/contexts/StoreContext";
import { observer } from "mobx-react";

import { useI18n } from "@/locales/client";
import { toAtlasImageView2URL } from "@meitu/util";
import { IsLogin, usePageStart } from "@/hooks/useTrack";
import { trackEvent } from "@/services/tracer";
import { TemplateExposureLocation } from "@/types/tracking";
const generateMockItems = (count: number): any[] => {
  return Array.from({ length: count }, (_, index) => {
    // 固定比例选择
    const ratios = ["3:4", "1:1", "9:16", "3:2"];
    const ratio = ratios[Math.floor(Math.random() * ratios.length)];
    let width, height;

    // 根据比例设置固定的宽高
    switch (ratio) {
      case "1:1":
        width = 800;
        height = 800;
        break;
      case "3:4":
        width = 768;
        height = 1024;
        break;
      case "9:16":
        width = 576;
        height = 1024;
        break;
      case "3:2":
        width = 900;
        height = 600;
        break;
      default:
        width = 768;
        height = 1024;
    }

    return {
      id: index.toString(),
      pic: `https://picsum.photos/${width}/${height}?random=${
        index + Math.floor(Math.random() * 1000)
      }`,
      height: height,
      width: width,
      ratio: ratio,
      name: `模板${index}`,
      prompt: "示例提示词",
      recommendPrompt: "推荐提示词",
      config: {},
    };
  });
};

const WaterfallContent = () => {
  usePageStart("templates", IsLogin.login);
  const rootStore = useStore();
  const { posterConfigStore } = rootStore;

  const t = useI18n();

  const handleChange = (item: TabBarDataItem, e?: React.MouseEvent) => {
    setCategoryId(item.categoryId);
    // getInitData(item.categoryId);
    const waterfallContentWrapper = waterfallContentWrapperRef.current;
    if (waterfallContentWrapper) {
      waterfallContentWrapper.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
    if (e) {
      const target = e.target as Node;

      // 查找最近的 Element 节点（兼容子元素点击的情况）
      const selectedTab = e.currentTarget as HTMLElement;

      if (selectedTab && selectedTab.scrollIntoView) {
        selectedTab.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        });
      }
    }
  };

  const [categoryId, setCategoryId] = useState<number | undefined>(undefined);
  const isLastPage = categoryId && posterConfigStore.isLastPage(categoryId);
  const waterfallContentWrapperRef = useRef<HTMLDivElement>(null);
  const TabBarRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    getInitData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // API获取初始数据
  const getInitData = async (categoryId?: number) => {
    try {
      await posterConfigStore.fetchConfig({
        mode: "refresh",
        count: 20,
        categoryId,
      });
      !categoryId &&
        setCategoryId(posterConfigStore.config?.template[0].categoryId);
    } catch (error) {
      console.error("Failed to fetch poster config:", error);
    }
  };

  // 加载更多数据
  const loadMoreItems = async () => {
    if (isLastPage) return;
    try {
      await posterConfigStore.fetchConfig({
        mode: "append",
        count: 20,
        categoryId,
      });
    } catch (error) {
      console.error("Failed to fetch poster config:", error);
    }
  };

  // 计算项目高度函数 - 根据项目宽度和原始宽高比计算高度
  const getItemHeight = useCallback((item: any, itemWidth: number) => {
    // 备选使用宽高信息
    // if (item.width && item.height && item.width > 0) {
    //   return (item.height / item.width) * itemWidth;
    // }

    // 默认返回与宽度相等的高度（正方形）
    // return itemWidth;
    return (4 / 3) * itemWidth;
  }, []);

  // 渲染每个项目
  const renderItem = useCallback(
    (item: any) => (
      <TemplateCard
        detail={item}
        imageUrl={toAtlasImageView2URL(item?.pic, {
          // mode: 2 as any,
          format: "webp",
          height: 512 * window.devicePixelRatio,
        })}
        title={item?.name}
        width={384}
        height={512}
        location={TemplateExposureLocation.AllTemplate}
        onClick={() => {
          trackEvent("template_feed_click", {
            template_category_id: item.categoryId,
            template_id: item.id,
            location: TemplateExposureLocation.AllTemplate,
          });
        }}
        isActive={item.categoryId === categoryId}
      />
    ),
    [categoryId]
  );

  const tabData = posterConfigStore.templateList;
  // const currentData = categoryId
  //   ? posterConfigStore.templateList.find(
  //       (item) => item.categoryId === categoryId
  //     )?.list ?? []
  //   : [];

  return (
    <>
      <div className={styles["title-wrapper"]}>
        <Title title={t("Templates")} />
      </div>
      <div className={styles["tab-bar-wrapper"]} ref={TabBarRef}>
        <TabBar onChange={handleChange} data={tabData} />
      </div>
      <div className={styles["tab-bar-content"]}>
        {tabData.map((item) => {
          return (
            <div
              key={item.categoryId}
              className={styles["waterfall-content-wrapper"]}
              id={`waterfall-content-wrapper-${item.categoryId}`}
              ref={
                item.categoryId === categoryId
                  ? waterfallContentWrapperRef
                  : null
              }
              style={{
                zIndex: item.categoryId === categoryId ? 1 : -1,
              }}
            >
              <Waterfall
                items={item.list}
                renderItem={renderItem}
                columnGap={20}
                rowGap={20}
                loadMore={loadMoreItems}
                hasMore={!isLastPage}
                scrollableTarget={`waterfall-content-wrapper-${item.categoryId}`}
                getItemHeight={getItemHeight}
                itemKey={(item) => item.id}
                scrollThreshold={0.9}
                className="custom-waterfall"
                style={{ marginBottom: "50px" }}
                endMessageText=""
              />
            </div>
          );
        })}
      </div>
    </>
  );
};

export default observer(WaterfallContent);
