.teaching-center-card {
  width: 100%;
  cursor: pointer;
  :global {
    .img-container {
      width: 100%;
      aspect-ratio: 227.2/169.3;
      background-color: #1d1e23;
      overflow: hidden;
      position: relative;
      border-radius: var(--radius-12, 12px);
      margin-bottom: 16px;
      .teaching-center-card-cover {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: var(--radius-12, 12px);
      }
    }
    .teaching-center-card-title {
      color: var(--system-content-primary, #FFF);
      /* text_14_medium */
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 18.2px */
    }
    
  }
}