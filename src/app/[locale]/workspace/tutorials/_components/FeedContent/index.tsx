"use client";
import {
  TutorialItem,
  TutorialListResponse,
  TutorialType,
} from "@/api/types/tutorial";
import Waterfall from "@/components/CardWaterfall";
import { useCallback, useEffect, useState } from "react";
import Link from "next/link";
import TransformImage from "@/components/TransformImage";
import { getTutorialList } from "@/api/tutorial";
import styles from "./index.module.scss";
import { useRouter, usePathname } from "next/navigation";
import { toAtlasImageView2URL } from "@meitu/util";
import { trackEvent } from "@/services/tracer";
import { useCardExpo } from "@/hooks/useTrack/useCardExpo";
import { TutorialExposureLocation } from "@/types/tracking";
import { mtstatReady } from "@/utils/mtstatReady";

interface TeachingCenterCardProps {
  address: string;
  cover: string;
  name: string;
  type: TutorialType;
  id: number;
}
const TeachingCenterCard = ({
  address,
  cover,
  name,
  type,
  id
}: TeachingCenterCardProps) => {
  const cardExpo = useCardExpo<HTMLDivElement>({
    onExpo: () => {
      mtstatReady.then(() => {
        trackEvent('tutorial_feed_expo', {
          tutorial_id: id,
          tutorial_name: name,
          location: TutorialExposureLocation.TeachingCenter,
        });
      });
    },
    isActive: true,
  });
  const router = useRouter();
  const pathname = usePathname();
  const handleClick = () => {
    trackEvent('tutorial_feed_click', {
      tutorial_id: id,
      tutorial_name: name,
      location: TutorialExposureLocation.TeachingCenter,
    });
    if ( type === TutorialType.Scheme) {
      router.push(`${pathname}/${address}`);
      sessionStorage.setItem('tutorialId', id.toString());
      sessionStorage.setItem('tutorialName', name);
    } else if(type === TutorialType.Local) {
      router.push(`${pathname}/local/${address}`);
      sessionStorage.setItem('tutorialId', id.toString());
      sessionStorage.setItem('tutorialName', name);
    }
     else {
      window.open(address, "_blank");
    }
  };
  return (
    <div className={styles["teaching-center-card"]} ref={cardExpo} onClick={handleClick}>
      <div className="img-container">
        {cover && (
          <TransformImage
            className={"teaching-center-card-cover"}
            src={toAtlasImageView2URL(cover, {
              mode: 2 as any,
              height: 360 * window.devicePixelRatio,
            })}
            alt={name}
          />
        )}
      </div>
      <div className={"teaching-center-card-title"}>{name}</div>
    </div>
  );
};

const FeedContent = ({
  initialData,
  scrollableTarget = "waterfall-content-wrapper",
  columnCount = { default: 3, smallDesktop: 5, tablet: 2, mobile: 1 },
}: {
  initialData?: TutorialListResponse;
  scrollableTarget?: string;
  columnCount?: {
    default: number;
    smallDesktop: number;
    tablet: number;
    mobile: number;
  };
}) => {
  const [tutorialList, setTutorialList] = useState<TutorialItem[]>([]);
  const [cursor, setCursor] = useState<string>("");

  useEffect(() => {
    if (initialData) {
      // setTutorialList([...initialData.list, ...initialData.list, ...initialData.list, ...initialData.list, ...initialData.list ]);
      setTutorialList([...initialData.list]);
      setCursor(initialData.cursor);
    }
  }, [initialData]);

  // //初始化请求
  // useEffect(() => {
  //   getTutorialData({ cursor: '' });
  // }, []);

  const getTutorialData = async (params: { cursor: string }) => {
    const res = await getTutorialList(params);
    setTutorialList([...tutorialList, ...res.list]);
    setCursor(res.cursor);
  };

  const renderItem = (item: TutorialItem) => {
    return <TeachingCenterCard {...item} />;
  };

  const loadMoreItems = () => {
    if (cursor) {
      getTutorialData({
        cursor: cursor,
      });
    }
  };

  // 计算项目高度函数 - 根据项目宽度和原始宽高比计算高度
  const getItemHeight = useCallback((item: any, itemWidth: number) => {
    return (itemWidth * 203) / 227;
  }, []);

  return (
    <Waterfall
      columnCount={columnCount}
      items={tutorialList}
      renderItem={renderItem}
      columnGap={12}
      rowGap={32}
      loadMore={loadMoreItems}
      hasMore={!!cursor}
      scrollableTarget={scrollableTarget}
      getItemHeight={getItemHeight}
      itemKey={(item) => item.id}
      scrollThreshold={0.9}
      className="custom-waterfall"
      style={{ marginBottom: "50px" }}
      endMessageText=""
    />
  );
};

export default FeedContent;
