.teaching-center-page {
  width: 100%;
  height: 100%;
  padding-right: 16px;
  box-sizing: border-box;
  padding-top: 12px;
  display: flex;
  flex-direction: column;
  :global {
    .waterfall-content-wrapper {
      flex: 1;
      width: 100%;
      overflow: auto;
      scrollbar-width: none;
      min-height: 0;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}


.tutorial-detail {
  width: 1200px;
  margin: 0 auto;
  min-height: 90%;
  padding: 80px 0;
  box-sizing: border-box;
  margin-bottom: 16px;
  .time {
    color: var(--system-content-secondary, #a3aebf);
     font-family: var(--font-poppins);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 130%; /* 20.8px */
    margin-bottom: 16px;
  }
  .title {
    color: #FFF;
    text-align: center;
     font-family: var(--font-poppins);
    font-size: 48px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%; /* 57.6px */
    margin-bottom: 56px;
  }
}