
import styles from "./index.module.scss";
import { getI18n } from "@/locales/server";
import Title from "../_components/Title";
import { Suspense } from "react";
import FeedContent from "./_components/FeedContent";
import { getTutorialList } from "@/api/servers/tutorial";
import { PageStartClientTrack } from "../../../../components/PageStartClientTrack";
import { IsLogin } from "@/hooks/useTrack";
const RenderFeedContent = async () => {
  const res = await getTutorialList({});
  return (
    <FeedContent initialData={res} />
  );
};
const TeachingCenterPage = async () => {
  const t = await getI18n();
  const res = await getTutorialList({});
  return (
    <div className={styles["teaching-center-page"]}>
      <div className="pb-3">
        <Title
          title={t("Tutorials")}
        />
      </div>
      {/* <Suspense fallback={<div className="h-10">Loading...</div>}>
        <RenderFeedContent />
      </Suspense> */}
      <div className={"waterfall-content-wrapper"} id="waterfall-content-wrapper">
        <FeedContent initialData={res} scrollableTarget="waterfall-content-wrapper" />
      </div>
      <PageStartClientTrack pageName="tutorials" isLogin={IsLogin.login} />
    </div>
  );
};

export default TeachingCenterPage;
