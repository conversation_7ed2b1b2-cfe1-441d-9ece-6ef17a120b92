// app/api/sse/[...pathname]/route.ts (App Router)
// 稳定版 SSE 中转逻辑，支持超时控制、异常处理

import { NextRequest } from "next/server";
import { getConfig } from "@/config";

const config = getConfig();

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ pathname: string[] }> }
) {
  const { pathname } = await params;
  const apiUrl = config.API_URL;
  const authToken = request.cookies.get("auth_token")?.value;

  const headers = new Headers(request.headers);
  headers.set("Accept", "text/event-stream");
  headers.set("Cache-Control", "no-cache");
  if (authToken) headers.set("Access-Token", authToken);

  const controller = new AbortController();
  const timeout = 1000 * 120; // 120 秒超时，避免挂住连接
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.warn("SSE upstream fetch aborted due to timeout");
  }, timeout);

  try {
    const upstream = await fetch(`${apiUrl}/${pathname.join("/")}`, {
      method: "POST",
      headers,
      body: request.body,
      duplex: "half",
      signal: controller.signal,
    } as RequestInit & { duplex?: string });

    clearTimeout(timeoutId);

    if (!upstream.ok || !upstream.body) {
      return new Response("SSE upstream 无响应", { status: 502 });
    }

    const stream = new ReadableStream({
      async start(controller) {
        const reader = upstream.body!.getReader();

        // 定时发送 ping 防止断流
        const pingInterval = setInterval(() => {
          controller.enqueue(new TextEncoder().encode(": ping\n\n"));
        }, 15000);

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            controller.enqueue(value);
          }
        } catch (error) {
          console.error("SSE stream error:", error);
        } finally {
          clearInterval(pingInterval);
          controller.close();
          console.log("SSE stream closed");
        }
      },
    });

    return new Response(stream, {
      status: 200,
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    clearTimeout(timeoutId);
    console.error("SSE Error:", error);
    return new Response("SSE 连接失败", { status: 500 });
  }
}
