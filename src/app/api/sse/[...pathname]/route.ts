// app/api/sse/[...pathname]/route.ts (App Router)
// 稳定版 SSE 中转逻辑，支持超时控制、异常处理、连接断开重试

import { NextRequest } from "next/server";
import { getConfig } from "@/config";

const config = getConfig();

// 判断是否为网络连接错误
function isNetworkError(error: any): boolean {
  return (
    error?.code === "UND_ERR_SOCKET" ||
    error?.cause?.code === "UND_ERR_SOCKET" ||
    error?.message?.includes("terminated") ||
    error?.message?.includes("other side closed") ||
    (error?.name === "TypeError" && error?.message === "terminated")
  );
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ pathname: string[] }> }
) {
  const { pathname } = await params;
  const apiUrl = config.API_URL;
  const authToken = request.cookies.get("auth_token")?.value;

  const headers = new Headers(request.headers);
  headers.set("Accept", "text/event-stream");
  headers.set("Cache-Control", "no-cache");
  if (authToken) headers.set("Access-Token", authToken);

  const controller = new AbortController();
  const timeout = 1000 * 180; // 180 秒超时，避免挂住连接
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.warn("SSE upstream fetch aborted due to timeout");
  }, timeout);

  try {
    const upstream = await fetch(`${apiUrl}/${pathname.join("/")}`, {
      method: "POST",
      headers,
      body: request.body,
      duplex: "half",
      signal: controller.signal,
    } as RequestInit & { duplex?: string });

    clearTimeout(timeoutId);

    if (!upstream.ok || !upstream.body) {
      console.error(
        `SSE upstream error: ${upstream.status} ${upstream.statusText}`
      );
      return new Response("SSE upstream 无响应", { status: 502 });
    }

    const stream = new ReadableStream({
      async start(streamController) {
        const reader = upstream.body!.getReader();
        let pingInterval: NodeJS.Timeout | null = null;
        let isStreamClosed = false;

        // 安全的发送数据函数
        const safeEnqueue = (data: Uint8Array) => {
          try {
            if (!isStreamClosed) {
              streamController.enqueue(data);
            }
          } catch (error) {
            console.warn(
              "Failed to enqueue data, stream may be closed:",
              error
            );
            isStreamClosed = true;
          }
        };

        // 安全的关闭流控制器函数
        const safeCloseController = () => {
          try {
            if (!isStreamClosed) {
              streamController.close();
              isStreamClosed = true;
            }
          } catch (error: any) {
            // 忽略已经关闭的错误
            if (error?.code !== "ERR_INVALID_STATE") {
              console.warn("Failed to close stream controller:", error);
            }
          }
        };

        // 定时发送 ping 防止断流
        pingInterval = setInterval(() => {
          if (!isStreamClosed) {
            safeEnqueue(new TextEncoder().encode(": ping\n\n"));
          } else {
            if (pingInterval) clearInterval(pingInterval);
          }
        }, 15000);

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              console.log("SSE upstream stream completed normally");
              break;
            }
            safeEnqueue(value);
          }
        } catch (error: any) {
          if (isNetworkError(error)) {
            console.warn("SSE upstream connection closed by remote server:", {
              message: error?.message,
              code: error?.code || error?.cause?.code,
              socket: error?.cause?.socket,
            });
            // 发送连接关闭事件给客户端
            safeEnqueue(
              new TextEncoder().encode(
                'event: connection_lost\ndata: {"reason": "upstream_disconnected"}\n\n'
              )
            );
          } else {
            console.error("SSE stream error:", error);
            // 发送错误事件给客户端
            safeEnqueue(
              new TextEncoder().encode(
                'event: error\ndata: {"reason": "stream_error"}\n\n'
              )
            );
          }
        } finally {
          isStreamClosed = true;
          if (pingInterval) {
            clearInterval(pingInterval);
            pingInterval = null;
          }

          try {
            reader.cancel();
          } catch (e) {
            console.warn("Failed to cancel reader:", e);
          }

          try {
            streamController.close();
          } catch (e) {
            console.warn("Failed to close stream controller:", e);
          }

          console.log("SSE stream cleanup completed");
        }
      },
    });

    return new Response(stream, {
      status: 200,
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  } catch (error: any) {
    clearTimeout(timeoutId);

    if (isNetworkError(error)) {
      console.warn("SSE connection failed due to network error:", error);
      return new Response("SSE 网络连接失败", { status: 502 });
    } else {
      console.error("SSE Error:", error);
      return new Response("SSE 连接失败", { status: 500 });
    }
  }
}
