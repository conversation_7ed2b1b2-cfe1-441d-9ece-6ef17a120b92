<svg width="22" height="58" viewBox="0 0 22 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3065_93396)" filter="url(#filter0_d_3065_93396)">
<g filter="url(#filter1_iii_3065_93396)">
<path d="M6.22363 50.0001H15.7763V27.4425C15.7763 25.9441 15.6079 24.4504 15.2742 22.9896L12.3619 10.2376C12.0045 8.67264 9.77042 8.68694 9.43308 10.2563L6.67024 23.11C6.37334 24.4913 6.22363 25.9001 6.22363 27.3129V50.0001Z" fill="#F6F6F6"/>
</g>
<path d="M6.22363 50.0001H15.7763V27.4425C15.7763 25.9441 15.6079 24.4504 15.2742 22.9896L12.3619 10.2376C12.0045 8.67264 9.77042 8.68694 9.43308 10.2563L6.67024 23.11C6.37334 24.4913 6.22363 25.9001 6.22363 27.3129V50.0001Z" stroke="#C8C8C8" stroke-width="0.1" stroke-linejoin="round"/>
<mask id="mask0_3065_93396" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="6" y="9" width="10" height="41">
<path d="M6.22363 50.0001H15.7763V27.4425C15.7763 25.9441 15.6079 24.4504 15.2742 22.9896L12.3619 10.2376C12.0045 8.67264 9.77042 8.68694 9.43308 10.2563L6.67024 23.11C6.37334 24.4913 6.22363 25.9001 6.22363 27.3129V50.0001Z" fill="white"/>
</mask>
<g mask="url(#mask0_3065_93396)">
<path d="M6 15.1844H16L11.1209 1.9549C10.956 1.50787 10.3187 1.5229 10.175 1.97721L6 15.1844Z" fill="url(#paint0_linear_3065_93396)"/>
</g>
</g>
<defs>
<filter id="filter0_d_3065_93396" x="-4" y="0" width="30" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.249446 0 0 0 0 0.24769 0 0 0 0 0.24769 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3065_93396"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3065_93396" result="shape"/>
</filter>
<filter id="filter1_iii_3065_93396" x="6.06358" y="9.02148" width="12.7626" height="41.0286" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3065_93396"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3065_93396" result="effect2_innerShadow_3065_93396"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.11"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.241421 0 0 0 0 0.296255 0 0 0 0 0.378505 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_3065_93396" result="effect3_innerShadow_3065_93396"/>
</filter>
<linearGradient id="paint0_linear_3065_93396" x1="-1.69231" y1="52.3104" x2="24.4615" y2="52.3104" gradientUnits="userSpaceOnUse">
<stop offset="0.119435"/>
<stop offset="0.464435" stop-color="#26AF6D"/>
<stop offset="0.819435" stop-color="#010101"/>
</linearGradient>
<clipPath id="clip0_3065_93396">
<rect width="22" height="50" fill="white"/>
</clipPath>
</defs>
</svg>
