<svg width="21" height="58" viewBox="0 0 21 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3065_93395)" filter="url(#filter0_d_3065_93395)">
<g filter="url(#filter1_ii_3065_93395)">
<path d="M6.07812 50.2906H14.9202L14.9203 25.0464H6.07819L6.07812 50.2906Z" fill="url(#paint0_linear_3065_93395)"/>
</g>
<path d="M6.02812 50.2906C6.02812 50.3038 6.03339 50.3166 6.04277 50.3259C6.05215 50.3353 6.06486 50.3406 6.07812 50.3406H14.9202C14.9478 50.3406 14.9702 50.3182 14.9702 50.2906L14.9703 25.0464C14.9703 25.0331 14.965 25.0204 14.9556 25.011C14.9463 25.0017 14.9336 24.9964 14.9203 24.9964H6.07819C6.05057 24.9964 6.02819 25.0188 6.02819 25.0464L6.02812 50.2906Z" stroke="#C8C8C8" stroke-width="0.1" stroke-linejoin="round"/>
<rect x="6.07812" y="25.0464" width="8.8421" height="0.27907" fill="#D0D0D0" fill-opacity="0.8"/>
<g filter="url(#filter2_ii_3065_93395)">
<path d="M6.19043 25.0465H14.8089C14.9469 25.0465 15.0589 24.9346 15.0589 24.7965V17.343C15.0589 17.2049 14.9469 17.093 14.8089 17.093H6.19043C6.05236 17.093 5.94043 17.2049 5.94043 17.343V24.7965C5.94043 24.9346 6.05236 25.0465 6.19043 25.0465Z" fill="url(#paint1_linear_3065_93395)"/>
</g>
<path d="M6.19043 25.0965H14.8089C14.9745 25.0965 15.1089 24.9622 15.1089 24.7965V17.343C15.1089 17.1773 14.9745 17.043 14.8089 17.043H6.19043C6.02474 17.043 5.89043 17.1773 5.89043 17.343V24.7965C5.89043 24.9622 6.02474 25.0965 6.19043 25.0965Z" stroke="#C8C8C8" stroke-width="0.1" stroke-linejoin="round"/>
<g filter="url(#filter3_i_3065_93395)">
<path d="M6.07812 17.093H14.9202V13.4211C14.9202 13.0301 14.9202 12.8346 14.9066 12.6697C14.7451 10.7213 13.1989 9.17511 11.2505 9.01366C11.0857 9 10.8902 9 10.4992 9C10.1082 9 9.91268 9 9.74781 9.01366C7.79943 9.17511 6.25323 10.7213 6.09179 12.6697C6.07812 12.8346 6.07812 13.0301 6.07812 13.4211V17.093Z" fill="url(#paint2_linear_3065_93395)"/>
</g>
<path d="M14.8702 13.4211V17.043H6.12813V13.4211C6.12813 13.0291 6.12817 12.8361 6.14162 12.6738C6.30104 10.7498 7.82791 9.22292 9.75194 9.06349C9.91423 9.05004 10.1072 9.05 10.4992 9.05C10.8912 9.05 11.0841 9.05004 11.2464 9.06349C13.1704 9.22292 14.6973 10.7498 14.8567 12.6738C14.8702 12.8361 14.8702 13.0291 14.8702 13.4211Z" stroke="#B44645" stroke-width="0.1" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_3065_93395" x="-4" y="0" width="29" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.249446 0 0 0 0 0.24769 0 0 0 0 0.24769 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3065_93395"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3065_93395" result="shape"/>
</filter>
<filter id="filter1_ii_3065_93395" x="5.64803" y="24.9463" width="11.3722" height="25.4443" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3065_93395"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.33"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3065_93395" result="effect2_innerShadow_3065_93395"/>
</filter>
<filter id="filter2_ii_3065_93395" x="5.51033" y="16.9929" width="11.6486" height="8.15356" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3065_93395"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.33"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3065_93395" result="effect2_innerShadow_3065_93395"/>
</filter>
<filter id="filter3_i_3065_93395" x="6.07812" y="9" width="8.84204" height="9.09302" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3065_93395"/>
</filter>
<linearGradient id="paint0_linear_3065_93395" x1="14.8627" y1="34.1907" x2="8.07481" y2="34.1907" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DEDEDE"/>
</linearGradient>
<linearGradient id="paint1_linear_3065_93395" x1="14.9995" y1="20.2907" x2="7.99951" y2="20.2907" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DEDEDE"/>
</linearGradient>
<linearGradient id="paint2_linear_3065_93395" x1="6.07812" y1="13.186" x2="14.9202" y2="13.186" gradientUnits="userSpaceOnUse">
<stop stop-color="#DF8595"/>
<stop offset="0.52" stop-color="#FFA3C4"/>
<stop offset="1" stop-color="#E58BAB"/>
</linearGradient>
<clipPath id="clip0_3065_93395">
<rect width="21" height="50" fill="white"/>
</clipPath>
</defs>
</svg>
