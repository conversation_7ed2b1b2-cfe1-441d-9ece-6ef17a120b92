{"v": "5.6.9", "fr": 30, "ip": 0, "op": 450, "w": 1270, "h": 900, "nm": "首页首屏demo原", "ddd": 0, "assets": [{"id": "image_0", "w": 165, "h": 165, "u": "images/home/", "p": "img_0.png", "e": 0}, {"id": "image_1", "w": 166, "h": 166, "u": "images/home/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 184, "h": 190, "u": "images/home/", "p": "img_2.png", "e": 0}, {"id": "image_3", "w": 100, "h": 101, "u": "images/home/", "p": "img_3.png", "e": 0}, {"id": "image_4", "w": 507, "h": 201, "u": "images/home/", "p": "img_4.png", "e": 0}, {"id": "image_5", "w": 116, "h": 116, "u": "images/home/", "p": "img_5.png", "e": 0}, {"id": "image_6", "w": 116, "h": 116, "u": "images/home/", "p": "img_6.png", "e": 0}, {"id": "image_7", "w": 223, "h": 224, "u": "images/home/", "p": "img_7.png", "e": 0}, {"id": "image_8", "w": 468, "h": 160, "u": "images/home/", "p": "img_8.png", "e": 0}, {"id": "image_9", "w": 358, "h": 232, "u": "images/home/", "p": "img_9.png", "e": 0}, {"id": "image_10", "w": 468, "h": 160, "u": "images/home/", "p": "img_10.png", "e": 0}, {"id": "image_11", "w": 358, "h": 232, "u": "images/home/", "p": "img_11.png", "e": 0}, {"id": "image_12", "w": 233, "h": 64, "u": "images/home/", "p": "img_12.png", "e": 0}, {"id": "image_13", "w": 233, "h": 64, "u": "images/home/", "p": "img_13.png", "e": 0}, {"id": "image_14", "w": 187, "h": 64, "u": "images/home/", "p": "img_14.png", "e": 0}, {"id": "image_15", "w": 236, "h": 64, "u": "images/home/", "p": "img_15.png", "e": 0}, {"id": "image_16", "w": 233, "h": 64, "u": "images/home/", "p": "img_16.png", "e": 0}, {"id": "image_17", "w": 234, "h": 64, "u": "images/home/", "p": "img_17.png", "e": 0}, {"id": "image_18", "w": 541, "h": 733, "u": "images/home/", "p": "img_18.png", "e": 0}, {"id": "image_19", "w": 370, "h": 368, "u": "images/home/", "p": "img_19.png", "e": 0}, {"id": "image_20", "w": 541, "h": 733, "u": "images/home/", "p": "img_20.png", "e": 0}, {"id": "image_21", "w": 1108, "h": 652, "u": "images/home/", "p": "img_21.png", "e": 0}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "emojifire", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1047, 372, 0], "ix": 2}, "a": {"a": 0, "k": [82.5, 82.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 167, "op": 420, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "emojilike", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [243, 626, 0], "ix": 2}, "a": {"a": 0, "k": [83, 83, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 167, "op": 420, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "cursor", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [100]}, {"t": 150, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [757, 786, 0], "to": [10, 13, 0], "ti": [-10, -13, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [817, 864, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [817, 864, 0], "to": [-12, 0, 0], "ti": [12, 0, 0]}, {"t": 162, "s": [745, 864, 0]}], "ix": 2}, "a": {"a": 0, "k": [92, 95, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 15, "s": [100, 100, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, -4.833]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 30, "s": [90, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 34, "s": [100, 100, 100]}, {"t": 150, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 420, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "textSquare", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [783.5, 809.5, 0], "ix": 2}, "a": {"a": 0, "k": [10, 10.5, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.314, 0.16], [-0.366, 0.029], [-0.714, 0], [0, 0], [-0.375, -0.031], [-0.13, -0.066], [-0.159, -0.313], [-0.03, -0.365], [0, -0.714], [0, 0], [0.03, -0.375], [0.067, -0.13], [0.314, -0.16], [0.366, -0.03], [0.714, 0], [0, 0], [0.375, 0.031], [0.13, 0.066], [0.159, 0.314], [0.03, 0.365], [0, 0.713], [0, 0], [-0.03, 0.375], [-0.067, 0.131]], "o": [[0.13, -0.066], [0.375, -0.031], [0, 0], [0.714, 0], [0.366, 0.029], [0.314, 0.16], [0.067, 0.131], [0.03, 0.375], [0, 0], [0, 0.713], [-0.03, 0.365], [-0.159, 0.314], [-0.13, 0.066], [-0.375, 0.031], [0, 0], [-0.714, 0], [-0.366, -0.03], [-0.314, -0.16], [-0.067, -0.13], [-0.03, -0.375], [0, 0], [0, -0.714], [0.03, -0.365], [0.159, -0.313]], "v": [[-5.965, -6.693], [-5.282, -6.844], [-3.708, -6.876], [3.708, -6.876], [5.282, -6.844], [5.965, -6.693], [6.693, -5.965], [6.844, -5.283], [6.875, -3.708], [6.875, 3.708], [6.844, 5.283], [6.693, 5.964], [5.965, 6.693], [5.282, 6.844], [3.708, 6.874], [-3.708, 6.874], [-5.282, 6.844], [-5.965, 6.693], [-6.693, 5.964], [-6.844, 5.283], [-6.875, 3.708], [-6.875, -3.708], [-6.844, -5.283], [-6.693, -5.965]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.671, 0], [0, 0], [0.451, -0.036], [0.405, -0.206], [0.319, -0.628], [0.038, -0.468], [0, -0.671], [0, 0], [-0.037, -0.451], [-0.206, -0.405], [-0.627, -0.32], [-0.469, -0.038], [-0.671, 0], [0, 0], [-0.451, 0.037], [-0.405, 0.206], [-0.319, 0.627], [-0.038, 0.469], [0, 0.671], [0, 0], [0.037, 0.451], [0.206, 0.404], [0.627, 0.32], [0.469, 0.039]], "o": [[0, 0], [-0.671, 0], [-0.469, 0.039], [-0.627, 0.32], [-0.206, 0.404], [-0.037, 0.451], [0, 0], [0, 0.671], [0.038, 0.469], [0.319, 0.627], [0.405, 0.206], [0.451, 0.037], [0, 0], [0.671, 0], [0.469, -0.038], [0.627, -0.32], [0.206, -0.405], [0.037, -0.451], [0, 0], [0, -0.671], [-0.038, -0.468], [-0.319, -0.628], [-0.405, -0.206], [-0.451, -0.036]], "v": [[3.743, -8.542], [-3.743, -8.542], [-5.418, -8.505], [-6.722, -8.178], [-8.178, -6.721], [-8.505, -5.419], [-8.542, -3.742], [-8.542, 3.742], [-8.505, 5.417], [-8.178, 6.721], [-6.722, 8.178], [-5.418, 8.505], [-3.743, 8.542], [3.743, 8.542], [5.418, 8.505], [6.722, 8.178], [8.178, 6.721], [8.505, 5.417], [8.542, 3.742], [8.542, -3.742], [8.505, -5.419], [8.178, -6.721], [6.722, -8.178], [5.418, -8.505]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0, 0, 0, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [0, 0, 0, 1]}, {"t": 159, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10, 10.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.139, -0.557], [0, 0], [-0.446, -0.112], [-0.112, 0.446], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.46], [-0.461, 0], [0, 0], [0, 0.461], [0.46, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.446, 0.112], [0.111, 0.446], [0, 0], [0.573, 0]], "o": [[-0.574, 0], [0, 0], [-0.112, 0.446], [0.447, 0.112], [0, 0], [0, 0], [0, 0], [0, 0], [-0.461, 0], [0, 0.461], [0, 0], [0.46, 0], [0, -0.46], [0, 0], [0, 0], [0, 0], [0, 0], [0.111, 0.446], [0.447, -0.112], [0, 0], [-0.139, -0.557], [0, 0]], "v": [[-3.841, -5.208], [-5.054, -4.262], [-5.183, -3.743], [-4.577, -2.734], [-3.566, -3.34], [-3.516, -3.542], [-0.833, -3.542], [-0.833, 3.542], [-1.666, 3.542], [-2.5, 4.374], [-1.666, 5.208], [1.667, 5.208], [2.5, 4.374], [1.667, 3.542], [0.834, 3.542], [0.834, -3.542], [3.516, -3.542], [3.567, -3.34], [4.577, -2.734], [5.184, -3.743], [5.054, -4.262], [3.842, -5.208]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0, 0, 0, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [0, 0, 0, 1]}, {"t": 159, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10, 10.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "ultraHD", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [557.25, 809.8, 0], "ix": 2}, "a": {"a": 0, "k": [10.5, 10.5, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.454, 6.25], [3.874, 6.25], [4.792, 3.608], [5.441, 3.608], [6.361, 6.25], [5.779, 6.25], [5.583, 5.609], [4.65, 5.609]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.141, -0.068], [-0.073, -0.126], [0, -0.173], [0, 0], [0.085, -0.136], [0.151, -0.06], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0.2, 0], [0.141, 0.067], [0.074, 0.125], [0, 0], [0, 0.17], [-0.083, 0.134], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.458, 6.25], [1.458, 3.608], [2.559, 3.608], [3.07, 3.71], [3.393, 4], [3.505, 4.448], [3.505, 4.452], [3.378, 4.91], [3.027, 5.201], [3.583, 6.25], [2.956, 6.25], [2.464, 5.283], [2.012, 5.283], [2.012, 6.25]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.178, 6.25], [-0.376, 6.25], [-0.376, 4.064], [-1.169, 4.064], [-1.169, 3.608], [0.969, 3.608], [0.969, 4.064], [0.178, 4.064]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.9, 6.25], [-2.9, 3.608], [-2.346, 3.608], [-2.346, 5.794], [-1.171, 5.794], [-1.171, 6.25]], "c": true}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.227, 0], [0.166, 0.082], [0.092, 0.146], [0, 0.197], [0, 0], [0, 0], [0, 0], [-0.043, -0.084], [-0.082, -0.046], [-0.119, 0], [-0.082, 0.047], [-0.043, 0.083], [0, 0.113], [0, 0], [0, 0], [0, 0], [0.09, -0.148], [0.166, -0.082]], "o": [[-0.226, 0], [-0.165, -0.082], [-0.09, -0.147], [0, 0], [0, 0], [0, 0], [0, 0.113], [0.044, 0.083], [0.083, 0.047], [0.118, 0], [0.083, -0.046], [0.042, -0.084], [0, 0], [0, 0], [0, 0], [0, 0.195], [-0.089, 0.146], [-0.164, 0.082]], "v": [[-4.646, 6.318], [-5.234, 6.195], [-5.618, 5.853], [-5.753, 5.338], [-5.753, 3.608], [-5.201, 3.608], [-5.201, 5.289], [-5.137, 5.585], [-4.947, 5.779], [-4.646, 5.849], [-4.346, 5.779], [-4.156, 5.585], [-4.092, 5.289], [-4.092, 3.608], [-3.54, 3.608], [-3.54, 5.338], [-3.676, 5.853], [-4.059, 6.195]], "c": true}, "ix": 2}, "nm": "路径 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[-0.353, 0.18], [-0.378, 0.031], [-0.71, 0], [0, 0], [-0.386, -0.032], [-0.165, -0.084], [-0.179, -0.353], [-0.031, -0.378], [0, -0.71], [0, 0], [0, 0], [0, 0], [-0.031, 0.385], [-0.083, 0.164]], "o": [[0.164, -0.084], [0.386, -0.032], [0, 0], [0.71, 0], [0.378, 0.031], [0.353, 0.18], [0.084, 0.164], [0.031, 0.385], [0, 0], [0, 0], [0, 0], [0, -0.71], [0.031, -0.378], [0.18, -0.353]], "v": [[-6.684, -6.046], [-5.924, -6.218], [-4.333, -6.25], [4.333, -6.25], [5.924, -6.218], [6.684, -6.046], [7.503, -5.226], [7.676, -4.466], [7.708, -2.875], [7.708, 2.5], [-7.708, 2.5], [-7.708, -2.875], [-7.676, -4.466], [-7.505, -5.226]], "c": true}, "ix": 2}, "nm": "路径 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0.678, 0], [0, 0], [0.443, -0.036], [0.371, -0.189], [0.299, -0.588], [0.037, -0.455], [0, -0.677], [0, 0], [-0.036, -0.442], [-0.189, -0.37], [-0.588, -0.299], [-0.455, -0.037], [-0.677, 0], [0, 0], [-0.443, 0.036], [-0.371, 0.189], [-0.299, 0.588], [-0.038, 0.455], [0, 0.677], [0, 0], [0.036, 0.443], [0.189, 0.37], [0.587, 0.299], [0.455, 0.037]], "o": [[0, 0], [-0.677, 0], [-0.455, 0.037], [-0.588, 0.299], [-0.189, 0.37], [-0.036, 0.443], [0, 0], [0, 0.677], [0.037, 0.455], [0.299, 0.588], [0.371, 0.189], [0.443, 0.036], [0, 0], [0.678, 0], [0.455, -0.037], [0.587, -0.299], [0.189, -0.37], [0.036, -0.442], [0, 0], [0, -0.677], [-0.038, -0.455], [-0.299, -0.588], [-0.371, -0.189], [-0.443, -0.036]], "v": [[4.359, -7.5], [-4.361, -7.5], [-6.027, -7.464], [-7.253, -7.159], [-8.618, -5.794], [-8.923, -4.568], [-8.958, -2.902], [-8.958, 2.902], [-8.923, 4.568], [-8.618, 5.794], [-7.253, 7.159], [-6.027, 7.464], [-4.361, 7.5], [4.359, 7.5], [6.027, 7.464], [7.253, 7.159], [8.617, 5.794], [8.923, 4.568], [8.958, 2.902], [8.958, -2.902], [8.923, -4.568], [8.617, -5.794], [7.253, -7.159], [6.027, -7.464]], "c": true}, "ix": 2}, "nm": "路径 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.5, 10.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.34, 0.53], [0.34, 0.53], [0.017, -0.53], [-0.016, -0.53]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.617, 15.165], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.08, -0.076], [0, -0.128], [0, 0], [0.078, -0.074], [0.138, 0]], "o": [[0, 0], [0, 0], [0.139, 0], [0.079, 0.076], [0, 0], [0, 0.13], [-0.077, 0.073], [0, 0]], "v": [[-0.463, 0.42], [-0.463, -0.42], [0.017, -0.42], [0.345, -0.307], [0.463, -0.001], [0.463, 0.003], [0.346, 0.31], [0.024, 0.42]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.975, 14.96], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.14, -0.091], [-0.074, -0.164], [0, -0.218], [0.077, -0.25], [0.138, -0.175], [0.188, -0.091], [0.226, 0], [0, 0]], "o": [[0, 0], [0.199, 0], [0.143, 0.092], [0.075, 0.162], [0, 0.318], [-0.074, 0.251], [-0.135, 0.175], [-0.186, 0.092], [0, 0], [0, 0]], "v": [[-0.306, -1.562], [0.263, -1.562], [0.772, -1.426], [1.098, -1.042], [1.21, -0.471], [1.094, 0.382], [0.776, 1.02], [0.291, 1.421], [-0.326, 1.559], [-0.954, 1.559]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.371, 0.178], [-0.268, 0.321], [-0.144, 0.44], [0, 0.526], [0.186, 0.331], [0.334, 0.175], [0.443, 0]], "o": [[0, 0], [0, 0], [0.453, 0], [0.371, -0.178], [0.268, -0.323], [0.143, -0.439], [0, -0.469], [-0.186, -0.332], [-0.331, -0.178], [0, 0]], "v": [[-1.722, -2.917], [-2.944, 2.917], [-0.083, 2.917], [1.154, 2.65], [2.113, 1.902], [2.73, 0.758], [2.944, -0.689], [2.666, -1.889], [1.886, -2.65], [0.724, -2.917]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13.806, 8.625], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.087, 2.917], [3.308, -2.917], [1.61, -2.917], [1.144, -0.697], [-0.857, -0.697], [-0.392, -2.917], [-2.086, -2.917], [-3.308, 2.917], [-1.617, 2.917], [-1.139, 0.652], [0.862, 0.652], [0.388, 2.917]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.557, 8.625], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 452, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "imageBackgroundRemover", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [484.25, 809.8, 0], "ix": 2}, "a": {"a": 0, "k": [10.5, 10.5, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.004, 0.251], [0, 0]], "o": [[0, 0], [0, 0], [0, -0.336], [0, 0], [0, 0]], "v": [[5.417, -1.25], [7.708, -1.25], [7.708, -2.875], [7.704, -3.75], [5.417, -3.75]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0.336], [0, 0]], "o": [[0, 0], [0, 0], [0.004, -0.251], [0, 0], [0, 0]], "v": [[5.417, 1.25], [5.417, 3.75], [7.704, 3.75], [7.708, 2.875], [7.708, 1.25]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.352, 0.33], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.433, 0], [0, 0], [0.385, -0.032], [0.164, -0.084], [0.18, -0.353], [0.031, -0.378], [0, -0.71], [0, 0], [-0.032, -0.385], [-0.084, -0.164], [-0.353, -0.18], [-0.378, -0.031], [-0.71, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.117, 0.468], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.117, -0.468], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.297, -0.007], [0, 0], [-0.71, 0], [-0.378, 0.031], [-0.353, 0.18], [-0.084, 0.164], [-0.032, 0.385], [0, 0], [0, 0.71], [0.031, 0.378], [0.18, 0.353], [0.164, 0.084], [0.385, 0.031], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.352, -0.33], [0, 0], [0, 0]], "v": [[5.417, 1.25], [5.417, -1.25], [3.749, -1.25], [3.721, -1.366], [3.001, -2.588], [2.917, -2.667], [2.917, -3.75], [0.417, -3.75], [0.417, -6.25], [2.917, -6.25], [2.917, -3.75], [5.417, -3.75], [5.417, -6.243], [4.333, -6.25], [-4.333, -6.25], [-5.924, -6.218], [-6.684, -6.046], [-7.505, -5.226], [-7.676, -4.466], [-7.708, -2.875], [-7.708, 2.875], [-7.676, 4.466], [-7.505, 5.226], [-6.684, 6.046], [-5.924, 6.218], [-4.333, 6.25], [0.417, 6.25], [0.417, 3.75], [2.917, 3.75], [2.917, 2.667], [3.001, 2.588], [3.721, 1.366], [3.749, 1.25]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.297, 0.007]], "o": [[0, 0], [0, 0], [0, 0], [0.433, 0], [0, 0]], "v": [[5.417, 3.75], [2.917, 3.75], [2.917, 6.25], [4.333, 6.25], [5.417, 6.243]], "c": true}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0.037, -0.442], [0.189, -0.37], [0.588, -0.299], [0.455, -0.037], [0.678, 0], [0, 0], [0.443, 0.036], [0.37, 0.189], [0.299, 0.588], [0.037, 0.455], [0, 0.677], [0, 0], [-0.036, 0.443], [-0.189, 0.37], [-0.588, 0.299], [-0.455, 0.037], [-0.677, 0], [0, 0], [-0.443, -0.036], [-0.37, -0.189], [-0.299, -0.588], [-0.037, -0.455], [0, -0.677], [0, 0]], "o": [[-0.037, 0.455], [-0.299, 0.588], [-0.37, 0.189], [-0.443, 0.036], [0, 0], [-0.677, 0], [-0.455, -0.037], [-0.588, -0.299], [-0.189, -0.37], [-0.036, -0.442], [0, 0], [0, -0.677], [0.037, -0.455], [0.299, -0.588], [0.37, -0.189], [0.443, -0.036], [0, 0], [0.678, 0], [0.455, 0.037], [0.588, 0.299], [0.189, 0.37], [0.037, 0.443], [0, 0], [0, 0.677]], "v": [[8.922, 4.568], [8.618, 5.794], [7.251, 7.159], [6.027, 7.464], [4.359, 7.5], [-4.361, 7.5], [-6.027, 7.464], [-7.253, 7.159], [-8.618, 5.794], [-8.923, 4.568], [-8.958, 2.902], [-8.958, -2.902], [-8.923, -4.568], [-8.618, -5.794], [-7.253, -7.159], [-6.027, -7.464], [-4.361, -7.5], [4.359, -7.5], [6.027, -7.464], [7.251, -7.159], [8.618, -5.794], [8.922, -4.568], [8.958, -2.902], [8.958, 2.902]], "c": true}, "ix": 2}, "nm": "路径 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.5, 10.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 7, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.611], [-1.611, 0], [0, 1.611], [1.611, 0]], "o": [[0, 1.611], [1.611, 0], [0, -1.611], [-1.611, 0]], "v": [[-2.917, 0], [0.001, 2.917], [2.917, 0], [0.001, -2.917]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.5, 10.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 452, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "eliminate", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [631.25, 809.8, 0], "ix": 2}, "a": {"a": 0, "k": [10.5, 10.5, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.049, 0.298], [0.341, 0.056], [0.056, -0.341], [0, -0.38], [-0.059, -0.365], [-0.34, 0.056], [0.056, 0.341], [0, 0.313]], "o": [[0.056, -0.341], [-0.34, -0.056], [-0.059, 0.365], [0, 0.381], [0.056, 0.341], [0.341, -0.056], [-0.049, -0.298], [0, -0.312]], "v": [[0.634, -0.917], [0.118, -1.635], [-0.6, -1.12], [-0.69, -0.001], [-0.6, 1.119], [0.118, 1.635], [0.634, 0.917], [0.56, -0.001]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.857, 9.042], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.5, 0.359], [0.202, 0.28], [0.28, -0.202], [0.438, -0.61], [-0.28, -0.201], [-0.201, 0.28]], "o": [[0.28, -0.201], [-0.201, -0.281], [-0.61, 0.437], [-0.201, 0.28], [0.28, 0.201], [0.359, -0.5]], "v": [[1.084, -0.212], [1.227, -1.084], [0.355, -1.227], [-1.228, 0.356], [-1.085, 1.228], [-0.213, 1.085]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.681, 4.681], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.381, 0], [0.365, -0.06], [-0.056, -0.341], [-0.341, 0.056], [-0.313, 0], [-0.298, -0.049], [-0.056, 0.34], [0.34, 0.056]], "o": [[-0.381, 0], [-0.34, 0.056], [0.056, 0.34], [0.298, -0.049], [0.313, 0], [0.341, 0.056], [0.056, -0.341], [-0.365, -0.06]], "v": [[0, -0.69], [-1.12, -0.599], [-1.635, 0.119], [-0.917, 0.634], [0, 0.56], [0.917, 0.634], [1.635, 0.119], [1.12, -0.599]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.042, 2.857], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.139, 1.139], [0, 0], [0, 0], [0, 0], [1.139, 1.139]], "o": [[0, 0], [0, 0], [0, 0], [-1.139, 1.139], [-1.139, -1.139]], "v": [[-5.805, 1.68], [-5.156, 1.031], [-1.031, 5.156], [-1.68, 5.805], [-5.805, 5.805]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.147, -4.272], [4.272, -0.147], [-0.148, 4.272], [-4.272, 0.147]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.14, -1.139], [1.139, -1.139], [0, 0], [0, 0]], "o": [[1.139, -1.139], [1.139, 1.139], [0, 0], [0, 0], [0, 0]], "v": [[1.68, -5.805], [5.805, -5.805], [5.805, -1.68], [5.156, -1.031], [1.031, -5.156]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[1.628, 1.628], [1.627, -1.627], [0, 0], [-1.627, -1.627], [-1.627, 1.627], [0, 0]], "o": [[-1.627, -1.627], [0, 0], [-1.627, 1.627], [1.627, 1.627], [0, 0], [1.628, -1.627]], "v": [[6.688, -6.689], [0.796, -6.689], [-6.689, 0.796], [-6.689, 6.689], [-0.796, 6.689], [6.688, -0.796]], "c": true}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.639215686275, 0.682352941176, 0.749019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.497, 11.178], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 6, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 452, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "aiGenerateImage", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [705.25, 809.8, 0], "ix": 2}, "a": {"a": 0, "k": [10.5, 10.5, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.768, 3.475], [0.769, 3.475], [0.769, -3.475], [-0.768, -3.475]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 420, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 460, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 510, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 570, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 579, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 583, "s": [0, 0, 0, 1]}, {"t": 600, "s": [0.639215686275, 0.682352941176, 0.749019607843, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.481, 14.526], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.116, 0], [-0.042, -0.145], [0, 0], [0, 0]], "o": [[0.042, -0.145], [0.126, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.227, -1.982], [0.005, -2.189], [0.236, -1.982], [1.036, 0.775], [-1.038, 0.775]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.674, 0], [0.242, -0.754]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.242, -0.754], [-0.663, 0], [0, 0]], "v": [[-3.417, 3.542], [-1.848, 3.542], [-1.438, 2.128], [1.436, 2.117], [1.848, 3.542], [3.417, 3.542], [1.542, -2.426], [-0.005, -3.542], [-1.542, -2.426]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 420, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 460, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 510, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 570, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 579, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 583, "s": [0, 0, 0, 1]}, {"t": 600, "s": [0.639215686275, 0.682352941176, 0.749019607843, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13.083, 14.458], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.643, 0], [0, 0], [0.421, -0.034], [0.356, -0.18], [0.288, -0.56], [0.035, -0.431], [0, -0.635], [0, 0], [-0.035, -0.417], [-0.183, -0.355], [-0.564, -0.284], [-0.434, -0.035], [-0.643, 0], [0, 0], [0, 0], [0, 0], [0.365, 0.029], [0.152, 0.077], [0.167, 0.324], [0.03, 0.326], [0, 0], [-0.275, 0.226], [-0.159, 0.051], [-0.345, -0.109], [-0.269, -0.221], [-0.471, -0.455], [0, 0], [0, 0], [0, 0], [-0.26, 0.287], [0.312, 0.257], [0.373, 0.118], [0.591, -0.188], [0.328, -0.27], [0.448, -0.433], [0, 0], [0, 0], [-0.03, 0.359], [-0.076, 0.147], [-0.33, 0.166], [-0.358, 0.029], [-0.675, 0], [0, 0], [-0.365, -0.03], [-0.152, -0.076], [-0.167, -0.324], [-0.029, -0.353], [0, -0.668], [0, 0], [0, 0], [0, 0], [0.035, 0.417], [0.183, 0.355], [0.564, 0.284], [0.434, 0.035]], "o": [[0, 0], [-0.643, 0], [-0.434, 0.035], [-0.564, 0.284], [-0.183, 0.355], [-0.035, 0.417], [0, 0], [0, 0.635], [0.035, 0.431], [0.288, 0.56], [0.356, 0.179], [0.421, 0.034], [0, 0], [0, 0], [0, 0], [-0.675, 0], [-0.358, -0.029], [-0.33, -0.166], [-0.073, -0.142], [0, 0], [0.471, -0.455], [0.268, -0.221], [0.344, -0.109], [0.158, 0.051], [0.274, 0.226], [0, 0], [0, 0], [0, 0], [0.137, -0.425], [-0.437, -0.423], [-0.328, -0.27], [-0.591, -0.188], [-0.372, 0.118], [-0.318, 0.261], [0, 0], [0, 0], [0, -0.668], [0.029, -0.353], [0.167, -0.324], [0.152, -0.076], [0.365, -0.03], [0, 0], [0.675, 0], [0.358, 0.029], [0.329, 0.166], [0.076, 0.147], [0.03, 0.359], [0, 0], [0, 0], [0, 0], [0, -0.635], [-0.035, -0.431], [-0.288, -0.56], [-0.356, -0.18], [-0.421, -0.034]], "v": [[4.143, -7.083], [-4.143, -7.083], [-5.727, -7.049], [-6.901, -6.76], [-8.213, -5.462], [-8.507, -4.294], [-8.542, -2.727], [-8.542, 2.727], [-8.507, 4.294], [-8.213, 5.462], [-6.901, 6.76], [-5.727, 7.049], [-4.143, 7.083], [-1.465, 7.083], [-1.073, 5.833], [-4.117, 5.833], [-5.626, 5.803], [-6.339, 5.643], [-7.102, 4.889], [-7.258, 4.234], [-4.366, 1.438], [-3.293, 0.441], [-2.688, 0.07], [-1.629, 0.07], [-1.024, 0.441], [0.048, 1.438], [0.247, 1.63], [0.266, 1.57], [0.267, 1.566], [0.875, 0.499], [-0.23, -0.525], [-1.251, -1.122], [-3.067, -1.122], [-4.087, -0.525], [-5.216, 0.52], [-7.292, 2.527], [-7.292, -2.701], [-7.261, -4.191], [-7.102, -4.889], [-6.339, -5.645], [-5.626, -5.803], [-4.117, -5.833], [4.117, -5.833], [5.626, -5.803], [6.339, -5.645], [7.102, -4.889], [7.261, -4.191], [7.292, -2.701], [7.292, -0.283], [8.542, -0.283], [8.542, -2.727], [8.507, -4.294], [8.213, -5.462], [6.901, -6.76], [5.727, -7.049]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 420, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 460, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 510, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 570, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 579, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 583, "s": [0, 0, 0, 1]}, {"t": 600, "s": [0.639215686275, 0.682352941176, 0.749019607843, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.083, 10.083], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 420, "op": 872, "st": 420, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "aiGenerateImage", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [705.25, 809.8, 0], "ix": 2}, "a": {"a": 0, "k": [10.5, 10.5, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.768, 3.475], [0.769, 3.475], [0.769, -3.475], [-0.768, -3.475]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 40, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 90, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 150, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 167, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"t": 171, "s": [0, 0, 0, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.481, 14.526], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.116, 0], [-0.042, -0.145], [0, 0], [0, 0]], "o": [[0.042, -0.145], [0.126, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.227, -1.982], [0.005, -2.189], [0.236, -1.982], [1.036, 0.775], [-1.038, 0.775]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.674, 0], [0.242, -0.754]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.242, -0.754], [-0.663, 0], [0, 0]], "v": [[-3.417, 3.542], [-1.848, 3.542], [-1.438, 2.128], [1.436, 2.117], [1.848, 3.542], [3.417, 3.542], [1.542, -2.426], [-0.005, -3.542], [-1.542, -2.426]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 40, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 90, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 150, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 167, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"t": 168, "s": [0, 0, 0, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13.083, 14.458], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.643, 0], [0, 0], [0.421, -0.034], [0.356, -0.18], [0.288, -0.56], [0.035, -0.431], [0, -0.635], [0, 0], [-0.035, -0.417], [-0.183, -0.355], [-0.564, -0.284], [-0.434, -0.035], [-0.643, 0], [0, 0], [0, 0], [0, 0], [0.365, 0.029], [0.152, 0.077], [0.167, 0.324], [0.03, 0.326], [0, 0], [-0.275, 0.226], [-0.159, 0.051], [-0.345, -0.109], [-0.269, -0.221], [-0.471, -0.455], [0, 0], [0, 0], [0, 0], [-0.26, 0.287], [0.312, 0.257], [0.373, 0.118], [0.591, -0.188], [0.328, -0.27], [0.448, -0.433], [0, 0], [0, 0], [-0.03, 0.359], [-0.076, 0.147], [-0.33, 0.166], [-0.358, 0.029], [-0.675, 0], [0, 0], [-0.365, -0.03], [-0.152, -0.076], [-0.167, -0.324], [-0.029, -0.353], [0, -0.668], [0, 0], [0, 0], [0, 0], [0.035, 0.417], [0.183, 0.355], [0.564, 0.284], [0.434, 0.035]], "o": [[0, 0], [-0.643, 0], [-0.434, 0.035], [-0.564, 0.284], [-0.183, 0.355], [-0.035, 0.417], [0, 0], [0, 0.635], [0.035, 0.431], [0.288, 0.56], [0.356, 0.179], [0.421, 0.034], [0, 0], [0, 0], [0, 0], [-0.675, 0], [-0.358, -0.029], [-0.33, -0.166], [-0.073, -0.142], [0, 0], [0.471, -0.455], [0.268, -0.221], [0.344, -0.109], [0.158, 0.051], [0.274, 0.226], [0, 0], [0, 0], [0, 0], [0.137, -0.425], [-0.437, -0.423], [-0.328, -0.27], [-0.591, -0.188], [-0.372, 0.118], [-0.318, 0.261], [0, 0], [0, 0], [0, -0.668], [0.029, -0.353], [0.167, -0.324], [0.152, -0.076], [0.365, -0.03], [0, 0], [0.675, 0], [0.358, 0.029], [0.329, 0.166], [0.076, 0.147], [0.03, 0.359], [0, 0], [0, 0], [0, 0], [0, -0.635], [-0.035, -0.431], [-0.288, -0.56], [-0.356, -0.18], [-0.421, -0.034]], "v": [[4.143, -7.083], [-4.143, -7.083], [-5.727, -7.049], [-6.901, -6.76], [-8.213, -5.462], [-8.507, -4.294], [-8.542, -2.727], [-8.542, 2.727], [-8.507, 4.294], [-8.213, 5.462], [-6.901, 6.76], [-5.727, 7.049], [-4.143, 7.083], [-1.465, 7.083], [-1.073, 5.833], [-4.117, 5.833], [-5.626, 5.803], [-6.339, 5.643], [-7.102, 4.889], [-7.258, 4.234], [-4.366, 1.438], [-3.293, 0.441], [-2.688, 0.07], [-1.629, 0.07], [-1.024, 0.441], [0.048, 1.438], [0.247, 1.63], [0.266, 1.57], [0.267, 1.566], [0.875, 0.499], [-0.23, -0.525], [-1.251, -1.122], [-3.067, -1.122], [-4.087, -0.525], [-5.216, 0.52], [-7.292, 2.527], [-7.292, -2.701], [-7.261, -4.191], [-7.102, -4.889], [-6.339, -5.645], [-5.626, -5.803], [-4.117, -5.833], [4.117, -5.833], [5.626, -5.803], [6.339, -5.645], [7.102, -4.889], [7.261, -4.191], [7.292, -2.701], [7.292, -0.283], [8.542, -0.283], [8.542, -2.727], [8.507, -4.294], [8.213, -5.462], [6.901, -6.76], [5.727, -7.049]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 40, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 90, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 150, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 167, "s": [0.639215707779, 0.68235296011, 0.749019622803, 1]}, {"t": 168, "s": [0, 0, 0, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.083, 10.083], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 452, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 2, "nm": "selected", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 40, "s": [100]}, {"i": {"x": [0.583], "y": [1]}, "o": {"x": [0.24], "y": [0]}, "t": 150, "s": [100]}, {"i": {"x": [0.742], "y": [-10.631]}, "o": {"x": [0.258], "y": [0]}, "t": 153, "s": [0]}, {"i": {"x": [0.76], "y": [1]}, "o": {"x": [0.417], "y": [0.031]}, "t": 165, "s": [0]}, {"t": 167, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.33, "y": 0.33}, "t": 30, "s": [783.375, 814.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.33, "y": 0.33}, "t": 40, "s": [783.375, 814.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.448, "y": 1}, "o": {"x": 0.481, "y": 0}, "t": 153, "s": [783.375, 814.5, 0], "to": [-7.119, 0, 0], "ti": [6.047, 0, 0]}, {"t": 161, "s": [704.375, 814.5, 0]}], "ix": 2}, "a": {"a": 0, "k": [50, 50.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 419, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 2, "nm": "tab", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [635, 811, 0], "ix": 2}, "a": {"a": 0, "k": [253.5, 100.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 453, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 2, "nm": "emojilight", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [259, 636, 0], "ix": 2}, "a": {"a": 0, "k": [58, 58, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 167, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 2, "nm": "emojiraibbow", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [247, 112, 0], "ix": 2}, "a": {"a": 0, "k": [58, 58, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 167, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 2, "nm": "InputCursor", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1038, 381, 0], "ix": 2}, "a": {"a": 0, "k": [111.5, 112, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 167, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 2, "nm": "EditpictureUIStyle", "refId": "image_8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 179, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 197, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 306, "s": [100]}, {"t": 313, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.75, "y": 0.75}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [1037, 577, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.75, "y": 1}, "o": {"x": 0.25, "y": 0}, "t": 179, "s": [1037, 577, 0], "to": [0, -3.333, 0], "ti": [0, 3.333, 0]}, {"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.25, "y": 0.25}, "t": 197, "s": [1037, 557, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 306, "s": [1037, 557, 0], "to": [0, 3.333, 0], "ti": [0, -3.333, 0]}, {"t": 313, "s": [1037, 577, 0]}], "ix": 2}, "a": {"a": 0, "k": [234, 80, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 139, "op": 416, "st": -34, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 2, "nm": "PromptuiB", "refId": "image_9", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 139, "s": [0]}, {"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 183, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 199, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 311, "s": [100]}, {"t": 317, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.75, "y": 0.75}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [179.5, 337.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.75, "y": 1}, "o": {"x": 0.25, "y": 0}, "t": 183, "s": [179.5, 337.5, 0], "to": [0, -6.667, 0], "ti": [0, 6.667, 0]}, {"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.25, "y": 0.25}, "t": 199, "s": [179.5, 297.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 311, "s": [179.5, 297.5, 0], "to": [0, 6.667, 0], "ti": [0, -6.667, 0]}, {"t": 317, "s": [179.5, 337.5, 0]}], "ix": 2}, "a": {"a": 0, "k": [179, 116, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 139, "op": 416, "st": -34, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 2, "nm": "TexttopictureUIStyle", "refId": "image_10", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 40, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 58, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 167, "s": [100]}, {"t": 174, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.75, "y": 0.75}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [1037, 577, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.75, "y": 1}, "o": {"x": 0.25, "y": 0}, "t": 40, "s": [1037, 577, 0], "to": [0, -3.333, 0], "ti": [0, 3.333, 0]}, {"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.25, "y": 0.25}, "t": 58, "s": [1037, 557, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 167, "s": [1037, 557, 0], "to": [0, 3.333, 0], "ti": [0, -3.333, 0]}, {"t": 174, "s": [1037, 577, 0]}], "ix": 2}, "a": {"a": 0, "k": [234, 80, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 452, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 2, "nm": "PromptuiA", "refId": "image_11", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.75], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.25], "y": [0]}, "t": 60, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 172, "s": [100]}, {"t": 178, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.75, "y": 0.75}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [179.5, 337.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.75, "y": 1}, "o": {"x": 0.25, "y": 0}, "t": 44, "s": [179.5, 337.5, 0], "to": [0, -6.667, 0], "ti": [0, 6.667, 0]}, {"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.25, "y": 0.25}, "t": 60, "s": [179.5, 297.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 172, "s": [179.5, 297.5, 0], "to": [0, 6.667, 0], "ti": [0, -6.667, 0]}, {"t": 178, "s": [179.5, 337.5, 0]}], "ix": 2}, "a": {"a": 0, "k": [179, 116, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 452, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 2, "nm": "ObjectRemovercommentA", "refId": "image_12", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 195, "s": [0]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 202, "s": [100]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 330, "s": [100]}, {"t": 335, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1084, 230, 0], "ix": 2}, "a": {"a": 0, "k": [116.5, 32, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 195, "s": [70, 70, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 202, "s": [110, 110, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 209, "s": [100, 100, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 330, "s": [100, 100, 100]}, {"t": 335, "s": [70, 70, 100]}], "ix": 6}}, "ao": 0, "ip": 195, "op": 412, "st": -38, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 2, "nm": "ObjectRemovercommentB", "refId": "image_13", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 201, "s": [0]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 208, "s": [100]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 336, "s": [100]}, {"t": 341, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [174, 486, 0], "ix": 2}, "a": {"a": 0, "k": [116.5, 32, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 201, "s": [70, 70, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 208, "s": [110, 110, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 215, "s": [100, 100, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 336, "s": [100, 100, 100]}, {"t": 341, "s": [70, 70, 100]}], "ix": 6}}, "ao": 0, "ip": 201, "op": 418, "st": -32, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 2, "nm": "ObjectRemovercommentC", "refId": "image_14", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 207, "s": [0]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 214, "s": [100]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 342, "s": [100]}, {"t": 347, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1040.5, 731.5, 0], "ix": 2}, "a": {"a": 0, "k": [93.5, 32, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 207, "s": [70, 70, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 214, "s": [110, 110, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 221, "s": [100, 100, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 342, "s": [100, 100, 100]}, {"t": 347, "s": [70, 70, 100]}], "ix": 6}}, "ao": 0, "ip": 207, "op": 424, "st": -26, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 2, "nm": "TexttoPictureCommentA", "refId": "image_15", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 49, "s": [0]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 56, "s": [100]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 184, "s": [100]}, {"t": 189, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1084, 230, 0], "ix": 2}, "a": {"a": 0, "k": [118, 32, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 49, "s": [70, 70, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 56, "s": [110, 110, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 63, "s": [100, 100, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 184, "s": [100, 100, 100]}, {"t": 189, "s": [70, 70, 100]}], "ix": 6}}, "ao": 0, "ip": 49, "op": 199, "st": 49, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 2, "nm": "TexttopicturecommentB", "refId": "image_16", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 62, "s": [100]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 187, "s": [100]}, {"t": 192, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [176, 486, 0], "ix": 2}, "a": {"a": 0, "k": [116.5, 32, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 55, "s": [70, 70, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 62, "s": [110, 110, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 69, "s": [100, 100, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 187, "s": [100, 100, 100]}, {"t": 192, "s": [70, 70, 100]}], "ix": 6}}, "ao": 0, "ip": 55, "op": 205, "st": 55, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 2, "nm": "TexttopictureComment", "refId": "image_17", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 62, "s": [0]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 69, "s": [100]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 190, "s": [100]}, {"t": 195, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1065, 732, 0], "ix": 2}, "a": {"a": 0, "k": [117, 32, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 62, "s": [70, 70, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 69, "s": [110, 110, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [100, 100, 100]}, {"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 190, "s": [100, 100, 100]}, {"t": 195, "s": [70, 70, 100]}], "ix": 6}}, "ao": 0, "ip": 62, "op": 212, "st": 62, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 2, "nm": "emojilight", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 420, "s": [0]}, {"t": 429, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [259, 636, 0], "ix": 2}, "a": {"a": 0, "k": [58, 58, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 420, "op": 587, "st": 420, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 2, "nm": "emojiraibbow", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 420, "s": [0]}, {"t": 429, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [247, 112, 0], "ix": 2}, "a": {"a": 0, "k": [58, 58, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 420, "op": 587, "st": 420, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 2, "nm": "InputCursor", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 420, "s": [0]}, {"t": 429, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1038, 381, 0], "ix": 2}, "a": {"a": 0, "k": [111.5, 112, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 420, "op": 587, "st": 420, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 2, "nm": "pic", "refId": "image_18", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.658], "y": [1]}, "o": {"x": [0.325], "y": [0]}, "t": 420, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.348], "y": [0]}, "t": 421, "s": [0]}, {"t": 429, "s": [100]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 420, "s": [0]}, {"t": 439, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.33, "y": 0.33}, "t": 420, "s": [635, 388, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 439, "s": [635, 388, 0]}], "ix": 2}, "a": {"a": 0, "k": [270.5, 366.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 420, "s": [100, 100, 100]}, {"t": 439, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 420, "op": 732, "st": 420, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 2, "nm": "brush", "refId": "image_19", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 164, "s": [0]}, {"t": 167, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [630, 514, 0], "ix": 2}, "a": {"a": 0, "k": [185, 184, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 456, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 2, "nm": "workeditpicture", "refId": "image_20", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 164, "s": [0]}, {"t": 167, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [635, 388.5, 0], "ix": 2}, "a": {"a": 0, "k": [270.5, 366.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 462, "st": 0, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 2, "nm": "pic", "refId": "image_18", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 0, "s": [100]}, {"t": 19, "s": [100]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 0, "s": [0]}, {"t": 19, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.25, "y": 0.25}, "o": {"x": 0.33, "y": 0.33}, "t": 0, "s": [635, 388, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 19, "s": [635, 388, 0]}], "ix": 2}, "a": {"a": 0, "k": [270.5, 366.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.25, 0.25, 0.25], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"t": 19, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 312, "st": 0, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 2, "nm": "bgA", "refId": "image_21", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [623.5, 422, 0], "ix": 2}, "a": {"a": 0, "k": [554, 326, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}], "markers": []}