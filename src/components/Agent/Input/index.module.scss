@import "@/styles/variable.scss";

$line-height: 18px;

.input {
  display: inline-block;
  box-sizing: border-box;
  position: relative;
  width: 370px;
  padding: 10px 0 0;
  border-radius: 16px;
  border: 1px solid $system-stroke-input-default;
  background: $system-background-input;

  :global {
    .agent-input-images-container {
      position: relative;
      z-index: 0;
      .agent-input-images {
        position: relative;
        display: flex;
        flex: 0 0 auto;
        width: calc(100% - 2 * 10px);
        min-width: 0;
        margin: 0 10px 8px;
        overflow: auto;
        user-select: none;
        /* 隐藏 Firefox 的滚动条 */
        scrollbar-width: none;
  
        /* 隐藏 IE 和 Edge 的滚动条 */
        -ms-overflow-style: none;
  
        /* 隐藏 WebKit 浏览器（如 Chrome 和 Safari）的滚动条 */
        &::-webkit-scrollbar {
          display: none;
        }
  
        .agent-input-images-list {
          display: flex;
          flex: 0 0 auto;
          gap: 8px;
  
          .agent-input-image {
            position: relative;
            border-radius: 12px;
            width: 40px;
            height: 40px;
            background: $system-background-thirdary;
            overflow: hidden;
            .ant-image {
              img {
                width: 40px;
                height: 40px;
                object-fit: cover;
              }
  
              &-mask {
                .remove-btn {
                  position: absolute;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  top: 4px;
                  right: 4px;
                  border-radius: 50%;
                  width: 10px;
                  height: 10px;
                  background: #FFF;
                  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.04), 0px 2px 8px 0px rgba(0, 0, 0, 0.06);
  
                  svg {
                    width: 6px;
                    height: 6px;
                    color: #000;
                  }
                }
              }
            }
  
  
            &.uploading {
              .uploading-spin {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                pointer-events: none;
              }
            }
          }
        }
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        pointer-events: none;
        width: 50px;
        height: 40px;
        top: 0;
        z-index: 1;
      }


      &.scroll:not(.scroll-to-left)::before {
        display: block;
        left: 10px;
        background: linear-gradient(-90deg, rgba(22, 23, 28, 0.00) 9.76%, #16171C 68.18%);
      }

      &.scroll:not(.scroll-to-right)::after {
        display: block;
        right: 10px;
        background: linear-gradient(90deg, rgba(22, 23, 28, 0.00) 9.76%, #16171C 68.18%);
      }
    }


    .agent-input-container {
      display: block;
      position: relative;
      box-sizing: border-box;
      width: 100%;
      padding: 0 3px 60px;
      z-index: 0;

      .agent-input-chat {
        position: relative;
        display: block;
        resize: none;
        width: 100%;
        min-height: 1.1 * $line-height;
        max-height: 131px;
        background: transparent;
        padding: 0 7px;
        color: $system-content-secondary;
        caret-color: $system-content-brand-primary;
        font-size: 14px;
        line-height: $line-height;
        box-sizing: border-box;
        overflow: auto;

        /* Webkit浏览器 */
        &::-webkit-scrollbar {
          width: 4px;
        }

        /* 滚动条轨道 */
        &::-webkit-scrollbar-track {
          background-color: transparent;
        }

        /* 滚动条滑块 */
        &::-webkit-scrollbar-thumb {
          cursor: default;
          border-radius: 2px;
          background-color: $system-content-thirdary;
        }
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: calc(100% - 10px);
        height: 2 * $line-height;
        position: absolute;
        left: 0;
        display: none;
        pointer-events: none;
        z-index: 1;
      }


      &.scroll:not(.scroll-to-top)::before {
        display: block;
        top: 0;
        background: linear-gradient(0deg, rgba(22, 23, 28, 0.00) 0%, #16171C 85.12%);
      }

      &.scroll:not(.scroll-to-bottom)::after {
        display: block;
        bottom: 58px;
        background: linear-gradient(180deg, rgba(22, 23, 28, 0.00) 0%, #16171C 85.12%);
      }

      cursor: text;
    }

    .agent-input-footer-upload,
    .agent-input-footer-send {
      position: absolute;
      bottom: 10px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 32px;
      height: 32px;
      border-radius: 10px;
      cursor: pointer;
      &:disabled {
        cursor: not-allowed;
      }
    }

    .agent-input-footer-upload {
      left: 10px;
      border: 1px solid $system-stroke-button;
      background: $system-background-thirdary;
      svg {
        width: 16px;
        height: 16px;
        color: $system-content-secondary;
      }
    }

    .agent-input-footer-send {
      right: 10px;
      background: $system-content-brand-primary;
      svg {
        width: 16px;
        height: 16px;
        color: $system-content-on-primary;
      }

      &:disabled {
        border: 1px solid $system-stroke-button;
        background: $system-background-thirdary;
        svg {
          color: $system-content-disabled;
        }
      }
    }
  }
}