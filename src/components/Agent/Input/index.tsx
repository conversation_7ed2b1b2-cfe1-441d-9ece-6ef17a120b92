import classNames from "classnames";
import { Agent } from "../types";
import styles from "./index.module.scss";
import AutoHeightTextarea from "@/components/AutoHeightTextArea";
import FilePicker from "@/components/FilePicker";
import {
  CrossBlack,
  PlusBold,
  PublishBoldFill,
  StopFill,
} from "@meitu/candy-icons";
import runes from "runes2";
import { Fragment, useState } from "react";
import { Image, Spin } from "antd";
import { toAtlasImageView2URL } from "@meitu/util";
import { Loading } from "@/components/Loading";
import { getWheelHorizontalScrollRefHandler } from "@/utils/wheelHorizontalScroll";
import {
  useHorizontalScrollState,
  useVerticalScrollState,
} from "@/hooks/useScrollState";
type AgentInputProps = {
  className?: string;

  text?: string;
  onTextChange?: (text: string) => void;

  images?: Array<Agent.InputImage>;
  status?: Agent.Status;

  uploadImage?: (blob: Blob) => Promise<Agent.InputImage | null | undefined>;
  removeImage?: (image: Agent.InputImage) => void;

  onSend?: () => void;
};

export default function AgentInput({
  className,
  text = "",
  onTextChange,
  uploadImage,
  removeImage,
  images = [],
  status = Agent.Status.Loading,
  onSend,
}: AgentInputProps) {
  const renderImages = () => {
    return images.map((image) => {
      const handleRemoveFactory =
        (image: Agent.InputImage) => (e: React.MouseEvent) => {
          e.stopPropagation();
          removeImage?.(image);
        };

      if (image.type === Agent.InputImageType.Uploading) {
        const renderMask = () => {
          return (
            <button className="remove-btn" onClick={handleRemoveFactory(image)}>
              <CrossBlack />
            </button>
          );
        };

        return (
          <li key={image.uploadKey} className="agent-input-image uploading">
            <Image
              src={image.objectURL}
              preview={{ mask: renderMask(), visible: false }}
            />
            <Spin spinning className="uploading-spin" />
          </li>
        );
      }

      const renderMask = () => {
        return (
          <button className="remove-btn" onClick={handleRemoveFactory(image)}>
            <CrossBlack />
          </button>
        );
      };

      return (
        <li key={image.src} className="agent-input-image">
          <Image
            src={toAtlasImageView2URL(image.src, { mode: 2, width: 100 })}
            preview={{ mask: renderMask(), visible: false }}
            placeholder={<Loading />}
          />
        </li>
      );
    });
  };

  const handleUploadImage = async (fileList: FileList | null) => {
    if (!fileList || !fileList.length) {
      return;
    }

    for (let i = fileList.length - 1; i >= 0; --i) {
      const file = fileList[i];
      await uploadImage?.(file);
    }
  };

  const renderSendButton = () => {
    if (status === Agent.Status.Idle) {
      const sendDisabled =
        (runes(text.trim()).length === 0 && images.length === 0) ||
        images.some((image) => image.type === Agent.InputImageType.Uploading);

      return (
        <button
          className="agent-input-footer-send"
          disabled={sendDisabled}
          onClick={onSend}
        >
          <PublishBoldFill />
        </button>
      );
    }

    if (status === Agent.Status.Loading) {
      return (
        <button className="agent-input-footer-send" onClick={onSend}>
          <StopFill className="stop-icon" />
        </button>
      );
    }
  };

  const {
    elementRef: imagesElementRef,
    scrollStateClassName: imagesScrollStateClassName,
  } = useHorizontalScrollState<HTMLDivElement>();
  const {
    elementRef: chatElementRef,
    scrollStateClassName: chatScrollStateClassName,
  } = useVerticalScrollState<HTMLTextAreaElement>();

  return (
    <form
      className={classNames(styles.input, "agent-input", className)}
      onSubmit={(e) => e.preventDefault()}
    >
      <div
        className={classNames(
          "agent-input-images-container",
          imagesScrollStateClassName
        )}
      >
        <div
          className={"agent-input-images"}
          ref={(el) => {
            imagesElementRef.current = el;
            return getWheelHorizontalScrollRefHandler({
              alwaysPreventDefault: false,
            })(el);
          }}
        >
          <ul className="agent-input-images-list">{renderImages()}</ul>
        </div>
        <div className="images-scroll-mask-left"></div>
        <div className="images-scroll-mask-right"></div>
      </div>
      <label
        className={classNames(
          "agent-input-container",
          chatScrollStateClassName
        )}
      >
        <AutoHeightTextarea
          className="agent-input-chat"
          ref={chatElementRef}
          value={text}
          onChange={(e) => onTextChange?.(e.target.value)}
          onKeyDown={(e) => e.nativeEvent.stopImmediatePropagation()}
        />
      </label>

      <FilePicker onChange={handleUploadImage}>
        {({ openPicker }) => {
          return (
            <button className="agent-input-footer-upload" onClick={openPicker}>
              <PlusBold />
            </button>
          );
        }}
      </FilePicker>
      {renderSendButton()}
    </form>
  );
}
