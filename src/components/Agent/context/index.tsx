"use client";

import { createContext, useContext, useState } from "react";
import { Agent } from "../types";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import { v4 as uuid } from "uuid";
import { observer } from "mobx-react-lite";
import { useAgentInput } from "../hooks/useAgentInput";
import { Action } from "@/stores/TransferAction/types";
import {
  MediaInfo,
  Message,
  UserMessage,
} from "@/app/[locale]/canvas/_components/AgentPanel/types";
import useFetchSSE from "@/hooks/useFetchSSE";
import { message } from "antd";
import { useRootStore } from "@/app/[locale]/canvas/_store";
import { getDeviceId } from "@/utils/request/request";
import { getConfig } from "@/config";

type ChatDetailsType = {
  text: string;
  images: {
    src: string;
  }[];
};

export type AgentContextType = {
  status: Agent.Status;

  input: {
    readonly text: string;
    readonly images: Agent.InputImage[];
    setText: React.Dispatch<React.SetStateAction<string>>;
    setImages: React.Dispatch<React.SetStateAction<Agent.InputImage[]>>;
    removeImage: (image: Agent.InputImage) => void;
    pushImage: (image: Agent.InputImage) => void;
    unshiftImage: (image: Agent.InputImage) => void;
    uploadImage: (blob: Blob) => Promise<Agent.InputImage | null | undefined>;
    readonly hasUploadingImage: boolean;
    readonly existImages: Agent.ExistInputImage[];
  };

  send: (chatDetails?: ChatDetailsType) => Promise<void>;

  messages: (Message | UserMessage)[][];

  isBeginCard: boolean;
};

function noProviderNoop() {
  console.warn("AgentContext not provided");
}

export const AgentContext = createContext<AgentContextType>({
  status: Agent.Status.Idle,
  input: {
    text: "",
    images: [],
    setText: noProviderNoop,
    pushImage: noProviderNoop,
    unshiftImage: noProviderNoop,
    setImages: noProviderNoop,
    removeImage: noProviderNoop,
    uploadImage: () => {
      noProviderNoop();
      return Promise.resolve(null);
    },
    hasUploadingImage: false,
    existImages: [],
  },
  send: () => {
    noProviderNoop();
    return Promise.resolve();
  },
  messages: [],
  isBeginCard: false,
});

const cardTypes = [
  "image_analyzer",
  "text",
  "make_plan",
  "design",
  "request",
  "knowledge",
  "simple_text",
  "error",
  "guide_purchase",
];

type AgentContextProviderProps = React.PropsWithChildren<{}>;
export const AgentContextProvider = observer(
  ({ children }: AgentContextProviderProps) => {
    const { projectsStore } = useRootStore();
    const config = getConfig();

    const { connect, disconnect, readyState, latestEvent } = useFetchSSE(
      "/editor_chat/request.json",
      {
        onMessage: (mesData, event) => {
          mesData.role = "assistant";

          setMessages((prev) => {
            if (prev && prev?.length) {
              //
              // 接口到的消息，替换一下占位的thinking;
              const tempUserSendMsg = prev[prev.length - 1] as Message[];
              const tempMessage = {
                id: Date.now(),
                role: "assistant",
                title: "",
                content: "",
                type: "",
                show_type: "",
                content_steps: [],
                content_title: "",
                desc: "",
                keywords: [],
                task_id: "",
                question: "",
                media_item: {
                  media_type: "image",
                  media_cover_url: "",
                  media_url: "",
                },
                imageMediaInfo: {
                  content: "",
                  content_title: "",
                  media_items: [],
                  media_num: 0,
                },
                videoMediaInfo: {
                  content: "",
                  content_title: "",
                  media_items: [],
                  media_num: 0,
                },
                isHistory: false,
                mediasInfo: [],
              };

              if (mesData.error_code === 3001 && tempUserSendMsg?.length) {
                const userMessage = tempUserSendMsg[0] as UserMessage;
                const messageType = [
                  userMessage.imageList?.length && "image",
                  userMessage.videoList?.length && "video",
                  userMessage.content && "text",
                ]
                  .filter(Boolean)
                  .join(",");

                const checkSafeUrl =
                  "https://xximg1.meitudata.com/7339601035227322827.png";
                if (mesData.sensitive_type === "media") {
                  userMessage.imageList &&
                    userMessage.imageList.length &&
                    (userMessage.imageList = userMessage.imageList.map((i) => ({
                      url: checkSafeUrl,
                      uri: checkSafeUrl,
                      isHidePreview: true,
                    })));
                  userMessage.videoList &&
                    userMessage.videoList.length &&
                    (userMessage.videoList = userMessage.videoList.map((i) => ({
                      url: checkSafeUrl,
                      uri: checkSafeUrl,
                      coverUrl: checkSafeUrl,
                      isHidePreview: true,
                    })));
                }
                // userMessage.content = '******';
              }
              if (mesData.error_code === 5001 && tempUserSendMsg?.length) {
                message.error("Web_login_check_text");
                // navigate({
                //   mode: "url",
                //   url:
                //     getEnv().ENV === "development"
                //       ? "/--roboneo/home"
                //       : "/home",
                // });
              }
              if (mesData.type === "resp") {
                // setTaskId(mesData.task_id);
              }
              // 需要购买

              if (
                mesData.type === "status_change" &&
                mesData.status === "start"
              ) {
                tempMessage.task_id = mesData.task_id;
                tempUserSendMsg.splice(-1, 0, tempMessage as any);
                // 这里没start了 续传的时候
                // if(onlyConnectRef.current){
                //   tempUserSendMsg.concat([tempMessage as any]);
                // }
              }
              // 临时追加一个购买
              if (
                mesData.type === "status_change" &&
                mesData.status === "all_done"
              ) {
                tempUserSendMsg.pop(); // think移除

                // 第二条数据追加 标识带有头像
                if (tempUserSendMsg.length >= 1) {
                  tempUserSendMsg[1].mustWithAvatar = true;
                }
              }

              const currentMessagesLen = tempUserSendMsg.length;
              // 续传取得不是-2
              // let thinkIndex = 0;
              // if (tempUserSendMsg.length) {
              //   if (needContinueRef.current) {
              //     thinkIndex = currentMessagesLen - 1;
              //     needContinueRef.current = false;
              //   } else if (tempUserSendMsg.length >= 2) {
              //     thinkIndex = currentMessagesLen - 2;
              //   }
              // }
              const thinkPlaceholder = tempUserSendMsg.length
                ? (tempUserSendMsg[currentMessagesLen - 2] as Message)
                : ({
                    status: "loading",
                    id: Date.now(),
                    role: "assistant",
                    task_id: "",
                    title: "",
                    content: "",
                    type: "",
                    content_steps: [],
                    content_title: "",
                    desc: "",
                    keywords: [],
                    question: "",
                    show_type: "",
                    media_num: 0,
                    mediasInfo: [],
                    media_item: {
                      media_type: "image",
                      media_cover_url: "",
                      media_url: "",
                    },
                    imageMediaInfo: {
                      content: "",
                      content_title: "",
                      media_items: [],
                      media_num: 0,
                    },
                    videoMediaInfo: {
                      content: "",
                      content_title: "",
                      media_items: [],
                      media_num: 0,
                    },
                    isHistory: false,
                  } as Message);

              if (mesData.status === "start") {
                thinkPlaceholder.content = ""; // loading替换掉
              }
              // if (mesData.status === 'end') {
              //   return;
              // }
              if (cardTypes.includes(mesData.type) && mesData.title) {
                thinkPlaceholder.title = mesData.title;
              }
              if (cardTypes.includes(mesData.type)) {
                thinkPlaceholder.type = mesData.type;
                thinkPlaceholder.id = mesData.id;
                setIsBeginCard(true);
              }
              if (mesData.id) {
                thinkPlaceholder.id = mesData.id;
              }
              if (mesData.type === "room_title") {
                // setRoomTitle(mesData.content);
              }

              switch (mesData.type) {
                case "text":
                case "image_analyzer":
                case "simple_text":
                  thinkPlaceholder.content += mesData.content || "";
                  break;
                case "guide_purchase":
                  // setShouldPay(true);
                  thinkPlaceholder.content += mesData.content || "";
                  thinkPlaceholder.desc = mesData.product_desc || "";
                  break;
                case "error": {
                  // 获取用户的消息
                  thinkPlaceholder.mustWithAvatar = true;
                  thinkPlaceholder.content += mesData.error_msg || "";
                  const userMessage = tempUserSendMsg[0] as UserMessage;
                  const messageType = [
                    userMessage.imageList?.length && "image",
                    userMessage.videoList?.length && "video",
                    userMessage.content && "text",
                  ]
                    .filter(Boolean)
                    .join(",");

                  break;
                }
                case "request":
                  thinkPlaceholder.question += mesData.question || "";
                  break;
                case "make_plan":
                  thinkPlaceholder.content_steps =
                    thinkPlaceholder.content_steps?.concat(
                      mesData.content_steps || []
                    );
                  break;
                case "knowledge":
                  thinkPlaceholder.content_title = mesData.content_title;
                  thinkPlaceholder.desc = mesData.desc;
                  thinkPlaceholder.keywords = mesData.keywords;
                  break;
                // case 'design':
                case "media_card": {
                  // thinkPlaceholder.design_id = design_id
                  // 开始  需要将当前上一个生成的媒体isNewGenerator为空，避免
                  // if (thinkPlaceholder.mediasInfo.length) {
                  //   const lastMediaGroup = thinkPlaceholder.mediasInfo[thinkPlaceholder.mediasInfo.length - 1];
                  //   if (lastMediaGroup?.media_items) {
                  //     lastMediaGroup.media_items.forEach((item) => {
                  //       item.isNewGenerator = false; // 或者 = false
                  //     });
                  //   }
                  // }
                  // media_card 追加之前需要判断是否已有mediacard的数组，且media_card里面有效媒体url下发,插入画布

                  const hasDesignWithMediaUrl =
                    Array.isArray(tempUserSendMsg) &&
                    tempUserSendMsg.some((aiMessage) => {
                      // 检查 mediasInfo 是否存在
                      if (!Array.isArray(aiMessage.mediasInfo)) return false;

                      // 遍历 mediasInfo 中的每个 media_items 数组
                      return aiMessage.mediasInfo.some((mediaGroup) => {
                        return (
                          Array.isArray(mediaGroup.media_items) &&
                          mediaGroup.media_items.some(
                            (item) => item?.media_url?.trim() !== ""
                          )
                        );
                      });
                    });

                  // 将整个 media_card 数据对象 push 到数组中
                  thinkPlaceholder.mediasInfo?.push({
                    content_title: mesData.content_title,
                    content: mesData.content,
                    media_num: mesData.media_num,
                    // media_items: new Array(mesData.media_num).fill({}),
                    media_items: [],
                    render_percent: new Array(mesData.media_num).fill(0) || [],
                    render_remain_time:
                      new Array(mesData.media_num).fill("") || [],
                    hasDesignWithMediaUrl,
                  });

                  // 结束
                  thinkPlaceholder.imageMediaInfo.content_title =
                    mesData.content_title;
                  thinkPlaceholder.imageMediaInfo.content = mesData.content;
                  thinkPlaceholder.imageMediaInfo.media_num = mesData.media_num;
                  // media_num 在这个字段中
                  break;
                }
                case "render_percent": {
                  // 开始
                  // 将媒体数据进度条覆盖
                  const len = thinkPlaceholder.mediasInfo?.length;
                  const latestMedia = len
                    ? (thinkPlaceholder.mediasInfo[len - 1] as MediaInfo)
                    : { render_percent: [], render_remain_time: [] };
                  latestMedia.render_percent = mesData?.percent || [0];
                  latestMedia.render_remain_time = [`${mesData.desc || ""}`];
                  // 结束

                  // thinkPlaceholder.imageMediaInfo.render_percent = mesData?.percent || [];
                  break;
                }
                case "media": {
                  // 开始
                  const len = thinkPlaceholder.mediasInfo?.length;
                  const latestMedia = len
                    ? (thinkPlaceholder.mediasInfo[len - 1] as MediaInfo)
                    : ({ media_items: [] } as unknown as MediaInfo);
                  latestMedia.media_items?.push({
                    ...mesData.media_item,
                    isNewGenerator: !mesData?.is_retransfer,
                  });
                  // 结束
                  // 由于续传的时候，，media_item重新 下发一次需要有个标识
                  if (mesData.media_item.media_type === "image") {
                    thinkPlaceholder.imageMediaInfo.media_items?.push({
                      ...mesData.media_item,
                      isNewGenerator: !mesData?.is_retransfer,
                    });
                  }
                  if (mesData.media_item.media_type === "video") {
                    thinkPlaceholder.videoMediaInfo.media_items?.push({
                      ...mesData.media_item,
                      isNewGenerator: !mesData?.is_retransfer,
                    });
                  }
                  break;
                }
                default:
              }

              return [...prev];
            }
            return [[mesData]];
          });
        },
        onError: (err, count) => {
          message.error("RoboNeo_Web_network_fail");
          // disconnect();
          // throw err;

          // 可以返回自定义重试间隔
          // return count * 1000; // 指数退避
        },
      }
    );
    const inputContext = useAgentInput();

    const [status, setStatus] = useState(Agent.Status.Idle);
    const [messages, setMessages] = useState<(Message | UserMessage)[][]>([]);
    const [isBeginCard, setIsBeginCard] = useState(false);

    const send = async (chatDetails?: ChatDetailsType) => {
      const inputInfo = {
        text: chatDetails?.text || inputContext.text,
        images: (chatDetails?.images || inputContext.existImages)?.map(
          (item) => item.src
        ),
      };

      // 文本消息
      setIsBeginCard(true);

      const userMessage: UserMessage = {
        id: Date.now(),
        role: "user",
        content: inputInfo?.text || "",
        type: "text",
        imageList: (chatDetails?.images || inputContext.existImages).map(
          (item) => {
            return {
              ...item,
              url: item.src,
            };
          }
        ),
      };

      //  sse消息的临时占位图  //对话的dom 这个元素要放到最后
      const defaultThinking: Message = {
        id: Date.now(),
        sseing: true,
        role: "assistant",
        content: `Thinking...`,
        type: "simple_text",
        status: "loading", // 头像的状态
        showIcon: true,
        content_steps: [],
        task_id: "",
        content_title: "",
        show_type: "text",
        desc: "",
        keywords: [],
        question: "",
        media_num: 0,
        mediasInfo: [],
        media_item: {
          media_type: "image",
          media_cover_url: "",
          media_url: "",
        },
        imageMediaInfo: {
          content: "",
          content_title: "",
          media_items: [],
          media_num: 0,
        },
        videoMediaInfo: {
          content: "",
          content_title: "",
          media_items: [],
          media_num: 0,
        },
        mustWithAvatar: false,
      };
      setMessages((prev) => prev.concat([[userMessage, defaultThinking]]));

      // 清空输入框
      inputContext.setText("");
      inputContext.setImages([]);
      // 设置输入框按钮状态
      setStatus(Agent.Status.Loading);

      const body = {
        message: inputInfo.text,
        images: inputInfo.images,
        project_id: projectsStore.activeProjectId,
        client_id: config.ACCOUNT_CLIENT_ID,
        gnum: getDeviceId(),
      };
      await connect(body);

      // await new Promise((resolve) => setTimeout(resolve, 1000));
      setStatus(Agent.Status.Idle);
    };

    return (
      <AgentContext.Provider
        value={{
          status,
          input: inputContext,
          send,
          messages,
          isBeginCard,
        }}
      >
        {children}
      </AgentContext.Provider>
    );
  }
);

export function useAgentContext(): AgentContextType {
  return useContext(AgentContext);
}
