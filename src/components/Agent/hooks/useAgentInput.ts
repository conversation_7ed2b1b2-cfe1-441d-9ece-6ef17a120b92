import { useMemo, useState } from "react";
import { Agent } from "../types";
import { useMonitorUploadFunc } from "@/hooks/useMonitorUploadFunc";
import { v4 as uuid } from "uuid";

export const useAgentInput = () => {
  //#region 输入框
  const [text, setText] = useState("");
  const [images, setImages] = useState<Agent.InputImage[]>([]);

  const hasUploadingImage = useMemo(() => {
    return images.some(
      (image) => image.type === Agent.InputImageType.Uploading
    );
  }, [images]);

  const existImages = useMemo(() => {
    return images.filter((image) => image.type === Agent.InputImageType.Exist);
  }, [images]);

  const hasSameImageInChat = (image: Agent.InputImage) => {
    if (image.type !== Agent.InputImageType.Exist) {
      return false;
    }

    return images.some((img) => {
      if (img.type !== Agent.InputImageType.Exist) {
        return false;
      }

      return img.src === image.src;
    });
  };

  const pushImage = (image: Agent.InputImage) => {
    if (hasSameImageInChat(image)) {
      return;
    }

    setImages((prev) => [...prev, image]);
  };
  const unshiftImage = (image: Agent.InputImage) => {
    if (hasSameImageInChat(image)) {
      return;
    }

    setImages((prev) => [image, ...prev]);
  };

  const removeImage = (image: Agent.InputImage) => {
    setImages((images) =>
      images.filter((i) => {
        if (image !== i) {
          return true;
        }

        if (i.type !== Agent.InputImageType.Uploading) {
          return false;
        }

        i.abortController.abort();
        return false;
      })
    );
  };

  const upload = useMonitorUploadFunc();
  const uploadImage = async (blob: Blob) => {
    if (!upload) {
      return;
    }

    const abortController = new AbortController();
    const uploadKey = uuid();
    const objectURL = URL.createObjectURL(blob);

    unshiftImage({
      type: Agent.InputImageType.Uploading,
      blob,
      uploadKey,
      objectURL,
      abortController,
    });

    const remove = () => {
      setImages((prev) =>
        prev.filter(
          (image) =>
            image.type === Agent.InputImageType.Exist ||
            image.uploadKey !== uploadKey
        )
      );
    };

    try {
      const res = await upload({ file: blob, signal: abortController.signal });
      abortController.signal.throwIfAborted();

      if (res.result?.previewUrl) {
        const image: Agent.ExistInputImage = {
          type: Agent.InputImageType.Exist,
          src: res.result.previewUrl,
        };

        setImages((images) =>
          images.map((i) => {
            if (
              i.type === Agent.InputImageType.Uploading &&
              i.uploadKey === uploadKey
            ) {
              return image;
            }

            return i;
          })
        );

        return image;
      }
    } catch (e) {
      console.error(e);
    } finally {
      URL.revokeObjectURL(objectURL);
      remove();
    }

    return null;
  };
  //#endregion

  return {
    text,
    setText,
    images,
    pushImage,
    removeImage,
    uploadImage,
    setImages,
    unshiftImage,
    hasUploadingImage,
    existImages,
  };
};
