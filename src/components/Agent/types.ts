export namespace Agent {
  export enum Status {
    Idle = "idle",
    Loading = "loading",
  }

  export enum InputImageType {
    Uploading = "uploading",
    Exist = "exist",
  }

  export type UploadingInputImage = {
    type: InputImageType.Uploading;
    blob: Blob;
    uploadKey: string;
    objectURL: string;
    abortController: AbortController;
  };

  export type ExistInputImage = {
    type: InputImageType.Exist;
    src: string;
  };

  export type InputImage = UploadingInputImage | ExistInputImage;
}
