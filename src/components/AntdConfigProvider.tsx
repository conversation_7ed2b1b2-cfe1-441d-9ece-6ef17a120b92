import { ConfigProvider } from "antd";

export default function AntdConfigProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorBgElevated: "#1D1E23",
          colorTextHeading: "#A3AEBF",
          colorBgContainer: "#16171C",
          colorBorder: "#22272E",
          colorTextDescription: "#A3AEBF",
          colorTextPlaceholder: "#A3AEBF",
          colorText: "#A3AEBF",
          colorTextDisabled: "#A3AEBF",
        },
        components: {
          Collapse: {
            contentBg: "#1D1E23",
            headerBg: "#1D1E23",
          },
          Select: {
            optionSelectedBg: "transparent",
            optionSelectedColor: "#F5F7FA",
            optionActiveBg: "#272C33",
            activeBorderColor: "#64748B",
            hoverBorderColor: "#64748B",
          },
          InputNumber: {
            activeBorderColor: "#64748B",
            hoverBorderColor: "#64748B",
          },
          Input: {
            activeBorderColor: "#64748B",
            hoverBorderColor: "#64748B",
          },
          Slider: {
            railHoverBg: "#64748B",
            railBg: "#64748B",
            trackBg: "#fff",
            trackHoverBg: "#fff",
          },
          Segmented: {
            trackBg: "#16171C",
            itemSelectedBg: "#272C33",
            itemSelectedColor: "#fff",
            itemColor: "#6B7A8F",
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
}
