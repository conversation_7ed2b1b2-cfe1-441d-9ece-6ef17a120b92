"use client";

import { useVerticalScrollState } from "@/hooks/useScrollState";
import classNames from "classnames";
import { merge, omit } from "lodash-es";
import React, { forwardRef, useImperativeHandle, useLayoutEffect, useRef, useState } from "react"

type Props = React.DetailedHTMLProps<React.TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement> & {
  onHeightChange?: (height: number) => void;
}

const AutoHeightTextarea = forwardRef<HTMLTextAreaElement, Props>((props, ref) => {
  const [height, setHeight] = useState(1);
  
  const elementRef = useRef<HTMLTextAreaElement>(null);

  useImperativeHandle(ref, () => elementRef.current!);

  useLayoutEffect(() => {
    if (elementRef.current) {
      elementRef.current.style.height = "1px";
      const { scrollHeight } = elementRef.current;
      elementRef.current.style.height = `${height}px`;
      setHeight(scrollHeight);
      props.onHeightChange?.(scrollHeight);
    }
  }, [props.value]);


  const style = merge({}, { height: `${height}px` }, props.style);


  return (
    <textarea
      {...omit(props, ['onHeightChange', 'ref', 'style', 'className', 'onScroll'])}
      style={style}
      ref={elementRef}
      className={classNames(props.className)}
    />
  )
})

AutoHeightTextarea.displayName = 'AutoHeightTextarea'

export default AutoHeightTextarea  as (props: Props & React.RefAttributes<HTMLTextAreaElement | null>) => React.JSX.Element;