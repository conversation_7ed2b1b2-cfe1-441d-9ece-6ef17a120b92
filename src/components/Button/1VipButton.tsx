/**
 * 基于antd的button组件，添加了一些样式
 */

import { Button as AntdButton, ConfigProvider } from "antd";
import { ButtonProps } from "antd";
import "./index.style.scss";
export const VipButton = (props: ButtonProps) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            colorPrimary: "linear-gradient(88deg, #5DF5B9 0%, #D8EAFF 47.22%, #FBA6FF 100%);",
            defaultActiveBg: "linear-gradient(88deg, #5DF5B9 0%, #D8EAFF 47.22%, #FBA6FF 100%);",
            primaryShadow: 'none',
            colorPrimaryHover:
              "linear-gradient(0deg, #38D294 44.44%, #01331F 181.94%);",
            colorPrimaryActive: "#fff",
            colorBgContainerDisabled: "#393D48", // 禁用状态的背景颜色
            colorTextDisabled: "#4A5564", // 禁用状态的文字颜色
            primaryColor: "#000",
          },
        },
      }}
    >
      <AntdButton
        {...props}
        type={props.type ?? "primary"}
        className={`${props.className} button-primary`}
      />
    </ConfigProvider>
  );
};
