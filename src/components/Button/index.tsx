/**
 * 基于antd的button组件，添加了一些样式
 */

import { Button as AntdButton, ConfigProvider } from "antd";
import { ButtonProps } from "antd";
import "./index.style.scss";
export const Button = (props: ButtonProps) => {
  return (
    <ConfigProvider theme={{
        components: {
            Button: {
                colorPrimary: '#53F6B4',
                primaryShadow: '0px 0px 14.7px 1px rgba(255, 255, 255, 0.00) inset;',
                primaryColor: '#181818',
                colorPrimaryHover: 'linear-gradient(0deg, #38D294 44.44%, #01331F 181.94%);',
                // 主态悬浮border
                defaultHoverBorderColor: 'rgba(255, 255, 255, 0.20)',
                colorPrimaryActive: '#181818',
                colorBgContainerDisabled: '#393D48', // 禁用状态的背景颜色
                colorTextDisabled: '#4A5564', // 禁用状态的文字颜色
               
            },
        },
    }}>
      <AntdButton
        {...props}
        type={props.type ?? "primary"}
        className={`${props.className} button-primary`}
      />
    </ConfigProvider>
  );
};
