.waterfall-container {
  width: 100%;
  overflow: visible;
  position: relative;
  min-height: 200px;
  contain: layout style; /* 提高渲染性能 */
}

.waterfall-grid {
  position: relative;
  width: 100%;
  min-height: 100px;
  will-change: height; /* 提示浏览器优化高度变化 */
}

.waterfall-item {
  position: absolute;
  top: 0;
  left: 0;
  /* 默认不透明，避免闪烁 */
  opacity: 1; 
  will-change: transform; /* 告知浏览器需要优化变换 */
  transform: translate(0, 0); /* 设置初始值，促进GPU加速 */
  backface-visibility: hidden; /* 减少重绘 */
  perspective: 1000; /* 启用3D变换加速 */
  -webkit-font-smoothing: antialiased; /* 改善文字渲染 */
}


.waterfall-loader {
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
  color: var(--system-content-thirdary, #6B7A8F);
  font-family: Inter;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .loading-spinner {
    display: block;
    width: 28px;
    height:28px;
    border: 3px solid #FFF;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 12px;
  }
  
 
}

.waterfall-end-message {
  color: var(--system-content-thirdary, #6B7A8F);
  text-align: center;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 18.2px */
  margin-top: 24px;
 
  .waterfall-end-message-text {
    position: relative;
    &::before {
      content: '';
      width: 45px;
      height: 1px;
      background-color: var(--system-content-thirdary, #6B7A8F);
      position: absolute;
      top: 50%;
      left: -12px;
      transform: translateX(-100%);
    }
    &::after {
      content: '';
      width: 45px;
      height: 1px;
      background-color: var(--system-content-thirdary, #6B7A8F);
      position: absolute;
      top: 50%;
      right: -12px;
      transform: translateX(100%);
    }
  }
 
}


@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

