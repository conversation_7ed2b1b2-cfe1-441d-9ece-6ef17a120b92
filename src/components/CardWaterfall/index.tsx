import React, { useEffect, useState, useRef, useCallback } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import "./index.scss";

interface WaterfallProps<T> {
  items: T[]; // 要显示的数据项
  renderItem: (item: T, index: number) => React.ReactNode; // 渲染每个项目的函数
  columnCount?: { default: number; smallDesktop: number; tablet: number; mobile: number;  }; // 响应式列数配置
  columnGap?: number; // 列间距
  rowGap?: number; // 行间距
  loadMore: () => void; // 加载更多数据的回调函数
  hasMore: boolean; // 是否还有更多数据
  loader?: React.ReactNode; // 加载中显示的组件
  endMessage?: React.ReactNode; // 数据加载完毕显示的信息
  scrollThreshold?: number; // 滚动阈值，默认为0.8（80%）
  className?: string; // 自定义类名
  style?: React.CSSProperties; // 自定义样式
  scrollableTarget?: string;
  itemKey?: (item: T, index: number) => string | number; // 自定义 key 函数
  getItemHeight?: (item: T, itemWidth: number) => number; // 计算项目高度的函数
  maxColumns?: number; // 最大列数，默认为6
  recalculateOnImgLoad?: boolean; // 图片加载后是否重新计算布局，默认为false
  loadingText?: string; // 加载中显示的文本，默认为"正在加载更多内容..."
  endMessageText?: string; // 数据加载完毕显示的信息，默认为"已经滑到底部了"
}

// 定义卡片位置类型
interface CardPosition {
  left: number;
  top: number;
  width: number;
  height: number;
}

function Waterfall<T>({
  items,
  renderItem,
  columnCount = { default: 3, smallDesktop: 5, tablet: 2, mobile: 1 },
  columnGap = 16,
  rowGap = 16,
  loadMore,
  hasMore,
  loadingText = "Loading...",
  endMessageText = "No more data",
  loader = (
    <div className="waterfall-loader">
      <div className="loading-spinner"></div>
      {loadingText}
    </div>
  ),
  endMessage = (
    <div className="waterfall-end-message">
      {endMessageText && (
        <span className="waterfall-end-message-text">{endMessageText}</span>
      )}
    </div>
  ),
  scrollThreshold = 0.8,
  className = "",
  style = {},
  scrollableTarget = "scrollableDiv",
  itemKey,
  getItemHeight,
  maxColumns = 6, // 默认最大列数为6
}: WaterfallProps<T>) {
  // 状态管理
  const [columns, setColumns] = useState(columnCount.default);
  const [containerWidth, setContainerWidth] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const [cardPositions, setCardPositions] = useState<CardPosition[]>([]);

  // refs
  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<Map<number, HTMLDivElement>>(new Map());
  const prevItemsLengthRef = useRef(0);
  const prevColumnsRef = useRef(columns);
  const prevWidthRef = useRef(0);
  const calculatingRef = useRef(false);

  // 从容器宽度获取列数，而非窗口宽度
  const getColumnCountByWidth = useCallback(
    (containerWidth: number): number => {
      // 更清晰的断点定义，确保列数平滑过渡
      if (containerWidth <= 576) {
        // 手机屏幕
        return columnCount.mobile;
      } else if (containerWidth <= 768) {
        // 平板竖屏
        return columnCount.tablet;
      } else if (containerWidth <= 992) {
        // 平板横屏
        return 4;
      } else if (containerWidth <= 1200) {
        // 小屏幕桌面
        return columnCount.smallDesktop;
      } else if (containerWidth <= 1600) {
        // 中等屏幕桌面
        return 6;
      } else {
        // 大屏幕桌面
        return Math.min(6, maxColumns);
      }
    },
    [columnCount, maxColumns]
  );

  // 计算卡片布局位置 - 优化版本
  const calculateLayout = useCallback(() => {
    if (calculatingRef.current) return;
    if (containerWidth === 0 || items.length === 0 || columns === 0) return;

    calculatingRef.current = true;
    requestAnimationFrame(() => {
      try {
        // 计算每列宽度
        const columnWidth =
          (containerWidth - (columns - 1) * columnGap) / columns;

        // 初始化每列的当前高度
        const columnHeights = Array(columns).fill(0);

        // 新的卡片位置数组
        const positions: CardPosition[] = new Array(items.length);

        // 计算每个卡片的位置
        items.forEach((item, index) => {
          // 找到高度最小的列
          const minHeightColumn = columnHeights.indexOf(
            Math.min(...columnHeights)
          );

          // 计算卡片高度
          let cardHeight = columnWidth; // 默认正方形

          if (getItemHeight) {
            // 使用提供的计算函数
            cardHeight = getItemHeight(item, columnWidth);
          } else if (itemRefs.current.has(index)) {
            // 从DOM元素获取实际高度
            const element = itemRefs.current.get(index);
            if (element) {
              // 图片方式：使用内部元素的宽高比
              const img = element.querySelector("img");
              if (img && img.naturalWidth > 0 && img.naturalHeight > 0) {
                const aspectRatio = img.naturalHeight / img.naturalWidth;
                cardHeight = columnWidth * aspectRatio;
              } else {
                // 或使用元素自身的宽高比
                const aspectRatio = element.offsetHeight / element.offsetWidth;
                cardHeight = columnWidth * aspectRatio;
              }
            }
          }

          // 确保高度有效
          cardHeight = cardHeight > 0 ? cardHeight : columnWidth;

          // 计算卡片位置
          const left = minHeightColumn * (columnWidth + columnGap);
          const top = columnHeights[minHeightColumn];

          // 更新该列的高度
          columnHeights[minHeightColumn] = top + cardHeight + rowGap;

          // 保存卡片位置
          positions[index] = {
            left,
            top,
            width: columnWidth,
            height: cardHeight,
          };
        });

        // 设置容器高度为最高列的高度
        const maxColumnHeight = Math.max(...columnHeights);
        setContainerHeight(maxColumnHeight > 0 ? maxColumnHeight - rowGap : 0);

        // 更新卡片位置
        setCardPositions(positions);
      } finally {
        // 使用RAF确保在下一帧结束计算状态，优化性能
        requestAnimationFrame(() => {
          calculatingRef.current = false;
        });
      }
    });
  }, [containerWidth, columns, columnGap, rowGap, items, getItemHeight]);

  // 生成item的key
  const getItemKey = useCallback(
    (item: T, index: number) => {
      return itemKey ? itemKey(item, index) : index;
    },
    [itemKey]
  );

  // 获取item的ref
  const setItemRef = useCallback(
    (element: HTMLDivElement | null, index: number) => {
      if (element) {
        itemRefs.current.set(index, element);
      } else {
        itemRefs.current.delete(index);
      }
    },
    []
  );

  // 统一处理屏幕和容器变化的逻辑
  const handleSizeChange = useCallback(() => {
    if (!containerRef.current) return;

    // 1. 获取容器当前宽度
    const containerCurrentWidth = containerRef.current.offsetWidth;

    // 2. 根据容器宽度确定应该的列数
    const newColumnCount = getColumnCountByWidth(containerCurrentWidth);

    // 控制台输出当前宽度和列数，方便调试
    // console.log(
    //   `容器宽度: ${containerCurrentWidth}px, 列数: ${newColumnCount}`
    // );

    // 3. 检查容器宽度是否有变化 - 降低敏感度以优化性能
    const widthChangeThreshold = 5; // 容忍5px的误差
    const hasSignificantWidthChange =
      Math.abs(containerCurrentWidth - prevWidthRef.current) >
      widthChangeThreshold;

    // 4. 检查列数是否需要变化
    const hasColumnChange = newColumnCount !== prevColumnsRef.current;

    // 只要有变化就更新布局
    if (hasSignificantWidthChange || hasColumnChange) {
      // 更新参考值
      prevWidthRef.current = containerCurrentWidth;
      setContainerWidth(containerCurrentWidth);

      if (hasColumnChange) {
        prevColumnsRef.current = newColumnCount;
        setColumns(newColumnCount);
      }
    }
  }, [getColumnCountByWidth]);

  // 监控容器尺寸变化
  useEffect(() => {
    if (!containerRef.current) return;

    // 使用ResizeObserver监控容器尺寸变化
    const observer = new ResizeObserver((entries) => {
      // 确保是我们关注的元素
      const containerEntry = entries.find(
        (entry) => entry.target === containerRef.current
      );
      if (containerEntry) {
        // 直接获取最新宽度
        const newWidth = containerEntry.contentRect.width;
        // console.log(`ResizeObserver检测到宽度变化: ${newWidth}px`);

        // 直接触发尺寸变化处理
        handleSizeChange();
      }
    });

    // 开始观察容器元素
    observer.observe(containerRef.current);

    return () => {
      observer.disconnect();
    };
  }, [handleSizeChange]);

  // 当容器宽度或列数变化时重新计算布局
  useEffect(() => {
    if (containerWidth > 0) {
      calculateLayout();
    }
  }, [containerWidth, columns, calculateLayout]);

  // 当有新项目加载时计算
  useEffect(() => {
    if (items.length > prevItemsLengthRef.current) {
      prevItemsLengthRef.current = items.length;
      calculateLayout();
    } else if (items.length !== prevItemsLengthRef.current) {
      // 当项目数量减少时（如筛选）也需要重新计算
      prevItemsLengthRef.current = items.length;
      calculateLayout();
    }
  }, [items, calculateLayout]);

  return (
    <div
      className={`waterfall-container ${className}`}
      style={style}
      ref={containerRef}
    >
      <InfiniteScroll
        dataLength={items.length}
        next={loadMore}
        hasMore={hasMore}
        loader={loader}
        endMessage={items.length > 0 ? endMessage : null}
        scrollThreshold={scrollThreshold}
        scrollableTarget={scrollableTarget}
        style={{ overflow: "visible" }}
      >
        <div
          className="waterfall-grid"
          style={{ height: `${containerHeight}px` }}
        >
          {items.map((item, index) => (
            <div
              key={getItemKey(item, index)}
              className="waterfall-item"
              ref={(el) => setItemRef(el, index)}
              style={{
                width: cardPositions[index]?.width || "auto",
                transform: cardPositions[index]
                  ? `translate(${cardPositions[index].left}px, ${cardPositions[index].top}px)`
                  : "translate(0, 0)",
                // transition: 'transform 0.25s ease' // 添加平滑过渡效果
              }}
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </InfiniteScroll>
    </div>
  );
}

export default Waterfall;
