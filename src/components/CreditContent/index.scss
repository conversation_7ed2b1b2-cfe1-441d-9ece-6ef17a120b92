.workspace-credit-modal {
  z-index: 1010 !important;

  .ant-modal {
    .ant-modal-content {
      padding: 0px 0px 32px;
      border-radius: var(--radius-12, 12px);
      border: 1px solid var(--system-stroke-input, #22272E);
      background: linear-gradient(0deg, var(--system-background-secondary, #1D1E23) 0%, var(--system-background-secondary, #1D1E23) 100%), #FFF;
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 24px 128px 0px rgba(0, 0, 0, 0.16);


      .ant-modal-body {
        .content-box {
          position: relative;

          .close-box {
            position: absolute;
            top: 11px;
            right: 12px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #303741;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            svg {
              width: 14px;
              height: 14px;
              color: #fff;
            }
          }

          .title-box {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 51px 0 58px;
            background: url('./model-bg.png') no-repeat right top;
            background-size: 80% 100%;

            svg {
              width: 98px;
              height: 98px;
            }

            .credit-count {
              display: flex;
              align-items: center;
              margin-left: 16px;

              span {
                color: #FFF;
                font-family: Poppins;
                font-size: 64px;
                font-style: normal;
                font-weight: 600;
                line-height: 130%;
                transition: all 0.2s ease-out;
              }

              .add-box {
                height: 40px;
                line-height: 28px;
              }
            }
          }

          .desc-box {
            padding: 0px 24px 48px;

            h3 {
              color: var(--system-content-primary, #FFF);
              text-align: center;
              font-family: Inter;
              font-size: 20px;
              font-style: normal;
              font-weight: 600;
              line-height: 130%;

              span {
                color: var(--system-content-brandPrimary, #53F6B4);
              }
            }

            p {
              margin-top: 16px;
              color: var(--system-content-secondary, #A3AEBF);
              text-align: center;
              font-family: Inter;
              font-size: 18px;
              font-style: normal;
              font-weight: 400;
              line-height: 130%;
            }
          }

          .action-box {
            width: 370px;
            height: 44px;
            border-radius: var(--radius-8, 8px);
            background: var(--system-content-brandPrimary, #53F6B4);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            margin: 0 auto;
            cursor: pointer;

            svg {
              width: 18px;
              height: 18px;
              color: #181818;
            }

            span {
              color: var(--system-content-onVip, #0F172A);
              font-family: Poppins;
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 130%;
            }
          }
        }
      }
    }
  }
}

.workspace-credit-modal-mask {
  background: rgba(0, 0, 0, 0.70);
  backdrop-filter: blur(32px);
}