import { observer } from "mobx-react";
import { CreditStar } from "@/assets/icons";
import { AiSparkleFill, CrossBlack } from "@meitu/candy-icons";
import "./index.scss";
import { useEffect, useRef, useState } from "react";
import confetti from "canvas-confetti";
import { trackEvent } from "@meitu/subscribe-intl";

// Modal 配置
export const MODAL_PROPS = {
  width: 418,
  centered: true,
  destroyOnClose: true,
  maskClosable: false,
  closable: false,
  footer: null,
  mask: true,
  classNames: {
    wrapper: "workspace-credit-modal",
    mask: "workspace-credit-modal-mask",
  },
};

// 奖励类型
export enum RewardType {
  newSignup = "new_signup",
  dailyLogin = "daily_login",
}

// 渲染奖励内容
export const renderRewardContent = (
  type: RewardType,
  rechargeAmount: number,
  t: (key: string, params?: any) => string,
  onClose: () => void
) => {
  const config = {
    [RewardType.newSignup]: {
      icon: "🥳",
      title: t("subscribe.Welcome to Whee AI!"),
      btnDescription: t("subscribe.Claim Credits"),
      description: t(
        "subscribe.Wishing you endless inspiration and limitless creativity!"
      ),
      rewardText: t("subscribe.Your first login reward"),
      creditsText: t("subscribe.{count} credits", { count: rechargeAmount }),
    },
    [RewardType.dailyLogin]: {
      icon: "🎉",
      title: t("subscribe.Welcome back!"),
      btnDescription: t("subscribe.Start Creating"),
      description: t("subscribe.Start creating your next masterpiece now!"),
      rewardText: t("subscribe.Your daily login reward"),
      creditsText: t("subscribe.{count} limited-time credits", {
        count: rechargeAmount,
      }),
    },
  };

  const { icon, title, btnDescription, description, rewardText, creditsText } =
    config[type];

  return (
    <CreditContent
      onClose={onClose}
      credit={rechargeAmount}
      btnDescription={btnDescription}
      description={description}
      rewardType={type}
      title={
        <>
          <h3>
            {icon} {title}
          </h3>
          {rewardText} — <span>{creditsText}</span> —{" "}
          {t("subscribe.has been delivered!")}
        </>
      }
    />
  );
};

type PropsType = {
  onClose: () => void;
  credit: number;
  btnDescription: string;
  title: React.ReactNode;
  description: string;
  rewardType: RewardType;
};

const CreditContent = (props: PropsType) => {
  const { onClose, credit, btnDescription, title, description, rewardType } =
    props;

  const [count, setCount] = useState(0);
  const animationRef = useRef<number>();

  // 计数器动画
  useEffect(() => {
    // 动画起始时间
    let startTime: number | null = null;
    // 动画持续时间（毫秒）
    const duration = 600;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;

      const progress = Math.min((currentTime - startTime) / duration, 1);

      // 使用 easeOut 缓动函数使动画更自然
      const easeOut = (t: number) => 1 - Math.pow(1 - t, 2);
      const currentCount = Math.floor(easeOut(progress) * credit);

      setCount(currentCount);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [credit]);

  // 使用 canvas-confetti 库实现庆祝动画
  useEffect(() => {
    const myCanvas = document.createElement("canvas");
    myCanvas.style.position = "fixed";
    myCanvas.style.top = "0";
    myCanvas.style.left = "0";
    myCanvas.style.pointerEvents = "none";
    myCanvas.width = window.innerWidth;
    myCanvas.height = window.innerHeight;
    myCanvas.style.zIndex = "1005";
    document.body.appendChild(myCanvas);

    const myConfetti = confetti.create(myCanvas, {
      resize: true,
      useWorker: true,
    });

    // 直接使用 confetti() 返回的 Promise
    const fireConfetti = async () => {
      try {
        await myConfetti({
          spread: 120,
          startVelocity: 30,
          decay: 0.92,
          scalar: 1,
          gravity: 1.2,
          ticks: 200,
          origin: { x: 0.5, y: 0.5 },
        });

        // 等待粒子完全消失
        setTimeout(() => {
          myConfetti.reset();
          myCanvas.remove();
        }, 1000);
      } catch (error) {
        console.error("礼花动画执行失败:", error);
        myCanvas.remove();
      }
    };

    // 执行动画
    fireConfetti();

    // 组件卸载时清理
    return () => {
      myConfetti.reset();
      myCanvas.remove();
    };
  }, []);

  useEffect(() => {
    trackEvent("credits_delivered_popup_expo", {
      popup_type: rewardType,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const clickHandler = (type: string) => {
    trackEvent("credits_delivered_popup_click", {
      popup_type: rewardType,
      click_type: type,
    });
  };

  return (
    <div className="content-box">
      <div
        className="close-box"
        onClick={() => {
          clickHandler("close");
          onClose();
        }}
      >
        <CrossBlack />
      </div>
      <div className="title-box">
        <CreditStar />
        <div className="credit-count">
          <span className="add-box">+</span>
          <span>{count}</span>
        </div>
      </div>
      <div className="desc-box">
        <h3>{title}</h3>
        <p>{description}</p>
      </div>
      <div
        className="action-box"
        onClick={() => {
          clickHandler(rewardType === RewardType.newSignup ? "get" : "start");
          onClose();
        }}
      >
        <AiSparkleFill />
        <span>{btnDescription}</span>
      </div>
    </div>
  );
};

export default observer(CreditContent);
