// 定义 SCSS 变量
$content-systemPrimary: #A3AEBF;
$content-inputPlaceHolder: #49576C;
$content-btnSecondary: #6B7A8F;
$stroke-input: #22272E;
$background-input: #16171C;
$radius-8: 8px;
$background-system-scrollbar: #d0d2d6;
$background-btn-ai: #272C33;

.deep-seek-container {

  // overflow-y: scroll;
  .button-container {
    width: 100%;
    margin-top: 10px;
  }

  .button-container:disabled {
    color: var(--content-btnSecondary, #1c1d1f);
    border: 1px solid var(--stroke-btnSecondary, #d0d2d6);
    opacity: 0.3;
    background: #f7f8fa;
  }
}

.deep-seek-title {
  color: $content-systemPrimary;
  /* text_14_bold */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-bottom: 8px;
  padding-top: 8px;

  &-expand {
    color: $content-inputPlaceHolder;
    cursor: pointer;
    /* text_14_bold */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
}

.thinking-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .thinking-container-title {
    color: $content-systemPrimary;
    /* text_14_bold */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
  }

  .thinking-container-content {
    width: 100%;
    height: 108px;
    display: flex;
    border-radius: $radius-8;
    border: 1px solid $stroke-input;
    background: $background-input;
    margin-top: 8px;
    padding-left: 8px;
    padding-top: 8px;
    color: $content-inputPlaceHolder;
    // 禁止鼠标选中
    user-select: none;
    /* text_14 */
    // font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 142.857% */
    position: relative;

    &-mask {
      width: 100%;
      height: 24px;
      border-top-left-radius: $radius-8;
      border-top-right-radius: $radius-8;
      background: linear-gradient(0deg,
          rgba(255, 255, 255, 0) 10.71%,
          rgba(12, 11, 12, 0.84) 55.36%,
          #000 100%);
      position: absolute;
      top: 0;
      left: 0;
    }

    &-text {
      overflow-y: scroll;
    }

    .text {
      height: 16px;
      display: flex;
      align-items: center;
    }

    &-loading {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      // 动画 旋转
      animation: rotate 1s linear infinite;
    }
  }
}

.success-container {

  // overflow-y: scroll;
  &-content {
    &-item {
      &-content {
        margin-top: 8px;
        padding: 8px;
        padding-right: 0px;
        width: 288px;
        height: 108px;
        border-radius: $radius-8;
        border: 1px solid $stroke-input;
        background: $background-input;
        margin-bottom: 16px;
        overflow-y: scroll;
        scrollbar-color: $background-system-scrollbar transparent;
        /* 滑块颜色 + 轨道颜色 */
        // // &::-webkit-scrollbar {
        // //   width: 10px;  /* 纵向滚动条宽度 */

        // // }
        &::-webkit-scrollbar-track {
          background: transparent;
          margin-top: 10px;
          margin-bottom: 10px;
        }
      }

      &-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $content-btnSecondary;
        /* text_14_medium */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        user-select: none;
        line-height: 20px;
        /* 142.857% */
      }

      .ant-btn {
        width: 76px;
        height: 26px;
        font-size: 12px;
        padding: 0;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.thinking-container-cancel {
  margin-top: 12px;
  width: 100%;
}

// 动画 线性执行1s 高度从0到100%
@keyframes slideOutDown {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: hidden;
  }
}

@keyframes slideOutUp {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: hidden;
  }
}

.zoomInDown {
  animation: slideOutDown 0.3s linear;
}

.zoomInUp {
  animation: slideOutUp 0.3s linear;
}

// 展开/收起动画
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    max-height: 1000px;
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    max-height: 1000px;
    opacity: 1;
    transform: translateY(0);
  }

  to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
}

.expand-animation {
  overflow: hidden;
  transition: all 0.3s ease-in-out;

  &-enter {
    animation: slideDown 0.3s ease-in-out forwards;
  }

  &-exit {
    animation: slideUp 0.3s ease-in-out forwards;
  }
}

// 添加图标文字并排的通用样式
.icon-text-flex {
  display: flex;
  align-items: center;

  svg {
    margin-right: 6px;
  }
}

// 为思考容器内的文字添加流式渲染样式
.thinking-container-content-text {
  overflow-y: auto;
  max-height: 100%;
  min-width: 100%;
  scrollbar-color: $background-system-scrollbar transparent;

  /* 滑块颜色 + 轨道颜色 */
  // 添加打字机光标效果（可选）
  &::after {
    content: '|';
    animation: blink 1s infinite;
    opacity: 1;
  }
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

.ultraClearBody1 {

  // width: 340px;
  :global(.ant-modal-content) {
    padding: 24px 16px 16px 16px !important;
  }

  h3 {
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .detailBtnS {
    margin-top: 24px;
  }

  .footer-box {
    display: flex;
    justify-content: space-between;

    .ok-btns {
      // background: $background-btn-ai;

      &:hover {
        // background: $background-btn-ai-hover;
      }
    }

    .ant-btn {
      width: 148px;
      height: 36px;
    }
  }

  .detailBox {
    text-align: center;
    font-size: 16px;
    font-weight: 500;

    .detailCheck {
      color: #616366;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin-top: 12px;
      /* 142.857% */
    }
  }

  :global(.ant-modal-body) {
    color: #1c1d1f;
    text-align: center;
  }
}

.designer-collapse.ant-collapse {
  // overflow: auto;
}