import { Tooltip } from "antd";
import { ExclamationMarkCircle } from "@meitu/candy-icons";
import styles from "./styles.module.scss";
import { useI18n } from "@/locales/client";

export function Disclaimer() {
  const t = useI18n();

  return (
    <section className={styles.disclaimer}>
      <Tooltip title="By using artificial intelligence features, you represent and warrant that the content uploaded by you (“Your Content”), your use and provision of Your Content to be made available through WheeAI and/or any use of Your Content by us on or through WheeAI will not infringe, misappropriate or violate any third party's intellectual property rights, or rights of publicity or privacy, or result in the violation of any applicable laws, rules or regulations. You understand that AI-generated content may not always be accurate, appropriate, or free from errors; and you must evaluate AI-generated content for accuracy and appropriateness for your use. You agree to use AI Features at your own risk. For details, please refer to our Terms of Service. ">
        <ExclamationMarkCircle />
        <span>{t("Disclaimer")}</span>
      </Tooltip>
    </section>
  );
}
