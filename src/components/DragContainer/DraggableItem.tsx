import { useRef, useState } from 'react';
import { DraggableItemRenderFunction, DragStatus } from './types';
import styles from './index.module.scss';

type DraggableItemProps = {
  containerRef: React.MutableRefObject<HTMLDivElement | null>,
  renderItem: DraggableItemRenderFunction;
  initialTransform?: [number, number];
}


export function DraggableItem({
  containerRef,
  renderItem,
  initialTransform = [12, 12],
}: DraggableItemProps) {

  const itemRef = useRef<HTMLDivElement | null>(null);
  const [translate, setTranslate] = useState(initialTransform);
  const dragStatus = useRef<DragStatus>({ status: 'default' });
  
  const handlePointerDown = (event: React.PointerEvent<HTMLElement>) => {
    const handlerElement = event.target as HTMLElement;
    const containerElement = containerRef.current as HTMLDivElement;
    const draggingItemElement = itemRef.current as HTMLDivElement;
    if (dragStatus.current.status !== 'default' || !containerElement || !draggingItemElement) {
      return;
    }

    const containerBBox = containerElement.getBoundingClientRect();
    const itemBBox = draggingItemElement.getBoundingClientRect();
    dragStatus.current = {
      status: 'dragging',
      pointerId: event.pointerId,
      downPointerPosition: [event.clientX, event.clientY],
      downItemPosition: translate,
      containerDimension: [containerBBox.width, containerBBox.height],
      itemDimension: [itemBBox.width, itemBBox.height],
    };
    handlerElement.setPointerCapture(event.pointerId);
  }

  const handlePointerUp = (event: React.PointerEvent<HTMLElement>) => {
    const handlerElement = event.target as HTMLElement;
    if (dragStatus.current.status !== 'dragging') {
      return;
    }

    const pointerId = dragStatus.current.pointerId;
    dragStatus.current = {
      status: 'default',
    };
    handlerElement.releasePointerCapture(pointerId);
  }

  const handlePointerMove = (event: React.PointerEvent<HTMLElement>) => {
    if (dragStatus.current.status !== 'dragging') {
      return;
    }

    const [downPointerX, downPointerY] = dragStatus.current.downPointerPosition;
    const [currPointerX, currPointerY] = [event.clientX, event.clientY];
    const [translateX, translateY] = [currPointerX - downPointerX, currPointerY - downPointerY];

    const [initX, initY] = dragStatus.current.downItemPosition;
    const [currX, currY] = [initX + translateX, initY + translateY];


    const { containerDimension, itemDimension } = dragStatus.current;
    const [maxX, maxY] = [
      containerDimension[0] - itemDimension[0],
      containerDimension[1] - itemDimension[1],
    ];
    const clamp = (min: number, max: number, value: number) => Math.min(max, Math.max(min, value));
   
    const [limitedX, limitedY] = [clamp(0, maxX, currX), clamp(0, maxY, currY)];
    setTranslate([limitedX, limitedY]);
  }

  return (
    <div
      className={styles.item}
      ref={itemRef}
      style={{
        transform: `translate(${translate[0]}px, ${translate[1]}px)`,
      }}
    >
      {
        renderItem({
          handlers: {
            onPointerDown: handlePointerDown,
            onPointerMove: handlePointerMove,
            onPointerUp: handlePointerUp
          }
        })
      }
    </div>
  )
}