import classNames from "classnames";
import styles from "./index.module.scss";
import { useRef } from "react";
import { DraggableItem } from "./DraggableItem";
import { DraggableItemRenderFunction } from "./types";
export * from './types';


type DraggableItemOptions = {
  key: React.Key,
  render: DraggableItemRenderFunction,
}


export type DragContainerProps = {
  className?: string;
  draggableItems: DraggableItemOptions[]
}


export function DragContainer({
  className,
  draggableItems
}: DragContainerProps) {

  const containerRef = useRef<HTMLDivElement | null>(null);

  return (
    <div
      className={classNames(styles.container, 'drag-container', className)}
      ref={containerRef}
    >
      {
        draggableItems?.map(({key, render}) => {
          return (
            <DraggableItem key={key} containerRef={containerRef} renderItem={render}/>
          )
        })
      }
    </div>
  )
}