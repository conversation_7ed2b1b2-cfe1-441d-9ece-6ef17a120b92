export type DragStatus = {
  status: 'default'
} | {
  status: 'dragging',
  pointerId: number,
  downPointerPosition: [number, number],
  downItemPosition: [number, number],
  containerDimension: [number, number],
  itemDimension: [number, number],
}


export type DraggableItemRenderFunctionParams = {
  handlers: {
    onPointerDown: (event: React.PointerEvent<HTMLElement>) => void,
    onPointerMove: (event: React.PointerEvent<HTMLElement>) => void,
    onPointerUp: (event: React.PointerEvent<HTMLElement>) => void,
  }
}

export type DraggableItemRenderFunction = (props: DraggableItemRenderFunctionParams) => React.ReactElement;