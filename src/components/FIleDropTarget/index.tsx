import React from "react";

type FilesDroppedHandlerParams = {
  files: File[],
  event: React.DragEvent<HTMLDivElement>,
}

type FileDropTargetProps = React.PropsWithChildren<{
  className?: string;
  onFilesDropped?: (params: FilesDroppedHandlerParams) => void;
}>

export default function FileDropTarget({ children, className, onFilesDropped }: FileDropTargetProps) {

  const ensureDragFiles = (fn?: (e: React.DragEvent<HTMLDivElement>) => void) => {
    return (e: React.DragEvent<HTMLDivElement>) => {
      if (!e.dataTransfer.types.includes("Files")) {
        return;
      }

      e.preventDefault();
      fn?.(e);
    }
  };

  const handleDrop = ensureDragFiles((e) => {
    const files: File[] = [];

    for (let i = 0; i < e.dataTransfer.files.length; ++i) {
      const file = e.dataTransfer.files[i];
      files.push(file);
    }

    onFilesDropped?.({ files, event: e });
  });

  return (
    <div className={className} onDragOver={ensureDragFiles()} onDrop={handleDrop}>
      {children}
    </div>
  );
}
