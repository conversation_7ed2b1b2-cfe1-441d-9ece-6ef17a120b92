import classNames from 'classnames';
import styles from './index.module.scss';
import { useRef } from 'react';

type FilePickerChildrenRenderParams = {
  openPicker: () => void;
}

type FilePickerProps = {
  className?: string
  accept?: Array<string>
  onChange?:(fileList: FileList | null) => void
  children: (params: FilePickerChildrenRenderParams) => React.ReactElement
}

export default function FilePicker({ children, className, accept, onChange }: FilePickerProps) {

  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.files);
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  }

  const openPicker = () => {
    inputRef.current?.click();
  }


  return (
    <div className={classNames(styles.picker, className)}>
      <input
        ref={inputRef}
        className="file-picker-input"
        type="file"
        accept={accept?.join(',')}
        onChange={handleFileChange}
      />
      {children({ openPicker })}
    </div>
  )
}