'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { rootStore } from '@/stores/RootStore';

export function GlobalRouteTracker() {
  const currentPathname = usePathname();
  const previousPathnameRef = useRef<string | null>(null);
  const { referrerStore } = rootStore;

  useEffect(() => {
    // 当 currentPathname 发生变化时:
    // previousPathnameRef.current 持有的是变化前的路径
    if (previousPathnameRef.current && previousPathnameRef.current !== currentPathname) {
      referrerStore.setPreviousPath(previousPathnameRef.current);
    }

    // 将当前路径存入 ref，供下一次导航变化时使用
    previousPathnameRef.current = currentPathname;

  }, [currentPathname, referrerStore]);

  return null; // 此组件不渲染任何UI
}

export default GlobalRouteTracker; 