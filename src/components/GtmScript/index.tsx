"use client";

import Script from "next/script";
import { useEffect } from 'react';
import { getConfig } from "@/config";
import { setGoogleAdsParams } from "@/services/tracer/globalParamsTracer";

const config = getConfig();
const { GTM_ID, GTM_AUTH, GTM_PREVIEW, GA_MEASUREMENT_ID } = config;

// 定义事件类型
interface GAClientIdEvent extends CustomEvent {
  detail: { clientId: string };
}

export function GTMInit() {
  useEffect(() => {
    const handleClientIdReady = (event: Event) => {
      const customEvent = event as GAClientIdEvent;
      if (customEvent.detail?.clientId) {
        const clientId = customEvent.detail.clientId;
        // console.log('[GTM] Client ID ready:', clientId);
        setGoogleAdsParams(clientId);
      } else {
        // console.warn('[GTM] Client ID event received but no valid ID found');
      }
    };

    window.addEventListener('gaClientIdReady', handleClientIdReady);
    return () => {
      window.removeEventListener('gaClientIdReady', handleClientIdReady);
    };
  }, []);

  return (
    <>
      <Script id="gtm-init" strategy="afterInteractive">
        {`
          (function(w,d,s,l,i){
            w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});
            var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;
            j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+
            '&gtm_auth=${GTM_AUTH}&gtm_preview=${GTM_PREVIEW}&gtm_cookies_win=x';
            f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${GTM_ID}');
        `}
      </Script>

      <Script id="ga-client-id-fetcher" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}

          function waitForGtagAndGetClientId(retryCount = 0, maxRetries = 20) {
            if (typeof gtag === 'function' && typeof window.gtag === 'function') {
              if (!'${GA_MEASUREMENT_ID}') {
                console.warn('[GTM] GA_MEASUREMENT_ID is not available');
                return;
              }

              gtag('get', '${GA_MEASUREMENT_ID}', 'client_id', function(clientId) {
                if (clientId && typeof clientId === 'string') {
                  const event = new CustomEvent('gaClientIdReady', { 
                    detail: { clientId: clientId }
                  });
                  window.dispatchEvent(event);
                } else {
                  console.warn('[GTM] Invalid client_id received:', clientId);
                }
              });
            } else {
              if (retryCount < maxRetries) {
                setTimeout(() => waitForGtagAndGetClientId(retryCount + 1, maxRetries), 500);
              } else {
                console.warn('[GTM] Max retries reached for gtag');
              }
            }
          }

          if ('${GTM_ID}') {
            waitForGtagAndGetClientId();
          } else {
            console.warn('[GTM] GTM_ID is not available');
          }
        `}
      </Script>
    </>
  );
}

export function GTMNoScript() {
  return (
    // <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}&gtm_auth=${GTM_AUTH}&gtm_preview=${GTM_PREVIEW}&gtm_cookies_win=x`}
        height="0"
        width="0"
        style={{ display: "none", visibility: "hidden" }}
      ></iframe>
    </noscript>
    // <!-- End Google Tag Manager (noscript) -->
  );
}
