"use client";
import type { MenuProps } from "antd";
import { useStore } from "@/contexts/StoreContext";
import { Tooltip, Avatar, Dropdown, Badge } from "antd";
import { UserBoldOutlined } from "@meitu/candy-icons";
import { observer } from "mobx-react-lite";
import { toAtlasImageView2URL } from "@meitu/util";
import  "./index.scss";
import classNames from "classnames";
import Link from "next/link";
import { SettingBold } from "@meitu/candy-icons";
import { useMemo, useState } from "react";
import { usePathname } from 'next/navigation';
import { MenuItemType } from "antd/es/menu/interface";
import { clearAccount, wheeToAccount } from "@/utils/account";
import { VipIcon } from "@/assets/icons";
import { CopyBold } from '@meitu/candy-icons'
import toast from "@/components/Toast";
import useConfirmModal from "@/hooks/useConfirmModal";

import { LogoutIcon } from "@/assets/icons";

function AccountAvatar({ onClick }: { onClick?: () => void }) {
  const [open, setOpen] = useState(false);
  const { userStore } = useStore();
  const isLogin = userStore.isLogin;
  const user = userStore.currentUser;
  const vipLevel = userStore?.vipLevel;

  const { open: confirmOpen, contextHolder } = useConfirmModal({
    title: 'Logout',
    description: 'Are you sure you want to logout?',
    onConfirm: () => {
      userStore.logout();
      setOpen(false);
      window.location.href = "/";
      clearAccount();
    },
  });
  // const vipLevel = 0;

  if (!isLogin || !user) {
    return (
      <span className={classNames( 'avatar', "avatar")}>
        <Avatar icon={<UserBoldOutlined />} />
      </span>
    );
  }

  // const menuItems = useMemo<NonNullable<MenuProps['items']>>(() => {

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);
  const fixturesMenuItems =  [
      {
        key: "member-desc",
        className: 'member-desc',
        label: (
          <>
            <SettingBold /> Account Setting
          </>
        ),
        onClick() {
          wheeToAccount();
        },
      },
      {
        type: "divider",
      },
      {
        key: "logout",
        className: "logout",
        label: <div className="logout-label" style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          
        }}><LogoutIcon /> &nbsp; Logout</div>,
        onClick() {

          confirmOpen();
          // userStore.logout();
          // setOpen(false);
          // window.location.href = "/";

        },
      },
    ];


  return (
    <>
      {contextHolder}
      <Dropdown
        overlayClassName={'dropdown-overlay'}
        menu={{ items: fixturesMenuItems as MenuItemType[] }}
        trigger={["hover", "click"]}
        open={open}
        onOpenChange={(open) => {
          setOpen(open);
          open && onClick?.();
        }}
      dropdownRender={(menu) => (
        <>
          {user && (
            <div className={'account'}>
              <div className="account-avatar-wrapper">
                {user.avatar && (
                  <Avatar
                    src={toAtlasImageView2URL(user.avatar, {
                    mode: 2,
                    width: 84,
                  })}
                  icon={<UserBoldOutlined />}
                  size="large"
                />
                
              )}
              {vipLevel ? (
                <span className={classNames("vip-icon", {
                  [`vip-icon-${vipLevel}`]: vipLevel,
                })}>
                  <VipIcon />
                </span>
              ) : null}
              </div>
              <span className={'account-profile'}>
                <strong>{user.screenName}</strong>
                <small>UID: {user.id} <CopyBold onClick={() => {
                  navigator.clipboard.writeText(user.id.toString());
                  toast.success('UID copied to clipboard');
                }} /></small>
              </span>
            </div>
          )}

          {menu}
        </>
      )}
    >
      <span className={'account-avatar'}>
        {user?.avatar && (
          <Avatar
            src={toAtlasImageView2URL(user.avatar, {
              mode: 2,
              width: 60,
            })}
            icon={<UserBoldOutlined />}
          />
        )}
        {vipLevel ? (
          <span className={classNames("vip-icon", {
            [`vip-icon-${vipLevel}`]: vipLevel,
          })}>
            <VipIcon />
          </span>
        ): null}
      </span>
     
    </Dropdown>
    </>
  );
}

export default observer(AccountAvatar);
