'use client'

import { useRouter } from "next/navigation";
import classNames from "classnames";

import buttonStyles from '@/styles/button.module.scss';
import styles from './index.module.scss';
import { useI18n } from '@/locales/client';
import { BackArrowBold } from "@meitu/candy-icons";

// import Link from "next/link";
// import { useRouter } from 'next/navigation';

export default function BackButton() {
  const router = useRouter();
  const t = useI18n()
  const handleClick = () => {
    router.back();
  }


  return (
    <button className={classNames(buttonStyles.secondary, styles.back, 'back-btn')} onClick={handleClick}>
      <BackArrowBold className="icon"/>
      <span className="label">
        {t('Back')}
      </span>
    </button>
  );
}