.header {
  display: grid;
  height: 48px;
  grid-template-columns: repeat(3, 1fr);
  overflow: hidden;
  border-bottom: 1px solid var(--system-stroke-input, #22272E);
  background: var(--system-background-secondary, #1D1E23);
  user-select: none;
  box-sizing: border-box;

  :global {
    .slot {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;

      &.left {
        justify-content: flex-start;
      }

      &.center {
        justify-content: center;
      }

      &.right {
        justify-content: flex-end;
      }
    }
  }
}