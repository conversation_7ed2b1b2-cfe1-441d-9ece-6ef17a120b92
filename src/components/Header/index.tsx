
import classNames from 'classnames';
import styles from './index.module.scss';
import { HeaderElement, renderElements } from './utils';


type HeaderProps = {
  /**
   * header中靠左边的元素
   */
  leftSlot?: HeaderElement[],
  /**
   * header中靠中间的元素
   */
  centerSlot?: HeaderElement[],
  /**
   * header中靠右边的元素
   */
  rightSlot?: HeaderElement[],
  /**
   * 用来调整元素间的布局
   */
  className?: string;
}

export default function Header( {
  leftSlot,
  centerSlot,
  rightSlot,
  className
}: HeaderProps) {

  return (
    <header className={classNames(styles.header, className)}>
      <div className="slot left">
        {leftSlot && renderElements(leftSlot)}
      </div>
      <div className="slot center">
        {centerSlot && renderElements(centerSlot) }
      </div>
      <div className="slot right">
        {rightSlot && renderElements(rightSlot)}
      </div>
    </header>
  )
}

export { HeaderElementType } from './utils';