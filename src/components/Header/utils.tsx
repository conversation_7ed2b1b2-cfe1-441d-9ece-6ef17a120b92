import { cloneElement } from "react";
import Logo from './Logo';
import BackButton from "./BackButton";
import AccountAvatar from "./AccountAvatar";
import MtBean from "./MtBean";

export enum HeaderElementType {
  Logo = 'logo',
  BackButton = 'back-button',
  MtBean = 'mt-bean',
  AccountAvatar = 'account-avatar',
  Custom = 'custom',
}

export type HeaderElement = {
  type: Exclude<HeaderElementType, HeaderElementType.Custom>,
  key?: string | number | null,
  onClick?: () => void;
} | {
  type: HeaderElementType.Custom,
  key: string | number | null,
  element: React.ReactElement,
}

export function renderElements(els: HeaderElement[]) {
  return els.map(el => {

    const key = el.key || el.type;

    switch(el.type) {
      case HeaderElementType.Custom: {
        return cloneElement(el.element, { key: `custom_${key}` });
      }

      case HeaderElementType.Logo: {
        return <Logo key={key}/>;
      }

      case HeaderElementType.BackButton: {
        return <BackButton key={key}/>;
      }

      case HeaderElementType.AccountAvatar: {
        return <AccountAvatar key={key} onClick={el.onClick}/>;
      }

      case HeaderElementType.MtBean: {
        return <MtBean key={key}/>
      }
    }
  });
}