"use client";

import { useEffect, useLayoutEffect } from "react";
import { initWheeMTLogin } from "@/utils/account";
import { initTracer } from "@/services/tracer";

export function InitClient() {
  // useLayoutEffect 会在 DOM 变更之后同步调用，比 useEffect 更早
  const isInit = typeof window !== "undefined" && window.mtstat.isInitialized();
  useLayoutEffect(() => {
    //  非生产模式打印
    if (process.env.NEXT_PUBLIC_ENV !== "release") {
      // console.log("init client before render========>");
    }

    initWheeMTLogin().then(() => {
      // console.log('initWheeMTLogin success');
    });

    // const isInit = window.mtstat.isInitialized
    // console.log('isInit', isInit);
    if (!isInit) {
      initTracer();
    } else {
      // console.log("tracer already initialized");
    }
  }, [isInit]);



  return null;
}
