.inputNumber:global(.ant-input-number-affix-wrapper),
.inputNumber:global(.ant-input-number) {
  height: 26px;
  width: 80px;
  box-shadow: none;
  // background: transparent;

  &.suffix {
    display: flex;

    :global .ant-input-number-prefix {
      margin-left: 4px;
      order: 1;
    }
  }

  :global .ant-input-number-input {
    height: 24px;
  }

  &.handler :global .ant-input-number-handler-wrap {
    opacity: 1;
    background-color: transparent;

    :global {
      .ant-input-number-handler {

        &-up-inner,
        &-down-inner {
          font-size: 8px;
        }
      }
    }
  }

  :global .ant-input-number-handler-wrap {
    &::before {
      position: absolute;
      width: 1px;
      height: 60%;
      top: 20%;
      background-color: #d9d9d9;
      content: '';
    }

    :global .ant-input-number-handler {
      height: 50% !important;
      border-inline-start: none;
    }

    :global .ant-input-number-handler-up {
      padding-top: 2px;
    }

    :global .ant-input-number-handler-down {
      padding-bottom: 2px;
      border-block-start: none;
    }
  }
}