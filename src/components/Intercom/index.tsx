'use client'
import React, { useEffect, useState, useRef } from "react";
import Intercom, { onShow, shutdown } from '@intercom/messenger-js-sdk';
import { useStore } from "@/contexts/StoreContext";
import mtstat from "@meitu/tracer-intl";
import { observer } from "mobx-react-lite";
import { trackEvent } from "@/services/tracer";
let isGlobalIntercomOnShowCallbackRegistered = false;
const IntercomFeedBack = observer(() => {

  const { userStore } = useStore();
  const user = userStore.currentUser;
  const isLogin = userStore.isLogin;
  const [intercomInit, setIntercomInit] = useState(false);
  const getDeviceId = () =>
    typeof window !== "undefined" ? mtstat.getDeviceId() : "";
  
  const onShowCallbackBound = useRef(false);


  // 在组件挂载时初始化 只有登录的时候才初始化  Intercom
  useEffect(() => {
    let _user = {
    }
    if(isLogin) {
      _user = {
        user_id: user?.id,
        name: user?.screenName,
        email: user?.email,
      }
      Intercom({
        app_id: 'b5kfg2e5', 
        ..._user,
        page_title: document.title,
        language_override: 'en',
       
      });
      setIntercomInit(true);
    
    } else {
      _user = {
        id: getDeviceId(),
      }
    }
    // console.log('user=????????', _user, isLogin)

  }, [user, isLogin]);

  useEffect(() => {
     // 添加事件监听
     if(intercomInit && !isGlobalIntercomOnShowCallbackRegistered) {
      onShow(() => {
        trackEvent('login_home_page_side_bar_click', {
          click_type: 'customer_service',
        });
      });
      isGlobalIntercomOnShowCallbackRegistered = true;
     }
      
     

  }, [intercomInit]);



  // 显示 Intercom 聊天窗口
  const showIntercom = () => {
    // @ts-ignore - 忽略类型检查，因为 Intercom 类型定义可能不完整
    Intercom('show');
  };

  return (
    <div>
    </div>
  );
});

// 重命名导出的组件避免与导入的 Intercom 冲突
export default IntercomFeedBack;
