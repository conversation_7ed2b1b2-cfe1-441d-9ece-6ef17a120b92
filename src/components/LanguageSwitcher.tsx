'use client';


import { useChangeLocale, useCurrentLocale } from '@/locales/client'
export function LanguageSwitcher() {
  const changeLocale = useChangeLocale()
  const locale = useCurrentLocale()
  return (
    <div>
      <button 
        onClick={() => changeLocale('en')}
        style={{ fontWeight: locale === 'en' ? 'bold' : 'normal' }}
      >
        English {locale === 'en' && '(current)'}
      </button>
      <button 
        onClick={() => changeLocale('zh')}
        style={{ fontWeight: locale === 'zh' ? 'bold' : 'normal' }}
      >
        中文 {locale === 'zh' && '(当前)'}
      </button>
    </div>
  );
} 