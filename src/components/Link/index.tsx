import Link, { LinkProps } from 'next/link';
import { ReactNode } from 'react';
import './index.scss';
import classNames from 'classnames';
type CustomLinkProps = LinkProps & React.HTMLAttributes<HTMLAnchorElement>

export default function CustomLink({  children, ...props }: CustomLinkProps) {
  return <Link {...props} draggable="false" className={classNames('custom-link', props.className)}>{children}</Link>
}