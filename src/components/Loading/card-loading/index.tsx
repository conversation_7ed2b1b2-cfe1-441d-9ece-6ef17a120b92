import React from 'react'
import Lottie from 'lottie-react'
import loadingAnimation from '@/assets/lottie/poster/loading/card-loading.json'
import styles from './index.module.scss'
import cn from 'classnames'


type Props = {
  lottieStyle?: React.CSSProperties
  className?:string
}

const CardLoading = (props: Props) => {
  const { lottieStyle={ width: '16%', minWidth:18}, className } = props;
  return (
    <div className={cn(styles.cardLoading, className)}>
      <Lottie animationData={loadingAnimation} loop autoplay style={lottieStyle}  />
    </div>
  )
}

export default CardLoading