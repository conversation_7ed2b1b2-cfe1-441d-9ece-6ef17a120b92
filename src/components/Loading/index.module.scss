$base-black-opacity-65: rgba(0, 0, 0, 0.65);

@keyframes scale {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  25% {
    transform: scale(0.9, 0.9);
  }

  50% {
    transform: scale(1, 1);
    margin: 0 3px;
    opacity: 1;
  }

  100% {
    transform: scale(0);
    opacity: 0;
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: transparent;
  color: #1c1d1f;

  :global {
    .loading-jumping {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: calc(48px * 2);

      &:global(.ant-spin-dot) {
        min-width: 96px;
      }
    }

    .loading-jumping,
    .loading-jumping * {
      box-sizing: border-box;
    }

    .loading-jumping span {
      display: inline-block;
      height: 16px;
      width: 16px;
      background: #D9D9D9;
      border-radius: 100%;
      background-clip: padding-box;
    }

    .loading-jumping span:nth-child(1) {
      & :local {
        animation: scale 1.15s 0.12s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
      }
    }

    .loading-jumping span:nth-child(2) {
      & :local {
        animation: scale 1.15s 0.23s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
      }
    }

    .loading-jumping span:nth-child(3) {
      & :local {
        animation: scale 1.15s 0.35s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
      }
    }
  }
}

.loading:global(.light) {
  .loading-jumping {
    span:nth-child(1) {
      animation: scale-light 1.15s 0.12s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(2) {
      animation: scale-light 1.15s 0.23s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(3) {
      animation: scale-light 1.15s 0.35s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }
  }

  @keyframes scale-light {
    0% {
      transform: scale(0);
      background: #fff;
    }

    25% {
      transform: scale(0.9, 0.9);
      background: #fff;
    }

    50% {
      transform: scale(1, 1);
      margin: 0 3px;
      background: #fff;
    }

    100% {
      transform: scale(0);
      background: #fff;
    }
  }
}

.loading:global(.dark) {
  .loading-jumping {
    span:nth-child(1) {
      animation: scale-dark 1.15s 0.12s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(2) {
      animation: scale-dark 1.15s 0.23s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(3) {
      animation: scale-dark 1.15s 0.35s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }
  }

  @keyframes scale-dark {
    0% {
      transform: scale(0);
      background: $base-black-opacity-65;
    }

    25% {
      transform: scale(0.9, 0.9);
      background: $base-black-opacity-65;
    }

    50% {
      transform: scale(1, 1);
      margin: 0 3px;
      background: $base-black-opacity-65;
    }

    100% {
      transform: scale(0);
      background: $base-black-opacity-65;
    }
  }
}