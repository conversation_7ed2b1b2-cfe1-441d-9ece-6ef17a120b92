import type { ReactNode } from "react";
import classNames from "classnames";
import styles from "./index.module.scss";

interface LoadingProps {
  type?: "light" | "dark" | "default";
  children?: ReactNode;
  className?: string;
}

/** 加载中 */
export function Loading(props: LoadingProps) {
  return (
    <div className={classNames(styles["loading"], props.type, props.className)}>
      <div className="loading-jumping">
        <span />
        <span />
        <span />
      </div>
      {props.children}
    </div>
  );
}
