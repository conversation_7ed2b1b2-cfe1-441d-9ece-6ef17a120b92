import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { MeiDouFetchPriceDescResponse } from "@/api/types/meidou";
import { FunctionCode } from "@/api/types/meidou";
import { fetchPriceDesc } from "@/api/meidou";
import {
  AiSparkleFill,
  ChevronDownBold,
  ExclamationMarkCircleBold,
} from "@meitu/candy-icons";
import { useI18n } from "@/locales/client";
import { Button } from "../Button";
import { Row, Col, Space, Popover, Tooltip, Image } from "antd";
import "./index.style.scss";
import { useMemberShipDesc } from "@/hooks/useMember";
import { useMeiDouBalance } from "@/hooks/useMeidou";
import { SubscribeModal } from "../SubscribeModal";
import meidou from "@/assets/images/meidou.png";
import { observer } from "mobx-react-lite";
import { useStore } from "@/contexts/StoreContext";
import { useSubscribeModal } from "@/contexts/SubscribeModalContext";
import { SubscribeModalType } from "../SubscribeModal/types";
import { Disclaimer } from "../Disclaimer";
import { MtccFuncCode, VipLevel } from "@/types";
import { useSubscribe } from "@/hooks/useSubscribe";
import { useOpenMeiDouRecordsPopup } from "@/hooks/useSubscribe";
import { VipIcons } from "@/assets/icons";

type ClickParams = {
  price?: MeiDouFetchPriceDescResponse;
  deficit: boolean;
};
export interface IMeiDouButtonProps {
  price?: MeiDouFetchPriceDescResponse | undefined;
  buttonText?: string;
  fetchPriceLoading?: boolean;
  functionId?: MtccFuncCode;
  functionBody?: string;
  disabled?: boolean;
  functionCode: FunctionCode;
  onClick?: (params: ClickParams) => void;
  getTemplateId?: () => string | undefined;
}

const MeiDouButton: React.FC<IMeiDouButtonProps> = ({
  // price,
  buttonText,
  fetchPriceLoading,
  functionId,
  functionBody,
  functionCode,
  disabled,
  onClick,
  getTemplateId,
  ...restProps
}) => {
  const t = useI18n();
  const [price, setPrice] = useState<MeiDouFetchPriceDescResponse | undefined>(
    undefined
  );
  const [showSubscribeModal, setShowSubscribeModal] = useState(false);
  // 判断是否是vip
  const { isVipCurrent } = useMemberShipDesc();
  const { userStore } = useStore();
  const { availableAmount, updateMeiDouBalance } = useMeiDouBalance({
    userStore,
  });
  const { open, close } = useSubscribeModal();

  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();

  const vipLevel = userStore.vipLevel;

  const deficit = (availableAmount ?? 0) < (price?.amount ?? 0);

  const getPrice = useCallback(async () => {
    if (JSON.parse(functionBody || "{}")?.batch_size) {
      const res = await fetchPriceDesc({
        functionCode,
        functionBody: functionBody || "",
      });

      setPrice(res);
    }
  }, [functionBody, functionCode]);

  useEffect(() => {
    // updateMeiDouBalance();
    // 这么监听是否合理
    getPrice();
  }, [functionBody, functionCode]);

  // 刷新美余额后，要更新价格描述。限免次数
  useEffect(() => {
    userStore.addUpdateMeidouListener(getPrice);
    return () => {
      userStore.removeUpdateMeidouListener(getPrice);
    };
  }, [userStore, getPrice]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    try {
      onClick?.({ deficit, price });
    } catch (e) {
      if (process.env.NODE_ENV === "development") {
        console.error(e);
      }
    }

    if (deficit) {
      // 打开订阅弹窗
      e.preventDefault();
      if (vipLevel === VipLevel.Plus) {
        // 打开没豆
        openMeiDouRecordsPopup(functionId);
      } else {
        // setShowSubscribeModal(true);
        const templateId = getTemplateId?.();
        open({
          productType: SubscribeModalType.Basic,
          templateId,
          functionCode,
          functionId,
          onSuccess: () => {
            // 执行原来的submit
            e.preventDefault();
            // close();
          },
        });
      }
    } else {
      // 执行原来的submit
      //  e.preventDefault();
    }
  };

  return (
    <div className="meidou-button">
      <Row
        justify="space-between"
        align="middle"
        style={{ width: "100%", marginBottom: "12px" }}
      >
        <>
          <Col>
            <Space size={0}>
              <Image src={meidou.src} preview={false} className={"icon"} />
              <span className={"count"}>{price?.costPriceText ?? ""}</span>

              {price?.costPriceTextOrigin &&
                `${t("Costs")} ` + price.costPriceTextOrigin !==
                  price.costPriceText && (
                  <span className={"before-discount"}>
                    {price.costPriceTextOrigin}
                  </span>
                )}

              {price?.costPriceTips && (
                <Tooltip
                  overlayClassName={"tooltip"}
                  title={
                    <span
                      dangerouslySetInnerHTML={{
                        __html: price.costPriceTips,
                      }}
                    />
                  }
                >
                  <ExclamationMarkCircleBold className={"icon2"} />
                </Tooltip>
              )}
            </Space>
          </Col>

          {price?.priceDetail && (
            <Col>
              <Popover
                overlayClassName={"details-popover"}
                title={t("Details")}
                arrow={false}
                placement="topRight"
                content={
                  <>
                    {price.priceDetail.map((detail, index) => (
                      <div
                        key={`${detail.itemName}-${index}`}
                        className={"details"}
                      >
                        <Space size={4}>
                          <span>{detail.itemName}</span>
                          <span>{detail.itemCount}</span>
                        </Space>
                        <Space>
                          {detail.priceOrigin !== detail.priceNow && (
                            <span className={"before-discount"}>
                              {detail.priceOrigin}
                            </span>
                          )}

                          <span>{detail.priceNow}</span>
                        </Space>
                      </div>
                    ))}
                  </>
                }
              >
                <Space className={"details-trigger"} size={4} align="center">
                  {t("Details")}
                  <ChevronDownBold className={"icon2"} />
                </Space>
              </Popover>
            </Col>
          )}
        </>
      </Row>
      <Button
        className="meidou-button-button"
        type="primary"
        htmlType="submit"
        disabled={disabled}
        loading={fetchPriceLoading}
        onClick={handleClick}
      >
        <AiSparkleFill />
        {buttonText || t("Generate")}

        <div
          className="vip-icon"
          style={{
            display:
              functionCode === FunctionCode.aiPosterTextEditing
                ? "block"
                : "none",
            // display: "block"
          }}
        >
          <VipIcons />
        </div>
      </Button>
      <Disclaimer />
      {/* <SubscribeModal show={showSubscribeModal} onClose={() => {}} type="basic" /> */}
    </div>
  );
};

export default observer(MeiDouButton);
