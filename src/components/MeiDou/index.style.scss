.meidou-button {
  &.has-border {
    bottom: 0;
    border-radius: 0;
    // box-shadow: @separator;
  }

  :global(.ant-row) {
    width: 100%;
  }

  .count {
    // color: @content-system-primary;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .before-discount {
    // color: @content-system-quaternary;
    color: #A3AEBF;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    text-decoration: line-through;
  }

  .icon,
  .ant-image {
    font-size: 15px;

    .ant-image-img {
      width: 15px;
      height: 15px;
      margin-top: 5px;
    }
  }

  .icon2 {
    font-size: 13px;
    // color: @content-system-quaternary;
    cursor: pointer;
  }

  .details-trigger {
    // color: @content-system-quaternary;       
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    cursor: pointer;
  }

  :global(.ant-btn) {
    margin: 7px 0 6px 0;
    border: none;
  }

  .skeleton {
    height: 22px;

    h3 {
      margin-bottom: 0 !important;
    }
  }
}

.details-popover {
  &-title {
    margin-bottom: 12px !important;
  }

  &-inner-content {
    .details {
      display: flex;
      justify-content: space-between;

      span {
        // color: @content-system-primary;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;

        &.before-discount {
          // color: @content-system-quaternary;
          text-decoration: line-through;
        }
      }
    }
  }
}

.tooltip {
  font-size: 12px !important;
}

.meidou-button {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 126px;
  padding: 16px;
  position: absolute;
  left: 0;
  bottom: 0;
  border-top: 1px solid var(--system-stroke-input, #22272E);
  background: #1D1E23;
  z-index: 9;
  box-sizing: border-box;

  &-button {
    width: 100%;
    height: 36px;
    border-radius: 8px;
    border: none !important;
    position: relative;
    &:hover{
      background: #53F6B4 !important;
      opacity: 0.9;
      &::after{
        display: block;
        content:'';
        position: absolute;
        top:0;
        right: 0;
        bottom: 0;
        left:0;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.20) !important;
      }
    }
    .vip-icon {
      position: absolute;
      right: -4px;
      top: -4px;
    }
  }
}