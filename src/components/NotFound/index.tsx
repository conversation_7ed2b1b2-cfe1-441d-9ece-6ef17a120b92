"use client";
import { useI18n } from "@/locales/client";
import styles from "./index.module.scss";
import { LogoSimpleIcon } from "@/assets/icons";
import { ForwardArrowBlack } from "@meitu/candy-icons";
import { useRouter } from "next/navigation";
import { rootStore } from '@/stores/RootStore';
import "../../app/[locale]/(home)/global.scss";
const NotFound = () => {
  const t = useI18n();
  
  const { userStore } = rootStore;
  const router = useRouter();
  const handleClick = () => {
    if (userStore.isLogin) {
      router.push("/workspace");
    } else {
      router.push("/");
    }
  };
  return (
    <div className={styles["not-found"]}>
      <LogoSimpleIcon />
      <div className={styles["not-found-title"]}>
        {t("Sorry, nothing to see here.")}
      </div>
      <div className={styles["not-found-description"]}>
        {t("The page your looking for does not exist")}
      </div>
      <div className={styles["not-found-button"]}>
        <div className={styles["not-found-button-text"]} onClick={handleClick}>
          {t("Go back to WheeAI")}
        </div>
        <ForwardArrowBlack />
      </div>
    </div>
  );
};

export default NotFound;
