.questionnaire-container {
    background-color: rgba(12, 14, 15, 0.95);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    backdrop-filter: blur(32px);
}

.questionnaire-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 30px;
    height: 30px;
    background-color: #303741;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.questionnaire-question {
    &-title {
        color: var(--system-content-primary, #FFF);
        text-align: center;
        font-family: Poppins;
        font-size: 30px;
        font-style: normal;
        font-weight: 600;
        margin-top: 133px;
        
    }
    &-options {
        display: flex;
        // flex-direction: row;
        flex-wrap: wrap;
        align-content: flex-start;
        margin-top: 64px;
        width: 1200px;
        min-height: 360px;
        margin: 64px auto;
    }
    &-option {
        border-radius: 16px;
        width: 373px;
        border: 1px solid var(--system-stroke-button, #323B48);
        background: var(--system-background-secondary, #1D1E23);
        box-shadow: 0px 0px 14.7px 1px rgba(255, 255, 255, 0.00) inset;
        display: flex;
        height: 72px;
        padding: var(--spacing-16, 16px) var(--spacing-24, 24px);
        justify-content: space-between;
        align-items: center;
        gap: 6px;
        margin-bottom: 16px;
        margin-left: 16px;
        cursor: pointer;
        box-sizing: border-box;
        &-input {
            width: 100%;
            height: 100%;
            border: none;
            background-color: transparent;
            background: var(--system-background-secondary, #1D1E23);
            font-family: Poppins;
            font-size: 16px;
            border-radius: var(--radius-16, 16px);
        }
        &-input:focus {
            outline: none;
        }
        &-input:hover {
           border: none;
        }
        // flex: 1 0 0;
    }
    &-option:hover {
        border-radius: var(--radius-16, 16px);
        border: 1px solid var(--system-content-brandPrimary, #53F6B4);
        background: linear-gradient(0deg, var(--system-content-brandSecondary, #0F221A) 0%, var(--system-content-brandSecondary, #0F221A) 100%), var(--system-background-thirdary, #272C33);
        box-shadow: 0px 0px 14.7px 1px rgba(255, 255, 255, 0.00) inset;
    }
    &-option-other:hover {
        // border: none;
    }

    &-footer {
        display: flex;
        width: 500px;
        margin: 0 auto;
        justify-content: center;
        &-item {
            width: 58px;
            margin-left: 8px;
            margin-right: 8px;
            height: 4px;
            border-radius: var(--radius-10, 10px);
            background: #303741;
        }
        &-item-active {
            background-color: #FFF;
        }
    }

}
.questionnaire-question-plans {
    margin: 0 auto;
    width: 1200px;
    display: flex;
    justify-content: center;
    &-title {
        color: #FFF;
        text-align: center;
        font-family: Poppins;
        font-size: 28px;
        font-style: normal;
        margin-top: 138px;
        margin-bottom: 64px;
        font-weight: 600;
        span {
            background: linear-gradient(90deg, #53F6B4 68.93%, #D4F2FF 78.88%, #FA9FFF 88.82%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}
.free {
    .subscribe-modal-plan-item {
        color: var(--system-content-secondary, #A3AEBF);
        /* text_12 */
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        margin-bottom: 10px;
        &-text {
            padding-left: 4px;
        }
    }
}

