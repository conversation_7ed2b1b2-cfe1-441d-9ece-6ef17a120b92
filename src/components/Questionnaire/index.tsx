/**
 *  @description 问卷组件
 */

"use client";

import { useStore } from "@/contexts/StoreContext";
import { Input } from "antd";
import "./index.scss";
import { CheckCircleBold, CrossBlack } from "@meitu/candy-icons";

import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { VipLevel } from "@/types";

import { trackEvent } from "@/services/tracer";
import { useRouter } from "next/navigation";
import { useSubscribe } from "@/hooks/useSubscribeQ";
import { useI18n } from "@/locales/client";
import { VipButton } from "../Button/1VipButton";

import { Button } from "../Button";
import { useQuestionModal } from "@/contexts/QuestionContext";
import CheckIcon from '@/assets/icons/check.svg'

const Question = [
  {
    title: "What kind of work do you do?",
    options: [
      "Freelance designer",
      "Designer for a company or studio",
      "Content creator or blogger",
      "Entrepreneur/business owner",
      "Student or teacher",
      "Marketer",
      "E-commerce practitioner",
      "Education trainer",
      "AI painting enthusiast",
      "AI software from the industry",
      "Other",
    ],
  },
  {
    title: "What do you most want to create with WheeAI?",
    options: [
      "Inspiration/ideas",
      "Graphic design",
      "Branding design",
      "E-commerce design",
      "Illustration",
      "Product design",
      "Games",
      "Photography",
      "Other",
    ],
  },
  {
    title: "How did you hear about WheeAI?",
    options: [
      "Discord",
      "Influencer",
      "TikTok",
      "Instagram",
      "Facebook",
      "Product Hunt",
      "Web search",
      "Friends and family",
      "YouTube",
      "X (Twitter)",
      "Other",
    ],
  },
  {
    title: "Have you ever used AI Image generation before?",
    options: [
      "No, it's my first time",
      "Yes, a couple of times",
      "Yes, quite often",
    ],
  },
];

const Questionnaire = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0);

  const [maxQuestion, setMaxQuestion] = useState(4);

  const [answer, setAnswer] = useState<string[]>([]);

  const { isOpen, close,  } = useQuestionModal();
  const [canInput, setCanInput] = useState(false);
  const t =  useI18n();
  const [tab, setTab] = useState<'year' | 'month'>('year');

  const [currentPlan, setCurrentPlan] = useState<any>(null);

  const [plans, setPlans] = useState<any[]>([

      {
          type: 'basic',
          name: 'Basic',
          description: t('Get more batch generations and access to additional AI tools.   Ideal for beginners looking to explore AI capabilities.'),
      }, {
          type: 'plus',
          name: 'Premium',
          description: t('More credits and AI tools to advance your designs'),
      }
  ]);

  const { userStore } = useStore();

// 
  const { isLogin, vipLevel } = userStore;
  const router = useRouter();

// const vipLevel = VipLevel.None;

  const { loading, error, yearProduct, monthProduct, createTradeUrl, querySubscribeResult, initializeSubscription, submitLoading } = useSubscribe(); 
  const yearProductPromotion = yearProduct[0]?.promotionBanner || '';

  useEffect(() => {
    // initializeSubscription();
    trackEvent('survey_expo', {})
     // 去掉告警
     // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  useEffect(() => {
    // if (vipLevel === VipLevel.Plus) {
    //   setMaxQuestion(4);
    // } else {
    //   setMaxQuestion(5);
    // }
  }, [vipLevel]);

  useEffect(() => {
    // initializeSubscription(); 
    // 将yearProduct和monthProduct合并到plans中每一项，按照index 例如：plans[0].yearProduct = yearProduct[0]
    plans.forEach((plan, index) => {
        // 跳过第一项
       if(tab === 'year') {
           plan.project = yearProduct[index];
       } else if(tab === 'month') {
           plan.project = monthProduct[index];
       } else {
        plan.project = null;
       }
    });

   //  setCurrentPlanList(plans);
    setPlans([...plans]);
    setCurrentPlan(null);
   // 去掉告警
   // eslint-disable-next-line react-hooks/exhaustive-deps
}, [tab, yearProduct, monthProduct, loading]);


const handlePrice = (price: any, type: 'year' | 'month') => {
    // 如果price是字符串，则先转换为数字
    if (price === undefined) {
        return '';
    }
   // 去除掉非数字符号
   price = price.replace(/[^0-9]/g, '');
    if(typeof price === 'string') {
        price = parseInt(price);
    }
    // console.log('price=>>>>>>>>',  price / 100);
    return type === 'month' ? price / 100 : price;
}
    const handleSubscribe = async (plan: any) => {
        // 设置当前选中的计划
        // setCurrentPlan(plan);
        close();
      
        const urlResponse = await createTradeUrl(plan.project)
        if(urlResponse) {
             // 新开tab
            window.open(urlResponse?.url, '_blank');
            querySubscribeResult(plan.project, () => {
                close();
                // onSuccessCallback?.();
            });
        }
    }
    const handleChangeTab = (tab: 'year' | 'month') => {
        setTab(tab);
      
    }



    const YearPriceComponent = (planItem: any) => {
       
        const { plan, index } = planItem;
        return (
            <>
                <div>
                    <span className='symbol'>{plan?.project?.productPrice?.moneySymbol}</span>
                    <span className='price'>{handlePrice(plan?.project?.productPrice?.productPriceExplain, 'year')}</span>
                    <span className='price-tips'>{plan?.project?.productPrice?.moneyUnit}/Month billed yearly</span>
                </div>
                <div className='subscribe-modal-plan-original-price'>
                    {
                      monthProduct[index]?.productPrice?.price ? `Original price: ${plan?.project?.productPrice?.moneySymbol}${handlePrice(monthProduct[index]?.productPrice?.price, 'month')}` : ''
                    }
                </div>
            </>
        )
    }

    const MonthPriceComponent = (planItem: any) => {
        const { plan, index } = planItem;
        return (
            <>
                <div>
                    <span className='symbol'>{plan?.project?.productPrice?.moneySymbol}</span>
                    <span className='price'>{handlePrice(plan?.project?.productPrice?.price, 'month')}</span>
                </div>
                <div className='subscribe-modal-plan-original-price'>
                    {
                        plan?.project?.productPrice?.originalPrice !== plan?.project?.productPrice?.price ?  
                        `${plan?.project?.productPrice?.moneySymbol}${handlePrice(plan?.project?.productPrice?.originalPrice, 'month')}` : ''
                    }
                </div>
            </>
        )
    }

  const handleNextQuestion = (option: string) => {
    // todo 埋点处理

    trackEvent('survey_option_click', {
        question_num: currentQuestion,
        answer: option,
    })

    let _answer = [...answer];

    if (option === "Other") {
        _answer[currentQuestion] = '';
        setAnswer(_answer);
        setCanInput(true);
        return;
    //   trackEvent("questionnaire_other", {
    //     question: Question[currentQuestion].title,
    //     option: option,
    //   });
    
    }
    if (currentQuestion < Question.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
    if(currentQuestion === 3 && vipLevel !== VipLevel.Plus) {
        setCurrentQuestion(5);
    }
    _answer[currentQuestion] = option;
    if(maxQuestion === 4 && currentQuestion === 3) {
       close();
       trackEvent('survey_complete', {
        work: _answer[0],
        plan_with_whee: _answer[1],
        hear_about_whee: _answer[2],
        use_ai_before: _answer[3],
       });
    }
    setAnswer(_answer);
  };
  const handleInput = (e: any) => {
    setCanInput(false);
    let _answer = [...answer];
    _answer[currentQuestion] = e.target.value;
    setAnswer(_answer);
    setCurrentQuestion(currentQuestion + 1);
  }

  return (
    <div className="questionnaire-container" style={{
        display: isOpen ? 'block' : 'none'
    }}> 
      <div className="questionnaire-close" onClick={close}>
        <CrossBlack />
      </div>

      <div className="questionnaire-question" style={{
        display: currentQuestion === 5 ? 'none' : 'block'
      }}>
        <div className="questionnaire-question-title">
          {Question[currentQuestion]?.title}
        </div>
        <div className="questionnaire-question-options">
          {Question[currentQuestion]?.options.map((option, index) => {
            return (
              <div
                className={`questionnaire-question-option ${option === "Other" ?'questionnaire-question-option-other' : ''}`}
                onClick={() => {
                  handleNextQuestion(option);
                }}
                key={index}
              >
                {
                    option === "Other" ? (
                         canInput ? <Input onBlur={handleInput} onPressEnter={handleInput} type="text" placeholder="Please, provide details" className="questionnaire-question-option-input" /> : option
                    ) : (
                      <>
                        {option}
                        {answer[currentQuestion]===option&&<CheckIcon></CheckIcon>}
                      </>
                    )
                }
              </div>
            );
          })}
        </div>

      </div>
      {/* {
            currentQuestion === 5 ? (
                <>
                <div className="questionnaire-question-plans-title">
              {`Which plan best suits your needs? Don't miss`}<span>limited-time </span>&nbsp;offer!
                </div>
                <div className='questionnaire-question-plans'>
                 
                    <div className="subscribe-modal-content">
        
        
                        <div className="subscribe-modal-plans">
                        <div className={`subscribe-modal-plan free`} style={{
                            display: vipLevel === VipLevel.None ? 'block' : 'none'
                        }} >
                                        <div className='subscribe-modal-plan-name'>{"Free"}</div>
                                        <div className='subscribe-modal-plan-description'>{"AI tools bring your ideas to life. Start exploring at no cost."}</div>
                                        <div className='subscribe-modal-plan-price'>
                                        <div>
                            <span className='symbol'>{'$'}</span>
                            <span className='price'>{0}</span>
                            <span className='price-tips'> {tab == 'year' ?'/Month billed yearly': ''}</span>
                        </div>
                        <div className='subscribe-modal-plan-original-price'> </div>
                                        </div>
                                        <div className='subscribe-modal-button' style={{
                                            marginBottom: '16px'
                                        }}>
                                                    <Button type='primary' onClick={() => {
                                                        close();
                                                    }} loading={submitLoading && currentPlan?.type === 'basic'}>Start now</Button>
                                        </div>
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("{count}credits per month" as any, { count: 20 })}</span>
                                        </div>
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("Generated posters are public")}</span>
                                        </div>
        
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("Generations per batch: {count}" as any, { count: 1 })}</span>
                                        </div>
        
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>2 {t("Projects")}</span>
                                        </div>
                        </div>
        
                            {plans.map((plan, index) => {
                                return (
                                    <div className={`subscribe-modal-plan ${plan?.type === 'plus' ? 'plus' : ''}`} key={index}>
                                        <div className='subscribe-modal-plan-name'>{plan?.name}</div>
                                        <div className='subscribe-modal-plan-description'>{plan?.description}</div>
                                        <div className='subscribe-modal-plan-price'>
                                            {tab === 'year' ? <YearPriceComponent plan={plan} index={index} /> : <MonthPriceComponent plan={plan} index={index} />}
                                        </div>
                                        <div className='subscribe-modal-button'>
                                            {
                                                plan?.type === 'plus' ?
                                                <>
                                                {  
                                                     <VipButton type='primary' onClick={() => handleSubscribe(plan)}>{t("Subscribe to the Premium plan")}</VipButton>
                                                }
                                                </> :
                                                <>
                                                    <>
                                         {
                                            vipLevel === VipLevel.Basic ?
                                            <Button type='primary' onClick={() => {
                                                close();
                                            }}>{'Start now'}</Button>:
                                            <Button type='primary' onClick={() => handleSubscribe(plan)}>{t('Subscribe to the Basic plan')}</Button>
                                         }
                                        </>
                                                </>
                                                 
                                            }
                                        </div>
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("{count}credits per month" as any, { count: plan?.type === 'plus' ? 100 : 20 })}</span>
                                        </div>
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("Generated posters are private")}</span>
                                        </div>
        
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("Generations per batch: {count}" as any, { count: plan?.type === 'plus' ? 5 : 3 })}</span>
                                        </div>
        
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{plan?.type === 'plus' ? 'Unlimited' : '10'} {t("Projects")}</span>
                                        </div>
        
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("No restrictions on commercial use")}</span>
                                        </div>
                                        
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("Access to all member benefits")}</span>
                                        </div>
                                        
                                        <div className={"subscribe-modal-plan-item"}>
                                            <CheckCircleBold/>
                                            <span className={"subscribe-modal-plan-item-text"}>{t("Watermark-free downloads")}</span>
                                        </div>
        
                                        {plan?.type === 'plus' ? (
                                            <div className={"subscribe-modal-plan-item"}>
                                                <CheckCircleBold/>
                                                <span className={"subscribe-modal-plan-item-text"}>{t("Early access to new features")}</span>
                                            </div>):   <div className={"subscribe-modal-plan-item"}></div>
                                        }
                                    </div>
                                )
                            })}
                        </div>
                    </div>

                </div>
                </>
            ) : null 
        } */}
      <div className="questionnaire-question-footer">
        {
            // 根据 maxQuestion 值 生成 div 
            Array.from({ length: maxQuestion }).map((_, index) => {
                return <div key={index} className={`questionnaire-question-footer-item ${index === currentQuestion ? 'questionnaire-question-footer-item-active' : ''}`}>
                   
                </div>
            })
        }
      </div>
    </div>
  );
};

export default observer(Questionnaire);
