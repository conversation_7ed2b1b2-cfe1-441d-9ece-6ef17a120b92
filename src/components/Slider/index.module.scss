.slider:global(.ant-slider) {
  margin: 0 0 0 2px !important;

  :global(.ant-slider-rail) {
    background-color: var(--system-background-thirdary, #272C33);
  }
  
  &:global(.ant-slider:hover .ant-slider-rail) {
    background-color: var(--system-background-thirdary, #272C33);
  }

  // :global .ant-slider-track {
  //   background-color: #fff;
  // }

  :global {
    .ant-slider-dot {
      width: 3px;
      height: 8px;
      border-radius: 1.5px;
      border-color: #fff !important;

      &.ant-slider-dot-active {
        border-color: #fff;
      }
    }
  }

  :global .ant-slider-handle {
    &::after {
      box-shadow: 0 0 0 2px #fff !important;
      background: #fff;
      outline: none;
    }
  }

  &:hover {
    // :global .ant-slider-track {
    //   background-color: #fff;
    // }

    :global .ant-slider-dot-active {
      border-color: #fff !important;
    }

    :global .ant-slider-handle {
      &::after {
        box-shadow: 0 0 0 2px #fff !important;
      }
    }
  }
}