.sliderInput {
  transition: color .3s;

  // &:focus-within {
  //   .title {
  //     color: @color-primary;
  //   }
  // }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px
  }

  .title {
    color: #616366;
  }

  .tooltip {
    color: #616366;
    margin-left: 8px;
    font-size: 12px
  }

  .label {
    margin-right: 12px;
    color: #616366;
  }

  .slider {
    flex: 1 0;
  }

  .input {
    margin-left: 12px;
  }

  .horizontal {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
  }
}