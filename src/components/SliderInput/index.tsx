import React from "react";
import styles from "./index.module.scss";
import classNames from "classnames";

import { Row, Col, Tooltip } from "antd";
import { SliderTooltipProps } from "antd/es/slider";
import type { InputNumberProps as AntdInputNumberProps } from "antd";
import { InputNumber, InputNumberProps } from "@/components/InputNumber";
import { Slider } from "@/components/Slider";
import _ from "lodash";
import { QuestionMarkCircleBold } from "@meitu/candy-icons";

import { isNumber } from "lodash";

type Direction = "vertical" | "horizontal";

export interface SliderInputProps
  extends Pick<
    InputNumberProps,
    "controls" | "suffix" | "disabled" | "formatter"
  > {
  value?: number;

  /** 刻度标记个数 */
  markNum?: number;

  /** 最小值 */
  min?: number;

  /** 最大值 */
  max?: number;

  /** 步长 */
  step?: number;

  className?: string;

  onChange?: (value: number) => void;

  /** 布局方向  默认 vertical */
  direction?: Direction;

  /** 左上角标题 仅 `direction = 'vertical'` 时生效*/
  title?: React.ReactNode;

  /** 左侧label标签 仅 `direction = 'horizontal'` 时生效*/
  label?: React.ReactNode;

  /** 提示文本 */
  tooltip?: React.ReactNode;

  /** 起点刻度 */
  origin?: number;

  /** 在slider上的tooltip */
  tooltipOnSlider?: SliderTooltipProps;

  onPointerEnterSlider?: () => void;

  onPointerLeaveSlider?: () => void;

  /** 输入框是否有控制按钮 */
  showInputControls?: boolean;

  customClassNames?: {
    title?: string;
    input?: string;
    slider?: string;
  };

  onKeydown?: (e: React.KeyboardEvent<HTMLInputElement>) => void; 
}

/** 标题和提示 */
export function Title({
  title,
  tooltip,
  className,
}: Pick<SliderInputProps, "title" | "tooltip"> & { className?: string }) {
  return (
    <section className={className}>
      <span className={classNames(styles.title, "title-content")}>{title}</span>
      {!!tooltip ? (
        <Tooltip title={tooltip}>
          <QuestionMarkCircleBold className={styles.tooltip} />
        </Tooltip>
      ) : null}
    </section>
  );
}

export function SliderInput(props: SliderInputProps) {
  const {
    className,
    markNum,
    label,
    controls,
    suffix,
    direction = "vertical",
    title,
    tooltip,
    min,
    max,
    step = 1,
    origin,
    disabled,
    onChange,
    tooltipOnSlider,
    onPointerEnterSlider,
    onPointerLeaveSlider,
    showInputControls,
    customClassNames,
    onKeydown,
  } = props;

  const onInputChange = (value: AntdInputNumberProps["value"]) => {
    if (isNumber(value)) {
      onChange?.(value);
    }
  };

  const onSliderChange = (value: number) => {
    onChange?.(value);
  };

  /** 输入框 */
  const inputNode = (
    <InputNumber
      className={customClassNames?.input}
      precision={step.toString().split(".")[1]?.length ?? 0}
      value={props.value ?? 0}
      disabled={disabled}
      onChange={onInputChange}
      showControls={showInputControls}
      onKeyDown={onKeydown}
      {..._.omitBy({ controls, suffix, max, min, step }, _.isUndefined)}
    />
  );

  /** 滑杆 */
  const sliderProps = {
    min,
    max,
    step,
    origin,
    markNum,
    tooltip: tooltipOnSlider ?? { open: false },
    onPointerEnterSlider,
    onPointerLeaveSlider,
  };

  return (
    <section className={classNames(styles.sliderInput, className)}>
      {direction === "horizontal" ? (
        <Row>
          {title ? (
            <Col span={24} className={styles.header}>
              {" "}
              <Title
                title={title}
                tooltip={tooltip}
                className={customClassNames?.title}
              />
            </Col>
          ) : null}
          <Col span={24} className={styles.horizontal}>
            {label ? <span className={styles.label}>{label}</span> : null}
            <span className={styles.slider}>
              <Slider
                {...sliderProps}
                markNum={0}
                value={props.value ?? 0}
                disabled={disabled}
                onChange={onSliderChange}
                className={customClassNames?.slider}
              />
            </span>
            <span className={styles.input}>{inputNode}</span>
          </Col>
        </Row>
      ) : (
        <Row>
          <Col span={24} className={styles.header}>
            {" "}
            <Title
              title={title}
              tooltip={tooltip}
              className={customClassNames?.title}
            />
            {inputNode}
          </Col>
          <Col span={24}>
            {label}
            <Slider
              {...sliderProps}
              value={props.value ?? 0}
              disabled={disabled}
              onChange={onSliderChange}
              className={customClassNames?.slider}
            />
          </Col>
        </Row>
      )}
    </section>
  );
}
