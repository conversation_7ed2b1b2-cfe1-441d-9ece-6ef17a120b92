'use client'

import { PlanCardProps } from './types';
import classNames from 'classnames';

export const PlanCard = ({
    type,
    name,
    price,
    originalPrice,
    description,
    features,
    credits,
    maxConcurrentJobs,
    canvases,
    isSelected,
    onClick
}: PlanCardProps) => {
    return (
        <div 
            className={classNames('plan-card', {
                'plan-card-selected': isSelected,
                'plan-card-plus': type === 'plus'
            })}
            onClick={onClick}
        >
 
            
            <div className="plan-price">
                <span className="currency">$</span>
                <span className="amount">{price}</span>
                <span className="period">/Month billed yearly</span>
                {originalPrice && (
                    <div className="original-price">Original price: ${originalPrice}</div>
                )}
            </div>

            <button 
                className={classNames('subscribe-button', {
                    'subscribe-button-plus': type === 'plus'
                })}
            >
                Subscribe {type === 'basic' ? 'Basic' : 'Plus'} Plan
            </button>

            <div className="plan-features">
                <div className="feature">
                    <span>{credits} credits monthly</span>
                </div>
                <div className="feature">
                    <span>Generated images are private</span>
                </div>
                <div className="feature">
                    <span>Maximum concurrent jobs: {maxConcurrentJobs}</span>
                </div>
                <div className="feature">
                    <span>{typeof canvases === 'number' ? `${canvases} canvases` : 'Unlimited canvases'}</span>
                </div>
                {features.map((feature, index) => (
                    <div key={index} className="feature">
                        <span>{feature}</span>
                    </div>
                ))}
            </div>
        </div>
    );
}; 