'use client'

import { useState } from 'react';

interface SubscribeTitleProps {
    title: string;
    description: string;
}

import './styles.scss';
export const SubscribeTitle = (props: SubscribeTitleProps) => {
    const { title, description } = props;
    return (
        <div className='subscribe-modal-header'>
            <h2>{title}</h2>
            <p>{description}</p>
        </div>
    )
}