"use client";
/**
 *  仅在客户端生效
 *  封装订阅弹框组件， 有以下几种情况
 *  1。显示优惠
 *  2. 项目不足转化弹框
 *  3. 并行数量解锁弹框
 *  4. 去水印转化弹框
 *  5. 积分（美豆）不足转化弹框
 *  6. 基础的会员开通弹框
 */
import { Modal, Spin } from "antd";
import { Button } from "@/components/Button";
import { useEffect, useRef, useState } from "react";
// import { Plan, SubscribeModalProps } from './types';
// import { PlanCard } from './PlanCard';
import { CrossBlack, CheckCircleBold } from "@meitu/candy-icons";

import { SubscribeTitle } from "./SubscribeTitle";

import { useSubscribe } from "@/hooks/useSubscribe";
import "./styles.scss";
import { useStore } from "@/contexts/StoreContext";
import { VipButton } from "../Button/1VipButton";
import { usePathname } from "next/navigation";

// import { useMeiDouBalance } from '@/hooks/useMeidou';
import { checkMeidouBalance } from '@/api/meidou';
import { useSubscribeModal } from '@/contexts/SubscribeModalContext';
import { SubscribeModalType, TrackPeriodType, TrackSubscribeModalTypeMap, TrackTabType } from './types';
import { useI18n } from '@/locales/client';
import { VipLevel } from '@/types';
import { trackEvent } from '@/services/tracer';
export const SubscribeModal: React.FC = () => {
  const t = useI18n();
  const [plans, setPlans] = useState<any[]>([
    {
      type: "basic",
      name: "Basic",
      description:
        "Unlock advanced features and create commercial-use content.",
    },
    {
      type: "plus",
      name: "Premium",
      description:
        "Premium for more monthly credits and unlimited project creations.",
    },
  ]);
  const [currentPlan, setCurrentPlan] = useState<any>(null);
  const [tab, setTab] = useState<"year" | "month">("year");
  const tabRef = useRef<"year" | "month">("year");
  const yearTrackRef = useRef(false);
  const monthTrackRef = useRef(false);
  const { userStore } = useStore();
  const vipLevel = userStore.vipLevel;
  const TITLE_MAP = {
    [SubscribeModalType.Discount]: {
      title: t("Unlock more exclusive member benefits"),
      description: t("Become a WheeAI VIP to upgrade your creativity!"),
    },
    [SubscribeModalType.Project]: {
      title: t("Need to create more projects?"),
      description: t(
        "You can create {count} projects with the current plan. Become a member to access more benefits." as any,
        {
          count:
            vipLevel === VipLevel.None
              ? 2
              : vipLevel === VipLevel.Basic
              ? 10
              : "unlimited",
        }
      ),
    },
    [SubscribeModalType.Concurrent]: {
      title: t("Enjoy more concurrent tasks"),
      description: t(
        "You can create {count} projects with the current plan. Become a member to access more benefits." as any,
        {
          count:
            vipLevel === VipLevel.None
              ? 1
              : vipLevel === VipLevel.Basic
              ? 3
              : 5,
        }
      ),
    },
    [SubscribeModalType.Watermark]: {
      title: t("High definition and watermark free"),
      description: t("Subscribe to enjoy watermark-free downloads."),
    },
    [SubscribeModalType.Credits]: {
      title: t("Need more credits?"),
      description: t(
        "Become a WheeAI VIP and fuel your creativity with many more credits"
      ),
    },
    [SubscribeModalType.Basic]: {
      title: t("Need more powerful AI tools?"),
      description: t("Become a WheeAI VIP to upgrade your creativity!"),
    },
  };
  const {
    loading,
    error,
    yearProduct,
    monthProduct,
    createTradeUrl,
    querySubscribeResult,
    initializeSubscription,
    submitLoading,
  } = useSubscribe();
  const {
    isOpen,
    close,
    selectedProductType,
    onSuccessCallback,
    templateId,
    functionId,
  } = useSubscribeModal();
  const pathname = usePathname();

  // When modal opens
  useEffect(() => {
    if (isOpen) {
      initializeSubscription("", functionId);
    } else {
      yearTrackRef.current = false;
      monthTrackRef.current = false;
      setTab("year");
      tabRef.current = "year";
    }
  }, [isOpen]);

  //初始化年曝光
  useEffect(() => {
    if (
      tab &&
      templateId &&
      functionId &&
      yearProduct.length > 0 &&
      monthProduct.length > 0 &&
      selectedProductType &&
      isOpen &&
      !yearTrackRef.current
    ) {
      handleVipTrack("vip_price_exp");
      yearTrackRef.current = true;
    }
  }, [
    tab,
    templateId,
    functionId,
    yearProduct,
    monthProduct,
    selectedProductType && isOpen,
  ]);

  useEffect(() => {
    // 将yearProduct和monthProduct合并到plans中每一项，按照index 例如：plans[0].yearProduct = yearProduct[0]
    plans.forEach((plan, index) => {
      if (tab === "year") {
        plan.project = yearProduct[index];
      } else {
        plan.project = monthProduct[index];
      }
    });
    //  setCurrentPlanList(plans);
    setPlans([...plans]);
    setCurrentPlan(null);
    // 去掉告警
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tab, yearProduct, monthProduct, loading, isOpen]);

  // sub_period	integer
  // 非必须
  // 当商品为订阅商品时有值（0:天 1: 月 2: 季 3: 年 5:周）

  // 获取年付会员的优惠文案

  const yearProductPromotion = yearProduct[0]?.promotionBanner || "";

  const handleChangeTab = (tab: "year" | "month") => {
    setTab(tab);
    tabRef.current = tab;
    if (tab == "month" && !monthTrackRef.current) {
      handleVipTrack("vip_price_exp");
      monthTrackRef.current = true;
    }
  };

  // 处理分-> 元
  const handlePrice = (price: any, type: "year" | "month") => {
    // 如果price是字符串，则先转换为数字
    if (price === undefined) {
      return "";
    }
    // 去除掉非数字符号
    price = price.replace(/[^0-9]/g, "");
    if (typeof price === "string") {
      price = parseInt(price);
    }
    // console.log('price=>>>>>>>>',  price / 100);
    return type === "month" ? price / 100 : price;
  };


  const handleSubscribe = async (plan: any, index: number) => {
    // 设置当前选中的计划
    setCurrentPlan(plan);

    const urlResponse = await createTradeUrl(plan.project);
    if (urlResponse) {
      handleVipTrack("vip_pay_click", index, plan);
      const newWindow = window.open("", "_blank", "width=800,height=600");
      if (newWindow) {
        newWindow.location.href = urlResponse?.url;
      }
      querySubscribeResult(plan.project, () => {
        close();
        onSuccessCallback?.();
        handleVipTrack("vip_paidprocess_success", index, plan);
      });
    }
  };

  const handleVipTrack = (event: string, index?: number, plan?: any) => {
    //曝光是多项 点击是单项
    let productItems = tabRef.current === "year" ? yearProduct : monthProduct;
    if (index !== undefined) {
      productItems =
        tabRef.current === "year"
          ? [yearProduct[index]]
          : [monthProduct[index]];
    }
    const trackParams = {
      touch_type: TrackSubscribeModalTypeMap[selectedProductType],
      open_source: pathname,
      function_id: functionId,
      product_id: productItems.map(item => item.productId).join(','),
      period_type: tabRef.current === 'year' ? TrackPeriodType.Year : TrackPeriodType.Month,
      product_type: productItems[0].productType,
      sku_type: plan?.type ||  'basic,plus' , //曝光时两个都曝光 点击和成功时单项
      tab: tabRef.current === 'year' ? TrackTabType.Year : TrackTabType.Month,

    };
    trackEvent(event, trackParams);
  };

  const YearPriceComponent = (planItem: any) => {
    const { plan, index } = planItem;
    return (
      <>
        <div>
          <span className="symbol">
            {plan?.project?.productPrice?.moneySymbol}
          </span>
          <span className="price">
            {handlePrice(
              plan?.project?.productPrice?.productPriceExplain,
              "year"
            )}
          </span>
          <span className="price-tips">
            {plan?.project?.productPrice?.moneyUnit}/Month billed yearly
          </span>
        </div>
        <div className="subscribe-modal-plan-original-price">
          {monthProduct[index]?.productPrice?.price
            ? `Original price: ${
                plan?.project?.productPrice?.moneySymbol
              }${handlePrice(
                monthProduct[index]?.productPrice?.price,
                "month"
              )}`
            : ""}
        </div>
      </>
    );
  };

  const MonthPriceComponent = (planItem: any) => {
    const { plan, index } = planItem;
    return (
      <>
        <div>
          <span className="symbol">
            {plan?.project?.productPrice?.moneySymbol}
          </span>
          <span className="price">
            {handlePrice(plan?.project?.productPrice?.price, "month")}
          </span>
        </div>
        <div className="subscribe-modal-plan-original-price">
          {plan?.project?.productPrice?.originalPrice !==
          plan?.project?.productPrice?.price
            ? `${plan?.project?.productPrice?.moneySymbol}${handlePrice(
                plan?.project?.productPrice?.originalPrice,
                "month"
              )}`
            : ""}
        </div>
      </>
    );
  };

  const handleClose = () => {
    // 更新下美豆信息
    // useMeiDouBalance();
    checkMeidouBalance();

    close?.();
  };

  return (
    <Modal
      title={null}
      open={isOpen}
      footer={null}
      onCancel={handleClose}
      width={654}
      zIndex={10000}
      className="subscribe-modal"
      closeIcon={
        <CrossBlack
          style={{
            color: "#fff",
          }}
        />
      }
      centered
    >
      <Spin spinning={loading}>
        <div className="subscribe-modal-content">
          <SubscribeTitle
            title={TITLE_MAP[selectedProductType].title}
            description={TITLE_MAP[selectedProductType].description}
          />
          <div className="subscribe-modal-tabs">
            <div
              className={`tab ${tab === "year" ? "tab-selected" : ""}`}
              onClick={() => handleChangeTab("year")}
            >
              {" "}
              {t("Annually")}{" "}
              {yearProductPromotion && `(${yearProductPromotion})`}
            </div>
            <div
              className={`tab ${tab === "month" ? "tab-selected" : ""}`}
              onClick={() => handleChangeTab("month")}
            >
              {t("Monthly")}
            </div>
          </div>

          <div className="subscribe-modal-plans">
            {plans.map((plan, index) => {
              return (
                <div
                  className={`subscribe-modal-plan ${
                    plan?.type === "plus" ? "plus" : ""
                  }`}
                  key={index}
                >
                  <div className="subscribe-modal-plan-name">
                    {plan?.name}
                    {plan?.type === "plus" ? (
                      <span className="subscribe-modal-plan-name-number">
                        {"Most popular"}
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="subscribe-modal-plan-description">
                    {plan?.description}
                  </div>
                  <div className="subscribe-modal-plan-price">
                    {tab === "year" ? (
                      <YearPriceComponent plan={plan} index={index} />
                    ) : (
                      <MonthPriceComponent plan={plan} index={index} />
                    )}
                  </div>
                  <div className="subscribe-modal-button">
                    {plan?.type === "plus" ? (
                      <>
                        {
                          <VipButton
                            type="primary"
                            onClick={() => handleSubscribe(plan, index)}
                          >
                            {t("Subscribe to the Premium plan")}
                          </VipButton>
                        }
                      </>
                    ) : (
                      <>
                        {vipLevel === VipLevel.Basic ? (
                          <div style={{ color: "#fff" }}>
                            {t(
                              "You have already subscribed to the current plan. Keep creating!"
                            )}
                          </div>
                        ) : (
                          <Button
                            type="primary"
                            onClick={() => handleSubscribe(plan, index)}
                          >
                            {t("Subscribe to the Basic plan")}
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                  <div className={"subscribe-modal-plan-item"}>
                    <CheckCircleBold />
                    {/* <span className={"subscribe-modal-plan-item-text"}>{t("{count}credits per month" as any, { count: plan?.type === 'plus' ? 3000 : 1000 })}</span> */}
                    <span className={"subscribe-modal-plan-item-text"}>
                      <span className="subscribe-modal-plan-item-text-number">
                        {plan?.type === "plus" ? "3000" : "1000"}{" "}
                      </span>
                      credits per month
                    </span>
                  </div>
                  <div className={"subscribe-modal-plan-item"}>
                    <CheckCircleBold />
                    <span className={"subscribe-modal-plan-item-text"}>
                      {t("Generated posters are private")}
                    </span>
                  </div>

                  <div className={"subscribe-modal-plan-item"}>
                    <CheckCircleBold />
                    <span className={"subscribe-modal-plan-item-text"}>
                      Generations per batch:{" "}
                      <span className="subscribe-modal-plan-item-text-number">
                        {plan?.type === "plus" ? "5" : "3"}
                      </span>
                    </span>
                  </div>

                  <div className={"subscribe-modal-plan-item"}>
                    <CheckCircleBold />
                    <span className={"subscribe-modal-plan-item-text"}>
                      <span className="subscribe-modal-plan-item-text-number">
                        {plan?.type === "plus" ? "Unlimited" : "10"}{" "}
                      </span>
                      {t("Projects")}
                    </span>
                  </div>

                  <div className={"subscribe-modal-plan-item"}>
                    <CheckCircleBold />
                    <span className={"subscribe-modal-plan-item-text"}>
                      {t("No restrictions on commercial use")}
                    </span>
                  </div>

                  <div className={"subscribe-modal-plan-item"}>
                    <CheckCircleBold />
                    <span className={"subscribe-modal-plan-item-text"}>
                      {t("Access to all member benefits")}
                    </span>
                  </div>

                  <div className={"subscribe-modal-plan-item"}>
                    <CheckCircleBold />
                    <span className={"subscribe-modal-plan-item-text"}>
                      {t("Watermark-free downloads")}
                    </span>
                  </div>

                  {plan?.type === "plus" ? (
                    <div className={"subscribe-modal-plan-item"}>
                      <CheckCircleBold />
                      <span className={"subscribe-modal-plan-item-text"}>
                        {t("Early access to new features")}
                      </span>
                    </div>
                  ) : (
                    <div className={"subscribe-modal-plan-item"}></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </Spin>
    </Modal>
  );
};
