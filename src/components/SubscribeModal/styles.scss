.subscribe-modal {
    :global(.ant-modal-content) {
        padding: 32px !important;
        border-radius: var(--radius-16, 16px);
        border: 1px solid var(--system-stroke-input, #22272E);
        background: var(--system-background-secondary, #1D1E23);
        box-shadow: 0px 4px 50px 0px rgba(33, 33, 33, 0.08), 0px 4px 6px 0px rgba(33, 33, 33, 0.04);

        .ant-modal-close {
            color: rgba(107, 122, 143, 1);
            top: 20px;
            right: 20px;
        }
    }
}

.subscribe-modal-content {
    .subscribe-modal-header {
        text-align: center;
        margin-bottom: 16px;

        h2 {
            color: var(--system-content-primary, #FFF);
            font-size: 24px;
            font-weight: 600;
            line-height: 32px;
            margin-bottom: 8px;
        }

        p {
            color: var(--system-content-secondary, #A3AEBF);
            font-size: 14px;
            line-height: 20px;
        }
    }

    .subscribe-modal-tabs {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 24px;
        background: var(--system-background-input, #16171C);
        border-radius: 4px;
        padding: 8px;


        .tab {
            padding: 8px 16px;
            border-radius: 8px;
            color: var(--system-content-secondary, #A3AEBF);
            cursor: pointer;
            transition: all 0.3s;
            width: 240px;
            text-align: center;
            color: var(--system-content-primary, #FFF);
            text-align: center;
            /* text_16_bold */
             font-family: var(--font-poppins);
            font-size: 16px;
            font-style: normal;
            font-weight: 600;

            &.tab-selected {
                background: var(--system-background-thirdary, #272C33);
                color: var(--system-content-primary, #FFF);
            }
        }
    }

    .subscribe-modal-plans {
        display: flex;
        gap: 16px;

    }
    .subscribe-modal-plan {
        display: flex;
        width: 280px;
        padding: var(--spacing-24, 24px);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-16, 16px);
        border-radius: var(--radius-12, 12px);
        border: 1px solid var(--system-stroke-input-thirdary, #323B48);
        background: #272C33;
        &-name {
            color: var(--system-content-primary, #FFF);
            /* text_20_bold */
            font-family: Inter;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            // width: 232px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            &-number {
                color: var(--system-content-brandPrimary, #53F6B4);
                /* text_12 */
                font-family: Inter;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 125%; /* 15px */
                border: 1px solid var(--system-content-brandPrimary, #53F6B4);
                display: flex;
                height: 24px;
                padding: 0px var(--spacing-8, 8px);
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border-radius: var(--radius-6, 6px);
                border: 1px solid var(--system-content-brandPrimary, #53F6B4);
            }
            // line-height: 130%; 
        }
        &-description {
            color: var(--system-content-secondary, #A3AEBF);
            /* text_12 */
            font-family: Inter;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            height: 40px;
            // line-height: 125%;
        }
        &-price {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
            .symbol {
                font-size: 24px;
                color: #FFF;
            }
            .price {
                font-size: 40px;
                font-weight: 600;
                color: #FFF;
            }
            .price-tips {
                color: var(--system-content-secondary, #A3AEBF);
                /* text_11 */
                font-family: Inter;
                margin-left: 2px;
                font-size: 11px;
                // 缩放 50%
                transform: scale(0.5);
                font-style: normal;
                font-weight: 400;
                // line-height: 125%; 
            }
        }
        &-original-price {
            color: var(--system-content-secondary, #A3AEBF);
            height: 12px;
            /* text_11 */
            font-family: Inter;
            font-size: 11px;
            text-decoration: line-through;
            // 缩放 50%
            // transform: scale(0.5); 
            font-style: normal;
            font-weight: 400;
            // line-height: 125%; /* 13.75px */
            text-decoration-line: strikethrough;
            text-align: left;
        }
        &-item {
            color: var(--system-content-secondary, #A3AEBF);
            /* text_12 */
            font-family: Inter;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            &-text {
                padding-left: 4px;
            }
            &-text-number {
                color: var(--system-content-brandPrimary, #53F6B4);
            }
        }
    } 
    .plus {
        background: linear-gradient(184deg, #060606 15.69%, #12202D 31.73%, #18372B 50.3%, #270525 99.64%), var(--system-background-thirdary, #272C33);
        position: relative;
        overflow: hidden;
        z-index: 1;
        &::before {
            content: "";
            position: absolute;
            inset: 0;
            padding: 2px; // 边框宽度
            border-radius: 12px;
            background: linear-gradient(88deg, #5DF5B9 0%, #D8EAFF 47.22%, #FBA6FF 100%);
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box, 
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            z-index: -1;
        }

    }

    // plus 实现一个渐变 边框  border-image: 1px solid linear-gradient(88deg, #5DF5B9 0%, #D8EAFF 47.22%, #FBA6FF 100%);  borderImage 不生效， 修改一下
}
.subscribe-modal-button {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 14px;
    button {
        width: 100%;
    }
}

.plan-card {
    flex: 1;
    padding: 24px;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-button, #323B48);
    background: var(--system-background-thirdary, #272C33);
    cursor: pointer;
    transition: all 0.3s;

    &.plan-card-selected {
        border-color: var(--system-content-brandPrimary, #53F6B4);
    }

    &.plan-card-plus {
        background: linear-gradient(180deg, rgba(83, 246, 180, 0.05) 0%, rgba(83, 246, 180, 0) 100%);
    }

    .plan-header {
        margin-bottom: 16px;

        h3 {
            color: var(--system-content-primary, #FFF);
            font-size: 18px;
            font-weight: 600;
            line-height: 24px;
            margin-bottom: 8px;
        }

        p {
            color: var(--system-content-secondary, #A3AEBF);
            font-size: 14px;
            line-height: 20px;
        }
    }

    .plan-price {
        margin-bottom: 24px;

        .currency {
            color: var(--system-content-primary, #FFF);
            font-size: 24px;
            font-weight: 600;
            vertical-align: top;
        }

        .amount {
            color: var(--system-content-primary, #FFF);
            font-size: 48px;
            font-weight: 600;
            line-height: 48px;
            margin: 0 4px;
        }

        .period {
            color: var(--system-content-secondary, #A3AEBF);
            font-size: 14px;
        }

        .original-price {
            color: var(--system-content-thirdary, #6A7B94);
            font-size: 12px;
            text-decoration: line-through;
            margin-top: 4px;
        }
    }

    .subscribe-button {
        width: 100%;
        height: 40px;
        border-radius: 10px;
        background: var(--system-content-brandPrimary, #53F6B4);
        color: var(--system-content-onPrimary, #181818);
        font-size: 16px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        margin-bottom: 24px;
        transition: all 0.3s;

        &:hover {
            opacity: 0.9;
        }

        &.subscribe-button-plus {
            background: linear-gradient(92deg, #53F6B4 0%, #48E1FF 100%);
        }
    }

    .plan-features {
        .feature {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            color: var(--system-content-secondary, #A3AEBF);
            font-size: 14px;
            line-height: 20px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
} 