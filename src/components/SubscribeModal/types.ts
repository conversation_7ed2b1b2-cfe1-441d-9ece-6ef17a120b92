export interface Plan {
    type: 'basic' | 'plus';
    name: string;
    price?: number;
    originalPrice?: number;
    description: string;
    features: string[];
    credits: number;
    maxConcurrentJobs: number;
    canvases: number | 'unlimited';
}

export interface SubscribeModalProps {
    onClose?: () => void;
    onSubscribe?: (plan: Plan) => void;
    show: boolean;
    //  1。显示优惠
    //  2. 项目不足转化弹框
    //  3. 并行数量解锁弹框
    //  4. 去水印转化弹框
    //  5. 积分（美豆）不足转化弹框
    //  6. 基础的会员开通弹框
    type: 'discount' | 'project' | 'concurrent' | 'watermark' | 'credits' | 'basic';
    onSubscribeSuccess?: () => void;
}

export interface PlanCardProps extends Plan {
    isSelected?: boolean;
    onClick?: () => void;
} 
// 订阅弹框类型
export enum SubscribeModalType {
    Basic = 'basic',
    Project = 'project',
    Discount = 'discount',
    Concurrent = 'concurrent',
    Watermark = 'watermark',
    Credits = 'credits',
}

export const TrackSubscribeModalTypeMap = {
   [SubscribeModalType.Discount]: 1,
   [SubscribeModalType.Project]: 2,
   [SubscribeModalType.Basic]: 3,
   [SubscribeModalType.Concurrent]: 4,
   [SubscribeModalType.Watermark]: 5,
   [SubscribeModalType.Credits]: 6,
}

export enum  TrackPeriodType {
   Month = 1,
   Year = 3,
}
export enum  TrackTabType {
    Month = 1,
    Year = 2,
 }