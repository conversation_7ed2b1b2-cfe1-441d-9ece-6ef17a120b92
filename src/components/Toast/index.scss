@mixin toast-icon-divider {
  padding-right: 6px;
  position: relative;
  &::after {
    content: '';
    display: block;
    width: 1px;
    height: 24px;
    background: #fff;
    opacity: 0.1;
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
  }
}

.toast-container {
  position: fixed;
  top: 116px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.toast-item {
  display: flex;
  align-items: center;
  padding: 7px 16px;
  box-sizing: border-box;
  border-radius: var(--radius-12, 12px);
  border: 1px solid rgba(255, 255, 255, 0.10);
  /* level_3 */
  background: var(--system-background-fifth, #323B48);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15), 0px 10px 40px 0px rgba(0, 0, 0, 0.30);
  cursor: pointer;
  min-width: 415px;
  min-height: 48px;
  position: relative;
  overflow: hidden;
  .toast-content {
    padding-left: 8px;
    box-sizing: border-box;
   .toast-title {
    color: #FFF;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 100%; /* 14px */
    margin-right: 24px;
   }
   .toast-description {
    color: var(--system-content-thirdary, #6B7A8F);
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
   }
  }
  // &::before {
  //   content: '';
  //   position: absolute;
  //   bottom: 0;
  //   left: 0;
  //   height: 3px;
  //   background: rgba(255, 255, 255, 0.3);
  //   animation: toastProgress 3s linear;
  // }

  &.toast-success {
    background: linear-gradient(90deg, #27361D 0%, #1D1E23 40.6%), var(--system-background-secondary, #1D1E23);
    .toast-icon {
      @include toast-icon-divider;
    }
  }

  &.toast-error {
    background: linear-gradient(90deg, rgba(255, 61, 119, 0.20) 0%, rgba(255, 61, 119, 0.00) 40.6%), var(--system-background-fourth, #2B323D);
    .toast-icon {
      @include toast-icon-divider;
    }
  }

  &.toast-warning {
    background: linear-gradient(90deg, rgba(255, 210, 30, 0.15) 0%, rgba(255, 210, 30, 0.00) 40.6%), var(--system-background-thirdary, #272C33);
    .toast-icon {
      @include toast-icon-divider;
    }
  }

  &.toast-info {
    .toast-icon {
      color: #fff;
      margin-right: 5px;
      svg {
        width: 18px;
        height: 18px;
      }
    }
  }

  &.toast-loading {
    .toast-icon {
      color: #fff;
      margin-right: 5px;
      svg {
        width: 18px;
        height: 18px;
        animation: toastRotate 1s linear infinite;
      }
    }
  }
}

.toast-content {
  flex: 1;
  margin-right: 8px;
}

.toast-close {
  cursor: pointer;
  color: rgba(107, 122, 143, 1);
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  margin-left: 6px;
  svg {
    width: 16px;
    height: 16px;
  }
  &:hover {
    opacity: 0.8;
  }
}

@keyframes toastProgress {
  from {
    width: 100%;
  }
  to {
    width: 0;
  }
}

// 退出动画
.toast-item-exit {
  animation: toastSlideOut 0.3s ease-in forwards;
}

@keyframes toastSlideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

//旋转动画
@keyframes toastRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}