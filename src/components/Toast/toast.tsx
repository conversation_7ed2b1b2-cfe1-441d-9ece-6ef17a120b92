'use client'
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { createRoot } from 'react-dom/client';
import { motion, AnimatePresence } from 'framer-motion';
import { SuccessIcon, ErrorIcon, WarningIcon } from '@/assets/icons';
import { CrossBlack, ExclamationMarkCircleBold, LoadingCircleBold } from "@meitu/candy-icons";
import { v4 as uuid } from 'uuid';

import './index.scss';

export interface ToastConfig {
  type: 'success' | 'error' | 'info' | 'warning' | 'loading';
  title?: React.ReactNode;
  description?: React.ReactNode;
  duration?: number;
  key?: string;
  onClose?: () => void;
}

export interface ToastInstance {
  success: (title: React.ReactNode, description?: React.ReactNode, duration?: number) => Promise<void>;
  error: (title: React.ReactNode, description?: React.ReactNode, duration?: number) => Promise<void>;
  info: (title: React.ReactNode, description?: React.ReactNode, duration?: number) => Promise<void>;
  warning: (title: React.ReactNode, description?: React.ReactNode, duration?: number) => Promise<void>;
  loading: (title: React.ReactNode, description?: React.ReactNode, duration?: number) => Promise<void>;
  show: (config: ToastConfig) => Promise<void>;
  destroy: (key?: string) => Promise<void>;
}

interface ToastMethods {
  show: (config: ToastConfig) => string;
  remove: (id: string) => void;
  destroy: (key?: string) => void;
}

// 全局状态管理
let root: ReturnType<typeof createRoot> | null = null;
let unmountTimer: NodeJS.Timeout | null = null;
let hasInitialized = false;
let closeCallbacks: Record<string, () => void> = {};

// 安全地卸载 root
const safeUnmount = () => {
  if (unmountTimer) {
    clearTimeout(unmountTimer);
    unmountTimer = null;
  }

  requestAnimationFrame(() => {
    setTimeout(() => {
      if (root) {
        root.unmount();
        root = null;
        const container = document.getElementById('global-toast-container');
        if (container && container.parentNode) {
          container.parentNode.removeChild(container);
        }
        hasInitialized = false;
        window.__TOAST_METHODS__ = undefined;
      }
    }, 0);
  });
};

// 全局 Toast 组件
const ToastContainer = () => {
  const [toasts, setToasts] = useState<(ToastConfig & { id: string })[]>([]);
  const mountedRef = useRef(false);
  
  // 组件挂载标记
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 监听 toasts 变化
  useEffect(() => {
    if (!hasInitialized) {
      hasInitialized = true;
      return;
    }

    if (toasts.length === 0 && mountedRef.current) {
      if (unmountTimer) {
        clearTimeout(unmountTimer);
      }
      
      unmountTimer = setTimeout(() => {
        if (mountedRef.current) {
          safeUnmount();
        }
      }, 300); // 等待动画完成
    }
  }, [toasts]);

  // 提供全局方法
  const show = useCallback((config: ToastConfig) => {
    const id = uuid();
    
    // 保存关闭回调
    if (config.onClose) {
      closeCallbacks[id] = config.onClose;
    }
    
    // 如果提供了 key，尝试更新已存在的 toast
    if (config.key) {
      setToasts(prev => {
        const existingToastIndex = prev.findIndex(t => t.key === config.key);
        if (existingToastIndex !== -1) {
          // 更新已存在的 toast
          const updatedToasts = [...prev];
          updatedToasts[existingToastIndex] = { ...config, id: prev[existingToastIndex].id };
          return updatedToasts;
        }
        // 如果没有找到，创建新的 toast
        return [...prev, { ...config, id }];
      });
    } else {
      // 如果没有提供 key，创建新的 toast
      const newToast = { ...config, id };
      setToasts(prev => [...prev, newToast]);
    }

    if (config.duration !== 0) {
      setTimeout(() => {
        if (mountedRef.current) {
          // 使用 id 而不是 key 来移除特定的 toast
          setToasts(prev => prev.filter(t => t.id !== id));
          // 执行关闭回调
          if (closeCallbacks[id]) {
            closeCallbacks[id]();
            delete closeCallbacks[id];
          }
        }
      }, config.duration || 3000);
    }
    
    return id;
  }, []);

  const remove = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
    // 执行关闭回调
    if (closeCallbacks[id]) {
      closeCallbacks[id]();
      delete closeCallbacks[id];
    }
  }, []);

  const destroy = useCallback((key?: string) => {
    if (key) {
      setToasts(prev => {
        console.log("销毁toast=====", prev);
        // 找到要移除的toast，执行它们的回调
        const toRemove = prev.filter(t => t.key === key);
        toRemove.forEach(toast => {
          if (closeCallbacks[toast.id]) {
            closeCallbacks[toast.id]();
            delete closeCallbacks[toast.id];
          }
        });
        return prev.filter(t => t.key !== key);
      });
    } else {
      // 执行所有toast的回调
      toasts.forEach(toast => {
        if (closeCallbacks[toast.id]) {
          closeCallbacks[toast.id]();
          delete closeCallbacks[toast.id];
        }
      });
      setToasts([]);
    }
  }, [toasts]);

  // 将方法暴露到全局
  useEffect(() => {
    window.__TOAST_METHODS__ = {
      show,
      remove,
      destroy
    };
  }, [show, remove, destroy]);

  return (
    <div className="toast-container">
      <AnimatePresence>
        {toasts.map(toast => (
          <motion.div
            key={toast.id}
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.3 }}
            className={`toast-item toast-${toast.type}`}
          >
            <div className="toast-icon">
              {toast.type === 'success' && <SuccessIcon />}
              {toast.type === 'error' && <ErrorIcon />}
              {toast.type === 'warning' && <WarningIcon />}
              {toast.type === 'info' && <ExclamationMarkCircleBold />}
              {toast.type === 'loading' && <LoadingCircleBold />}
            </div>
            <div className="toast-content">
              <div className="toast-title">{toast.title}</div>
              <div className="toast-description">{toast.description}</div>
            </div>
           { toast.type !== 'loading' && toast.type !== 'info' && <div className="toast-close" onClick={() => remove(toast.id)}><CrossBlack /></div>}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

// 获取或创建容器
const getContainer = () => {
  if (typeof document === 'undefined') return null;
  
  let container = document.getElementById('global-toast-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'global-toast-container';
    document.body.appendChild(container);
  }
  return container;
};

// 确保 Toast 容器已创建并返回方法
const ensureToastMethods = async (): Promise<ToastMethods | null> => {
  if (window.__TOAST_METHODS__) {
    return window.__TOAST_METHODS__;
  }

  const container = getContainer();
  if (!container) return null;

  if (!root) {
    root = createRoot(container);
    root.render(<ToastContainer />);
  }

  return new Promise<ToastMethods>((resolve) => {
    const checkMethods = () => {
      if (window.__TOAST_METHODS__) {
        resolve(window.__TOAST_METHODS__);
      } else {
        setTimeout(checkMethods, 16);
      }
    };
    checkMethods();
  });
};

// 创建 Toast 实例
export const createToastInstance = (): ToastInstance => {
  const instance = {
    success: async (title: React.ReactNode, description?: React.ReactNode, duration?: number) => {
      const methods = await ensureToastMethods();
      return new Promise<void>(resolve => {
        if (methods) {
          const id = methods.show({ type: 'success', title, description, duration, onClose: resolve });
        } else {
          resolve();
        }
      });
    },
    error: async (title: React.ReactNode, description?: React.ReactNode, duration?: number) => {
      const methods = await ensureToastMethods();
      return new Promise<void>(resolve => {
        if (methods) {
          const id = methods.show({ type: 'error', title, description, duration, onClose: resolve });
        } else {
          resolve();
        }
      });
    },
    info: async (title: React.ReactNode, description?: React.ReactNode, duration?: number) => {
      const methods = await ensureToastMethods();
      return new Promise<void>(resolve => {
        if (methods) {
          const id = methods.show({ type: 'info', title, description, duration, onClose: resolve });
        } else {
          resolve();
        }
      });
    },
    warning: async (title: React.ReactNode, description?: React.ReactNode, duration?: number) => {
      const methods = await ensureToastMethods();
      return new Promise<void>(resolve => {
        if (methods) {
          const id = methods.show({ type: 'warning', title, description, duration, onClose: resolve });
        } else {
          resolve();
        }
      });
    },
    loading: async (title: React.ReactNode, description?: React.ReactNode, duration?: number) => {
      const methods = await ensureToastMethods();
      return new Promise<void>(resolve => {
        if (methods) {
          const id = methods.show({ type: 'loading', title, description, duration, onClose: resolve });
        } else {
          resolve();
        }
      });
    },
    show: async (config: ToastConfig) => {
      const methods = await ensureToastMethods();
      return new Promise<void>(resolve => {
        if (methods) {
          // 如果没有提供onClose，使用resolve作为onClose
          const configWithCallback = config.onClose 
            ? config 
            : { ...config, onClose: resolve };
          const id = methods.show(configWithCallback);
        } else {
          resolve();
        }
      });
    },
    destroy: async (key?: string) => {
      const methods = await ensureToastMethods();
      return new Promise<void>(resolve => {
        if (methods) {
          methods.destroy(key);
        }
        // 销毁操作完成后立即resolve
        resolve();
      });
    },
  };

  // 添加函数调用方式
  const toastFunction = async (config: ToastConfig) => {
    return instance.show(config);
  };

  return Object.assign(toastFunction, instance);
};

// 声明全局方法类型
declare global {
  interface Window {
    __TOAST_METHODS__?: ToastMethods;
  }
}




