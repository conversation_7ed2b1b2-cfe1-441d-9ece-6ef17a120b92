.transform-image-container {
  position: relative;
  overflow: hidden; // 确保光束动画不会溢出容器
  display: inline-block; // 或者 block，根据布局需求
  box-sizing: border-box;
  min-width: 100%; // 示例最小宽度
  min-height: 100%; // 示例最小高度
}

.shine-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1; // 确保在图片之上
}

.shine-placeholder::before {
  content: '';
  position: absolute;
  top: -450%;
  left: -50%;
  width: 200%;
  height: 1000%;
  background: linear-gradient(
    90deg,
    hsla(0, 0%, 100%, 0) 0%,
    hsla(0, 0%, 100%, .12) 44.6%,
    hsla(0, 0%, 100%, .12) 56.31%,
    hsla(0, 0%, 100%, 0) 99.37%
  );
  background-repeat: no-repeat;
  background-size: 50% 100%;
  transform: rotate(22.5deg);
  transform-origin: center;
  animation: light 2s linear infinite;
}

@keyframes light {
  0% {
    background-position: -200% 0;
  }
  70% {
    background-position: right -100% top 0;
  }
  100% {
    background-position: right -100% top 0;
  }
}

.transform-image {
  display: block;
  opacity: 0; // 初始时图片透明
  filter: blur(5px); // 初始模糊效果
  transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out, transform 0.3s ease;
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: relative;
  z-index: 0;

  &:hover {
    transform: scale(1.05);
  }
}

.transform-image.is-loaded {
  opacity: 1; // 图片加载完成后完全不透明
  filter: blur(0); // 图片加载完成后清晰
}
