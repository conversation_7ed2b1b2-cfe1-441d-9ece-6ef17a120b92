/* eslint-disable @next/next/no-img-element */
"use client";

import { useState, useRef, useEffect } from "react";
import cn from "classnames";
import "./index.scss";

interface TransformImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  // 如果有特定的宽高比例需求，可以在这里添加，例如 aspectRatio?: number;
}

const TransformImage = ({
  onLoad,
  className,
  src,
  alt,
  ...props
}: TransformImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showShine, setShowShine] = useState(true);
  const [isClient, setIsClient] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // 当 src 改变时，重置加载状态并显示光束占位
    setIsLoaded(false);
    setShowShine(true);

    if (!src) {
      // 如果没有 src，则不显示光束，图片也非加载状态
      setShowShine(false);
      setIsLoaded(false);
      return;
    }

    // 创建一个新的 Image 对象来检查图片是否已缓存
    // 这样做比直接检查 imgRef.current?.complete 更可靠，尤其是在 src 刚改变时
    const imageTester = new Image();
    imageTester.src = src;

    if (isClient && imageTester.complete) {
      // 如果图片已在缓存中并加载完成
      setIsLoaded(true);
      setShowShine(false); // 立即隐藏光束效果
    }
    // 如果图片未在缓存中或非客户端检查，则 isLoaded 保持 false, showShine 保持 true
    // 实际 <img> 元素的 onLoad 事件会处理后续的加载完成逻辑
  }, [src, isClient]); // 依赖 src 和 isClient，当它们变化时此 effect 会重新运行

  const handleLoad = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    onLoad?.(e); // 调用外部传入的 onLoad 回调
    setIsLoaded(true);
    setShowShine(false); // 立即隐藏光束效果
  };

  return (
    <div
      className={cn("transform-image-container", className)} 
    >
      {showShine && <div className="shine-placeholder"></div>}
      <img
        {...props}
        ref={imgRef}
        src={src}
        alt={alt || ""}
        onLoad={handleLoad}
        className={cn("transform-image", {
          "is-loaded": isLoaded,
        })}
      />
    </div>
  );
};

export default TransformImage;
