// 功能列表组件

import React from "react";

// 导入多语言
import { useLanguage } from "@/contexts/LanguageContext";

import {
  UltraHdBold,
  NoCutoutBold,
  EliminateBold,
  TextSquareBold,
  LosslessAmplificationBold,
} from "@meitu/candy-icons";

import "./index.scss";

const Functions: React.FC = () => {
  const { t } = useLanguage();
  const functions = [
    {
      icon: <UltraHdBold />,
      title: t("Image Enhancer"),
      background: `linear-gradient(93deg, #19113A 2.45%, #332182 97.23%), #1D1E23;`,
      color: "#FFFFFF",
    },
    {
      icon: <NoCutoutBold />,
      title: t("No Cutout"),
      background: `linear-gradient(95deg, #2E1815 3.14%, #482723 99.92%), #1D1E23;`,
      color: "#FFFFFF",
    },
    {
      icon: <EliminateBold />,
      title: t("Eliminate"),
      background: `linear-gradient(94deg, #3A1A39 0.57%, #5E1D5B 100.09%), #1D1E23;`,
      color: "#FFFFFF",
    },
    {
      icon: <TextSquareBold />,
      title: t("Text"),
      background: `linear-gradient(94deg, #3A1A39 0.57%, #5E1D5B 100.09%), #1D1E23;`,
      color: "#FFFFFF",
    },
    {
      icon: <LosslessAmplificationBold />,
      title: t("Lossless Amplification"),
      background: `linear-gradient(94deg, #3A1A39 0.57%, #5E1D5B 100.09%), #1D1E23;`,
      color: "#FFFFFF",
    },
  ];
  return (
    <div className="workspace-functions">
      <h2>{t("Features")}</h2>
      <div className="workspace-functions-list">
        {functions.map((item, index) => (
          <div
            className="workspace-functions-item"
            key={index}
            style={{ background: item.background }}
          >
            <div className="workspace-functions-item-title">{item.title}</div>
            <div className="workspace-functions-item-icon">{item.icon}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Functions;
