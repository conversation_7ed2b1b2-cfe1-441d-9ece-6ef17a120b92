import { PosterRatio } from "@/api/types/poster";
import {
  <PERSON><PERSON>11,
  <PERSON>io12,
  Ratio169,
  Rat<PERSON>21,
  Rat<PERSON>23,
  Ratio32,
  Ratio34,
  Ratio43,
  <PERSON>io916,
  <PERSON>io<PERSON><PERSON>,
} from "@meitu/candy-icons";
import styles from "./index.module.scss";

// 宽高 limit
export const maxWidth = 1280;
export const maxHeight = 1280;
export const minWidth = 20;
export const minHeight = 20;

/**
 * 尺寸类型
 */
export enum DimensionType {
  /**
   * 自由尺寸
   *
   * 用户可以自定义宽高
   */
  FREE = "free",

  /**
   * 固定比例
   *
   * 当用户输入某一个尺寸（宽或高）时，会按照比例同时修改另一个尺寸
   */
  FIXED_ASPECT = "fixed_aspect",
}
export type Dimension = {
  id: number;
  display?: string;
  label: React.ReactNode;
  value: PosterRatio;
  size?: [number, number] | PosterRatio.ORIGINAL;
} & (
  | {
      type: DimensionType.FIXED_ASPECT;
      size: [number, number] | PosterRatio.ORIGINAL;
    }
  | {
      type: DimensionType.FREE;
    }
);

let dimensionId = 0;
const getId = () => dimensionId++;

export const fixedAspectDimensions: Array<Dimension> = [
  // {
  //   id: getId(),
  //   type: DimensionType.FIXED_ASPECT,
  //   size: PosterRatio.ORIGINAL,
  //   display: '原比例',
  //   icon: <Picture />
  // },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [768, 768],
    label: (
      <div className={styles.selection}>
        <Ratio11 />
        {PosterRatio.RATIO_1_1}
      </div>
    ),
    value: PosterRatio.RATIO_1_1,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1152, 768],
    label: (
      <div className={styles.selection}>
        <Ratio32 />
        {PosterRatio.RATIO_3_2}
      </div>
    ),
    value: PosterRatio.RATIO_3_2,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [768, 1152],
    label: (
      <div className={styles.selection}>
        <Ratio23 />
        {PosterRatio.RATIO_2_3}
      </div>
    ),
    value: PosterRatio.RATIO_2_3,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [512, 1024],
    label: (
      <div className={styles.selection}>
        <Ratio12 />
        {PosterRatio.RATIO_1_2}
      </div>
    ),
    value: PosterRatio.RATIO_1_2,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1024, 512],
    label: (
      <div className={styles.selection}>
        <Ratio21 />
        {PosterRatio.RATIO_2_1}
      </div>
    ),
    value: PosterRatio.RATIO_2_1,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1024, 768],
    label: (
      <div className={styles.selection}>
        <Ratio43 />
        {PosterRatio.RATIO_4_3}
      </div>
    ),
    value: PosterRatio.RATIO_4_3,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [768, 1024],
    label: (
      <div className={styles.selection}>
        <Ratio34 />
        {PosterRatio.RATIO_3_4}
      </div>
    ),
    value: PosterRatio.RATIO_3_4,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1024, 576],
    label: (
      <div className={styles.selection}>
        <Ratio169 />
        {PosterRatio.RATIO_16_9}
      </div>
    ),
    value: PosterRatio.RATIO_16_9,
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [576, 1024],
    label: (
      <div className={styles.selection}>
        <Ratio916 />
        {PosterRatio.RATIO_9_16}
      </div>
    ),
    value: PosterRatio.RATIO_9_16,
  },
];

export const freeDimension: Dimension = {
  id: getId(),
  type: DimensionType.FREE,
  label: (
    <div className={styles.selection}>
      <RatioFree />
      Lock
    </div>
  ),
  value: PosterRatio.FREE,
};


export const dimensionList: Array<Dimension> = [
  ...fixedAspectDimensions,
  freeDimension,
];

export function dimensionToRequestImageRatio(dimension: Dimension) {
  if (dimension.type === DimensionType.FREE) {
    return DimensionType.FREE;
  }

  if (dimension.type === DimensionType.FIXED_ASPECT) {
    if (dimension.size === PosterRatio.ORIGINAL) {
      return PosterRatio.ORIGINAL;
    }

    return dimension.size.join(":");
  }
}
