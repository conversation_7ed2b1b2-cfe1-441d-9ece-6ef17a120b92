"use client";
import React, { useContext } from 'react'
import useS<PERSON> from 'swr' //用于数据请求的 React Hooks 库,使用 SWR，组件将会不断地、自动获得最新数据流。UI 也会一直保持快速响应。
import signin from '@/services/auth/signin'
import signout from '@/services/auth/signout'
// import type { User } from '@/types'

type AuthContextType = {
  authUser?: any
  isLoading: boolean
  signin: (username: string, password: string) => Promise<void>
  signout: () => Promise<void>
  mutate: (
    data?: any | Promise<any>,
    shouldRevalidate?: boolean,
  ) => Promise<any | undefined>
}

type AuthContextProviderProps = {
  authUser?: any
}

const AuthContext = React.createContext<AuthContextType>({
  authUser: undefined,
  isLoading: false,
  signin: async () => Promise.resolve(),
  signout: async () => Promise.resolve(),
  mutate: async () => Promise.resolve(undefined),
})

export const useAuthContext = (): AuthContextType =>
  useContext<AuthContextType>(AuthContext)

/**
 * @param params
 */
export const AuthContextProvider = ({
  authUser,
  children,
}: React.PropsWithChildren<AuthContextProviderProps>) => {
  const { data, error, mutate } = useSWR<any>(
    `${process.env.NEXT_PUBLIC_API_BASE_PATH}/users/me`,
  )
  const isLoading = !data && !error

  // 登录
  const signinInternal = async (username: string, password: string) => {
    await signin({ username, password })
    await mutate()
  }

  // 登出
  const signoutInternal = async () => {
    await signout()
    await mutate()
  }

  console.log('error', error)
  console.log('data', data)

  return (
    <AuthContext.Provider
      value={{
        authUser: data ?? authUser,
        isLoading,
        signin: signinInternal,
        signout: signoutInternal,
        mutate,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
