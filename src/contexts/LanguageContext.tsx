'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { setCookie, getCookie } from 'cookies-next';

type Translations = Record<string, string>;
type LanguageContextType = {
  locale: string;
  t: (key: string) => string;
  changeLanguage: (locale: string) => void;
};

const LanguageContext = createContext<LanguageContextType | null>(null);

export function LanguageProvider({ 
  children, 
  initialLocale, 
  initialMessages 
}: { 
  children: ReactNode; 
  initialLocale: string;
  initialMessages: Translations;
}) {
  const [locale, setLocale] = useState(initialLocale);
  const [messages, setMessages] = useState<Translations>(initialMessages);

  useEffect(() => {
    // 客户端加载时，从 Cookie 获取语言
    const savedLocale = getCookie('NEXT_LOCALE') as string;
    if (savedLocale && savedLocale !== locale) {
      changeLanguage(savedLocale);
    }
    
  }, [locale]);

  const changeLanguage = async (newLocale: string) => {
    try {
      // 加载新语言的翻译
      const newMessages = await import(`../../public/locales/${newLocale}.json`);
      setMessages(newMessages.default);
      setLocale(newLocale);
      
      // 保存到 Cookie
      setCookie('NEXT_LOCALE', newLocale, { maxAge: 60 * 60 * 24 * 365 });
    } catch (error) {
      console.error(`Failed to load messages for locale: ${newLocale}`, error);
    }
  };

  const t = (key: string) => {
    return messages[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ locale, t, changeLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
} 