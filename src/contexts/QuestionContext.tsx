// 问卷上下文

'use client'
import React, { createContext, useState, useContext, ReactNode } from 'react';
import { useStore } from './StoreContext';

// import {  }
// 
// 定义 Context 的类型
interface QuestionContextType {
  open: (options?: { 
    onSuccess?: () => void 
  }) => void;
  close: () => void;
  isOpen: boolean;
  loading?: boolean;
  error?: Error | null;
  onSuccessCallback: (() => void) | null;
}

// 创建 Context
const QuestionContext = createContext<QuestionContextType | undefined>(undefined);

const HasQuestionnaireKey = 'hasQuestionnaire';

// Context Provider
export const QuestionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [onSuccessCallback, setOnSuccessCallback] = useState<(() => void) | null>(null);
  
  const { userStore } = useStore();
  // 使用已有的 useSubscribe hook
//   const { loading, error } = useSubscribe();

  // 打开订阅弹框的方法，现在接受 onSuccess 回调
  const open = (options?: { onSuccess?: () => void }) => {
  
    
    // 保存成功回调
    if (options?.onSuccess) {
      setOnSuccessCallback(() => options.onSuccess);
    } else {
      setOnSuccessCallback(null);
    }
    
    setIsOpen(true);
  };

  // 关闭订阅弹框的方法
  const close = () => {
    setIsOpen(false);
    if(userStore?.currentUser?.id) {
      localStorage.setItem(HasQuestionnaireKey + userStore?.currentUser?.id, 'true');
    } else {
      localStorage.setItem(HasQuestionnaireKey, 'true');
    }

  };

  // 提供 Context 值
  const value = {
    open,
    close,
    isOpen,
    // loading,
    // error,
    onSuccessCallback, // 添加回调到 context 值
  };

  return (
    <QuestionContext.Provider value={value}>
      {children}
    </QuestionContext.Provider>
  );
};

// 自定义 Hook 以方便使用 Context
export const useQuestionModal = () => {
  const context = useContext(QuestionContext);
  if (context === undefined) {
    throw new Error('useQuestionModal must be used within a QuestionContext');
  }
  return context;
}; 