"use client";

import { createContext, useContext, ReactNode, use } from "react";
import { RootStore, rootStore } from "@/stores/RootStore";
import { enableStaticRendering } from "mobx-react-lite";
import { useI18n } from "@/locales/client";

// 在服务端渲染时禁用 MobX 响应式
enableStaticRendering(typeof window === "undefined");

const StoreContext = createContext<RootStore | undefined>(undefined);

export function StoreProvider({ children }: { children: ReactNode }) {
  const t = useI18n();
  rootStore.t = t;

  return (
    <StoreContext.Provider value={rootStore}>{children}</StoreContext.Provider>
  );
}

export function useStore() {
  const context = useContext(StoreContext);
  if (context === undefined) {
    throw new Error("useStore must be used within a StoreProvider");
  }
  return context;
}
