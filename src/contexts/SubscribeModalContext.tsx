"use client";
import React, { createContext, useState, useContext, ReactNode } from "react";

// 定义 Context 的类型
import { SubscribeModalType } from "@/components/SubscribeModal/types";
import { FunctionCode } from "@/api/types/meidou";
import { MtccFuncCode } from "@/types";
interface SubscribeModalContextType {
  open: (options?: {
    productType?: SubscribeModalType;
    onSuccess?: () => void;
    templateId?: string;
    functionCode?: FunctionCode;
    functionId?: MtccFuncCode;
  }) => void;
  close: () => void;
  isOpen: boolean;
  loading?: boolean;
  error?: Error | null;
  selectedProductType: SubscribeModalType;
  onSuccessCallback: (() => void) | null;
  templateId: string;
  functionCode: FunctionCode | string;
  functionId: MtccFuncCode | string;
}

// 创建 Context
const SubscribeModalContext = createContext<
  SubscribeModalContextType | undefined
>(undefined);

// Context Provider
export const SubscribeModalProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProductType, setSelectedProductType] =
    useState<SubscribeModalType>(SubscribeModalType.Basic);
  const [onSuccessCallback, setOnSuccessCallback] = useState<
    (() => void) | null
  >(null);
  const [templateId, setTemplateId] = useState<string>("undefined");
  const [functionCode, setFunctionCode] = useState<FunctionCode | string>(
    "undefined"
  );
  const [functionId, setFunctionId] = useState<MtccFuncCode | string>(
    "undefined"
  );

  // 使用已有的 useSubscribe hook
  //   const { loading, error } = useSubscribe();

  // 打开订阅弹框的方法，现在接受 onSuccess 回调
  const open = (options?: {
    productType?: SubscribeModalType;
    onSuccess?: () => void;
    templateId?: string;
    functionCode?: FunctionCode;
    functionId?: MtccFuncCode;
  }) => {
    if (options?.productType) {
      setSelectedProductType(options.productType);
    }
    setTemplateId(options?.templateId || "undefined");
    setFunctionCode(options?.functionCode || "undefined");
    setFunctionId(options?.functionId || "undefined");

    // 保存成功回调
    if (options?.onSuccess) {
      setOnSuccessCallback(() => options.onSuccess);
    } else {
      setOnSuccessCallback(null);
    }

    setIsOpen(true);
  };

  // 关闭订阅弹框的方法
  const close = () => {
    setIsOpen(false);
    setSelectedProductType(SubscribeModalType.Basic);
    // 不清除回调，因为可能在关闭后仍需要执行
  };

  // 提供 Context 值
  const value = {
    open,
    close,
    isOpen,
    // loading,
    // error,
    selectedProductType,
    onSuccessCallback, // 添加回调到 context 值
    templateId, // 添加模板ID到 context 值
    functionCode, // 添加功能代码到 context 值
    functionId, // 添加功能ID到 context 值
  };

  return (
    <SubscribeModalContext.Provider value={value}>
      {children}
    </SubscribeModalContext.Provider>
  );
};

// 自定义 Hook 以方便使用 Context
export const useSubscribeModal = (): SubscribeModalContextType => {
  const context = useContext(SubscribeModalContext);
  if (context === undefined) {
    throw new Error(
      "useSubscribeModal must be used within a SubscribeModalProvider"
    );
  }
  return context;
};
