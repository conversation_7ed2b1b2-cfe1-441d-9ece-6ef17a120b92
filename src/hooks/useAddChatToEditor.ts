import { useStore } from "@/contexts/StoreContext";
import { Action } from "@/stores/TransferAction/types";
import { useRouter } from "next/navigation";
import { useCreateProject } from "./useCreateProject";

export type ChatType = {
  text: string;
  images: {
    src: string;
  }[];
};

const useAddChatToEditor = () => {
  const { transferActionStore, userStore, globalProjectStore } = useStore();
  const { createProject } = useCreateProject({
    globalProjectStore,
    userStore,
  });
  const router = useRouter();

  const addChatToEditor = async (detail: ChatType) => {
    const project = await createProject();
    if (!project) return;

    transferActionStore.pushAction({
      consumer: Action.Consumer.EditorProject,
      type: Action.EditorProject.Type.AddChat,
      payload: {
        detail,
      },
    });

    router.push(`/canvas/project/${project.projectId}`);
  };

  return {
    addChatToEditor,
  };
};

export default useAddChatToEditor;
