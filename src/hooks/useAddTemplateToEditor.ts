import { TemplateItemType } from "@/api/types/poster";
import { useStore } from "@/contexts/StoreContext";
import { Action } from "@/stores/TransferAction/types";
import { useRouter } from "next/navigation";
import { useCreateProject } from "./useCreateProject";

const useAddTemplateToEditor = () => {
  const { transferActionStore, userStore, globalProjectStore } = useStore();
  const { createProject } = useCreateProject({
    globalProjectStore,
    userStore,
  });
  const router = useRouter();

  const addTemplateToEditor = async (detail: TemplateItemType) => {
    const project = await createProject();
    if (!project) return;

    transferActionStore.pushAction({
      consumer: Action.Consumer.EditorProject,
      type: Action.EditorProject.Type.AddTemplate,
      payload: {
        categoryId: detail.categoryId ?? 0,
        templateId: detail.id,
        detail,
      },
    });

    transferActionStore.pushAction({
      consumer: Action.Consumer.EditorProject,
      type: Action.EditorProject.Type.LocateTemplate,
      payload: {
        categoryId: detail.categoryId ?? 0,
        templateId: detail.id,
        detail,
      },
    });

    router.push(`/canvas/project/${project.projectId}`);
  };

  return {
    addTemplateToEditor,
  };
};

export default useAddTemplateToEditor;
