import { useEffect } from 'react';

export const useClickOutside = (
  nodes: React.RefObject<HTMLElement | null>[],
  onClickOutside: (e: MouseEvent) => any
) => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const checkNeedsCall = (el?: HTMLElement | null) => {
        return !!(
          el &&
          el !== event.target &&
          !el.contains(event.target as Node)
        );
      };

      const needsCall = nodes.reduce((needsCall, nodeRef) => {
        return needsCall && checkNeedsCall(nodeRef.current);
      }, true);

      if (needsCall) {
        onClickOutside(event);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  });
};
