.confirm-modal-wrap {
  :global {
    .confirm-modal {
      .ant-modal-content {
        box-sizing: border-box;
        padding: 23px;
        border-radius: var(--radi-xl, 16px);
        border: 1px solid var(--system-stroke-input, #22272E);
        background: var(--system-background-secondary, #1D1E23);
        /* shadow/centreOp4 */
        box-shadow: 0px 4px 50px 0px var(--shadow-op2, rgba(33, 33, 33, 0.08)), 0px 4px 6px 0px var(--shadow-op1, rgba(33, 33, 33, 0.04));

        .ant-modal-close {
          color: rgba(107, 122, 143, 1);
          top: 20px;
        }

        .confirm-modal-content {
          .confirm-modal-title {
            color: var(--system-content-primary, #FFF);
            font-family: var(--fontFamilies-primary, Inter);
            font-size: var(--fontSize-lg, 18px);
            font-weight: 700;
            line-height: var(--lineHeights-xxl, 26px);
            /* 144.444% */
            letter-spacing: var(--letterSpacing-xsm, -0.2px);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .confirm-modal-close {
              cursor: pointer;
              color: rgba(107, 122, 143, 1);
              width: 20px;
              height: 20px;
              flex-shrink: 0;
              margin-left: 6px;

              svg {
                width: 20px;
                height: 20px;
              }

              &:hover {
                opacity: 0.8;
              }
            }
          }

          .confirm-modal-description {
            overflow: hidden;
            color: var(--system-content-secondary, #A3AEBF);
            text-overflow: ellipsis;
            font-family: Inter;
            font-size: var(--fontSize-sm, 14px);
            font-style: normal;
            font-weight: 400;
            line-height: var(--lineHeights-md, 20px);
            /* 142.857% */
            letter-spacing: var(--letterSpacing-md, 0px);
            margin-bottom: 16px;
          }

          .confirm-modal-footer {
            .confirm-modal-actions {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              gap: 16px;
  
              .button {
                border-radius: var(--radius-8, 8px);
                cursor: pointer;
                border: none;
                padding: 9px 12px;
                box-sizing: border-box;
  
                &:hover {
                  opacity: 0.8;
                }
  
                .button-text {
                  font-family: var(--fontFamilies-primary, Inter);
                  font-size: var(--fontSize-md, 16px);
                  font-weight: 400;
                  line-height: var(--lineHeights-xxl, 26px);
                  /* 162.5% */
                }
              }
  
              .button-cancel {
                background: var(--system-background-thirdary, #272C33);
  
                .button-text {
                  color: var(--system-content-secondary, #A3AEBF);
                }
              }
  
              .button-confirm {
                background: var(--system-content-brandPrimary, #53F6B4);
  
                .button-text {
                  color: var(--system-content-primary, #181818);
                }
              }
            }
          }
        }
      }
    }
  }
}