import { Modal, ModalProps } from "antd";
import { useEffect, useRef, useState } from "react";
import styles from "./index.module.scss";
import { CrossBlack } from "@meitu/candy-icons";
import classNames from "classnames";

export interface ConfirmModalProps {
  title: string;
  description: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
  className?: string;
  slots?: {
    footer?: () => React.ReactNode;
  }
}

const useConfirmModal = ({
  title = "title",
  description = "description",
  onConfirm,
  onCancel,
  okText = "Confirm",
  cancelText = "Cancel",
  className,
  slots,
}: ConfirmModalProps) => {
  const [visible, setVisible] = useState(false);
  const open = () => setVisible(true);
  const close = () => setVisible(false);

  const contextHolder = (
    <Modal
      width={550}
      title={null}
      open={visible}
      onOk={open}
      footer={null}
      wrapClassName={classNames(styles["confirm-modal-wrap"], className)}
      className={"confirm-modal"}
      centered
      closeIcon={null}
      destroyOnClose
      maskClosable={false}
      
    >
      <div className={"confirm-modal-content"}>
        <h1 className={"confirm-modal-title"}>
          {title}
          <div className={"confirm-modal-close"} onClick={close}>
            <CrossBlack />
          </div>
        </h1>
        <p className={"confirm-modal-description"}> {description} </p>

        <div className="confirm-modal-footer">
          {slots?.footer?.()}
          <div className={"confirm-modal-actions"}>
            <button
              className={"button button-cancel"}
              onClick={(e) => {
                e.stopPropagation();
                onCancel?.();
                close();
              }}
            >
              <span className={"button-text"}>{cancelText}</span>
            </button>
            <button
              className={"button button-confirm"}
              onClick={(e) => {
                e.stopPropagation();
                onConfirm?.();
                close();
              }}
            >
              <span className={"button-text"}>{okText}</span>
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
  return {
    open,
    close,
    contextHolder,
  };
};
export default useConfirmModal;
