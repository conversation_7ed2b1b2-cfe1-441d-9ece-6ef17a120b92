import { useState, useRef, useEffect } from "react";
import {
  fetchEventSource,
  EventSourceMessage,
} from "@microsoft/fetch-event-source";

export enum ReadyState {
  Connecting = 0,
  Open = 1,
  Closing = 2,
  Closed = 3,
}

export interface SSEOptions<T = any> {
  method?: "GET" | "POST";
  headers?: Record<string, string>;
  withCredentials?: boolean;
  retryLimit?: number; // 最大重试次数
  retryInterval?: number; // 重试间隔（毫秒）
  onOpen?: (response: Response) => void;
  onClose?: () => void;
  onMessage?: (message: T, event: EventSourceMessage) => void;
  onError?: (error: Error, retryCount: number) => void | number; // 返回自定义重试间隔
}

export interface SSEResult {
  readyState: ReadyState;
  sendMessage: (
    body?: any,
    queryParams?: Record<string, string | number | boolean>
  ) => void;
  disconnect: () => void;
  latestEvent?: EventSourceMessage;
  error?: Error;
  retryCount: number; // 当前重试次数
}

export default function useFetchSSE<T = any>(
  url: string,
  options: SSEOptions<T> = {}
) {
  const {
    method = "POST",
    headers = {},
    withCredentials = false,
    onOpen,
    onClose,
    onMessage,
    onError,
  } = options;

  const sseUrl = `/api/sse${url}`;
  const abortControllerRef = useRef<AbortController>();
  const retryCountRef = useRef(0); // 用于跟踪重试次数
  const [readyState, setReadyState] = useState<ReadyState>(ReadyState.Closed);
  const [latestEvent, setLatestEvent] = useState<EventSourceMessage>();
  const [error, setError] = useState<Error>();
  const [retryCount, setRetryCount] = useState(0);

  // 构建带查询参数的 URL
  const buildUrl = (
    reqUrl: string,
    params?: Record<string, string | number | boolean>
  ) => {
    if (!params || Object.keys(params).length === 0) return reqUrl;
    const queryString = Object.entries(params)
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
      )
      .join("&");
    return `${reqUrl}${reqUrl.includes("?") ? "&" : "?"}${queryString}`;
  };

  // 清除重试状态
  const clearRetry = () => {
    retryCountRef.current = 0;
    setRetryCount(0);
  };

  // 连接 SSE
  const connect = async (
    body: any,
    queryParams?: Record<string, string | number | boolean>
  ) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort(); // 取消之前的连接
    }

    const controller = new AbortController();
    abortControllerRef.current = controller;

    setReadyState(ReadyState.Connecting);
    setError(undefined);
    const fullUrl = buildUrl(sseUrl, queryParams);

    try {
      await fetchEventSource(fullUrl, {
        method,
        headers: {
          "Content-Type": "application/json",
          ...headers,
        },
        openWhenHidden: true,
        body: method === "POST" ? JSON.stringify(body) : undefined,
        credentials: withCredentials ? "include" : undefined,
        signal: controller.signal,
        // 连接成功
        async onopen(response) {
          if (response.ok) {
            clearRetry(); // 重置重试计数
            setReadyState(ReadyState.Open);
            onOpen?.(response);
            return;
          }
          throw new Error(`SSE 连接失败: ${response.status}`);
        },

        // 收到消息
        onmessage(event) {
          setLatestEvent(event);

          // 处理特殊事件类型
          if (event.event === "connection_lost") {
            console.warn("SSE connection lost, upstream disconnected");
            setError(new Error("Connection lost: upstream disconnected"));
            return;
          }

          if (event.event === "error") {
            console.error("SSE stream error from server");
            setError(new Error("Stream error from server"));
            return;
          }

          try {
            const parsedData = event.data ? JSON.parse(event.data) : undefined;

            const data = parsedData?.data
              ? JSON.parse(parsedData.data)
              : undefined;

            onMessage?.(data as T, event);
          } catch (err) {
            console.error("消息解析错误:", err);
          }
        },

        // 连接关闭
        onclose() {
          setReadyState(ReadyState.Closed);
          console.log("close---sse");
          onClose?.();
        },

        // 错误处理（自动重试逻辑）
        onerror(err) {
          setError(err);
          onError?.(err, 0);
          if (abortControllerRef.current) {
            abortControllerRef.current?.abort();
          }

          throw err;
        },
      });
    } catch (err: any) {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = undefined;
      }
    }
  };

  // 断开连接
  const disconnect = () => {
    console.log("disconnect");

    clearRetry();
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = undefined;
    }
    setReadyState(ReadyState.Closed);
  };

  // 组件卸载时自动断开连接
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    connect,
    disconnect,
    readyState,
    latestEvent,
    error,
    retryCount,
  };
}
