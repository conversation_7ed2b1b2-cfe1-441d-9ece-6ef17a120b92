import { UserStore } from '@/stores/UserStore';

type UseMeidouBalanceDeps = {
  userStore: UserStore;
}

export const useMeiDouBalance = ({
  userStore,
}: UseMeidouBalanceDeps) => {

  const state = userStore.mtBeanBalance;

  const updateMeiDouBalance = () => userStore.refreshMtBeanBalance();

  return {
    updateMeiDouBalance,
    availableAmount: state?.availableAmount,
    tips: state?.tips,
    benefitsDescription: state?.benefitsDescription,
    detailTitle: state?.detailTitle,
    detailDesc: state?.detailDesc,
    benefitsDetail: state?.benefitsDetail ?? []
  };
};
