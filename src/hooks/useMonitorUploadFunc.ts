import { useStore } from "@/contexts/StoreContext";
import { createUploaderProcessor } from "@/utils/uploader";
import { beforeValidatorPass } from "@/utils/uploader/middleware/beforeValidatorPass";
import { monitorPass } from "@/utils/uploader/middleware/monitorPass";
import { uploadPass } from "@/utils/uploader/middleware/uploadPass";
import { useMemo } from "react";

export function useMonitorUploadFunc() {
  const { userStore } = useStore();
  const accessToken = userStore.authToken;

  const upload = useMemo(() => {
    if (!accessToken) {
      return;
    }
    return createUploaderProcessor([
      beforeValidatorPass({
        extension: ['png', 'jpg'],
        size: 30,
        dimension: {
          min: 50,
          max: 9500,
        },
      }),
      uploadPass({ accessToken }),
      monitorPass(),
    ])
  }, [accessToken]);

  return upload;
}