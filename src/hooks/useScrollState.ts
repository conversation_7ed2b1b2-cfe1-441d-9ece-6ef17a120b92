// type UseScrollStateDeps = {
//   elementRef: React.RefObject<HTMLElement>
// }

import { throttle } from "lodash-es";
import { useEffect, useLayoutEffect, useRef, useState } from "react";

// 竖直滚动状态
export function useVerticalScrollState<T extends HTMLElement>() {

  const elementRef = useRef<T>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [scrollHeight, setScrollHeight] = useState(0);

  useEffect(() => {
    if (!elementRef.current) {
      return;
    }

    const abortController = new AbortController();

    // 同步scrollHeight
    const el = elementRef.current;
    const observer = new MutationObserver(() => {
      setScrollHeight(el.scrollHeight);
    });
    observer.observe(el, {
      childList: true,
      subtree: true,
      characterData: true,
    });
    abortController.signal.onabort = () => {
      observer.disconnect();
    };

    const throttledSetScrollTop = throttle(setScrollTop, 15, { trailing: true });
    // 同步scrollTop
    el.addEventListener("scroll", () => {
      throttledSetScrollTop(el.scrollTop);
    }, { signal: abortController.signal, passive: true });


    return () => {
      abortController.abort();
    };
  }, []);

  const getScrollStateClassName = () => {
    if (!elementRef.current) {
      return {};
    }

    const el = elementRef.current;
    if (el.clientHeight >= el.scrollHeight) {
      return {}
    }

    if (scrollTop < 1) {
      return {
        scroll: true,
        'scroll-to-top': true
      }
    }

    if (Math.abs(scrollTop - (el.scrollHeight - el.clientHeight)) < 1) {
      return {
        scroll: true,
        'scroll-to-bottom': true
      }
    }

    return { scroll: true  }
  }
  const [scrollStateClassName, setScrollStateClassName] = useState(getScrollStateClassName);

  useLayoutEffect(() => {
    setScrollStateClassName(getScrollStateClassName())
  }, [scrollHeight, scrollTop]);

  return {
    scrollStateClassName,
    scrollHeight,
    scrollTop,
    elementRef,
  }
}

// 水平滚动状态
export const useHorizontalScrollState = <T extends HTMLElement>() => {
  const elementRef = useRef<T | null>(null);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [scrollWidth, setScrollWidth] = useState(0);

  useEffect(() => {
    if (!elementRef.current) {
      return;
    }
    const abortController = new AbortController();

    // 同步scrollWidth
    const el = elementRef.current;
    const observer = new MutationObserver(() => {
      setScrollWidth(el.scrollWidth);
    });
    observer.observe(el, {
      childList: true,
      subtree: true,
      characterData: true,
    });
    abortController.signal.onabort = () => {
      observer.disconnect();
    };

    const throttledSetScrollLeft = throttle(setScrollLeft, 15, {
      trailing: true,
    });

    // 同步scrollLeft
    el.addEventListener("scroll", (e) => {
      throttledSetScrollLeft(el.scrollLeft);
    }, { signal: abortController.signal, passive: true });


    return () => {
      abortController.abort();
    };
  }, []);

  const getScrollStateClassName = () => {
    if (!elementRef.current) {
      return {};
    }

    const el = elementRef.current;
    if (el.clientWidth >= el.scrollWidth) {
      return {}
    }

    if (scrollLeft < 1) {
      return {
        scroll: true,
        'scroll-to-left': true
      }
    }

    if (Math.abs(scrollLeft - (el.scrollWidth - el.clientWidth)) < 1) {
      return {
        scroll: true,
        'scroll-to-right': true
      }
    }

    return { scroll: true }
  }
  const [scrollStateClassName, setScrollStateClassName] = useState(getScrollStateClassName);

  const timeoutKey = useRef(0);
  useLayoutEffect(() => {
    if (timeoutKey.current) {
      clearTimeout(timeoutKey.current)
    }
    setScrollStateClassName(getScrollStateClassName())
  }, [scrollLeft, scrollWidth]);

  return {
    scrollStateClassName,
    scrollWidth,
    scrollLeft,
    elementRef,
  }
}


