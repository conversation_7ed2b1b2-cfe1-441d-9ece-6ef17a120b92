// 订阅相关逻辑 ---- 带折扣
'use client'

import { rootStore } from "@/stores/RootStore";

import { useObserver } from "mobx-react-lite";
import {
    SubscribeEnv,
    Subscribe,
    Product,
    openSubscribePopup,
    Theme,
    LanguageCode,
    SystemType,
} from '@meitu/subscribe-intl'


import toast from "@/components/Toast";


import { getConfig } from "@/config";
import { useState, useEffect, useRef } from 'react';
import { getLocalStorageItem, setLocalStorageItem, setStorageItem, toCamelCase, toPascalCase } from "@meitu/util";
import { mock } from "@/components/SubscribeModal/mock";

import mtstat  from "@meitu/tracer-intl";

import { SubscribeType } from "@/types/meidou";
import { checkMeidouBalance } from '@/api/meidou';
import _ from "lodash";
import { useI18n } from "@/locales/client";
const { userStore } = rootStore;


const executeThreeTimes = (callback: () => void, onComplete?: () => void) => {
    let count = 0;
    
    const execute = () => {
      // Execute the callback
      callback();
      count++;
      
      // Schedule the next execution if we haven't done 3 yet
      if (count < 3) {
        setTimeout(execute, 5000); // 5 seconds
      } else if (onComplete) {
        // Call the completion handler after all 3 executions
        onComplete();
      }
    };
    
    // Start the execution
    execute();
  };

const envMap = {
    release: SubscribeEnv.Release,
    beta: SubscribeEnv.Beta,
    pre: SubscribeEnv.Pre,
    dev: SubscribeEnv.Pre,
}


// 初始化订阅信息

const getDeviceId = () => {
    if(typeof window !== 'undefined') {
        return mtstat.getDeviceId();
    }
    return '';
}



export const getBaseSubscribeConfig = () => {
    // 从userStore中获取accessToken
    const accessToken = userStore.authToken;
    // if (!accessToken) {
    //     return
    // }
    return {
        accessToken: accessToken ?? '',
        env: envMap[process.env.NEXT_PUBLIC_ENV as keyof typeof envMap],
        debug: process.env.NEXT_PUBLIC_ENV === 'dev',
        clientInfo: {
            appId: getConfig().SUBSCRIBE_APP_ID,
            vipGroup: getConfig().SUBSCRIBE_GROUP,
            language: LanguageCode.EN,
            platform: 4,
            systemType: SystemType.Web,
            transferData: {
                baseData: {
                    device_type: 3,
                    oper_system: 7,
                },
                businessData: {},
                bigData: {
                    device_id: getDeviceId(),
                    app_key: getConfig().STATISTIC_APP_KEY,
                    app_version: process.env.REACT_APP_VERSION,
                    platform: 4,
                    // channel: channelParams?.channel,
                },
            }
        },
        requestOptions: {
            headers: {
                ['Client-Business-Trace-Id']: '',
                // ['Language'] : LanguageCode.EN,

            }
        }
    }
   
}

// 新增类型定义
interface SubscribeResult {
    instance: Subscribe | null;
    transferId: string;
    products: any[];
    basicProduct: any[];
    advancedProduct: any[];
    yearProduct: any[];
    monthProduct: any[];
}

// 将原来的 useSubscribe 重命名为 initSubscribe
export const initSubscribe = async (_accessToken?: string, vipLevel?: number): Promise<SubscribeResult> => {
    try {
        // 初始化订阅SDK
        const accessToken = _accessToken ?? userStore.authToken;
        // if (!accessToken) {
        //     throw new Error('No access token found');
        // }

        const baseSubscribeConfig = getBaseSubscribeConfig();
        if (!baseSubscribeConfig) {
            throw new Error('Failed to get subscribe config');
        }

        const instance = new Subscribe({
            accessToken: accessToken ?? '',
            env: baseSubscribeConfig.env,
            clientInfo: baseSubscribeConfig.clientInfo,
            requestOptions: baseSubscribeConfig.requestOptions,
        });

        const { transferId } = await instance.trackSubscribeData({
            baseData: JSON.stringify(baseSubscribeConfig.clientInfo.transferData.baseData),
            businessData: JSON.stringify(baseSubscribeConfig.clientInfo.transferData.businessData),
            bigData: JSON.stringify(baseSubscribeConfig.clientInfo.transferData.bigData),
        });
       
        // dev 环境 使用mock数据
        let products: any[] = [];
        products = await instance.fetchProducts({
            entranceBizCode: vipLevel === 0 ? 'wheeai.vip.Byearly' : 'wheeai.premium.Ayearly',
        });
        // if(vipLevel === 0) {
        //     products = await instance.fetchProducts({
        //             entranceBizCode: 'wheeai.vip.Byearly',
        //     });
        // } else if(vipLevel === 1) {
        //     products = await instance.fetchProducts({
        //             entranceBizCode: 'wheeai.premium.Ayearly',
        //         });
        // }
        // }
        // const products =
        // console.log('products=>>>>>>>>', products);
        if (!products || !Array.isArray(products)) {
            throw new Error('Failed to fetch products');
        }

        // 获取全部 的基础会员商品 ，月付和年付
        const basicProduct = products.filter((item: any) => item.commodityId === getConfig().SUBSCRIBE_BASIC_ID || item.commodity_id === getConfig().SUBSCRIBE_BASIC_ID);
        // 获取全部 的高级会员商品 
        const advancedProduct = products.filter((item: any) => item.commodityId === getConfig().SUBSCRIBE_ADVANCED_ID || item.commodity_id === getConfig().SUBSCRIBE_ADVANCED_ID);

        //  当商品为订阅商品时有值（0:天 1: 月 2: 季 3: 年 5:周）

        // 获取年付商品
        let yearProduct = products.filter((item: any) => item.subPeriod === SubscribeType.Year);

        if(vipLevel === 1) {
            yearProduct = [yearProduct[0], yearProduct[0]]
        }

        // 获取月付商品
        const monthProduct = products.filter((item: any) => item.subPeriod === SubscribeType.Month);
        return {
            instance,
            transferId,
            products: products,
            basicProduct,
            advancedProduct,
            yearProduct,
            monthProduct,
        };
    } catch (error) {
        console.error('Subscribe error:', error);
        return {
            instance: null,
            transferId: '',
            products: [],
            basicProduct: [],
            advancedProduct: [],
            yearProduct: [],
            monthProduct: [],
        };
    }
}

// 新增 React Hook
export const useSubscribe = () => {
    const [loading, setLoading] = useState(false);
    const [submitLoading, setSubmitLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const initRef = useRef(false);

    const interval = useRef<NodeJS.Timeout | null>(null);
    const uid = useObserver(() => userStore.UID);

    const vipLevel = useObserver(() => userStore.vipLevel);
    // const vipLevel = 1;
    const t = useI18n();

    const [subscribeData, setSubscribeData] = useState<SubscribeResult>({
        instance: null,
        transferId: '',
        products: [],
        basicProduct: [],
        advancedProduct: [],
        yearProduct: [],
        monthProduct: [],
    });

    // Add a function to manually initialize subscription
    const initializeSubscription = async (_accessToken?: string) => {
      
        // if(initRef.current) {
        //     return;
        // }
        
        try {
            setLoading(true);
            const result = await initSubscribe(_accessToken, vipLevel);
            setSubscribeData(result);
            initRef.current = true;
            return result;
        } catch (err) {
            setError(err instanceof Error ? err : new Error('Unknown error'));
        } finally {
            setLoading(false);
        }
    };

    // 组件卸载后，清除轮训
    useEffect(() => {
        return () => {
            clearInterval(interval.current as NodeJS.Timeout);
            initRef.current = false;
        }
    }, []);

    const createTradeUrl = async (product: Product) => {
        const { promotions } = product;
        const [promotion] = promotions;
        setSubmitLoading(true);

        const response = await subscribeData.instance?.createTradeUrl({
            productId: product.productId,
            tradeSessionId: product?.tradeSessionId,
            promotionId: promotion?.promotionId,
            transferId: subscribeData.transferId,
        });

        return response;
    }

    // 查询订阅结果，手动关闭等逻辑
     const querySubscribeResult = async (product: Product, onSuccess?: () => void) => {
        interval.current = setInterval(async () => {
            const result = await subscribeData?.instance?.getProductTradeProgress({
            tradeSessionId: product?.tradeSessionId,
           })
           
           const tradeKey = `subscribe_trade_key_${uid}`;
           const cachedTradeList = getLocalStorageItem(tradeKey) ?? [];


           // 交易订单中 支付状态为1 标识支付成功
           const finishedTransaction = result?.transactionList?.filter((item) => item.payStatus === 1);
           // / 去重已交易订单，由于服务端会重复返回，所以需要过滤掉已缓存的交易单
           const dedupeTransaction = finishedTransaction?.filter((item) => {
            const tradeIdIsCached = cachedTradeList.length && cachedTradeList?.includes(String (item?.transactionId));
            return !tradeIdIsCached;
           })

           if(dedupeTransaction?.length) {
            const tradeIds = finishedTransaction?.map((item: any) => item?.transactionId);

            // const oneDay = 24 * 60 * 60 * 1;
            tradeIds && setLocalStorageItem(tradeKey, tradeIds);

            // console.log('支付完成', tradeIds);
            //  更新用户信息

            // 关闭轮训
            if(interval.current) {
                clearInterval(interval.current as NodeJS.Timeout);
                setSubmitLoading(false);
            }

            // 更新美豆, 延时5s 执行

            //  轮训3次 ，每次执行时间3s 间隔

            
            setTimeout(() => {
                if(product.commodityId === getConfig().SUBSCRIBE_BASIC_ID) {
                    toast.success(t("Cheers! 🥂 Your exclusive benefits are ready to use"));
                } else if(product.commodityId === getConfig().SUBSCRIBE_ADVANCED_ID) {
                    toast.success(t('You are now VIP! Thank you for staying with us. ❤️'));
                }
                onSuccess?.();
            }, 5 *1000);
            executeThreeTimes(() => {
                setSubmitLoading(false);
                userStore.refreshMtBeanBalance();
                userStore.refreshUserInfo();
                setSubmitLoading(false);
            })
           }


        }, 3000);
     }

    return {
        ...subscribeData,
        loading,
        error,
        submitLoading,
        createTradeUrl,
        querySubscribeResult,
        initializeSubscription,
    };
};


// 美豆订阅弹框
/**
 * 获取美豆详情列表弹窗
 */
export const useOpenMeiDouRecordsPopup = () => {
    const baseSubscribeConfig = getBaseSubscribeConfig();
    const t = useI18n();
    const openMeiDouRecords = () => {
      if (!baseSubscribeConfig?.accessToken) return;

  
      /** https://cf.meitu.com/confluence/pages/viewpage.action?pageId=389157071 */
     const modal = openSubscribePopup({
        config:  _.merge(baseSubscribeConfig, {
          clientInfo: {
            // categoryGroupCode: process.env.REACT_APP_SUBSCRIBE_MEIDOU_CATEGORY
            categoryGroupCode: getConfig().SUBSCRIBE_MEIDOU_KEY,
            theme: Theme.Dark, // 非必须
          }
        }),
        onTradeConfirm() {
            // console.log('onTradeConfirm=>>>>>>>>');
            checkMeidouBalance();
            userStore.refreshUserInfo();
            userStore.refreshMtBeanBalance();
            modal.close();
            toast.success('Boom! Credits added 💥 Let’s get creative!');
            
            
        },
      });
    };
  
    return openMeiDouRecords;
  };


  // 美豆订阅弹框

