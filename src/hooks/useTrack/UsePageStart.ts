'use client'
import { useEffect } from "react";
import { trackEvent } from "@/services/tracer";
import { mtstatReady } from '@/utils/mtstatReady';
import { IsLogin } from "./type";

export const usePageStart = (pageName: string, isLogin: IsLogin) => {
  useEffect(() => {
    mtstatReady.then(() => {
      trackEvent('page_enter', {
        page_name: pageName,
        is_login: isLogin,
      });
    });
  }, [pageName, isLogin]);
}