import { useEffect, useRef } from 'react';

interface UseCardExpoOptions {
  onExpo?: () => void;
  threshold?: number;
  rootMargin?: string;
  isActive?: boolean;
}

export const useCardExpo = <T extends HTMLElement>({
  onExpo,
  threshold = 0.5,
  rootMargin = '0px',
  isActive = true,
}: UseCardExpoOptions = {}) => {
  const elementRef = useRef<T | null>(null);
  const hasExposedRef = useRef(false);

  // 存储之前的标识属性以检测它们是否发生变化
  const prevOnExpoRef = useRef(onExpo);
  const prevThresholdRef = useRef(threshold);
  const prevRootMarginRef = useRef(rootMargin);

  useEffect(() => {
    const currentElement = elementRef.current;

    // 如果标识属性发生变化，这是一个新的曝光任务，需要重置'hasExposed'
    if (
      onExpo !== prevOnExpoRef.current ||
      threshold !== prevThresholdRef.current ||
      rootMargin !== prevRootMarginRef.current
    ) {
      hasExposedRef.current = false; // 重置曝光状态
      // 更新存储的引用为新值
      prevOnExpoRef.current = onExpo;
      prevThresholdRef.current = threshold;
      prevRootMarginRef.current = rootMargin;
    }

    if (!isActive || !currentElement || !onExpo) {
      return; // 曝光条件未满足
    }

    if (hasExposedRef.current) {
      // 已经使用当前的标识属性集完成曝光
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 再次检查hasExposedRef以确保安全，尽管外部检查应该足够
            if (!hasExposedRef.current) {
              onExpo();
              hasExposedRef.current = true; // 标记为已曝光
            }
            // 从此观察器实例中取消观察该特定元素
            observer.unobserve(currentElement);
          }
        });
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(currentElement);

    return () => {
      observer.disconnect();
    };
  }, [onExpo, threshold, rootMargin, isActive]); // 重新运行effect的依赖项

  return elementRef;
};
