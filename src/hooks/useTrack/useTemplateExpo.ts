import { trackEvent } from "@/services/tracer";
import { useCardExpo } from "./useCardExpo";
import { TemplateExposureLocation } from "@/types";
import { mtstatReady } from "@/utils/mtstatReady";
import { useCallback } from "react";

export const useTemplateExpo = <T extends HTMLElement>(
  templateId: number, 
  templateCategoryId?: number, 
  location?: TemplateExposureLocation,
  isActive: boolean = true
) => {
  const handleTemplateExpo = useCallback(() => {
    if (location) {
      mtstatReady.then(() => {
        trackEvent('template_feed_expo', {
          template_id: templateId,
          template_category_id: templateCategoryId,
          location: location,
        });
      });
    }
  }, [location, templateId, templateCategoryId]);

  const cardExpo = useCardExpo<T>({
    onExpo: handleTemplateExpo,
    isActive: isActive,
  });

  return cardExpo;
};