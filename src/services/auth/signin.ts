// import { User } from '@/types'
import { fetcher } from '@/utils/request/fetcher'

export type SigninParamsType = {
  username: string
  password: string
}

const signin = async (
  params: SigninParamsType,
): Promise<any> => {
  return await fetcher(
    `${process.env.NEXT_PUBLIC_API_BASE_PATH}/auth/signin`,
    {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    },
  )
}

export default signin