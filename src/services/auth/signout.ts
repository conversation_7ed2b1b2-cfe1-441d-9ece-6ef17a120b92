import { fetcher } from '@/utils/request/fetcher'

const signout = async (): Promise<{ message: string }> => {
  return { message: 'signout' }
}
// const signout = async (): Promise<{ message: string }> => {
//   // return await fetcher(
//   //   `${process.env.NEXT_PUBLIC_API_BASE_PATH}/auth/signout`,
//   //   {
//   //     method: 'POST',
//   //     headers: {
//   //       Accept: 'application/json',
//   //       'Content-Type': 'application/json',
//   //     },
//   //   },
//   // )
// }

export default signout