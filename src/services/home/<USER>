import useSWR from 'swr'
import { fetcher } from '@/utils/request/fetcher'

export type listType = {
  userId: number,
  id: number,
  title: string,
  completed: boolean
}



const getShortList = async (): Promise<listType> => {
  return {
    userId: 1,
    id: 1,
    title: 'test',
    completed: false
  }
  // return await fetcher(
  //   `http://localhost:3000/api/todos/1`,
  //   {
  //     headers: {
  //       Accept: 'application/json',
  //       'Content-Type': 'application/json',
  //     },
  //   },
  // )
}

export default getShortList