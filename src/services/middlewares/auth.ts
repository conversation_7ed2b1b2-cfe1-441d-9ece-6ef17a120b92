import { getConfig } from "@/config";
import { isRootPath } from "@/utils/isRootPath";
import { fetcher } from "@/utils/request/fetcher";
import { NextRequest, NextResponse } from "next/server";
// 使用正则表达式定义受保护的路由
const PROTECTED_ROUTES_PATTERNS = [
  /^(?:\/[a-z]{2})?\/workspace(\/.*)?$/,
  /^(?:\/[a-z]{2})?\/editor(\/.*)?$/,
];

const config = getConfig();

// 查询余额的函数
export const queryBalance = async () => {
  const res = await fetcher<any>(
    `${config.API_URL}/sub/query_balance.json`,
    {
      method: "GET",
    },
    {
      withCookies: true,
    }
  );

  return res?.data || {};
};

export const authMiddleware = async (request: NextRequest) => {
  // 检查当前路径是否为受保护路径
  const isProtectedRoute = PROTECTED_ROUTES_PATTERNS.some((pattern) =>
    pattern.test(request.nextUrl.pathname)
  );
  const authToken = request.cookies.get("auth_token");
  const locale = request.cookies.get("Next-Locale")?.value ?? "en";

  let isLoggedIn = false;

  // 如果存在 auth_token，尝试查询余额以验证登录状态
  if (authToken) {
    try {
      await queryBalance();
      isLoggedIn = true;
    } catch (error) {
      console.error("Error querying balance:", error);
      // 如果查询失败，认为用户未登录
      isLoggedIn = false;
    }
  }

  // 获取原始查询参数
  const searchParams = request.nextUrl.searchParams.toString();
  const queryString = searchParams ? `?${searchParams}` : "";

  if (isProtectedRoute) {
    if (!isLoggedIn) {
      const redirectUrl = new URL(`/${locale}${queryString}`, request.url);
      return NextResponse.redirect(redirectUrl);
    }
  } else {
    if (isLoggedIn && isRootPath(request.nextUrl.pathname)) {
      const redirectUrl = new URL(
        `/${locale}/workspace${queryString}`,
        request.url
      );
      return NextResponse.redirect(redirectUrl);
    }
  }
};
