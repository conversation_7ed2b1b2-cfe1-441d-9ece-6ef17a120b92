import { NextRequest, NextResponse } from "next/server";
import { getConfig } from "@/config";

const config = getConfig();
/**
 * 处理 API 请求转发的中间件
 */
export function handleApiProxy(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  // 处理 /aigc/api/ 开头的请求
  if (pathname.startsWith("/aigc/api/")) {
    // 获取环境变量中的 API URL
    const apiUrl = config.API_URL;
    if (!apiUrl) {
      console.error("环境变量 API_URL 未设置");
      return NextResponse.next();
    }

    // 从 cookie 中获取 auth_token
    const authToken = request.cookies.get("auth_token")?.value;

    // 创建新的请求头，保留原始请求的所有头信息
    const headers = new Headers(request.headers);

    // 如果存在 auth_token，则添加到请求头中
    if (authToken) {
      // headers.set("Access-Token", '_v2NmMwZmU4MDYjMTc0NzY1MTE1MCMyNTYjNzIjNzJiYmY3NmMzM2M0NzM0ZTk3OTVlZDUyZWY1ZTBhOWYzNSNIVUFXRUlfQ0xPVUQjQkpfSFcjNjdiNDYzNGU=');
      headers.set("Access-Token", authToken);
    }

    // 创建新的 URL 用于转发,并拼接查询参数
    const url = new URL(
      pathname.replace("/aigc/api", "") + "?" + searchParams.toString(),
      apiUrl
    );

    // 注意：这里我们不修改 URL，只修改请求头
    // rewrites 配置将负责实际的 URL 重写
    return NextResponse.rewrite(url, {
      headers: headers,
    });
  }

  // 如果不匹配，返回 null 表示这个中间件没有处理请求
  return null;
}
