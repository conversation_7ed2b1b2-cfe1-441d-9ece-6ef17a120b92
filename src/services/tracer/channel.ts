import type { CommonStatsParams } from "@meitu/tracer-intl";

import { toSnakeCase } from '@meitu/util';
import qs from 'qs';

/**
 * 统计渠道参数
 */
interface TraceChannelParams {
  firstUrl: string;
  channel: string;
}

/**
 * 统计渠道查询参数
 */
interface TraceChannelSearchParams {
  first_url?: string;
  channel?: string;
}

/**
 * 获取会话存储的值
 * @param key 键
 */
function getSessionStorageItem(key: string) {
  return decodeURIComponent(sessionStorage.getItem(key) || '');
}

/**
 * 将提供的值存储至会话存储
 * @param key
 * @param value
 */
function setSessionStorageItem(key: string, value: string) {
  sessionStorage.setItem(key, encodeURIComponent(value));
}

/**
 * 获取统计参数
 */
export function getTraceChannelParams() {
  const search = window.location.search.replace(/^\?/, '');
  const searchParams = qs.parse(search) as TraceChannelSearchParams;
  const channel = searchParams.channel || getSessionStorageItem('channel');
  const partialInitConfig: Partial<any> = {};

  const channelParams: TraceChannelParams = {
    firstUrl:
      searchParams.first_url ||
      getSessionStorageItem('first_url') ||
      window.location.href,
    channel
  };

  if (searchParams.channel) {
    partialInitConfig.channel = channel;

    setSessionStorageItem('channel', searchParams.channel);

    setSessionStorageItem(
      'first_url',
      searchParams.first_url
        ? decodeURIComponent(searchParams.first_url)
        : window.location.href
    );
  }

  return [
    toSnakeCase(channelParams) as CommonStatsParams,
    partialInitConfig
  ] as const;
}
