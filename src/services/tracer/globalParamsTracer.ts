import { toSnakeCase, getSessionStorageItem, setSessionStorageItem } from '@meitu/util';
import { get } from 'lodash-es';
import qs from 'qs';
import { safeEncodeURL } from '@/utils/url';

export const GOOGLE_CLICK_IDENTIFY = 'gclid';
export const GOOGLE_CLIENT_ID = 'gg_client_id';
export const FIRST_URL = 'first_url';

/**
 * google 广告追踪参数 (url 和 session 上的参数)
 */
interface GoogleTraceSearchParams {
  // 第一次进入站点的 url
  first_url?: string;

  // Google Click Identifier
  gclid?: string;

  // google 客户端 id
  gg_client_id?: string;
}

/**
 * 从会话存储中读取 google ads 参数
 */
function readGoogleAdsParams() {
  const gclid = getSessionStorageItem(GOOGLE_CLICK_IDENTIFY);
  const ggClientId = getSessionStorageItem(GOOGLE_CLIENT_ID);
  const firstUrl = getSessionStorageItem(FIRST_URL);

  return {
    gclid,
    firstUrl,
    ggClientId,
  };
}

/**
 * 保存 google ads 参数到会话存储
 * @param params 要保存的参数
 */
function saveGoogleAdsParams(params: { gclid: string; ggClientId: string; firstUrl: string }) {
  setSessionStorageItem(GOOGLE_CLICK_IDENTIFY, params.gclid);
  setSessionStorageItem(GOOGLE_CLIENT_ID, params.ggClientId);
  setSessionStorageItem(FIRST_URL, params.firstUrl);
}

/**
 * 从 URL 上获取 google 广告追踪参数 gclid 和 次进入时的 url
 */
export function getGoogleAdsParamsFromUrl() {
  // 获取 google ads 追踪参数
  const search = window.location.search.replace(/^\?/, '');
  const urlParams = qs.parse(search) as GoogleTraceSearchParams;
  const gclid = get(urlParams, GOOGLE_CLICK_IDENTIFY);
  const ggClientId = get(urlParams, GOOGLE_CLIENT_ID);
  const firstUrl = decodeURIComponent(get(urlParams, FIRST_URL, ''));

  return {
    gclid,
    ggClientId,
    firstUrl,
  };
}

/**
 * 获取并设置 google 广告追踪参数到本地 session
 */
export async function setGoogleAdsParams(googleClientId: string) {
  // 获取 google ads 追踪参数 gclid  和 首次进入时的 url
  const { gclid: gclidFromUrl, firstUrl: firstUrlFromUrl, ggClientId: ggClientIdFromUrl } = getGoogleAdsParamsFromUrl();
  // 获取 google client_id, 为了区分大账号的 client_id 因此定义成了 gg_client_id
  const ggClientId = getSessionStorageItem(GOOGLE_CLIENT_ID) || ggClientIdFromUrl || googleClientId;
  const gclid = getSessionStorageItem(GOOGLE_CLICK_IDENTIFY) || gclidFromUrl;
  const firstUrl = getSessionStorageItem(FIRST_URL) || firstUrlFromUrl || window.location.href;

  // 如果URL中包含 google 广告点击标识 gclid，则保存到 session 并更新配置
  if (gclid) {
    saveGoogleAdsParams({
      gclid,
      ggClientId,
      firstUrl,
    });
  }
}

/**
 * 获取 google 广告追踪参数 （使用的最终参数）
 */
export  function getGoogleAdsParams() {
  // 获取 google ads 追踪参数 gclid  和 首次进入时的 url
  const { gclid, ggClientId, firstUrl } = readGoogleAdsParams();

  /**
   * 投放需要透传的字段是 gclid、first_url 、gg_client_id, 要进行 encode 不然透传给订阅的时候会报错，
   * 因为订阅的接口使用的是 application/x-www-form-urlencoded
   * https://cf.meitu.com/confluence/pages/viewpage.action?pageId=397070754
   */
  return toSnakeCase({
    gclid,
    firstUrl: safeEncodeURL(firstUrl || ''),
    ggClientId,
  }) as GoogleTraceSearchParams;
}

/**
 * 为 URL 添加追踪参数
 * @param url 原始 URL
 * @returns 添加追踪参数后的 URL
 */
export function addTraceParamsToUrl(url: string) {
  try {
    const traceParams = getGoogleAdsParams();
    // 拆分 base 和 query
    const [baseUrl, queryString = ''] = url.split('?');

    const originalParams = qs.parse(queryString);
    const mergedParams = {
      ...originalParams,
      ...(traceParams?.gclid && { gclid: traceParams.gclid }),
      // eslint-disable-next-line camelcase
      ...(traceParams?.first_url && { first_url: traceParams.first_url }),
      // eslint-disable-next-line camelcase
      ...(traceParams?.gg_client_id && { gg_client_id: traceParams.gg_client_id }),
    };

    const queryParams = qs.stringify(mergedParams, { encode: false });

    return queryParams ? `${baseUrl}?${queryParams}` : baseUrl;
  } catch (error) {
    console.error('Failed to add trace params:', error);
    return url;
  }
}
