/**
 * 谷歌分析事件上报工具
 */

interface GTMEventParams {
  [key: string]: any;
}

/**
 * 发送GTM自定义事件
 * @param eventName 事件名称
 * @param params 事件参数
 */
export const trackGTMEvent = (eventName: string, params?: GTMEventParams): void => {
  if (typeof window !== 'undefined' && window.dataLayer) {
    window.dataLayer.push({
      event: eventName,
      ...params,
    });
  }
};

// 预定义的事件名称，便于统一管理
export const GTM_EVENTS = {
  // 新注册用户
  SIGN_UP: 'sign_up',
} as const;
