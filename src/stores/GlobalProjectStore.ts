import { deleteProject, saveProject } from "@/api/aiPoster/project";
import { SaveProjectRequest } from "@/api/types/aiPoster/project";
import { makeAutoObservable } from "mobx";
import { RootStore } from "./RootStore";

export class GlobalProjectStore {

  rootStore: RootStore;
  projectCounts: number = 0;
  projectSaveSuccess: boolean = false;

  public setProjectCounts(counts: number) {
    this.projectCounts = counts;
  }

  public async createProject(info?: Partial<Omit<SaveProjectRequest, 'projectId'>>) {
    const res = await saveProject({
      ...info,
    });

    this.setProjectCounts(this.projectCounts + 1);
    return res;
  }

  public async deleteProject(projectId: number) {
    const res = await deleteProject({ projectId });
    if (res.result) {
      this.setProjectCounts(Math.min(this.projectCounts - 1));
    }
  }

  public setProjectSaveSuccess(isAwait: boolean) {
    this.projectSaveSuccess = isAwait;
  }

  get canCreateProject() {
    const userStore = this.rootStore.userStore;

    const isReady = userStore.isReady;
    const projectCounts = this.projectCounts;
    const limitCounts = userStore.userInfo?.task.aiPosterProjectLimit ?? -1;
    // console.group('can create project');
    // console.log('isReady', isReady);
    // console.log('subscribeCOnfigIsReady', subscribeConfigIsReady);
    // console.log('isVip', isVip);
    // console.log('projectCounts', projectCounts);
    // console.log('limitCounts', limitCounts);
    // console.groupEnd();
    // 用户登陆未完成 不可创建
    if (!isReady) {
      return false;
    }

    return limitCounts === 0 || projectCounts < limitCounts;
  }
  
  get isReady() {
    return this.rootStore.userStore.isReady;
  }

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this, {
      rootStore: false,
    });
  }
}