import { makeAutoObservable, runInAction } from 'mobx';
import { RootStore } from './RootStore';
import { setCookie, getCookie } from 'cookies-next';

export class LanguageStore {
  rootStore: RootStore;
  locale: string = 'en';
  messages: Record<string, string> = {};
  isLoading: boolean = false;

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this, { rootStore: false });
    
    // 在客户端初始化时加载语言
    if (typeof window !== 'undefined') {
      const savedLocale = getCookie('NEXT_LOCALE') as string;
      if (savedLocale) {
        this.changeLanguage(savedLocale);
      }
    }
  }

  setLocale(locale: string) {
    this.locale = locale;
  }

  setMessages(messages: Record<string, string>) {
    this.messages = messages;
  }

  setLoading(loading: boolean) {
    this.isLoading = loading;
  }

  t(key: string): string {
    return this.messages[key] || key;
  }

  async changeLanguage(newLocale: string) {
    try {
      this.setLoading(true);
      
      // 加载新语言的翻译
      const newMessages = await import(`../../public/locales/${newLocale}.json`);
      
      runInAction(() => {
        this.setMessages(newMessages.default);
        this.setLocale(newLocale);
        this.setLoading(false);
      });
      
      // 保存到 Cookie
      setCookie('NEXT_LOCALE', newLocale, { maxAge: 60 * 60 * 24 * 365 });
    } catch (error) {
      console.error(`Failed to load messages for locale: ${newLocale}`, error);
      this.setLoading(false);
    }
  }
} 