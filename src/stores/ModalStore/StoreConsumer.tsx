"use client";

import { useStore } from "@/contexts/StoreContext"
import { Modal } from "antd";
import { observer } from "mobx-react-lite";
import { Fragment } from "react";

function ModalStoreConsumer() {
  const { modalStore } = useStore();

  const renderModals = () => {
    const modals = modalStore.queueTop;
    return modals.map(({ id, modalProps, renderContent }) => {

      const onClose = () => {
        modalStore.closeModal(id);
      }

      return (
        <Modal key={id} {...modalProps} open={true} destroyOnClose>
          { renderContent({ onClose }) }
        </Modal>
      )
    });
  }

  return (
    <Fragment>
      {renderModals()}
    </Fragment>
  )
}

export default observer(ModalStoreConsumer);