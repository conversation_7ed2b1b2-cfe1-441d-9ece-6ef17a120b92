import { makeAutoObservable, toJS } from "mobx";
import { ModalDescription, ModalHandler, ModalProps, ModalState } from "./types";


const getModalId = (function () {
  let modalId = 1;
  return () => modalId++;
})();

export class ModalStore {
  statusQueue: Array<Array<ModalState>> = [];

  public get queueTop() {
    return this.statusQueue[0] || [];
  }

  public _createStatus(desc: ModalDescription): ModalState {

    const modalId = getModalId();

    return {
      id: modalId,
      ...desc,
    }
  }

  public _findStatusLocation(modalId: number) {
    
    let statusIndex = -1;
    const queueIndex = this.statusQueue.findIndex(modals => {
      const index = modals.findIndex(m => m.id === modalId);

      if (index > -1) {
        statusIndex = index;
      }

      return statusIndex > -1;
    });

    return [queueIndex, statusIndex];
  }

  public _createModalHandler(modalId: number) {
    return {
      close: () => this.closeModal(modalId),
      updateModalProps: (props: Partial<ModalProps>) => this.updateModalProps(modalId, props),
    }
  }

  public openModalImmediately(desc: ModalDescription): ModalHandler {
    const status = this._createStatus(desc);
    const { id: modalId } = status;

    const top = this.queueTop || [];
    top.push(status);
    this.statusQueue[0] = top;

    return this._createModalHandler(modalId);
  }

  public openModalAfterAllClose(desc: ModalDescription): ModalHandler {

    if (!this.statusQueue.length) {
      return this.openModalImmediately(desc);
    }

    const status = this._createStatus(desc);
    const { id: modalId } = status;

    const modals = this.statusQueue[1] || [];
    modals.push(status);

    this.statusQueue[1] = modals;

    return this._createModalHandler(modalId);
  }

  public appendModal(desc: ModalDescription) {
    const status = this._createStatus(desc);
    const { id: modalId } = status;
    this.statusQueue.push([status]);

    return {
      close: () => this.closeModal(modalId),
      updateModalProps: (props: Partial<ModalProps>) => this.updateModalProps(modalId, props),
    }
  }

  public closeModal(modalId: number) {
    const [queueIndex, statusIndex] = this._findStatusLocation(modalId);
    if (queueIndex < 0 || statusIndex < 0) {
      return;
    }

    const modals = this.statusQueue[queueIndex];
    modals.splice(statusIndex);

    if (!modals.length) {
      this.statusQueue.splice(queueIndex, 1);
    }
  }

  public updateModalProps(modalId: number, modalProps: Partial<ModalProps>) {
    const [queueIndex, statusIndex] = this._findStatusLocation(modalId);
    if (queueIndex < 0 || statusIndex < 0) {
      return;
    }

    const modal = this.statusQueue[queueIndex][statusIndex];

    const nextProps = {
      ...toJS(modal.modalProps),
      ...modalProps,
    }

    modal.modalProps = nextProps;
  }

  constructor() {
    makeAutoObservable(this);
  }
}