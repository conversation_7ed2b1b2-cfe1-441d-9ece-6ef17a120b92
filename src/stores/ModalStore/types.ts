import { ModalProps as InternalProps } from "antd";


export type ModalProps = InternalProps;

export type RenderContentOptions = {
  onClose: () => void;
}

export type ModalDescription = {
  modalProps: ModalProps;
  renderContent: (options: RenderContentOptions) => React.ReactNode;
}

export type ModalState = ModalDescription & {
  id: number;
}

export type ModalHandler = {
  close: () => void;
  updateModalProps: (props: ModalProps) => void;
}
