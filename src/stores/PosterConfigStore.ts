import { makeAutoObservable, runInAction, toJS } from "mobx";
import { PosterConfigType, TemplateListType } from "@/api/types/poster";
import { fetchPosterConfig } from "@/api/aiPoster/editor";
import { loadRemoteFont } from "@/utils/loadRemoteFont";
import { defaultErrorHandler } from "@/utils/defaultErrorHandler";

export type PaginationStatus = {
  /**
   * 当需要拉取第一页时 传递的cursor为空
   * 当拉取到最后一页 服务端返回空的cursor
   * 
   * 因此，需要用一个标志来区分cursor为空时
   * 1. 第一页还没有拉取完成
   * 2. 最后一页已经拉取完成
   */
  lastPageFlag: boolean;
  cursor: string;
  loading: boolean;
  loadingTimestamp: number;
}

/**
 * 模版支持分页获取
 */
export type FetchConfigOptions = {
  /**
   * refresh：从第一页开始重新拉取
   * append： 追加下一页的数据 如果已经到最后一页了不再重新拉取
   */
  mode: 'refresh' | 'append';
  /**
   * 要获取哪个分类的数据
   */
  categoryId?: number;
  /**
   * 每一页的条数
   */
  count?: number;
}

const defaultFetchConfigOptions: FetchConfigOptions = {
  mode: 'refresh',
}

export type PaginationStatusBook = Record<number, PaginationStatus | undefined>;

/**
 * 编辑器配置及模版相关
 * 
 * ！！！不要直接使用下划线开头的方法
 */
export class PosterConfigStore {
  config: PosterConfigType | null = null;
  /**
   * 所有的模范分类都刷新
   * @private
   */
  _globalRefreshing: boolean = false;

  /**
   * 分页拉取时依赖的状态
   * @private
   */
  _paginationStatusBook: PaginationStatusBook = {};

  constructor() {
    makeAutoObservable(this);
  }

  async fetchConfig(options: FetchConfigOptions = defaultFetchConfigOptions) {
    const { categoryId, mode, count } = options;

    switch(mode) {
      case 'refresh': {
        await this._refreshConfig(categoryId, count);
        if (!categoryId) {
          runInAction(() => {
            if (!this.config) {
              return;
            }
          });
        }

        return;
      }

      case 'append': {
        if (!categoryId) {
          return;
        }

        return await this._appendConfig(categoryId, count);
      }
    }
  }

  /**
   * @private
   * 
   * 刷新配置
   */
  async _refreshConfig(categoryId?: number, count?: number) {
    if (this._globalRefreshing) {
      return;
    }

    /**
     * 刷新所有的子分类
     */
    const globalRefresh = async () => {
      this._globalRefreshing = true;
      try{
        this._expirePaginationLoadingStatus();
        const res = await fetchPosterConfig({ count });
  
        runInAction(() => {
          this.config = res;
          this._paginationStatusBook = {};
          this.config.template.forEach(category => {
            this._setPaginationStatus(category.categoryId, {
              loading: false,
              cursor: category.cursor,
              lastPageFlag: !category.cursor,
            });
          })
        });
      } catch(e) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(e);
        }
  
        defaultErrorHandler(e);
      } finally {
        this._globalRefreshing = false;
      }
    }

    if (!categoryId) {
      return await globalRefresh();
    }

    /**
     * 刷新参数中指定的子分类
     */
    const localRefresh = async () => {
      try{
        this._expirePaginationLoadingStatus(categoryId);
        this._setPaginationStatus(categoryId, { loading: true, cursor: '', lastPageFlag: false,loadingTimestamp: Date.now() });
        const res = await fetchPosterConfig({ categoryId, count });
  
        runInAction(() => {
          const category = res.template.find(c => c.categoryId === categoryId);

          if (!category) {
            return;
          }

          if (!this.config) {
            this.config = res;
            return;
          }

          const config = toJS(this.config);
          const nextTemplate = config.template.map(c => {
            if (c.categoryId === category.categoryId) {
              return category;
            }

            return c;
          });
          this.config = {
            ...config,
            template: nextTemplate,
          }

          this._setPaginationStatus(category.categoryId, {
            loading: false,
            cursor: category.cursor,
            lastPageFlag: !category.cursor,
          });
        });
      } catch(e) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(e);
        }
  
        defaultErrorHandler(e);
      } finally {
        this._expirePaginationLoadingStatus();
      }
    }

    return await localRefresh();
  }

  /**
   * @private
   * 
   * 分页后将模版追加到当前列表
   */
  _appendTemplate(category: TemplateListType) {
    if (!this.config) {
      return;
    }
    const config = toJS(this.config);

    const nextTemplate = config.template.map(c => {
      if (c.categoryId === category.categoryId) {
        return {
          ...c,
          list: [...c.list, ...category.list],
        }
      }

      return c;
    });

    this.config = {
      ...config,
      template: nextTemplate,
    }
  }

  /**
   * @private
   * 
   * 设置分页参数
   */
  _setPaginationStatus(categoryId: number, status?: Partial<PaginationStatus>) {
    const preStatus: PaginationStatus = this._paginationStatusBook[categoryId] || {
      cursor: '',
      lastPageFlag: false,
      loading: false,
      loadingTimestamp: Date.now(),
    };


    this._paginationStatusBook = {
      ...toJS(this._paginationStatusBook),
      [categoryId]: {
        ...preStatus,
        ...status,
      }
    }
  }

  /**
   * @private
   * 
   * 使当前的加载状态过期
   */
  _expirePaginationLoadingStatus(categoryId?: number) {
    const time = Date.now();
    if (categoryId) {
      this._setPaginationStatus(categoryId, { loadingTimestamp: time, loading: false });
      return;
    }

    Object.keys(toJS(this._paginationStatusBook)).forEach((id: string) => {
      this._setPaginationStatus(parseInt(id), { loadingTimestamp: time, loading: false });
    });
  }

  /**
   * @private
   * 
   * 分页模式追加模版
   */
  async _appendConfig(categoryId: number, count?: number) {
    const paginationStatus = this._paginationStatusBook[categoryId];
    // 当前分类还没有拉取过 不走append逻辑
    if (!paginationStatus) {
      return await this._refreshConfig(categoryId);
    }

    const { cursor, lastPageFlag, loading } = paginationStatus;
    // 已经到最后一页了 不做处理
    if (!cursor && lastPageFlag) {
      return;
    }

    // 如果增在拉取这一页 不做处理
    if (loading) {
      return;
    }

    const tm = Date.now();

    try{
      this._setPaginationStatus(categoryId, { loading: true, loadingTimestamp: tm });
      const res = await fetchPosterConfig({
        categoryId,
        cursor,
        count,
      });
      const paginationStatus = this._paginationStatusBook[categoryId];
      if (paginationStatus?.loadingTimestamp !== tm) {
        return;
      }

      runInAction(() => {
        if (!this.config) {
          this.config = res;
        }

        const category = res.template?.find(c => c.categoryId === categoryId);
        
        if (category) {
          this._appendTemplate(category);
          this._setPaginationStatus(categoryId, {
            loading: false,
            cursor: category.cursor,
            lastPageFlag: !category.cursor
          });
        }  
      });
    } catch(e) {
      if (process.env.NODE_ENV === 'development') {
        console.warn(e);
      }

      defaultErrorHandler(e);
    } finally {
      if (tm === paginationStatus?.loadingTimestamp) {
        this._setPaginationStatus(categoryId, { loading: false });
      }
    }
  }

  get templateList() {
    // console.log('getter', toJS(this.config?.template));
    return this.config?.template ?? [];
  }

  get paginationStatusBook() {
    return this._paginationStatusBook;
  }

  public getPaginationStatusBook(categoryId: number) {
    return this.paginationStatusBook[categoryId] as PaginationStatus | undefined;
  }

  public isLoading(categoryId?: number) {
    if (!categoryId) {
      return this._globalRefreshing;
    }

    const status = this.getPaginationStatusBook(categoryId);
    return this._globalRefreshing || !!status?.loading;
  }

  public isLastPage(categoryId: number) {
    const status = this.getPaginationStatusBook(categoryId);
    return !status?.cursor && status?.lastPageFlag;
  }
}
