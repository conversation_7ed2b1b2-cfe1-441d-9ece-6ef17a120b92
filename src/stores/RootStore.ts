import { makeAutoObservable } from "mobx";
import { UserStore } from "./UserStore";
import { PosterConfigStore } from "./PosterConfigStore";
import { ModalStore } from "./ModalStore";
import { TransferActionStore } from "./TransferAction";
import { GlobalProjectStore } from "./GlobalProjectStore";
import { ReferrerStore } from "./ReferrerStore";

export class RootStore {
  userStore: UserStore;
  posterConfigStore: PosterConfigStore;
  modalStore: ModalStore;
  transferActionStore: TransferActionStore;
  globalProjectStore: GlobalProjectStore;
  referrerStore: ReferrerStore;
  t: ReturnType<typeof import("@/locales/client").useI18n> = () => "";

  constructor() {
    makeAutoObservable(this);
    this.userStore = new UserStore(this);
    this.posterConfigStore = new PosterConfigStore();
    this.modalStore = new ModalStore();
    this.transferActionStore = new TransferActionStore();
    this.globalProjectStore = new GlobalProjectStore(this);
    this.referrerStore = new ReferrerStore();
  }
}

// 创建一个单例实例
export const rootStore = new RootStore();
