/**
 * 客户端路由时 用来传递一些action
 */

import { makeAutoObservable } from "mobx";
import { Action } from "./types";

export class TransferActionStore {

  actionList = [] as Action.Body<Action.Consumer>[]

  constructor() {
    // 仅用作数据传输 与页面渲染无关 这里不需要响应式
    makeAutoObservable(this, {
      actionList: false,
    });
  }

  public pushAction(action: Action.Body<Action.Consumer>) {
    this.actionList.push(action);
  }

  public consumeAction(consumer: Action.Consumer) {
    const [remain, consume] = this.actionList.reduce(([remain, consume], action) => {
      if (action.consumer === consumer) {
        consume.push(action);
      } else {
        remain.push(action);
      }
      return [remain, consume]
    }, [[] as Action.Body<Action.Consumer>[], [] as Action.Body<Action.Consumer>[]]);

    this.actionList = remain;

    return consume;
  }
}