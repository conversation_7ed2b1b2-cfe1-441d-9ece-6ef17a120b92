import { TemplateItemType } from "@/api/types/poster";
import { ChatType } from "@/hooks/useAddChatToEditor";

export namespace Action {
  export enum Consumer {
    // 编辑器
    EditorProject = "editor/project",

    // 类型声明时测试用
    Other = "other",
  }

  export namespace EditorProject {
    export enum Type {
      /**
       * 添加模版
       */
      AddTemplate = "@add-template",
      /**
       * 模版列表定位到模版
       */
      LocateTemplate = "@locate-template",
      /**
       * 添加图片
       */
      AddImage = "@add-image",
      /**
       * 添加会话
       */
      AddChat = "@add-chat",
    }

    export type AddTemplatePayload = {
      categoryId: number;
      templateId: number;
      detail: TemplateItemType;
    };

    export type LocateTemplatePayload = {
      categoryId: number;
      templateId: number;
      detail: TemplateItemType;
    };

    export type AddImagePayload = {
      url: string;
    };

    export type AddChatPayload = {
      detail: ChatType;
    };

    export type Payload<T extends Type> = T extends Type.AddTemplate
      ? AddTemplatePayload
      : T extends Type.LocateTemplate
      ? LocateTemplatePayload
      : T extends Type.AddImage
      ? AddImagePayload
      : T extends Type.AddChat
      ? AddChatPayload
      : never;
  }

  export type Body<
    C extends Consumer,
    T = C extends Consumer.EditorProject ? EditorProject.Type : never
  > = {
    consumer: C;
    type: T;
    payload: T extends EditorProject.Type ? EditorProject.Payload<T> : never;
  };
}
