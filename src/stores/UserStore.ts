import { makeAutoObservable, runInAction } from "mobx";
import { RootStore } from "./RootStore";
import { fetchUserInfo } from "@/api/user";
import { wheeLogoutPopup } from "@/utils/account";
import {
  UserInfoResponse,
  MemberStatus,
  VipLevel,
  MeidouBalance,
} from "@/types";
import { fetchWithRetry } from "@/utils/fetchWithRetry";
import { checkMeidouBalance } from "@/api/meidou";
import EventEmitter from "events";
import {
  MODAL_PROPS,
  renderRewardContent,
  RewardType,
} from "@/components/CreditContent";

export interface User {
  id: number;
  screenName: string;
  avatar?: string;
  email?: string;
}

export class UserStore {
  rootStore: RootStore;
  currentUser: User | null = null;
  isLoading: boolean = false;
  error: string | null = null;
  authToken: string | null = null;
  UID: number | null = null;
  isLogin: boolean = false;
  userInfo: UserInfoResponse | null = null;
  mtBeanBalance: MeidouBalance | null = null;

  // 会员等级
  vipLevel: VipLevel = VipLevel.None;

  // 过期会员等级， 现有逻辑存在过期后，会员等级扔是当前等级， 所以需要记录过期时的会员等级
  expiredVipLevel: VipLevel = VipLevel.None;

  _eventBus = new EventEmitter();

  // 是否是会员
  isVipCurrent: boolean = false;
  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this, { rootStore: false, _eventBus: false });
  }
  setAuthToken(token: string) {
    this.authToken = token;
  }
  async setUID(uid: number | null) {
    if (this.UID !== uid) {
      runInAction(() => {
        this.UID = uid;
        this.userInfo = null;
      });
    }

    if (uid) {
      await this.refreshUserInfo();
      await this.refreshMtBeanBalance();
    }
  }
  setIsLogin(isLogin: boolean) {
    this.isLogin = isLogin;
  }

  async refreshUserInfo() {
    if (!this.UID) {
      return;
    }

    try {
      const userInfo = await fetchUserInfo();
      const { membership } = userInfo;
      // 如果会员状态是正常，则判断是否过期
      if (membership.status === MemberStatus.NORMAL) {
        const remainingExpirationTime =
          membership.invalidTime - membership.serverTime;

        // console.log('remainingExpirationTime=>>>>>>>>', remainingExpirationTime);
        if (remainingExpirationTime > 0) {
          this.isVipCurrent = true;
          // toDO:  设置权限，
          // console.log('membership.level=>>>>>>>>', membership);
          this.vipLevel = membership.level;
        } else {
          this.isVipCurrent = false;
          this.expiredVipLevel = membership.level;
        }

        // this.vipLevel = membership.level;
      } else if (membership.status === MemberStatus.EXPIRED) {
        this.isVipCurrent = false;
        this.vipLevel = VipLevel.None;
      }
      // console.log("user info", userInfo);
      runInAction(() => {
        this.userInfo = userInfo;
        this.isLogin = true;
        this.currentUser = userInfo.user;
        this.rootStore.globalProjectStore.setProjectCounts(
          userInfo?.task?.aiPosterCount ?? 0
        );

        const t = this.rootStore.t;
        // 如果是新用户，弹出奖励提示
        if (userInfo.tip?.showNewUserAwardTip) {
          this.rootStore.modalStore.openModalImmediately({
            modalProps: MODAL_PROPS,
            renderContent: ({ onClose }) =>
              renderRewardContent(
                RewardType.newSignup,
                userInfo.tip?.rechargeAmount || 0,
                t,
                onClose
              ),
          });
        }
        // 如果是今天第一次登录，弹出奖励提示
        if (userInfo.tip?.showTodayLoginAwardTip) {
          this.rootStore.modalStore.openModalImmediately({
            modalProps: MODAL_PROPS,
            renderContent: ({ onClose }) =>
              renderRewardContent(
                RewardType.dailyLogin,
                userInfo.tip?.rechargeAmount || 0,
                t,
                onClose
              ),
          });
        }
      });
      // 触发用户刷新事件
      this._eventBus.emit("updateUserInfo");
    } catch (e) {
      if (process.env.NODE_ENV === "development") {
        console.warn("获取用户信息出错", e);
      }
    }
  }

  async refreshMtBeanBalance() {
    if (!this.UID) {
      return;
    }

    try {
      const balance = await fetchWithRetry(checkMeidouBalance, 3, 1000);

      runInAction(() => {
        this.mtBeanBalance = balance;
      });
      // 触发获取价格描述事件
      this._eventBus.emit("updateMeidou");
    } catch (e) {
      if (process.env.NODE_ENV === "development") {
        console.warn("更新美豆失败");
      }
    }
  }

  // 绑定余额刷新
  public addUpdateMeidouListener(handler: any) {
    this._eventBus.addListener("updateMeidou", handler);
  }
  // 解绑余额刷新
  public removeUpdateMeidouListener(handler: any) {
    this._eventBus.removeListener("updateMeidou", handler);
  }

  // 绑定用户信息刷新
  public addUpdateUserInfoListener(handler: any) {
    this._eventBus.addListener("updateUserInfo", handler);
  }
  // 解绑用户信息刷新
  public removeUpdateUserInfoListener(handler: any) {
    this._eventBus.removeListener("updateUserInfo", handler);
  }

  get membership() {
    return this.userInfo?.membership;
  }

  // 用户弹窗权限
  get userModalTip() {
    return this.userInfo?.tip;
  }

  get isReady() {
    return this.isLogin && !!this.UID && !!this.userInfo;
  }

  logout() {
    this.setUID(null);
    this.setIsLogin(false);
    this.authToken = null;
    this.userInfo = null;
    this.currentUser = null;
    wheeLogoutPopup();
  }
}
