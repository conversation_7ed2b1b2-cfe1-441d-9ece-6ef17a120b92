.meitu-subscribe, .intl-subscribe [data-theme='dark']{
  /* Color styles */
    /* Color styles */
      /* Color styles */
      --background-banner: rgba(26, 26, 26, 1);
      --background-button-main: rgba(30, 30, 30, 1);
      --background-carousel-selected: rgba(127, 127, 127, 1);
      --background-carousel-unselected: rgba(52, 54, 56, 0.2);
      --background-close: rgba(30, 30, 30, 1);
      --background-close-hover: rgba(40, 40, 40, 1);
      --background-credit-price-package: rgba(26, 26, 26, 1);
      --background-credit-price-package-selected: rgba(40, 40, 40, 1);
      --background-frame: rgba(26, 26, 26, 1);
      --background-frame-highlight: rgba(40, 40, 40, 1);
      --background-gift: rgba(30, 30, 30, 1);
      --background-mask-overlay: rgba(0, 0, 0, 0.7);
      --background-popup: rgba(20, 20, 20, 1);
      --background-popup-button: rgba(30, 30, 30, 1);
      --background-primary: rgba(20, 20, 20, 1);
      --background-scroll-indicator: rgba(15, 15, 15, 1);
      --background-secondary: rgba(26, 26, 26, 1);
      --background-tab: rgba(26, 26, 26, 1);
      --background-tab-selcted: rgba(20, 20, 20, 1);
      --background-tab-selcted-hover: rgba(30, 30, 30, 1);
      --background-tab-unselcted-hover: rgba(15, 15, 15, 1);
      --background-tab-hover: rgba(30, 30, 30, 1);
      --background-tab-selected: rgba(40, 40, 40, 1);
      --background-tab-selected-hover: rgba(51, 51, 51, 1);
      --background-tertiary: rgba(40, 40, 40, 1);
      --background-toast: rgba(0, 0, 0, 0.6);
      --background-user-profile: rgba(26, 26, 26, 1);
      --background-vip-price-package: rgba(26, 26, 26, 1);
      --background-vip-price-package-hover: rgba(30, 30, 30, 1);
      --background-vip-price-package-selected: rgba(40, 40, 40, 1);
      --content-button-main: rgba(255, 255, 255, 1);
      --content-close: rgba(127, 127, 127, 1);
      --content-close-hover: rgba(255, 255, 255, 1);
      --content-credit-price-package: rgba(255, 255, 255, 1);
      /* --content-credit-price-package-secondary: rgba(127, 127, 127, 1); */
      --content-description-label: rgba(153, 153, 153, 1);
      --content-popup-button: rgba(255, 255, 255, 1);
      --content-price-package-hover-arrows: rgba(255, 255, 255, 1);
      --background-credit-pay-button: linear-gradient(90deg, #56F69E 0%, #D8EAFF 50%, #F9A9FE 100%);
      --background-checkbox: linear-gradient(90deg, #56F69E 0%, #D8EAFF 50%, #F9A9FE 100%);
      --background-credit-price-package-selected: #272C33;
      --background-scroll-indicator: #272C33;
      --content-credit-pay-button: rgba(0, 0, 0, 1);
      --content-primary: rgba(255, 255, 255, 1);
      --content-checkbox: #0F221A;
      --content-quarternary: rgba(51, 51, 51, 1);
      --content-scroll-indicator: rgba(250, 250, 250, 1);
      --content-secondary: rgba(127, 127, 127, 1);
      --content-tab: rgba(153, 153, 153, 1);
      --content-tab-hover: rgba(255, 255, 255, 1);
      --content-tab-selected: rgba(255, 255, 255, 1);
      --content-tertiary: rgba(77, 77, 77, 1);
      --content-title: rgba(255, 255, 255, 1);
      --content-toast: rgba(255, 255, 255, 1);
      --content-user-profile: rgba(153, 153, 153, 1);
      --content-vip-price-package: rgba(255, 255, 255, 1);
      --content-vip-price-package-secondary: rgba(127, 127, 127, 1);
      --stroke-banner: rgba(20, 20, 20, 1);
      --stroke-border-overlay: rgba(255, 255, 255, 0.06);
      --stroke-vip-price-package-selected: #56F69E;
      --stroke-carousel: rgba(255, 255, 255, 0.2);
      --stroke-checkbox: rgba(77, 77, 77, 1);
      --stroke-credit-pay-button-hover: rgba(255, 255, 255, 0.2);
      --stroke-credit-price-package-hover: rgba(255, 255, 255, 0.06);
      --stroke-divider: rgba(255, 255, 255, 0.04);
      --stroke-page: rgba(15, 15, 15, 1);
      --stroke-popup: rgba(30, 30, 30, 1);
      --stroke-popup-button: rgba(255, 255, 255, 0.04);
      --stroke-page: #22272E;
      --stroke-scroll-indicator: rgba(30, 30, 30, 1);
      --stroke-user-profile: rgba(30, 30, 30, 1);
      --stroke-vip-price-package: rgba(255, 255, 255, 0.06);
      --stroke-vip-price-package-popular-hover: rgba(255, 255, 255, 0.2);
      --background-rolling-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #1D1E23 100%);
      --background-credit-price-package-badge: #56F69E;
      --background-frame: #1D1E23;
      --background-primary: #1D1E23;
      --background-credit-price-package: #272C33;

      --content-credit-price-package-secondary: #A3AEBF;
      --content-credit-price-package-badge: #0F172A ;
      --content-secondary: #A3AEBF;
      --content-tertiary: #49576c;
      --content-credit-price-package-secondary: #6B7A8F;
      /* Radius styles */
      --test-radius: 4px 4px 0px 0px ;
      --radius-2: 2px;
      --radius-4: 4px;
      --radius-8: 8px;
      --radius-12: 12px;
      --radius-16: 16px;
      --radius-20: 20px;
      --radius-24: 24px;
      --radius-32: 32px;
      --radius-40: 40px;
      --radius-48: 48px;
      --radius-56: 56px;
      --radius-64: 64px;
      /* Spacing styles */
      --spacing-4: 4px;
      --spacing-8: 8px;
      --spacing-12: 12px;
      --spacing-16: 16px;
      --spacing-20: 20px;
      --spacing-24: 24px;
      --spacing-28: 28px;
      --spacing-32: 32px;
      --spacing-36: 36px;
      --spacing-40: 40px;
      --spacing-44: 44px;
      --spacing-48: 48px;
      --spacing-52: 52px;
      --spacing-56: 56px;
      --spacing-60: 60px;
      --spacing-64: 64px;
    
  
}
