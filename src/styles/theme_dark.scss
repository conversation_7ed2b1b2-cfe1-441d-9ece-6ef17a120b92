.meitu-subscribe[date-theme='default-dark'] {

  $colorBackgroundBubble: #1a1a1acc;
  $colorBackgroundButtonDisable: #43464dff;
  $colorBackgroundButtonHover: #3436380d;
  $colorBackgroundButtonMain: linear-gradient(360deg,#3086fcff,#378afcff,#3f8ffcff);
  $colorBackgroundButtonPricePackage: #3086fc1a;
  $colorBackgroundButtonPricePackageSelected: #222326ff;
  $colorBackgroundButtonPrimary: #222326ff;
  $colorBackgroundButtonSecondary: #272c33ff;
  $colorBackgroundCornerBadge: #50a0ffff;
  $colorBackgroundCornerBadgeDiscount: linear-gradient(90deg,#f11d48ff,#fa3232ff,#ffa346ff);
  $colorBackgroundCornerBadgeSecondry: #7ebeffff;
  $colorBackgroundForm: #272c33ff;
  $colorBackgroundFrame: #313438ff;
  $colorBackgroundMaskOverlay: #00000080;
  $colorBackgroundMeidouButton: linear-gradient(90deg,#ffcea3ff,#ffcea3ff,#fabe96ff);
  $colorBackgroundMeidouCornermark: linear-gradient(90deg,#ffcea3ff,#ffcea3ff,#fabe96ff);
  $colorBackgroundMeidouDescription: #3086fc0d;
  $colorBackgroundMeidouSelected: #fab5870d;
  $colorBackgroundPopupDiscountBadge: linear-gradient(90deg,#f11d48ff,#fa3232ff,#ffa346ff);
  $colorBackgroundPopupDiscountButton: #222326ff;
  $colorBackgroundPriceCornermark: linear-gradient(90deg,#ff8008ff,#ffa31fff,#ffc837ff);
  $colorBackgroundPriceCornermarkSecondary: linear-gradient(90deg,#fab587ff,#fab98eff,#fabe96ff);
  $colorBackgroundPriceDescription: #272c33ff;
  $colorBackgroundPriceHover: #313438ff;
  $colorBackgroundPricePackage: #272c33ff;
  $colorBackgroundPricePackageCountDown: linear-gradient(360deg,#3086fcff,#378afcff,#3f8ffcff);
  $colorBackgroundPricePackageCountDownDiscount: linear-gradient(90deg,#ff5560ff,#f84b30ff,#f24100ff);
  $colorBackgroundPricePackageSelected: #3086fc0d;
  $colorBackgroundPricePackageSelectedDiscount: linear-gradient(90deg,#262222ff,#262222ff,#262222ff);
  $colorBackgroundPrimary: #1d1e23ff;
  $colorBackgroundQrcodeFailure: #000000cc;
  $colorBackgroundSalesDescription: #3086fc1a;
  $colorBackgroundScrollbar: #313438ff;
  $colorBackgroundScrollbarPressed: #43464dff;
  $colorBackgroundSecondary: #272c33ff;
  $colorBackgroundSwitchButton: #3a424cff;
  $colorBackgroundSwitchMask: linear-gradient(90deg,#1c1d1f00,#1c1d1fff);
  $colorBackgroundSwitchMaskPrev: linear-gradient(90deg,#1c1d1fff,#1c1d1f00);
  $colorBackgroundTab: #16171cff;
  $colorBackgroundTabSelected: #272c33ff;
  $colorBackgroundTertiary: #313438ff;
  $colorBackgroundToast: #00000099;
  $colorBackgroundVipCornermark: linear-gradient(225deg,#ffc39eff,#ffc9aaff,#ffd6bfff);
  $colorBackgroundVipCornermarkgrey: #43464dff;
  $colorContentBadgePopupDiscount: #ffffffff;
  $colorContentButtonDisable: #ffffff4d;
  $colorContentButtonGhost: #ffffffb2;
  $colorContentButtonMain: #f7f8faff;
  $colorContentButtonOutline: #3086fcff;
  $colorContentButtonPricePackage: #3086fcff;
  $colorContentButtonPricePackageSelected: #3086fcff;
  $colorContentButtonSecondary: #ffffffff;
  $colorContentButtonTertiary: #ffffff99;
  $colorContentButtonPopupDiscount: #ffffffff;
  $colorContentCarousel: #ffffff80;
  $colorContentCarouselSelected: #ffffff33;
  $colorContentCarouselUnselected: #ffffff1a;
  $colorContentCheckBox: #3086fcff;
  $colorContentClose: #49576ccc;
  $colorContentClosePopupDiscount: #161719ff;
  $colorContentCornerBadge: #ffffffff;
  $colorContentCornerBadgeDiscount: #ffffffff;
  $colorContentLink: #56f69eff;
  $colorContentMeidou: #fab587ff;
  $colorContentMeidouButton: #57331bff;
  $colorContentMeidouCornermark: #57331bff;
  $colorContentMeidouTabSelected: #fab587ff;
  $colorContentPopupDiscountPrimary: #161719ff;
  $colorContentPopupDiscountQuaternary: #00000080;
  $colorContentPopupDiscountSecondary: #161719ff;
  $colorContentPopupDiscountSpecial: #f11d48ff;
  $colorContentPopupDiscountTertiary: #00000080;
  $colorContentPopupDiscountTitle: #161719ff;
  $colorContentPrice: #53f6b4ff;
  $colorContentPriceCornermarkText: #f7f8faff;
  $colorContentPriceCornermarkTextSecondary: #f7f8faff;
  $colorContentPriceDescription: #56f69eff;
  $colorContentPricePackageCountDown: #f7f8faff;
  $colorContentPricePackageCountDownDiscount: #ffffffff;
  $colorContentPricePackageDiscount: #f7f8faff;
  $colorContentPricePackageSelected: #56f69eff;
  $colorContentPricePackageSelectedDiscount: #f84b30ff;
  $colorContentPrimary: #ffffffff;
  $colorContentQuarternary: #49576cff;
  $colorContentSalesDescription: #3086fcff;
  $colorContentSecondary: #a3aebfff;
  $colorContentSepartor: #ffffff0d;
  $colorContentSwitchButton: #ffffffff;
  $colorContentTab: #a3aebfff;
  $colorContentTabSelected: #f7f8faff;
  $colorContentTertiary: #6b7a8fff;
  $colorContentToast: #ffffffff;
  $colorContentVipCornermark: #001533ff;
  $colorContentVipCornermark: #001533ff;
  $colorContentVipCornermarkgrey: #ffffffff;
  $colorContentWarning: #f24e4eff;
  $colorStrokeBanner: #ffffff1a;
  $colorStrokeBorderOverlay: #22272eff;
  $colorStrokeButtonMain: #f7f8faff;
  $colorStrokeButtonPricePackage: #3086fc1a;
  $colorStrokeButtonPricePackageSelected: #3086fc80;
  $colorStrokeButtonSecondry: #313438ff;
  $colorStrokeCarousel: #ffffff33;
  $colorStrokeCheckBox: #ffffff33;
  $colorStrokeDivider: #ffffff0d;
  $colorStrokeMeidou: #fab587ff;
  $colorStrokeMeidouPackageSelected: linear-gradient(90deg,#ffcea3ff,#ffcea3ff,#fabe96ff);
  $colorStrokePricePackage: #323b48ff;
  $colorStrokePricePackageSelected: #3086fcff;
  $colorStrokePricePackageSelectedDiscount: #f84b30ff;
  /* Radius styles */
  $radiustestRadius: 4px 4px 0px 0px ;
  $radiusradius0: 0px;
  $radiusradius2: 2px;
  $radiusradius4: 4px;
  $radiusradius8: 8px;
  $radiusradius12: 12px;
  $radiusradius16: 16px;
  $radiusradius20: 20px;
  $radiusradius24: 24px;
  $radiusradius32: 32px;
  $radiusradius40: 40px;
  $radiusradius48: 48px;
  $radiusradius56: 56px;
  $radiusradius64: 64px;
  $radiusradiusCapsule: 999px;
  /* Spacing styles */
  $spacingspacing0: 0px;
  $spacingspacing2: 2px;
  $spacingspacing4: 4px;
  $spacingspacing8: 8px;
  $spacingspacing12: 12px;
  $spacingspacing16: 16px;
  $spacingspacing20: 20px;
  $spacingspacing24: 24px;
  $spacingspacing28: 28px;
  $spacingspacing32: 32px;
  $spacingspacing36: 36px;
  $spacingspacing40: 40px;
  $spacingspacing44: 44px;
  $spacingspacing48: 48px;
  $spacingspacing52: 52px;
  $spacingspacing56: 56px;
  $spacingspacing60: 60px;
  $spacingspacing64: 64px;
}