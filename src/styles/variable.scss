$header-height: 48px;

$system-content-brand-primary: var(--system-content-brandPrimary, #53F6B4);
$system-content-on-primary: var(--system-content-onPrimary, #181818);
$system-content-primary: var(--system-content-primary, #fff);
$system-content-secondary: var(--system-content-secondary, rgba(163, 174, 191, 1));
$system-content-thirdary: var(--system-content-thirdary, rgba(106, 123, 148, 1));
$system-content-tertiary: $system-content-secondary;
$system-content-fourth: var(--system-content-forth, #49576C);
$system-content-fifth: var(--system-content-fifth, #303741);
$system-content-disabled: #4A5564;

$system-background-secondary: var(--system-background-secondary, #1D1E23);
$system-background-thirdary: var(--system-background-thirdary, #272C33);
$system-background-fourth: var(--system-background-fourth, #303640);
$system-background-input: var(--system-background-input, #16171C);

$system-stroke-input: var(--system-stroke-input, rgba(34, 39, 46, 1));
$system-stroke-input-default: var(--system-stroke-input-default, #22272E);
$system-stroke-button: var(--system-stroke-button, #323B48);
$system-stroke-divider: var(--system-stroke-divider, #22272E);

// Icon Colors
$icon-color-secondary: var(--icon-color-secondary, #A3AEBF);
$icon-color-thirdary: var(--icon-color-thirdary, #6B7A8F);

$background-editor-popup-default: var(--background-editorPopup-default, #1D1E23);
$background-editor-popup-hover: var(--background-editorPopup-hover, #272C33);

$stroke-editor-border-overlay: var(--stroke-editorBorderOverlay, rgba(255, 255, 255, 0.08));

// Background colors
$background-editor-popup-hover: var(--background-editorPopup-hover, #272C33);
$background-black-25: rgba(0, 0, 0, 0.25);
$background-black-30: rgba(0, 0, 0, 0.30);

// Border colors
$border-white-25: rgba(255, 255, 255, 0.25);

// System background input
$system-background-input: var(--system-background-input, #16171C);