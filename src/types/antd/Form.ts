import type { FormListOperation, FormItemProps, FormProps } from 'antd';
import type { NonUndefined } from 'utility-types';

export type FormItemRules = NonUndefined<FormItemProps['rules']>;

export type FormListAddOperation = FormListOperation['add'];
export type FormListRemoveOperation = FormListOperation['remove'];
export type FormListMoveOperation = FormListOperation['move'];

export type OnFormFinish<Values = any> = NonUndefined<
  FormProps<Values>['onFinish']
>;

export type onFinishFailed<Values = any> = FormProps<Values>['onFinishFailed'];

export type OnValuesChange<Values = any> = NonUndefined<
  FormProps<Values>['onValuesChange']
>;

export type ShouldFormItemUpdate<Values = any> = Exclude<
  NonUndefined<FormItemProps<Values>['shouldUpdate']>,
  boolean
>;
