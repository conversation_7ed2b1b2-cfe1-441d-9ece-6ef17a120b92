export enum MtccFuncCode {
  // FuncCodeImageInpaint = 'whee.jbxg.tst.v2', // 局部修改
  // FuncCodeAIModelImage = 'whee.aimodel.tst.v2', // AI 模特图
  // // FuncCodeAiVideo = 'whee.aivideo.tst.v2', // AI 视频
  // FuncCodeAiVideo = 'whee.aivideomv5.tst', // AI 视频
  // FuncCodeImageEraser = 'whee.aieliminate.tst.v2', // AI 消除
  // FuncCodeImageMagicsrHDWithRight = 'whee.chaofen.tst', // AI 超清 -快捷超清
  // FuncCodeImageMagicsrHDWithRightV2 = 'whee.aihd.tst.v2', // AI 超清 - 高清档位
  // FuncCodeImageMagicsrUHDWithRight = 'whee.aiultrahd.tst.v2', // AI 超清 - 超清档位
  // FuncCodeAiSynthesisRight = 'whee.aisynthesis.tst.v2', // AI 合成
  // FuncCodeImageModifyInpaintRight = 'whee.aiedit.tst.v2', // AI 改图 - 局部修改 (对应 whee.jbxg.tst.v2)
  // FuncCodeImageModifyExtendRight = 'whee.aienlarge.tst.v2', // AI 改图 - 图像扩展 (对应 whee.textenlarge.tst.v2)
  // FuncCodeImageModifyEraserRight = 'whee.airemove.tst.v2', // AI 改图 - AI 消除 (对应 whee.aieliminate.tst.v2)
  // FuncCodeImageCutout = 'whee.imagecutout', // AI 改图 - 抠图（抠图不用钱，所以没有FuncCode，自定义标识 whee.imagecutout）
  // FuncCodeConceptMapDesign = 'whee.conceptmapdesign.tst', //概念图设计
  // FuncCodeIpImageTraining = 'whee.ipimagetraining.tst', //IP形象训练
  // FuncCodeIpImageDrawing = 'whee.ipimagedrawing.tst', // IP形象生图
  // FuncCodePlaneTo3d = 'whee.planeto3d.tst', // 转3d
  // FuncCodeMaterial = 'whee.aifreecutoutmaterial.tst', // 免抠素材
  // FuncCodePoster = 'whee.aiposter.tst', // AI 海报
  // FuncCodePosterCutout = 'wheeai.backgroundremoval', // AI 海报 – 抠图
  // FuncCodeModelTraining = 'whee.model.tst.v2', // 模型训练
  FuncCodeText2Image = "wheeai.texttoimage", // 文生图
  FuncCodeImage2Image = "wheeai.imagetoimage", // 图生图
  FuncCodeImageExtend = "wheeai.imageextension", // 图像扩展
  FuncCodePosterText2Image = "wheeai.posterfromtext", // AI 海报 – 文生图
  FuncCodePosterImage2Image = "wheeai.posterfromimage", // AI 海报 – 图生图
  FuncCodePosterCutout = "wheeai.backgroundremoval ", //AI 海报 – 抠图
  FuncCodePosterSuper = "wheeai.imageupscaling", // AI 海报 – 超清
  FuncCodePosterTextEditing = "wheeai.textediting", // AI 海报 – 改字
  FuncCodePosterEraser = "wheeai.inpainting", // AI 海报 – 消除
  FuncCodePosterInpaint = "wheeai.imageediting", // AI 海报 – 改图
}
export interface CommonPostResponse {
  result: boolean;
  message?: string;
}

/**
 * 请求错误信息
 */
export interface CommonRequestError {
  code?: number;
  message?: string;

  /** 秀秀社区的错误额外信息（一般是中文的） */
  msg?: string;

  /** 秀秀社区的错误额外信息（一般是英文的） */
  error?: string;
}
