/** 权重 */
export enum CorpusPriorityLevel {
  High = 100,
  Medium = 200,
  Low = 300,
  None = 400
}

/** 语料来源类型 */
export enum CorpusType {
  // 来源为自定义
  Custom = 'custom',
  // 来源为预设
  Preset = 'preset'
}

/** 语料库分类 */
export interface CorpusCategory {
  /** 分类 id */
  id: number;
  /** 分类名称 */
  name: string;
  /** 下（二）级分类 */
  list?: Omit<CorpusCategory, 'list'>[];
  /** 图标图片路径 */
  icon?: string;
}

/** 语料库单元 */
export interface Corpus {
  /** id */
  id: string;
  /** 原文 */
  text: string;
  /** 译文 */
  translation?: string;
  /** 权重 */
  priority?: CorpusPriorityLevel;
}

export interface CorpusWithPriority extends Omit<Corpus, 'priority'> {
  priority?: CorpusPriorityLevel;
  /** 原始原文/id（若编辑后需要重新匹配权重使用） */
  originText?: string;
  originId?: Corpus['id'];
  originTranslation?: string;
  /** 文本框显示内容 */
  displayText?: string;
  /** 语料来源类型 */
  type?: CorpusType;
}

/** 词库正/负向筛选 */
export enum CorpusFilter {
  /** 全部 */
  ALL = 1,
  /** 正向 */
  POSITIVE = 2,
  /** 负向 */
  NEGATIVE = 3,
  /** IP创作 */
  IP_CHARACTER = 4,
  /** IP创作 - 形象定制 */
  IP_CHARACTER_CUSTOM = 5
}

export type Language = 'text' | 'translation';
