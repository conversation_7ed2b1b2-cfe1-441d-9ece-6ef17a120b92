/**
 * 编辑器可访问性
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120297 接口文档}
 */
export enum EditorAccessibility {
  /** 不可访问 */
  None = 0,

  /** 审核中 */
  Reviewing = 1,

  /** 可使用 */
  Usable = 2
}

/**
 * 编辑器各个模块访问权限控制
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120297 接口文档}
 */
export interface EditorModuleAccessibility {
  /** 是否有模型训练模块的权限 */
  hasTrainingAccess: boolean;

  /** 是否有发布到画廊的控制按钮权限 */
  hasPublishGalleryControlAccess: boolean;

  /** 是否提交过用户职业信息收集 */
  hasSubmitJobInfo: boolean;
}
