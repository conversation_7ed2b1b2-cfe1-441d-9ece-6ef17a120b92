// import { CommonConfigResponse } from '@/types/common'; 

/**
 * 首页
 * @deprecated 使用 `OverviewBanner[]` 代替
 */
export type OverviewBanners = Array<OverviewBanner>;

/**
 * 横幅链接类型
 */
export enum OverviewBannerLinkType {
  /** 普通链接 */
  Default = 1,

  /** 协议链接 */
  Scheme = 2,

  /**  点击事件 */
  click = 6
}

/**
 * 横幅
 */
export interface OverviewBanner {
  /** 横幅编号 */
  id: string;

  /** 封面 */
  cover: string;

  /** 描述 */
  description?: string;

  /** 链接类型 */
  linkType?: OverviewBannerLinkType;

  /** 链接地址 */
  link?: string;

  title?: string;
}

/**
 * 加群引导弹窗配置
 */
export interface GroupPopupConfig {
  /**
   * 弹窗id
   */
  id: number;
  /**
   * 标题
   */
  title: string;
  /**
   * 副标题
   */
  description: string;
  /**
   * 背景图
   */
  backgroundImage: string;
  /**
   * 群二维码
   */
  qrcode: string;
}

/**
 * 全局共用配置
 */
export interface CommonConfig
  extends Omit<any, 'addGroupPopup'> {
  /**
   * 加群引导弹窗配置
   */
  groupPopupConfig: GroupPopupConfig;
}

/**
 * AI创作工具卡片
 */
export interface OverviewRecommendCard {
  /** 卡片编号 */
  id: string;
  /** 封面 */
  cover: string;
  /** 悬浮封面 */
  hoverCover: string;
  /** 标题 */
  title: string;
  /** 描述 */
  description: string;
  /** 链接类型 */
  linkType?: OverviewBannerLinkType;
  /** 链接地址 */
  link: string;
  /** 功能状态 */
  status: OverviewRecommendCardStatus;
  /** 标签 角标 url */
  tag?: string;
  /** 列数 */
  colspan?: number;
}

/** 状态 1可用，2不可用(前端只展示 不可用，点击提示开发中)	*/
export enum OverviewRecommendCardStatus {
  /** 可用 */
  Enable = 1,
  /** 不可用 */
  Disabled = 2
}

/**
 * 运营活动区
 */
export interface OverviewOperation {
  /** 运营编号 */
  id: string;
  /** 标题 */
  title: string;
  /** 封面 */
  cover: string;
  /** 悬浮封面 */
  hoverCover?: string;
  /** 链接地址 */
  link?: string;
  /** 链接类型 */
  linkType?: OverviewBannerLinkType;
  /** 点击事件 仅在非link */
  onClick?: () => void;
  /** 任务类别 */
  taskCategory?: string;
}

/** 功能栏类型 */
export enum FuncType {
  /** 首页-feed流 */
  Feed = 7,
  /** 首页-单行多功能栏 */
  Card = 8
}