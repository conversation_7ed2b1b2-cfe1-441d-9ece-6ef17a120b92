export enum BenefitsType {
  // 永久有效
  Permanent = 1,

  // 临期美豆
  Temporarily = 2,

  // 跟随会员过期
  FollowMember = 3
}

export enum MemberGroupCategory {
  Meidou = 'meidou',

  Member = 'member'
}

// 触发订阅弹窗来源
export enum MemberGroupSource {
  ImageUpscale = '4',
  MissionCenter = '5'
}

export interface MeidouBalance {
  /**
   * 可用余额（美豆+美叶）
   * */
  availableAmount: number;
  /**
   * 文案提示(默认输出为：美豆可通过购买获得，购买后即永久有效。您可使用美豆购买并体验WHEE的各种权益～更多美豆玩法即将上线～)
   * */
  tips: string;

  // 会员权益描述(本月已发放美豆 xx 个，m月d日待发放xx个)
  benefitsDescription: string;

  //明细页面标题(剩余美豆)
  detailTitle: string;

  // 明细页面描述(“优先消耗临期美豆”)
  detailDesc: string;

  // 美豆明细列表
  benefitsDetail: Array<{
    // 类型：1-永久有效，前端加粗，2-临期美豆
    type: number;
    // 过期描述：N天后过期
    key: string;
    //描述：“（付费美豆可用于美图系全部产品）”
    desc: string;
    // 数量
    value: number;
  }>;
}


// 订阅商品类型
//  当商品为订阅商品时有值（0:天 1: 月 2: 季 3: 年 5:周）
export enum SubscribeType {
  // 天付
  Day = 0,
  // 月付
  Month = 1,
  // 季付
  Quarter = 2,
  // 年付
  Year = 3,
  // 周付
  Week = 5
}