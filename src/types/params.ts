import type { CanvasSize } from "@/types";

/** 风格模型 */
export interface StyleModel {
  /** 风格编号 */
  id: number;

  /** 强度 */
  styleModelWeight: number;

  /** 分类id */
  categoryIds: string;

  /** 封面 */
  images?: string;

  /** 名称 */
  name?: string;

  /** 标签 */
  typeName?: string;
}

/** 画面缩放 */
export enum ControlNetZoomMode {
  /** 拉伸 */
  Stretching,

  /** 裁剪 */
  Cropping,

  /** 自动缩放 */
  Auto,
}

export interface ControlNetParams {
  /** 输入图片 */
  inputImage: string;

  /** 模型 */
  model: string;

  /** 模块 */
  module: string;

  /** 是否启用 */
  enable: boolean;

  /** 权重 */
  weight: number;

  /** 控制时段 */
  interventionTiming: [number, number];

  /** 画面缩放 */
  zoomMode: ControlNetZoomMode;

  imageProcessParams: {
    /** 输入图片 */
    image: string;

    /** 模型 */
    model: string;

    /** 模块 */
    module: string;

    /**模型id */
    modelId: number;
  };
}

/**
 * 参数设定
 */
export interface BasicParams {
  /** 提示词 */
  prompt: string;
  /** 用户填写的原始值  未经过智能联想*/
  userPrompt?: string;

  /** 不希望出现的内容 */
  negativePrompt?: string;

  /** 创意相关性 */
  promptWeight: number;

  /** 模型编号 */
  baseModelId: number;

  /** 风格模型 */
  styleModel: StyleModel[];

  /** 画布 */
  canvas: {
    /** 比例 */
    proportion: string;

    /** 画面尺寸 */
    size: CanvasSize;
  };

  /** 数量 */
  quantity: number;

  /** 采样器同步 */
  sampler: {
    /** 采样器类型 */
    type: string;

    /** 采样步骤 */
    steps: number;
  };

  /** Seed */
  seed: number;

  /** 生成批次 */
  batches?: number;

  /** 更多细节 */
  moreDetailWeight: number;

  /** ControlNet */
  controlNet: ControlNetParams[];

  /** 图像超分 */
  superResolution?: boolean;

  /** 是否开启面部修复 AD 插件 */
  adetailerFace?: boolean;

  /** 是否开启使用推荐参数 */
  recommendParams?: boolean;
}
