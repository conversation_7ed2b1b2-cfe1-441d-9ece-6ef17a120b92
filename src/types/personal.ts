export enum MemberStatus {
  /** 非会员 */
  NOT_MEMBER = 0,
  /** 正常 */
  NORMAL = 1,
  /** 过期 */
  EXPIRED = 2,
  /** 禁用 */
  DISABLED = 3,
}

export enum VipLevel {
  None = 0, // 非会员
  Basic = 1, // 基础会员
  Plus = 2, // 高级会员
  Super = 3, // 超级会员
}

export interface MembershipDescResponse {
  /**
   * @deprecated
   * 是否是vip会员（过期会员也是true）
   */
  isVip: boolean;
  /**
   * 有效期起始时间（不是会员返回0）单位:毫秒，示例：1692948009794
   * */
  validTime: number;
  /**
   * 有效期时间结束时间（不是会员返回0）单位:毫秒
   * */
  invalidTime: number;
  /**
   * 服务器时间，单位:毫秒
   * */
  serverTime: number;
  /**
   * 会员状态 1-正常 2-过期 3-禁用
   * */
  status: MemberStatus;
  /**
   * 展示文案
   */
  desc: string;
  /**
   * 会员等级
   */
  level: VipLevel;
}

/**
 * 账号信息
 */
export interface UserInfoResponse {
  user: {
    id: number;
    screenName: string;
    avatar: string;
  };
  task: {
    // 发布作品数量
    feedCount: number;
    // 发布模型数量
    modelCount: number;
    // 收藏作品数量
    favoriteFeedCount: number;
    // 收藏模型数量
    favoriteModelCount: number;
    // 改图编辑器项目数量
    projectCount: number;
    // 海报项目数量
    aiPosterCount: number;
    /**
     * 当前用户vip等级可以创建的项目数
     * 如果没有数量限制（高级会员），这里服务端下发0
     */
    aiPosterProjectLimit: number;
  };
  membership: MembershipDescResponse;
  tip: {
    // 是否展示新用户奖励
    showNewUserAwardTip: boolean;
    // 是否展示今日登录奖励提示
    showTodayLoginAwardTip: boolean;
    // 金额
    rechargeAmount: number;
  };
}

export interface UserInfo {
  // uid
  uid: number;
  // 用户名
  screenName: string;
  // 头像
  avatarUrl: string;
  // 发布作品数量
  feedCount: number;
  // 发布模型数量
  modelCount: number;
  // 收藏作品数量
  favoriteFeedCount: number;
  // 收藏模型数量
  favoriteModelCount: number;
  // 改图编辑器项目数量
  projectCount: number;
}

// 个人主页以及tab
export enum TabItemsType {
  Works = "works",
  Models = "models",
  Collections = "collections",
  Projects = "projects",
}
