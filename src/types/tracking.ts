/** 切换事件类型: 下一张 | 上一张 | 缩略图点击 */
export type SwitchType = 'next' | 'last' | 'click';

/** 页面进入来源 */
export enum Source {
  /** 首页左侧导航栏 */
  Sider = 1,

  /** 首页功能格进入 */
  RecommendCard = 2,

  /** 链接跳转 */
  Link = 3
}

/**
 * 模块参数（页面级别，供埋点使用）
 */
export enum AppModuleParam {
  /**首页 */
  Overview = 'home_page',
  /** 灵感 */
  Art = 'gallery_explore',
  /** 文生图 */
  TextToImage = 'text_to_image',
  /** 图生图 */
  ImageToImage = 'image_to_image',
  /** 创意文本 */
  TextGenerator = 'creative_thesaurus',
  /** 风格模型训练 */
  StyleModelTraining = 'model_training',
  /** 教程 */
  Tutorial = 'learning_center',
  /** 图像扩展 */
  ImageExtension = 'image_extension',
  /** 下载页面 */
  DownloadPage = 'app_download',
  /** 局部修改 */
  ImagePartialRepaint = 'local_modification',
  /** 个人主页 */
  Personal = 'personal_center',
  /** 搜索结果页 */
  SearchResult = 'search_results',
  /** AI 模特	 */
  ImageAIModel = 'ai_model',
  /** AI 生视频 */
  ImageToVideo = 'ai_to_video',
  /** AI 快捷超清 */
  ImageUpscale = 'ai_ultra_hd',
  /** AI 消除 */
  ImageEraser = 'ai_clear',
  /** AI 改图编辑器 */
  ImageEditor = 'ai_modification',
  /** IP形象定制 概念图设计 */
  IPCharacterConcept = 'concept_design',
  /** IP形象定制 形象设计 */
  IPCharacterCustomization = 'portrait_design',
  /** IP 形象定制	 */
  IPDesign = 'ip_design',
  /** AI 免抠素材	 */
  Material = 'ai_cutout',
  /** AI 海报	 */
  Poster = 'ai_poster'
}

/**
 * 创作同款的点击来源
 */
export enum CreateLocationType {
  // feed 列表
  Feed = 'feed_page',
  // 详情弹窗
  Detail = 'detail_page'
}


/**
 * 模板曝光位置
 */
export enum TemplateExposureLocation {
  /** 未登录首页 */
  NoLoginHome = 1,
  /** 登录首页 */
  LoginHome = 2,
  /** 已登录模板聚合页	 */
  AllTemplate = 3,
  /** 功能摆渡页	 */
  FunctionDock = 4,
  /** 编辑器内部（右侧模板）	 */
  Editor = 5,
}

/**
 * 教程曝光位置
 */
export enum TutorialExposureLocation {
  /** 教程页 */
  TeachingCenter = 'teaching_center',
  /** 编辑器 */
  Editor = 'editor',
}

