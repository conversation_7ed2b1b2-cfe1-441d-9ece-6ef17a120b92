import { getConfig } from "@/config";
import cookieUtils from "js-cookie";
import { rootStore } from "@/stores/RootStore";
import { trackAccountId } from "@/services/tracer";
import { GTM_EVENTS, trackGTMEvent } from "@/services/tracer/gtm";
import { getSessionStorageItem } from "@meitu/util";
import { GOOGLE_CLICK_IDENTIFY } from "@/services/tracer/globalParamsTracer";
import { isRootPath } from "../isRootPath";

const config = getConfig();

// const ACCOUNT_CLIENT_ID = config.ACCOUNT_CLIENT_ID;

function getEnvValue(env?: string) {
  // 1 test, 0 线上（默认）, 2 beta
  let value;
  switch (env) {
    case "release":
      value = 0;
      break;
    case "beta":
      value = 2;
      break;
    default:
      value = 1;
  }
  return value;
}

let MtAccountSDK: any;
let ROUTE_ACCOUNT: any;

export const initWheeMTLogin = async () => {
  if (MtAccountSDK) return MtAccountSDK;

  // console.log("initWheeMTLogin", getEnvValue(process.env.NEXT_PUBLIC_ENV));
  const { MeituAccount, EmailLoginMethod } = await import(
    "@meitu/account-intl"
  );
  MtAccountSDK = MeituAccount({
    env: getEnvValue(process.env.NEXT_PUBLIC_ENV), // 指定环境 0 正式环境（默认）、1 Preview 测试环境、2、Beta 拟真测试环境
    clientId: config.ACCOUNT_CLIENT_ID,
    // gid: '', // TODO 补充gid
    clientLanguage: "en",
    // debug: true, // 可选；是否开启调试模式
    // sourceFrom: 'sdk-dev-demo', // 可选；自定义来源统计标识
    clientConfigs: {
      emailLoginMethod: EmailLoginMethod.VerifyCode,
      googleClientId: config.GOOGLE_CLIENT_ID,
      useGoogleAutoSelect: false,
    },
    syncDomains: true, // 可选；是否开启跨域名（非 *.meitu.com 域）同步登录和退出状态（HTTPS 域名才有效，目前只支持 Chrome 系浏览器，不支持 Safari 和火狐浏览器
  });

  const isLogin = await MtAccountSDK.isLogin();
  if (isLogin) {
    // console.log("已登录");
    rootStore.userStore.setAuthToken(MtAccountSDK.accessToken);
    // 在setUID时会刷新其他状态 需要调用接口 因此提前设置token
    cookieUtils.set(config.AUTH_TOKEN, MtAccountSDK.accessToken, {
      expires: 1000,
    });

    // 设置用户ID 会触发me.json  在非登陆态首页，不设置
    if (!isRootPath()) {
      rootStore.userStore.setUID(MtAccountSDK.user.id);
      rootStore.userStore.setIsLogin(true);
      trackAccountId(MtAccountSDK.user.id);
    }

    //   cookieUtils.set(AUTH_TOKEN, MtAccountSDK.accessToken);
    //   setAccessToken(AccountSDK.accessToken);
    //   setUID(AccountSDK.user.id);
  } else {
    // console.log("未登录");
    //   cookieUtils.remove(AUTH_TOKEN);
    //   removeAccessToken();
    //   removeUID();
    rootStore.userStore.setIsLogin(false);
    rootStore.userStore.setAuthToken("");
    rootStore.userStore.setUID(null);
    cookieUtils.remove(config.AUTH_TOKEN);
  }
  return MtAccountSDK;
};

export const wheeLogoutPopup = () => {
  // console.log("退出登录");
  MtAccountSDK.logout();
  rootStore.userStore.setIsLogin(false);
  rootStore.userStore.setAuthToken("");
  rootStore.userStore.setUID(null);
  cookieUtils.remove(config.AUTH_TOKEN);
  rootStore.referrerStore.clearPreviousPath();
};

export const wheeLoginPopup = async ({
  useLoginMethod = "third-party",
  afterLogin,
  loginSuccess,
  setUserStore = true,
}: {
  afterLogin?: () => void;
  loginSuccess?: (accessToken?: string) => void;
  useLoginMethod?: "third-party" | "google-one-tap";
  setUserStore?: boolean; // 是否设置store
}): Promise<any> => {
  const { openLoginPopup } = await import("@meitu/account-login-popup-intl");
  const { ThirdPartyPlatform } = await import("@meitu/account-intl");
  return new Promise((resolve, reject) => {
    openLoginPopup({
      // ...登录弹窗配置选项，详见：LoginPopupProps
      // 第三方登录列表 第一期先支持Google
      thirdPartyAccounts: [
        ThirdPartyPlatform.Google,
        ThirdPartyPlatform.Email,
        // ThirdPartyPlatform.Facebook
        // ThirdPartyPlatform.Apple,
      ],
      useLoginMethod,
      useGoogleFedCM: true,
      force: true,
      switch: true, // 切换账号，会先清除登录状态
      containerClass: "whee-account-login-popup",
      googleClientId: config.GOOGLE_CLIENT_ID,
      // showMeituXiuXiuQRCode: false,
      leftSide: () =>
        `<div style={{ width: 324, height: '100%', minHeight: '500px', background: 'url(\"https://avatar.stariidata.com/avatar/674d50ac70bd290fcb45ep9230.jpg\") top center no-repeat', backgroundSize: 'cover' }}></div>`,
      // 隐藏关闭按钮
      // showCloseButton: true,
      // closeable: true,
      onClose() {
        // ATTENTION: 登录成功和主动关闭弹窗都会执行
        afterLogin?.();
        // 关闭弹窗后的自定义处理，略...
      },
      onLoginSuccess: async (result: any, isRegister: boolean) => {
        cookieUtils.set(config.AUTH_TOKEN, result.accessToken, {
          expires: 1000,
        });
        rootStore.userStore.setAuthToken(result.accessToken);
        // 设置用户ID 会触发me.json
        setUserStore && (await rootStore.userStore.setUID(result.uid));
        rootStore.userStore.setIsLogin(true);
        const gclid = getSessionStorageItem(GOOGLE_CLICK_IDENTIFY);
        loginSuccess?.(result.accessToken);
        if (isRegister && gclid) {
           // 新注册用户上报事件到 GA 配合渠道投放归因
          trackGTMEvent(GTM_EVENTS.SIGN_UP);
        }
        resolve(result);
      },
      onLogoutSuccess(silent: any) {
        // console.log("onLogoutSuccess ====>", { silent });
      },
      onLoginError: reject,
      onLogoutError: reject,
    });
  });
};

export const wheeToAccount = () => {
  MtAccountSDK.openPage("/account", {}, { blank: true });
};

export const clearAccount = () => {
  // lo
  localStorage.removeItem("deepseek_prompt_optimization_text");
  localStorage.removeItem("deepseek_prompt_optimization_image");
};
