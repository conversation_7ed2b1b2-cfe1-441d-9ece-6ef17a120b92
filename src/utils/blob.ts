import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import _ from 'lodash';

/**
 * 支持文件类型
 */
export enum CompressionFormat {
  /** zip */
  ZIP = 'zip',
  /** 单文件 */
  SINGLE = 'single'
}

/**
 * 检查图片宽高比
 * @param file 文件
 * @param aspectRatio 宽高比
 * @returns Promise<Boolean>
 */
export const checkAspectRatio = (file: File, aspectRatio: number) => {
  return new Promise<Boolean>((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const { width, height } = img;
      const ratio = width / height;

      if (ratio > aspectRatio || 1 / ratio > aspectRatio) {
        resolve(false);
      } else {
        resolve(true);
      }
    };

    img.onerror = reject;
  }).catch((err) => {
    // console.log(err);
  });
};

/**
 * 检查图片最小边
 * @param file 文件
 * @param minEdge 限制的最小边
 * @returns Promise<Boolean>
 */
export const checkLimitMinEdge = (file: File, minEdge: number) => {
  return new Promise<Boolean>((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const { width, height } = img;

      if (width > minEdge && height > minEdge) {
        resolve(true);
      } else {
        resolve(false);
      }
    };

    img.onerror = reject;
  });
};

/**
 * 检查图片最长边
 * @param file 文件
 * @param maxEdge 限制的最长边
 * @returns Promise<Boolean>
 */
export const checkLimitMaxEdge = (file: File, maxEdge: number) => {
  return new Promise<Boolean>((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const { width, height } = img;

      if (width <= maxEdge && height <= maxEdge) {
        resolve(true);
      } else {
        resolve(false);
      }
    };

    img.onerror = reject;
  });
};

export const getImageDimensions = (url: string) => {
  return new Promise<{ ratio: number; width: number; height: number }>(
    (resolve, reject) => {
      const img = new Image();
      img.src = url;
      img.onload = () => {
        const { width, height } = img;
        const ratio = width / height;
        resolve({ ratio, width, height });
      };

      img.onerror = reject;
    }
  );
};

/**
 * 下载文件
 * 1.单张图片 (fileOrBlob: string | Blob, compressionFormat, fileName)
 * 2.多张图片 (fileOrBlob: string[], compressionFormat, fileName), 并压缩成zip包, 数组要是只有一个文件特殊处理
 * @param {string | Blob | string[]} fileOrBlob 文件
 * @param {string} fileName 完整文件名(多数情况下从路径中截取) -- Blob/多张图片时且需要压缩zip传入
 * @param {CompressionFormat} compressionFormat 压缩文件类型
 */
export const downloadFile = async (
  fileOrBlob: string | Blob | string[],
  fileName?: string,
  compressionFormat?: CompressionFormat
) => {
  try {
    if (!fileOrBlob || (Array.isArray(fileOrBlob) && fileOrBlob.length <= 0)) {
      return;
    }

    if (fileOrBlob instanceof Blob) {
      saveAs(fileOrBlob, fileName);
      return;
    }

    // 多文件
    if (Array.isArray(fileOrBlob)) {
      if (
        fileOrBlob.length > 1 &&
        compressionFormat === CompressionFormat.ZIP
      ) {
        const blob = await createZipFile(fileOrBlob, fileName);
        saveAs(blob, fileName);
        return;
      }

      for (let i = 0; i < fileOrBlob.length; i++) {
        const filePath = fileOrBlob[i];
        const blob = await fetchFileBlob(filePath);
        const finalFileName =
          _.last(new URL(filePath).pathname.split('/')) ??
          `${i + 1}_${fileName}`;
        saveAs(blob, finalFileName);
      }
    } else {
      const filePath = fileOrBlob;
      const blob = await fetchFileBlob(filePath);
      const finalFileName =
        fileName ?? _.last(new URL(filePath).pathname.split('/'));
      saveAs(blob, finalFileName);
    }
  } catch (error) {
    throw new Error('Error download images:' + error);
  }
};

/**
 * 获取文件blob
 * @param {string} filePath
 * @returns Promise<Blob>
 */
export const fetchFileBlob = async (filePath: string) => {
  const response = await fetch(filePath.replace(/^http:/, 'https:'), {
    mode: 'cors'
  });
  return await response.blob();
};

/**
 * 读取文件为base64
 * @param file 文件
 * @returns  Promise<string>
 */
export const readFileAsDataURL = (blob: Blob): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.addEventListener(
      'load',
      () => resolve(reader.result as string),
      false
    );
    reader.readAsDataURL(blob);
  });
};

/**
 * 创建zip包
 * @param {string[]} filePaths
 * @param {string} fileName
 * @returns Promise<Blob>
 */
export const createZipFile = async (filePaths: string[], fileName?: string) => {
  const zip = new JSZip();

  // 请求每个文件并将其添加到 zip 中
  const fetchAndAddToZip = async (filePath: string, index: number) => {
    const response = await fetch(filePath);
    const blob = await response.blob();
    zip.file(
      _.last(new URL(filePath).pathname.split('/')) ??
        `${index + 1}_${fileName}`,
      blob
    );
  };

  // 等待所有文件被获取并添加到 zip 中
  const fetchFilePromises = (filePaths as string[]).map(fetchAndAddToZip);

  await Promise.all(fetchFilePromises);

  return zip.generateAsync({ type: 'blob' });
};

/**
 * url转base64
 * @param url  图片地址
 * @returns  图片base64
 */
export async function urlToBase64(url: string) {
  try {
    const blob = await fetchFileBlob(url);

    return await readFileAsDataURL(blob);
  } catch (error: any) {
    console.error(error);

    throw new Error('An error occurred: ' + error?.message);
  }
}
