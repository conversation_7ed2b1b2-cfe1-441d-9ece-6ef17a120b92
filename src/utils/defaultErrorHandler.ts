import { AxiosError, isAxiosError } from 'axios';
import { CommonRequestError } from '@/api/types/common';
import toast from '@/components/Toast';

let isShowingNetworkMessage = false;
function messageNetworkError() {
  if (isShowingNetworkMessage) {
    return;
  }

  toast.error('No network, please check the network settings.').then(() => {
    isShowingNetworkMessage = false;
  });

  isShowingNetworkMessage = true;
}

export function defaultErrorHandler(
  err: any,
  additionalErrorHandling?: (err?: CommonRequestError) => string | void
) {
  if (isAxiosError(err)) {
    const typedError = err as AxiosError<CommonRequestError>;

    const responseData = typedError.response?.data;

    const msg = additionalErrorHandling?.(responseData);

    if (!!additionalErrorHandling) {
      if (msg) toast.error(msg);
      return;
    }

    if (
      !navigator.onLine ||
      err.code === 'ERR_NETWORK' ||
      err.response?.status === 504
    ) {
      messageNetworkError();
      return;
    }

    responseData &&
      toast.error(
        responseData?.message ??
          responseData?.msg ??
          responseData?.error ??
          `[No Response]`
      );
    return;
  }

  throw err;
}
