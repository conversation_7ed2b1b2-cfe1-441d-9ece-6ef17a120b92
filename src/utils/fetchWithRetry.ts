import { defaultErrorHand<PERSON> } from './defaultErrorHandler';

type FetchFunction<Result> = () => Promise<Result>;

export async function fetchWithRetry<Result>(
  fetchFn: FetchFunction<Result>,
  maxRetries: number,
  initialDelay: number
): Promise<Result | null> {
  let retries = 0;
  let retryDelay = initialDelay;

  while (retries < maxRetries) {
    try {
      const result = await fetchFn();
      return result; // Return result if successful
    } catch (error) {
      defaultErrorHandler(error);
      console.error(`Attempt ${retries + 1} failed:`, error);
      retries++;
      // eslint-disable-next-line no-loop-func
      await new Promise((resolve) => setTimeout(resolve, retryDelay));

      // Increase the delay exponentially
      retryDelay *= 2;
    }
  }

  console.error(`Maximum number of retries (${maxRetries}) reached.`);
  return null; // Return null if retries are exhausted
}
