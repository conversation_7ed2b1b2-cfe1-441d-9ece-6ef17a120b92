type FontCache = {
  /**
   * 字体子集
   */
  subset: string | null;
  /**
   * 字体全集
   */
  total: string | null;
};

/**
 * 字体加载url缓存
 */
const loadedFonts = new Map<string, FontCache>(); // 用于跟踪已加载的字体

export function getFontUrl(fontName: string) {
  const cache = loadedFonts.get(fontName);

  return cache?.total || cache?.subset;
}

/**
 * 加载字体
 * @param fontName 字体名
 * @param fontUrl  字体链接
 * @param isSubSet 是否为子集
 * @returns
 */
export const loadRemoteFont = (
  fontName: string,
  fontUrl: string,
  isSubSet = false
) => {
  const cache = loadedFonts.get(fontName);

  // 如果已加载全量包 无需加载子集包
  if (cache?.total) {
    return Promise.resolve();
  }

  // 注意：这里即使加载过子集包 也需要重新加载子集包 因为每次加载时 子集包包含的文字都不同

  return new Promise((resolve, reject) => {
    const font = new FontFace(fontName, `url(${fontUrl})`);

    font
      .load()
      .then(() => {
        document.fonts.add(font);

        const cache = loadedFonts.get(fontName) ?? {
          subset: null,
          total: null
        };
        cache[isSubSet ? 'subset' : 'total'] = fontUrl;
        loadedFonts.set(fontName, cache);

        resolve('success'); // 加载成功
      })
      .catch((error) => {
        reject(error); // 加载失败
      });
  });
};
