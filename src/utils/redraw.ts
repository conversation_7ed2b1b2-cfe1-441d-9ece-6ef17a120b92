import { canvasToBlob } from "./canvasToBlob";
import { createImage } from "./cropImage";

type RedrawOptions = {
  width: number;
  height: number;
}

export async function redrawImage(blob: Blob, { width, height }: RedrawOptions): Promise<Blob> {
  const canvas = document.createElement("canvas");
  const objectURL = URL.createObjectURL(blob);
  try {
    const img = await createImage(objectURL);
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext("2d");
    if (!ctx) {
      return blob;
    }

    ctx.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, width, height);
    return await canvasToBlob(canvas, { type: blob.type })
  } catch(e) {
    if (process.env.NODE_ENV === 'development') {
      console.error(e);
    }

    return blob;
  } finally {
    URL.revokeObjectURL(objectURL);
  }
}
