import { toCamelCase } from "@meitu/util"

export const transformPosterConfigStructByAiPosterConfig = (template: Array<Record<string, any>>) => {
    const newTemplate = []
    for (const item of template) {
      const newItem = {
        categoryId: item.category_id,
        categoryName: item.category_name,
        cursor: item.cursor,
        list: item.list.map((listItem: Record<string, any>) => ({
          ...toCamelCase(listItem),
          newConfig: listItem.new_config,
        }))
      }
      newTemplate.push(newItem)
    }
    return newTemplate
  }