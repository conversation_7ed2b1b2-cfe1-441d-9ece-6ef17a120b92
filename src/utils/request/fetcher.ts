// 封装的公共请求方法
'use server'
import qs from "qs";
import { cookies } from 'next/headers'
import { getConfig } from "@/config";

const config = getConfig();

const getGid = () => {
  return "193aa4ecada268a-0d67494e745f7e-1e525636-1484784-193aa4ecadb8517";
};


// 定义通用的 Fetcher 响应结构
interface FetcherResponse<T> {
  data: T;
  code: number;
  reqid: string;
  message?: string;
}

/**
 * 封装的 fetch 函数，自动处理 Cookie、公共参数和错误
 * @param resource 请求资源 URL 或 Request 对象
 * @param init 请求配置，同原生 fetch 的 init 参数
 */
export const fetcher = async <T>(
  resource: RequestInfo,
  init?: RequestInit,
  options?: {
    withCookies?: boolean;
  }
): Promise<FetcherResponse<T>> => {
  let token: string | undefined;
  let locale: string;

  // 🟡 只有需要 Cookie 时才调用 cookies()
  if (options?.withCookies) {
    try {
      const cookieStore = cookies();
      token = cookieStore.get('auth_token')?.value;
      locale = cookieStore.get('Next-Locale')?.value || 'en';
    } catch (e) {
      console.error("访问 Cookie 失败 (可能在非请求上下文中):", e);
      token = '';
      locale = 'en';
    }
  } else {
    token = '';
    locale = 'en'; // 默认语言
  }

  // 定义需要添加到请求中的公共参数
  const commonParams = {
    client_id: config.ACCOUNT_CLIENT_ID,
    gnum: getGid(),
    client_language: locale,
  };

  let finalResource: RequestInfo = resource;
  let finalInit: RequestInit = init ? { ...init } : {};

  // 始终将公共参数添加到 URL 查询字符串
  const originalUrl = typeof resource === 'string' ? resource : resource.url;
  const [baseUrl, queryString] = originalUrl.split("?");
  const existingParams = qs.parse(queryString || '');
  // 合并现有参数和公共参数，公共参数会覆盖同名现有参数
  const allParams = { ...existingParams, ...commonParams }; 
  finalResource = `${baseUrl}?${qs.stringify(allParams)}`;

  // 打印最终的请求信息用于调试
  // console.log("最终请求 URL:", finalResource);
  // console.log("最终请求 Init:", finalInit);

  // 执行 fetch 请求
  const res = await fetch(finalResource, {
    ...finalInit,
    headers: {
      ...finalInit.headers,
      // 添加认证 Token
      'Access-Token': token || '',
    },
  });

  // 处理 HTTP 错误状态
  if (!res.ok) {
    let errorRes;
    try {
      errorRes = await res.json(); // 尝试解析 JSON 格式的错误信息
    } catch { 
       // 如果响应体不是 JSON 或解析失败，使用状态文本
      errorRes = { message: res.statusText || 'request failed' };
    }
    // 创建一个包含错误信息的 Error 对象
    const error = new Error(errorRes?.message ?? `HTTP error! status: ${res.status}`);
    // 可以考虑附加更多信息到 error 对象上，例如 status code
    // (error as any).status = res.status;
    // (error as any).response = errorRes;
    throw error; // 抛出错误，由调用方处理
  }

  // 返回 JSON 格式的响应数据
  return res.json();
};
