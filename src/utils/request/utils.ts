/* eslint-disable no-param-reassign */
import { Base64 } from 'js-base64';

import type { AxiosRequestConfig } from 'axios';
import { getChannelKey, getFirstUrl } from '@/utils/channelUtil';
import { toCamelCase } from '@meitu/util';


// import { getLocale } from '@/utils/localeUtil';
// import { getClientLocale } from '@/common/official/officialStore/locale';
// import { getAccessToken, getUID } from './accountUtil';

const getFieldName = (config: AxiosRequestConfig) => (config.method?.toLowerCase() === 'post' ? 'data' : 'params');

/**
 * Add request unique id to params
 * @param config
 */
export const addRequestUniqueID = (config: AxiosRequestConfig) => {
  const id = `${new Date().getTime()}_${Math.floor(1000000 * Math.random())}`;
  const key = 'request_unique_id';

  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  config[fieldName][key] = id;
};

export const setContentType = (config: AxiosRequestConfig) => {
  config.headers = config.headers || {};
  if (config.method?.toLowerCase() === 'post') {
    config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
  }
};

export const addClientID = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  // 优先使用传入的 client_id
  config[fieldName].client_id = config[fieldName].client_id || '';
};

export const addGID = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  config[fieldName].gnum = '';
};

export const addUID = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
//   config[fieldName].uid = getUID();
config[fieldName].uid = '';
};

export const addAppID = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  config[fieldName].app_id = ''
};

export const addChannelID = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  config[fieldName].client_channel_id = getChannelKey();
};

export const addVersion = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  config[fieldName].version = '';
};

export const addReleaseTime = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  config[fieldName].release_time = process.env.WEBPACK_RELEASE_TIME;
};

export const addClientLAnguage = (config: AxiosRequestConfig) => {
  const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
  config[fieldName] = config[fieldName] || {};
  config[fieldName].client_language = ''
};

export const addHeaderToken = (config: AxiosRequestConfig) => {
  config.headers = config.headers || {};
//   config.headers['Access-Token'] = getAccessToken();
    config.headers['Access-Token'] = '';

};

export interface CustomRequestConfig {
  checkAuth?: boolean;
  function?: {
    name: string;
    effect?: number;
  };
  media_type?: string;
  res_media_type?: string;
  ext_info?: {
    duration?: number;
    number: number;
  };
}

export const addXMtccHeader = (config: AxiosRequestConfig & CustomRequestConfig) => {
  // x-mtcc-client请求头只需要在POST请求添加
  if (config.method?.toLowerCase() === 'post') {
    const fieldName = getFieldName(config); // 请求方法不同, 写到不同的位置
    const configData = config[fieldName];
    config.headers = config.headers || {};
    const mtccData = {
      app_id: configData.app_id,
      country_code: 'CN',
      os_type: 'web',
      gnum: configData.gnum,
      // biz_id: configData.app_id,
      order_id: configData.request_unique_id,
      uid: configData.uid,

      function: config.function,
      media_type: config.media_type,
      res_media_type: config.res_media_type,
      ext_info: {
        app_version: configData.version,
        ...config.ext_info,
      },
    };

    config.headers['x-mtcc-client'] = Base64.encode(JSON.stringify(mtccData));
  }
};

// 透传渠道归因请求头标识，服务端获取
export const addChannelSourceHeader = (config: AxiosRequestConfig) => {
  if (config.method?.toLowerCase() === 'post' && config.headers) {
    const channel = getChannelKey();
    const firstUrl = getFirstUrl();
    if (channel) {
      config.headers.channel = channel;
    }
    if (firstUrl) {
      config.headers.first_url = firstUrl;
    }
  }
};


export const transformPosterConfigStruct = (template: Array<Record<string, any>>) => {
  const newTemplate = []
  for (const item of template) {
    const newItem = {
      categoryId: item.category_id,
      categoryName: item.category_name,
      categoryIcon: item.category_icon,
      hoverCategoryIcon: item.hover_category_icon,
      cursor: item.cursor,
      list: item.list.map((listItem: Record<string, any>) => ({
        ...toCamelCase(listItem),
        newConfig: listItem.new_config,
      }))
    }
    newTemplate.push(newItem)
  }
  return newTemplate
  }

export const getUserHeaders = (headers: Headers) => {
  const userAgent = headers.get("user-agent");
  const xForwardedFor = headers.get("x-forwarded-for");
  const xRealIp = headers.get("x-real-ip");
  const customHeaders: Record<string, string> = {};
  if (userAgent) {
    customHeaders["User-Agent"] = userAgent;
  }
  if (xForwardedFor) {
    customHeaders["X-Forwarded-For"] = xForwardedFor;
  }
  if (xRealIp) {
    customHeaders["X-Real-Ip"] = xRealIp;
  }
  return customHeaders;
};
