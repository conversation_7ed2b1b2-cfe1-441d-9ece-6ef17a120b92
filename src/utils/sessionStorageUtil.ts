// 禁用eslint检查
/* eslint-disable */
type SessionStorageItemKey =
'matrix_channel'
| 'channel'
| 'first_url'
| 'cutoutData';

function setItem(key: SessionStorageItemKey, value: string): void {
  // eslint-disable-next-line ban/ban
  sessionStorage.setItem(key, value);
}

function getItem(key: SessionStorageItemKey): string | null {
  // eslint-disable-next-line ban/ban
  return sessionStorage.getItem(key);
}

function removeItem(key: SessionStorageItemKey): void {
  // eslint-disable-next-line ban/ban
  return sessionStorage.removeItem(key);
}

const sessionStorageUtil = {
  setItem,
  getItem,
  removeItem,
};

export default sessionStorageUtil;
