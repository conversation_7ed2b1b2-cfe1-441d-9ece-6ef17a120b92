import type {
  ToMTCCRequestHeadersOptions,
  EntrancePositionParam,
  MTCCRequestHeaders,
  CountryCode,
  MTCCClient,
  ClientId
} from './types';

import { toClientEntrancePosition, toClientFunction } from './utils';
import { toSnakeDict } from '@meitu/util';
import { encode } from 'js-base64';

/**
 * 转换为云处理成本分摊请求头
 * @param clientId 账号客户端编号
 * @param countryCode 国家/地区码
 * @param entrancePosition 入口位置
 * @param options 可配置项
 *
 * @example
 * toMTCCRequestHeaders('0806449', 'CN', 910);
 * // => { "x-mtcc-client": "<base64>" }
 *
 * @example
 * toMTCCRequestHeaders('0806449', 'CN', ['910'], { function: 1007 });
 * // => { "x-mtcc-client": "<base64>" }
 *
 * @example
 * toMTCCRequestHeaders('0806449', 'CN', { level: ['910'] }, { function: { name: '00009' }, gid: '0806449' });
 * // => { "x-mtcc-client": "<base64>" }
 */
export function toMTCCRequestHeaders(
  clientId: ClientId,
  countryCode: CountryCode,
  entrancePosition: EntrancePositionParam,
  options?: ToMTCCRequestHeadersOptions
): MTCCRequestHeaders {
  const {
    function: cloudComputingFunction,
    gnum,
    osType = 'h5',
    ...restOptions
  } = options ?? {};

  const mtccClient = toSnakeDict(
    Object.assign(
      {
        appId: clientId,
        osType,
        countryCode,
        function: toClientFunction(cloudComputingFunction),
        position: toClientEntrancePosition(entrancePosition),
        gnum
      },
      restOptions
    )
  ) as unknown as MTCCClient;

  return {
    'x-mtcc-client': encode(JSON.stringify(mtccClient))
  };
}
