/**
 * 美图账号客户端编号
 * @description 即 `client_id`
 */
export type ClientId = string;

/**
 * 国家/地区码
 * @description [国别代码名称]{@link http://wiki.meitu.com/%E5%9B%BD%E5%88%AB%E4%BB%A3%E7%A0%81%E5%90%8D%E7%A7%B0}
 */
export type CountryCode = string;

/**
 * 平台
 * @description 在 Web / H5 场景下值为 "h5"
 */
export type OsType = 'h5' | 'mini_app' | string;

/**
 * 设备唯一标记
 */
export type Gid = string;

/**
 * 云处理功能名称
 */
export type CloudComputingFunctionName = string | number;

/**
 * 云处理功能
 */
export interface CloudComputingFunction {
  /**
   * 功能名称
   */
  name?: CloudComputingFunctionName;

  /**
   * 效果编号
   */
  effect?: string;
}

/**
 * 入口位置级别
 */
export type EntrancePositionLevel = string | number;

/**
 * 入口位置级别列表
 */
export type EntrancePositionLevelList = EntrancePositionLevel[];

/**
 * 入口位置
 */
export interface EntrancePosition {
  /**
   * 多级分类
   */
  level: EntrancePositionLevelList;
}

/**
 * 入口位置参数
 */
export type EntrancePositionParam =
  | EntrancePositionLevel
  | EntrancePositionLevelList
  | EntrancePosition;

export type ClientPosition<Depth extends number = 1> = Record<
  `level${Depth}`,
  string
>;

export type ClientFunction = Omit<CloudComputingFunction, 'name'> & {
  /**
   * 功能名称
   */
  name?: string;
};

/**
 *  客户端请求头
 */
export type MTCCRequestHeaders = Record<'x-mtcc-client', string>;

export interface MTCCClient<LevelDepth extends number = 1> {
  /**
   * 美图账号客户端编号
   * @description 即 `client_id`
   */
  app_id: ClientId;

  /**
   * 平台
   * @description 在 Web / H5 场景下值为 "h5"
   */
  os_type: OsType;

  /**
   * 国家/地区码
   * @description [国别代码名称]{@link http://wiki.meitu.com/%E5%9B%BD%E5%88%AB%E4%BB%A3%E7%A0%81%E5%90%8D%E7%A7%B0}
   */
  country_code: CountryCode;

  /**
   * 入口位置
   */
  position: ClientPosition<LevelDepth>;

  /**
   * 业务编号
   */
  biz_id?: string;

  /**
   * 云处理功能
   */
  function?: ClientFunction;

  /**
   * 设备唯一标记
   * @description 其实就是 `gid`，在上传通道中需要规避隐私风险故改名
   */
  gnum?: Gid;
}

/**
 * 配置项
 */
export interface ToMTCCRequestHeadersOptions {
  /**
   * 平台
   * @description 在 Web / H5 场景下值为 "h5"
   */
  osType?: OsType;

  /**
   * 业务编号
   */
  bizId?: string;

  /**
   * 云处理功能
   */
  function?: CloudComputingFunction | CloudComputingFunctionName;

  /**
   * 设备唯一标记
   */
  gid?: Gid;

  /**
   * 设备唯一标记
   * @deprecated 使用 `gid` 配置替换
   */
  gnum?: Gid;

  /**
   * 每次业务请求均有一个新唯一标识
   */
  orderId?: string;
  /**
   * 上传的媒体格式。
   */
  mediaType?: string;
  /**
   * 目标生成的媒体格式。
   */
  resMediaType?: string;
  /**
   * 额外参数
   */
  extInfo?: {
    number: string;
  };
}
