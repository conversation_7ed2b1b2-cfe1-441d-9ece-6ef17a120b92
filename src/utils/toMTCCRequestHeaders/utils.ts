import {
  ToMTCCRequestHeadersOptions,
  EntrancePositionParam,
  ClientPosition,
  ClientFunction
} from './types';

export function toClientFunction(
  cloudComputingFunction: ToMTCCRequestHeadersOptions['function']
): ClientFunction | undefined {
  if (cloudComputingFunction === undefined) {
    return;
  }

  if (
    typeof cloudComputingFunction === 'string' ||
    typeof cloudComputingFunction === 'number'
  ) {
    return { name: String(cloudComputingFunction) };
  }

  return Object.assign({}, cloudComputingFunction, {
    name: String(cloudComputingFunction.name)
  });
}

export function toClientEntrancePosition(
  entrancePosition: EntrancePositionParam
): ClientPosition {
  if (
    typeof entrancePosition === 'string' ||
    typeof entrancePosition === 'number'
  ) {
    entrancePosition = [entrancePosition];
  }

  if (Array.isArray(entrancePosition)) {
    entrancePosition = { level: entrancePosition };
  }

  const { level, ...restEntrancePosition } = entrancePosition;

  const clientLevels = level.reduce(
    (acc, level, index) =>
      Object.assign(acc, {
        [`level${index + 1}`]: String(level)
      }),
    {} as ClientPosition
  );

  return Object.assign({}, restEntrancePosition, clientLevels);
}
