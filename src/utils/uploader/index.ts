import { rootStore } from '@/stores/RootStore';
import { UploadResult } from '@meitu/upload-intl';
import compose, { type Middleware } from 'koa-compose';
import { uploadPass } from './middleware/uploadPass';


export type UploadContext = {
  /**
   * 图片文件
   */
  file: File | Blob,
  /**
   * 上传结果
   */
  result?: UploadResult
  /**
   * 中断信号 ！！！！！！写中间件时记得处理！！！！！！！！
   */
  signal?: AbortSignal,
}

export function createUploaderProcessor(middlewares: Array<Middleware<UploadContext>>) {
  return async (context: UploadContext) => {
    await compose(middlewares)(context, async () => {});
    return context;
  };
}


/**
 * 创建上传的工具函数
 * 
 * 如果用户未登陆 则会抛出异常
 * @param accessToken 
 * @returns 
 */
export function createPureUpload(accessToken = rootStore.userStore.authToken) {
  if (!accessToken) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('上传需要accessToken')
    }

    throw new Error('用户未登陆');
  }
  return createUploaderProcessor([
    uploadPass({ accessToken }),
  ]);
}