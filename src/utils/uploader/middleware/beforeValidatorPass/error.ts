import { BeforeValidatorCheckList, BeforeValidatorOptions, FileInfo } from "./types";

const flag = Symbol('before-validator-error');

export class BeforeValidatorError extends Error {
  
  public flag = flag;
  
  constructor(public options: BeforeValidatorOptions, public checkList: BeforeValidatorCheckList, public fileInfo: FileInfo) {
    super();
  }
}

export function isBeforeValidatorError(e: Error) {
  return (e as any).flag === flag;
}