/**
 * 各项是否通过检测
 */
export type BeforeValidatorCheckList = {
  size: boolean;
  dimension: {
    min: boolean;
    max: boolean;
  };
  extension: boolean;
}

export type BeforeValidatorOptions = {
  /**
   * 大小不能超过该值 单位为M byte
   */
  size?: number;
  /**
   * 尺寸的上下限
   */
  dimension?: {
    min?: number;
    max?: number;
  }
  /**
   * 文件类型校验 传支持的文件后缀（例如：png,jpg等）
   */
  extension?: Array<string>;
}

export type FileInfo = {
  type: string;
  size: number;
  dimension: [number, number];
}