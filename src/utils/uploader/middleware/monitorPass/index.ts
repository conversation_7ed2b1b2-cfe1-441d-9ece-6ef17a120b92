import { Middleware } from "koa-compose";
import { UploadContext } from "../..";
import { imageMonitor } from "@/api/image";
import { ImageMonitorError } from "./error";

export function monitorPass(): Middleware<UploadContext> {

  return async function (context, next) {
    const { result, signal } = context;
    if (!result) {
      return;
    } 

    const res = await imageMonitor({
      data: result.url
    });

    signal?.throwIfAborted();

    if (!res.result) {
      throw new ImageMonitorError(res.message || '此图片不合规，请重新选择图片上传');
    }

    return await next();
  }
}