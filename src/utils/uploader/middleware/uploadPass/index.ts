import { Middleware } from "koa-compose";
import { UploadContext } from "../..";
import { upload as internalUpload } from '@/utils/upload';
import mime from "mime";


const defaultUploadConfig = {
  app: 'wheeai',
  type: 'editor'
}

export type UploadPassOptions = {
  app?: string,
  type?: string,
  accessToken: string;
}

export function uploadPass({
  app = defaultUploadConfig.app,
  type = defaultUploadConfig.type,
  accessToken,
}: UploadPassOptions): Middleware<UploadContext> {

  return async function (context, next) {

    const { signal } = context;
    let { file } = context;
    const extension = mime.getExtension(file.type) ?? 'png'
    const suffix = '.' + extension;

    // 上传sdk传入blob会报错 这里需要将blob转为file
    if (!(file instanceof File)) {
      file = new File([file], `upload${suffix}`, { type: file.type });
    }
    context.result = await internalUpload(file, { accessToken }, { app, type, suffix }, { signal });

    return await next();
  }
}