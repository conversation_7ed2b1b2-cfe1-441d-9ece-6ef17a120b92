/**
 * 检测一个字符串是否已经被 encodeURIComponent 编码过
 * @param str - 需要检测的字符串
 * @returns boolean - 如果字符串已经被编码则返回 true，否则返回 false
 */
export function isURLEncoded(str: string): boolean {
  if (!str || typeof str !== 'string') {
    return false;
  }

  // 检查是否包含百分号编码的模式
  const hasEncodedPattern = /%[0-9A-F]{2}/i.test(str);
  if (!hasEncodedPattern) {
    return false;
  }

  try {
    // 尝试解码
    const decoded = decodeURIComponent(str);
    // 如果解码成功，重新编码并比较
    const reEncoded = encodeURIComponent(decoded);

    // 如果重新编码后与原字符串相同，说明原字符串是编码过的
    return reEncoded === str;
  } catch (e) {
    // 如果解码失败，说明不是有效的 URL 编码
    return false;
  }
}

/**
 * 安全地对 URL 进行编码，如果 URL 已经被编码则直接返回，否则进行编码
 * @param url - 需要编码的 URL 字符串
 * @returns string - 编码后的 URL 字符串
 */
export function safeEncodeURL(url: string): string {
  return isURLEncoded(url) ? url : encodeURIComponent(url);
}
