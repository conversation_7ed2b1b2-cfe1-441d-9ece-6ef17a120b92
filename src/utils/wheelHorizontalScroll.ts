/**
 * 直接绑定React的onWheel事件无法调用preventDefault
 */


type HandlerOptions = {
  alwaysPreventDefault?: boolean;
}

export function getWheelHorizontalScrollRefHandler ({
  alwaysPreventDefault = true,
}: HandlerOptions = {}) {

  return function wheelHorizontalScrollRef(el: HTMLDivElement) {
    const handleTabContainerWheel = (event: WheelEvent) => {
      /**
       * 直接通过props中的onWheel绑定时 无法调用preventDefault
       */
      if (alwaysPreventDefault) {
        event.preventDefault;
      }
      const { deltaX, deltaY } = event;
      const tan = deltaY / deltaX;
      // 纵向滚动
      if (Math.abs(tan) > 1) {
        const nextScrollLeft = el.scrollLeft + deltaY;
        el?.scroll({
          left: nextScrollLeft,
          behavior: "instant",
        });
  
        if (nextScrollLeft > 0 && nextScrollLeft < el.scrollWidth - el.clientWidth) {
          event.preventDefault();
        }
      } else {
        el?.scroll({
          left: el.scrollLeft + deltaX,
          behavior: "instant",
        })
      }
    }
  
    el?.addEventListener("wheel", handleTabContainerWheel);
    return () => {
      el?.removeEventListener("wheel", handleTabContainerWheel);
    }
  }
}