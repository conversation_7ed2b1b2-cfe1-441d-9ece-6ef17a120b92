const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const BaseUrl = 'https://smartcat.com';
const Authorization = 'Basic YzUxOGM4YTUtNGRkZC00ZmFkLTgxYTgtYjYzNjg3MzA4NDI3OjM2X2FPZmRCNFNxWDVqbGFZdG1yMUY0VkxGeGE=';
const projectId = '7dda6774-ee95-4737-94e4-0661b0e261c7';

function smartcatProject(languages) {
  return new Promise((resolve, reject) => {
    exec(`curl -X POST ${BaseUrl}/api/integration/v2/project/${projectId}/export?languages=${languages} \
  -H "Authorization: ${Authorization}" -H "Content-Type: application/json"`, (error, stdout, stderr) => {
      if (error) {
        reject(stderr || error);
      } else {
        try {
          resolve(JSON.parse(stdout));
        } catch {
          resolve(stdout); // fallback plain text
        }
      }
    });
  });
}
function smartcatDocument(taskId) {
  const execFn = (resolve, reject) => {
    exec(`curl -X GET ${BaseUrl}/api/integration/v1/document/export/${taskId} \
  -H "Authorization: ${Authorization}"`, (error, stdout, stderr) => {
      if (error) {
        reject(stderr || error);
      } else if (stdout) {
        try {
          resolve(JSON.parse(stdout));
        } catch {
          resolve(stdout); // fallback plain text
        }
      } else {
        setTimeout(() => {
          execFn(resolve, reject);
        }, 300);
      }
    });
  };
  return new Promise((resolve, reject) => {
    execFn(resolve, reject);
  });
}
async function fetchExportedDocument(languages) {
  const taskId = await smartcatProject(languages);
  const document = await smartcatDocument(taskId);
  let filePath = '';
  if (languages === 'en') {
    filePath = 'public/locales/smartcatTranslation/en.json';
  }
  if (languages === 'zh-Hans') {
    filePath = '.public/locales/smartcatTranslation/zh.json';
  }

  // console.log(filePath, 'filePath ====>')
  const outputPath = path.resolve(__dirname, filePath);
  fs.writeFile(outputPath, JSON.stringify(document, null, 4), 'utf8', (err) => {
    if (err) {
      console.error('写入文件时发生错误:', err);
    } else {
      // console.log('文件已成功保存！');
    }
  });
}

fetchExportedDocument('en');
fetchExportedDocument('zh-Hans');
